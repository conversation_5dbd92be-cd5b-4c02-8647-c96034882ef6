<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能算命 - 面相手相耳相分析</title>
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4.1.1/dist/tesseract.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 50px;
            padding: 5px;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 500;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-area.dragover {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .preview-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .analysis-result {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }

        .bagua-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .bagua {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                from 0deg,
                #ff6b6b 0deg 45deg,
                #4ecdc4 45deg 90deg,
                #45b7d1 90deg 135deg,
                #96ceb4 135deg 180deg,
                #feca57 180deg 225deg,
                #ff9ff3 225deg 270deg,
                #54a0ff 270deg 315deg,
                #5f27cd 315deg 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            animation: rotate 20s linear infinite;
        }

        .bagua::before {
            content: '☯';
            font-size: 80px;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .fortune-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .fortune-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .fortune-card:hover {
            transform: translateY(-5px);
        }

        .fortune-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }

        .fortune-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #667eea;
        }

        .fortune-score {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .yijing-hexagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }

        .hexagram-line {
            width: 60px;
            height: 8px;
            margin: 2px 0;
            border-radius: 4px;
        }

        .yang {
            background: #667eea;
        }

        .yin {
            background: linear-gradient(to right, #667eea 0%, #667eea 40%, transparent 40%, transparent 60%, #667eea 60%, #667eea 100%);
        }

        .feature-analysis {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .user-info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-form {
            max-width: 600px;
            margin: 0 auto;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
        }

        .form-group.full-width {
            flex: 100%;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .birth-chart {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .birth-chart h3 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .bazi-display {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
        }

        .bazi-pillar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            min-width: 80px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .bazi-pillar h4 {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .bazi-pillar .tiangan {
            font-size: 24px;
            font-weight: bold;
        }

        .bazi-pillar .dizhi {
            font-size: 20px;
        }

        .wuxing-analysis {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin: 20px 0;
        }

        .wuxing-item {
            text-align: center;
            padding: 15px 10px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
        }

        .wuxing-jin { background: linear-gradient(135deg, #ffd700, #ffed4e); color: #333; }
        .wuxing-mu { background: linear-gradient(135deg, #4ecca3, #44a08d); }
        .wuxing-shui { background: linear-gradient(135deg, #3498db, #2980b9); }
        .wuxing-huo { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .wuxing-tu { background: linear-gradient(135deg, #f39c12, #d68910); }

        .zodiac-display {
            text-align: center;
            margin: 20px 0;
        }

        .zodiac-animal {
            font-size: 4em;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .tabs {
                flex-direction: column;
                border-radius: 15px;
            }
            
            .tab-btn {
                border-radius: 15px;
                margin: 2px 0;
            }

            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .bazi-display {
                justify-content: center;
            }

            .wuxing-analysis {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 AI智能算命</h1>
            <p>基于面相、手相、耳相的人工智能命理分析系统</p>
        </div>

        <div class="main-content">
            <!-- 用户信息输入区域 -->
            <div class="user-info-section">
                <h2 style="text-align: center; color: #667eea; margin-bottom: 20px;">📝 个人信息录入</h2>
                <div class="user-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>姓名</label>
                            <input type="text" id="userName" placeholder="请输入您的姓名">
                        </div>
                        <div class="form-group">
                            <label>性别</label>
                            <select id="userGender">
                                <option value="male">男</option>
                                <option value="female">女</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>出生日期</label>
                            <input type="date" id="birthDate">
                        </div>
                        <div class="form-group">
                            <label>出生时间</label>
                            <select id="birthHour">
                                <option value="23">子时 (23:00-01:00)</option>
                                <option value="1">丑时 (01:00-03:00)</option>
                                <option value="3">寅时 (03:00-05:00)</option>
                                <option value="5">卯时 (05:00-07:00)</option>
                                <option value="7">辰时 (07:00-09:00)</option>
                                <option value="9">巳时 (09:00-11:00)</option>
                                <option value="11">午时 (11:00-13:00)</option>
                                <option value="13">未时 (13:00-15:00)</option>
                                <option value="15">申时 (15:00-17:00)</option>
                                <option value="17">酉时 (17:00-19:00)</option>
                                <option value="19">戌时 (19:00-21:00)</option>
                                <option value="21">亥时 (21:00-23:00)</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label>出生地点</label>
                            <input type="text" id="birthPlace" placeholder="请输入出生城市（可选）">
                        </div>
                    </div>
                </div>
            </div>

            <div class="tabs">
                <button class="tab-btn active" onclick="switchTab('face')">面相分析</button>
                <button class="tab-btn" onclick="switchTab('palm')">手相分析</button>
                <button class="tab-btn" onclick="switchTab('ear')">耳相分析</button>
            </div>

            <!-- 面相分析 -->
            <div id="face" class="tab-content active">
                <div class="upload-area" onclick="document.getElementById('faceInput').click()" 
                     ondrop="handleDrop(event, 'face')" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div class="upload-icon">👤</div>
                    <h3>上传面部照片</h3>
                    <p>支持拖拽上传或点击选择文件（JPG, PNG）</p>
                </div>
                <input type="file" id="faceInput" class="file-input" accept="image/*" onchange="handleFileSelect(event, 'face')">
                <div id="facePreview"></div>
                <button class="btn" onclick="analyzeFace()" id="faceAnalyzeBtn" style="display: none;">开始面相分析</button>
            </div>

            <!-- 手相分析 -->
            <div id="palm" class="tab-content">
                <div class="upload-area" onclick="document.getElementById('palmInput').click()" 
                     ondrop="handleDrop(event, 'palm')" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div class="upload-icon">🖐️</div>
                    <h3>上传手掌照片</h3>
                    <p>请拍摄清晰的手掌照片，包含完整的手掌和手指</p>
                </div>
                <input type="file" id="palmInput" class="file-input" accept="image/*" onchange="handleFileSelect(event, 'palm')">
                <div id="palmPreview"></div>
                <button class="btn" onclick="analyzePalm()" id="palmAnalyzeBtn" style="display: none;">开始手相分析</button>
            </div>

            <!-- 耳相分析 -->
            <div id="ear" class="tab-content">
                <div class="upload-area" onclick="document.getElementById('earInput').click()" 
                     ondrop="handleDrop(event, 'ear')" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div class="upload-icon">👂</div>
                    <h3>上传耳朵照片</h3>
                    <p>请拍摄侧面耳朵照片，确保耳朵轮廓清晰可见</p>
                </div>
                <input type="file" id="earInput" class="file-input" accept="image/*" onchange="handleFileSelect(event, 'ear')">
                <div id="earPreview"></div>
                <button class="btn" onclick="analyzeEar()" id="earAnalyzeBtn" style="display: none;">开始耳相分析</button>
            </div>

            <!-- 加载动画 -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>AI正在分析中，请稍候...</p>
            </div>

            <!-- 分析结果 -->
            <div id="result" class="analysis-result" style="display: none;">
                <h2 style="text-align: center; color: #667eea; margin-bottom: 30px;">🔮 命理分析结果</h2>
                
                <div class="bagua-container">
                    <div class="bagua"></div>
                </div>

                <div id="hexagram" class="yijing-hexagram"></div>

                <div class="fortune-categories">
                    <div class="fortune-card">
                        <div class="fortune-icon">💼</div>
                        <div class="fortune-title">事业运势</div>
                        <div class="fortune-score" id="careerScore">85</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="careerProgress"></div>
                        </div>
                    </div>
                    <div class="fortune-card">
                        <div class="fortune-icon">💕</div>
                        <div class="fortune-title">爱情运势</div>
                        <div class="fortune-score" id="loveScore">78</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="loveProgress"></div>
                        </div>
                    </div>
                    <div class="fortune-card">
                        <div class="fortune-icon">🏠</div>
                        <div class="fortune-title">家庭运势</div>
                        <div class="fortune-score" id="familyScore">92</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="familyProgress"></div>
                        </div>
                    </div>
                    <div class="fortune-card">
                        <div class="fortune-icon">💰</div>
                        <div class="fortune-title">财富运势</div>
                        <div class="fortune-score" id="wealthScore">73</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="wealthProgress"></div>
                        </div>
                    </div>
                    <div class="fortune-card">
                        <div class="fortune-icon">🌟</div>
                        <div class="fortune-title">健康运势</div>
                        <div class="fortune-score" id="healthScore">88</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="healthProgress"></div>
                        </div>
                    </div>
                    <div class="fortune-card">
                        <div class="fortune-icon">🎯</div>
                        <div class="fortune-title">综合运势</div>
                        <div class="fortune-score" id="overallScore">83</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="overallProgress"></div>
                        </div>
                    </div>
                </div>

                <div id="detailedAnalysis"></div>
            </div>
        </div>
    </div>

    <script>
        let currentType = 'face';
        let uploadedImages = {};
        let userBirthData = {};

        // 切换标签页
        function switchTab(type) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 激活当前标签
            event.target.classList.add('active');
            document.getElementById(type).classList.add('active');
            currentType = type;
        }

        // 处理拖拽
        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e, type) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0], type);
            }
        }

        // 处理文件选择
        function handleFileSelect(e, type) {
            const file = e.target.files[0];
            if (file) {
                processFile(file, type);
            }
        }

        // 处理文件
        function processFile(file, type) {
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件！');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                uploadedImages[type] = e.target.result;
                showPreview(e.target.result, type);
                document.getElementById(type + 'AnalyzeBtn').style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        // 显示预览
        function showPreview(src, type) {
            const preview = document.getElementById(type + 'Preview');
            preview.innerHTML = `<img src="${src}" class="preview-image" alt="${type} preview">`;
        }

        // 面相分析
        async function analyzeFace() {
            if (!uploadedImages.face) {
                alert('请先上传面部照片！');
                return;
            }
            
            const userInfo = getUserInfo();
            if (!userInfo) return;
            
            showLoading();
            
            // 模拟OCR和AI分析过程
            setTimeout(() => {
                const features = analyzeFacialFeatures();
                const fortune = calculateEnhancedFortune(features, 'face', userInfo);
                showResult(fortune, features, 'face');
                hideLoading();
            }, 3000);
        }

        // 手相分析
        async function analyzePalm() {
            if (!uploadedImages.palm) {
                alert('请先上传手掌照片！');
                return;
            }
            
            const userInfo = getUserInfo();
            if (!userInfo) return;
            
            showLoading();
            
            setTimeout(() => {
                const features = analyzePalmFeatures();
                const fortune = calculateEnhancedFortune(features, 'palm', userInfo);
                showResult(fortune, features, 'palm');
                hideLoading();
            }, 3000);
        }

        // 耳相分析
        async function analyzeEar() {
            if (!uploadedImages.ear) {
                alert('请先上传耳朵照片！');
                return;
            }
            
            const userInfo = getUserInfo();
            if (!userInfo) return;
            
            showLoading();
            
            setTimeout(() => {
                const features = analyzeEarFeatures();
                const fortune = calculateEnhancedFortune(features, 'ear', userInfo);
                showResult(fortune, features, 'ear');
                hideLoading();
            }, 3000);
        }

        // 分析面部特征
        function analyzeFacialFeatures() {
            return {
                faceShape: Math.random() > 0.5 ? '方形脸' : '圆形脸',
                eyeShape: Math.random() > 0.5 ? '丹凤眼' : '杏眼',
                noseShape: Math.random() > 0.5 ? '鹰钩鼻' : '狮子鼻',
                mouthShape: Math.random() > 0.5 ? '樱桃嘴' : '方口',
                earSize: Math.random() > 0.5 ? '大耳' : '小耳',
                foreheadHeight: Math.random() > 0.5 ? '高额头' : '低额头'
            };
        }

        // 分析手掌特征
        function analyzePalmFeatures() {
            return {
                lifeLineLength: Math.random() > 0.5 ? '长' : '短',
                heartLineShape: Math.random() > 0.5 ? '弯曲' : '直线',
                headLineClarity: Math.random() > 0.5 ? '清晰' : '模糊',
                mountOfVenus: Math.random() > 0.5 ? '饱满' : '平坦',
                fingerLength: Math.random() > 0.5 ? '修长' : '短粗',
                palmColor: Math.random() > 0.5 ? '红润' : '苍白'
            };
        }

        // 分析耳朵特征
        function analyzeEarFeatures() {
            return {
                earSize: Math.random() > 0.5 ? '大耳' : '小耳',
                earShape: Math.random() > 0.5 ? '圆耳' : '尖耳',
                earLobeSize: Math.random() > 0.5 ? '厚实' : '薄小',
                earPosition: Math.random() > 0.5 ? '高位' : '低位',
                earColor: Math.random() > 0.5 ? '红润' : '苍白',
                earHardness: Math.random() > 0.5 ? '坚硬' : '柔软'
            };
        }

        // 计算运势
        function calculateFortune(features, type) {
            const baseScores = {
                career: 60 + Math.random() * 40,
                love: 60 + Math.random() * 40,
                family: 60 + Math.random() * 40,
                wealth: 60 + Math.random() * 40,
                health: 60 + Math.random() * 40
            };

            // 根据特征调整分数
            Object.keys(features).forEach(feature => {
                Object.keys(baseScores).forEach(category => {
                    baseScores[category] += (Math.random() - 0.5) * 20;
                });
            });

            // 确保分数在合理范围内
            Object.keys(baseScores).forEach(key => {
                baseScores[key] = Math.max(30, Math.min(100, Math.round(baseScores[key])));
            });

            baseScores.overall = Math.round(
                (baseScores.career + baseScores.love + baseScores.family + 
                 baseScores.wealth + baseScores.health) / 5
            );

            return baseScores;
        }

        // 生成易经卦象
        function generateHexagram() {
            const lines = [];
            for (let i = 0; i < 6; i++) {
                lines.push(Math.random() > 0.5 ? 'yang' : 'yin');
            }
            return lines;
        }

        // 显示结果
        function showResult(fortune, features, type) {
            const result = document.getElementById('result');
            result.style.display = 'block';

            // 更新运势分数
            document.getElementById('careerScore').textContent = fortune.career;
            document.getElementById('loveScore').textContent = fortune.love;
            document.getElementById('familyScore').textContent = fortune.family;
            document.getElementById('wealthScore').textContent = fortune.wealth;
            document.getElementById('healthScore').textContent = fortune.health;
            document.getElementById('overallScore').textContent = fortune.overall;

            // 更新进度条
            setTimeout(() => {
                document.getElementById('careerProgress').style.width = fortune.career + '%';
                document.getElementById('loveProgress').style.width = fortune.love + '%';
                document.getElementById('familyProgress').style.width = fortune.family + '%';
                document.getElementById('wealthProgress').style.width = fortune.wealth + '%';
                document.getElementById('healthProgress').style.width = fortune.health + '%';
                document.getElementById('overallProgress').style.width = fortune.overall + '%';
            }, 500);

            // 生成卦象
            const hexagram = generateHexagram();
            const hexagramContainer = document.getElementById('hexagram');
            hexagramContainer.innerHTML = '<h3 style="text-align: center; margin-bottom: 15px;">易经卦象</h3>';
            hexagram.forEach(line => {
                const lineElement = document.createElement('div');
                lineElement.className = `hexagram-line ${line}`;
                hexagramContainer.appendChild(lineElement);
            });

            // 显示生辰八字信息
            if (userBirthData.bazi) {
                showBirthChart();
            }

            // 生成详细分析
            generateDetailedAnalysis(features, fortune, type);

            // 滚动到结果区域
            result.scrollIntoView({ behavior: 'smooth' });
        }

        // 生成详细分析
        function generateDetailedAnalysis(features, fortune, type) {
            const container = document.getElementById('detailedAnalysis');
            
            let analysisText = '';
            
            if (type === 'face') {
                analysisText = `
                    <div class="feature-analysis">
                        <h3>👤 面相特征分析</h3>
                        <p><strong>脸型：</strong>${features.faceShape} - ${getFaceShapeAnalysis(features.faceShape)}</p>
                        <p><strong>眼型：</strong>${features.eyeShape} - ${getEyeShapeAnalysis(features.eyeShape)}</p>
                        <p><strong>鼻型：</strong>${features.noseShape} - ${getNoseShapeAnalysis(features.noseShape)}</p>
                        <p><strong>嘴型：</strong>${features.mouthShape} - ${getMouthShapeAnalysis(features.mouthShape)}</p>
                        <p><strong>额头：</strong>${features.foreheadHeight} - ${getForeheadAnalysis(features.foreheadHeight)}</p>
                    </div>
                `;
            } else if (type === 'palm') {
                analysisText = `
                    <div class="feature-analysis">
                        <h3>🖐️ 手相特征分析</h3>
                        <p><strong>生命线：</strong>${features.lifeLineLength} - ${getLifeLineAnalysis(features.lifeLineLength)}</p>
                        <p><strong>感情线：</strong>${features.heartLineShape} - ${getHeartLineAnalysis(features.heartLineShape)}</p>
                        <p><strong>智慧线：</strong>${features.headLineClarity} - ${getHeadLineAnalysis(features.headLineClarity)}</p>
                        <p><strong>金星丘：</strong>${features.mountOfVenus} - ${getVenusAnalysis(features.mountOfVenus)}</p>
                        <p><strong>手指：</strong>${features.fingerLength} - ${getFingerAnalysis(features.fingerLength)}</p>
                    </div>
                `;
            } else if (type === 'ear') {
                analysisText = `
                    <div class="feature-analysis">
                        <h3>👂 耳相特征分析</h3>
                        <p><strong>耳朵大小：</strong>${features.earSize} - ${getEarSizeAnalysis(features.earSize)}</p>
                        <p><strong>耳朵形状：</strong>${features.earShape} - ${getEarShapeAnalysis(features.earShape)}</p>
                        <p><strong>耳垂：</strong>${features.earLobeSize} - ${getEarLobeAnalysis(features.earLobeSize)}</p>
                        <p><strong>耳朵位置：</strong>${features.earPosition} - ${getEarPositionAnalysis(features.earPosition)}</p>
                    </div>
                `;
            }

            analysisText += `
                <div class="feature-analysis">
                    <h3>🌟 综合运势解读</h3>
                    <p><strong>事业运：</strong>${getFortuneAnalysis(fortune.career, '事业')} 建议多与贵人交往，把握机遇。</p>
                    <p><strong>爱情运：</strong>${getFortuneAnalysis(fortune.love, '爱情')} 感情方面需要更多耐心和包容。</p>
                    <p><strong>家庭运：</strong>${getFortuneAnalysis(fortune.family, '家庭')} 家庭和睦，长辈支持较强。</p>
                    <p><strong>财运：</strong>${getFortuneAnalysis(fortune.wealth, '财富')} 理财需谨慎，避免投机。</p>
                    <p><strong>健康运：</strong>${getFortuneAnalysis(fortune.health, '健康')} 注意作息规律，适度运动。</p>
                </div>
                <div class="feature-analysis">
                    <h3>📿 易经智慧指导</h3>
                    <p>根据您的相学特征和易经卦象，建议您在${getSeasonAdvice()}多加注意运势变化。</p>
                    <p>吉利方位：${getLuckyDirection()}</p>
                    <p>幸运颜色：${getLuckyColor()}</p>
                    <p>开运数字：${getLuckyNumbers()}</p>
                    <p>注意事项：${getCautions()}</p>
                </div>
            `;

            container.innerHTML = analysisText;
        }

        // 各种分析函数
        function getFaceShapeAnalysis(shape) {
            const analyses = {
                '方形脸': '意志坚强，做事果断，有领导能力',
                '圆形脸': '性格温和，人缘好，适合从事服务行业'
            };
            return analyses[shape] || '特征明显，个性突出';
        }

        function getEyeShapeAnalysis(shape) {
            const analyses = {
                '丹凤眼': '聪明伶俐，有艺术天赋，桃花运较旺',
                '杏眼': '性格温和，善解人意，家庭观念强'
            };
            return analyses[shape] || '目光有神，洞察力强';
        }

        function getNoseShapeAnalysis(shape) {
            const analyses = {
                '鹰钩鼻': '个性独立，有商业头脑，适合创业',
                '狮子鼻': '有权威性，领导能力强，财运不错'
            };
            return analyses[shape] || '鼻梁挺拔，意志坚定';
        }

        function getMouthShapeAnalysis(shape) {
            const analyses = {
                '樱桃嘴': '性格甜美，人际关系好，有艺术细胞',
                '方口': '说话有分量，适合从政或管理工作'
            };
            return analyses[shape] || '口型端正，言而有信';
        }

        function getForeheadAnalysis(height) {
            const analyses = {
                '高额头': '智慧高，学习能力强，适合从事脑力工作',
                '低额头': '行动力强，实干型人才，适合技术工作'
            };
            return analyses[height] || '额头饱满，智慧过人';
        }

        function getLifeLineAnalysis(length) {
            const analyses = {
                '长': '生命力旺盛，身体健康，长寿之相',
                '短': '需要注意健康，但短而深的生命线也代表精力集中'
            };
            return analyses[length] || '生命力强，体质良好';
        }

        function getHeartLineAnalysis(shape) {
            const analyses = {
                '弯曲': '感情丰富，浪漫主义者，对爱情专一',
                '直线': '理性务实，感情稳定，适合长久关系'
            };
            return analyses[shape] || '感情线清晰，情感丰富';
        }

        function getHeadLineAnalysis(clarity) {
            const analyses = {
                '清晰': '思维敏捷，逻辑性强，智商较高',
                '模糊': '富有想象力，适合艺术创作，但需要专注'
            };
            return analyses[clarity] || '智慧线良好，思维活跃';
        }

        function getVenusAnalysis(shape) {
            const analyses = {
                '饱满': '精力充沛，热情洋溢，异性缘好',
                '平坦': '性格内敛，专注度高，适合专业工作'
            };
            return analyses[shape] || '金星丘发育良好，热情大方';
        }

        function getFingerAnalysis(length) {
            const analyses = {
                '修长': '艺术天赋高，审美能力强，适合精细工作',
                '短粗': '实干能力强，动手能力好，适合技术工作'
            };
            return analyses[length] || '手指形状良好，能力全面';
        }

        function getEarSizeAnalysis(size) {
            const analyses = {
                '大耳': '有福之相，聪明有智慧，容易得到贵人相助',
                '小耳': '心思细腻，做事认真，注重细节'
            };
            return analyses[size] || '耳朵形状良好，听力敏锐';
        }

        function getEarShapeAnalysis(shape) {
            const analyses = {
                '圆耳': '性格温和，人际关系和谐，家庭运好',
                '尖耳': '反应敏捷，学习能力强，有艺术天赋'
            };
            return analyses[shape] || '耳形端正，智慧聪颖';
        }

        function getEarLobeAnalysis(size) {
            const analyses = {
                '厚实': '福禄深厚，晚年运势好，财运亨通',
                '薄小': '早年辛苦，需要靠自己努力创业'
            };
            return analyses[size] || '耳垂形状良好，福运不错';
        }

        function getEarPositionAnalysis(position) {
            const analyses = {
                '高位': '智商较高，学习能力强，容易成功',
                '低位': '实干型人才，做事踏实，晚年发达'
            };
            return analyses[position] || '耳朵位置适中，平衡发展';
        }

        function getFortuneAnalysis(score, category) {
            if (score >= 90) return `${category}运势极佳，前程似锦`;
            if (score >= 80) return `${category}运势很好，顺风顺水`;
            if (score >= 70) return `${category}运势良好，稳步上升`;
            if (score >= 60) return `${category}运势平稳，需要努力`;
            return `${category}运势一般，需要谨慎`;
        }

        function getSeasonAdvice() {
            const seasons = ['春季', '夏季', '秋季', '冬季'];
            return seasons[Math.floor(Math.random() * seasons.length)];
        }

        function getLuckyDirection() {
            const directions = ['东方', '南方', '西方', '北方', '东南方', '西南方', '东北方', '西北方'];
            return directions[Math.floor(Math.random() * directions.length)];
        }

        function getLuckyColor() {
            const colors = ['红色', '黄色', '蓝色', '绿色', '紫色', '金色', '银色', '黑色'];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        function getLuckyNumbers() {
            const numbers = [];
            for (let i = 0; i < 3; i++) {
                numbers.push(Math.floor(Math.random() * 9) + 1);
            }
            return numbers.join(', ');
        }

        function getCautions() {
            const cautions = [
                '避免在阴天做重大决定',
                '注意与小人保持距离',
                '投资理财需谨慎',
                '健康方面多加注意',
                '感情方面需要耐心'
            ];
            return cautions[Math.floor(Math.random() * cautions.length)];
        }

        // 显示/隐藏加载动画
        function showLoading() {
            document.getElementById('loading').classList.add('show');
            document.getElementById('result').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').classList.remove('show');
        }

        // 获取用户信息
        function getUserInfo() {
            const userName = document.getElementById('userName').value;
            const userGender = document.getElementById('userGender').value;
            const birthDate = document.getElementById('birthDate').value;
            const birthHour = parseInt(document.getElementById('birthHour').value);
            const birthPlace = document.getElementById('birthPlace').value;

            if (!userName || !birthDate) {
                alert('请填写姓名和出生日期！');
                return null;
            }

            const birth = new Date(birthDate);
            return {
                name: userName,
                gender: userGender,
                birthYear: birth.getFullYear(),
                birthMonth: birth.getMonth() + 1,
                birthDay: birth.getDate(),
                birthHour: birthHour,
                birthPlace: birthPlace,
                age: new Date().getFullYear() - birth.getFullYear()
            };
        }

        // 生辰八字计算
        function calculateBaZi(userInfo) {
            const tianGan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
            const diZhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
            const hourZhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

            // 简化的天干地支计算（实际应该更复杂）
            const yearIndex = (userInfo.birthYear - 1924) % 60;
            const monthIndex = (userInfo.birthMonth - 1) % 12;
            const dayIndex = calculateDayIndex(userInfo.birthYear, userInfo.birthMonth, userInfo.birthDay);
            const hourIndex = Math.floor(userInfo.birthHour / 2) % 12;

            return {
                year: {
                    gan: tianGan[yearIndex % 10],
                    zhi: diZhi[yearIndex % 12]
                },
                month: {
                    gan: tianGan[(monthIndex + 2) % 10],
                    zhi: diZhi[monthIndex]
                },
                day: {
                    gan: tianGan[dayIndex % 10],
                    zhi: diZhi[dayIndex % 12]
                },
                hour: {
                    gan: tianGan[(hourIndex * 2 + dayIndex) % 10],
                    zhi: hourZhi[hourIndex]
                }
            };
        }

        // 计算日柱天干地支索引
        function calculateDayIndex(year, month, day) {
            // 简化的日柱计算
            const date = new Date(year, month - 1, day);
            const baseDate = new Date(1900, 0, 1);
            const diffDays = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));
            return (diffDays + 40) % 60; // 调整基准
        }

        // 五行分析
        function calculateWuXing(bazi) {
            const wuxingMap = {
                '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
                '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
                '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
                '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
                '戌': '土', '亥': '水'
            };

            const wuxingCount = { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 };
            
            // 统计各天干地支的五行
            Object.values(bazi).forEach(pillar => {
                wuxingCount[wuxingMap[pillar.gan]]++;
                wuxingCount[wuxingMap[pillar.zhi]]++;
            });

            // 计算五行得分
            const total = Object.values(wuxingCount).reduce((sum, count) => sum + count, 0);
            const wuxingScore = {};
            Object.keys(wuxingCount).forEach(element => {
                wuxingScore[element] = Math.round((wuxingCount[element] / total) * 100);
            });

            return wuxingScore;
        }

        // 生肖计算
        function calculateZodiac(year) {
            const zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
            const zodiacEmojis = ['🐭', '🐮', '🐯', '🐰', '🐲', '🐍', '🐴', '🐑', '🐵', '🐔', '🐶', '🐷'];
            const index = (year - 1900) % 12;
            return {
                name: zodiacs[index],
                emoji: zodiacEmojis[index],
                index: index
            };
        }

        // 姓名五行分析
        function calculateNameWuXing(name) {
            // 简化的姓名五行计算（实际应该根据笔画数和偏旁部首）
            const nameWuxing = ['金', '木', '水', '火', '土'];
            const hash = name.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
            return nameWuxing[hash % 5];
        }

        // 结合个人信息的运势计算
        function calculateEnhancedFortune(features, type, userInfo) {
            const baseFortune = calculateFortune(features, type);
            
            if (!userInfo) return baseFortune;

            const bazi = calculateBaZi(userInfo);
            const wuxing = calculateWuXing(bazi);
            const zodiac = calculateZodiac(userInfo.birthYear);
            const nameWuxing = calculateNameWuXing(userInfo.name);

            // 保存生辰信息
            userBirthData = {
                bazi: bazi,
                wuxing: wuxing,
                zodiac: zodiac,
                nameWuxing: nameWuxing,
                userInfo: userInfo
            };

            // 根据五行平衡调整运势
            const wuxingBalance = calculateWuXingBalance(wuxing);
            
            // 根据生肖调整运势
            const zodiacBonus = calculateZodiacBonus(zodiac.index, new Date().getFullYear());
            
            // 根据年龄和性别调整
            const ageGenderModifier = calculateAgeGenderModifier(userInfo.age, userInfo.gender);

            // 应用调整
            Object.keys(baseFortune).forEach(key => {
                if (key !== 'overall') {
                    baseFortune[key] += wuxingBalance[key] || 0;
                    baseFortune[key] += zodiacBonus[key] || 0;
                    baseFortune[key] += ageGenderModifier[key] || 0;
                    baseFortune[key] = Math.max(20, Math.min(100, Math.round(baseFortune[key])));
                }
            });

            // 重新计算综合运势
            baseFortune.overall = Math.round(
                (baseFortune.career + baseFortune.love + baseFortune.family + 
                 baseFortune.wealth + baseFortune.health) / 5
            );

            return baseFortune;
        }

        // 五行平衡分析
        function calculateWuXingBalance(wuxing) {
            const ideal = 20; // 理想情况下每个五行占20%
            const balance = {};
            
            // 五行相生相克的影响
            const wuxingRelation = {
                '金': { benefits: ['water'], harms: ['wood'] },
                '木': { benefits: ['fire'], harms: ['earth'] },
                '水': { benefits: ['wood'], harms: ['fire'] },
                '火': { benefits: ['earth'], harms: ['metal'] },
                '土': { benefits: ['metal'], harms: ['water'] }
            };

            // 计算各运势的五行影响
            balance.career = (wuxing['金'] - ideal) * 0.5;  // 金主事业
            balance.love = (wuxing['火'] - ideal) * 0.5;    // 火主爱情
            balance.family = (wuxing['土'] - ideal) * 0.5;  // 土主家庭
            balance.wealth = (wuxing['水'] - ideal) * 0.5;  // 水主财富
            balance.health = (wuxing['木'] - ideal) * 0.5;  // 木主健康

            return balance;
        }

        // 生肖运势加成
        function calculateZodiacBonus(zodiacIndex, currentYear) {
            const currentZodiacIndex = (currentYear - 1900) % 12;
            const bonus = {};
            
            // 本命年影响
            if (zodiacIndex === currentZodiacIndex) {
                bonus.career = -5;  // 本命年事业需谨慎
                bonus.love = 5;     // 爱情运可能提升
                bonus.family = 10;  // 家庭运势较好
                bonus.wealth = -5;  // 财运需要小心
                bonus.health = -3;  // 健康需要注意
            } else {
                // 根据生肖相合相冲计算
                const harmoniousZodiacs = getHarmoniousZodiacs(zodiacIndex);
                const conflictZodiacs = getConflictZodiacs(zodiacIndex);
                
                if (harmoniousZodiacs.includes(currentZodiacIndex)) {
                    Object.keys(bonus).forEach(key => bonus[key] = 8);
                } else if (conflictZodiacs.includes(currentZodiacIndex)) {
                    Object.keys(bonus).forEach(key => bonus[key] = -5);
                } else {
                    Object.keys(bonus).forEach(key => bonus[key] = 0);
                }
            }
            
            return bonus;
        }

        // 生肖相合
        function getHarmoniousZodiacs(zodiacIndex) {
            const harmony = {
                0: [4, 8], 1: [5, 9], 2: [6, 10], 3: [7, 11],
                4: [0, 8], 5: [1, 9], 6: [2, 10], 7: [3, 11],
                8: [0, 4], 9: [1, 5], 10: [2, 6], 11: [3, 7]
            };
            return harmony[zodiacIndex] || [];
        }

        // 生肖相冲
        function getConflictZodiacs(zodiacIndex) {
            const conflict = {
                0: [6], 1: [7], 2: [8], 3: [9],
                4: [10], 5: [11], 6: [0], 7: [1],
                8: [2], 9: [3], 10: [4], 11: [5]
            };
            return conflict[zodiacIndex] || [];
        }

                 // 年龄性别修正
        function calculateAgeGenderModifier(age, gender) {
            const modifier = {};
            
            // 年龄段影响
            if (age < 25) {
                modifier.career = -5;
                modifier.love = 10;
                modifier.family = 5;
                modifier.wealth = -3;
                modifier.health = 8;
            } else if (age < 35) {
                modifier.career = 10;
                modifier.love = 5;
                modifier.family = 8;
                modifier.wealth = 5;
                modifier.health = 5;
            } else if (age < 50) {
                modifier.career = 8;
                modifier.love = 3;
                modifier.family = 10;
                modifier.wealth = 10;
                modifier.health = 0;
            } else {
                modifier.career = 3;
                modifier.love = 8;
                modifier.family = 10;
                modifier.wealth = 8;
                modifier.health = -5;
            }

            // 性别影响（传统观念，仅供参考）
            if (gender === 'female') {
                modifier.love += 3;
                modifier.family += 5;
            } else {
                modifier.career += 3;
                modifier.wealth += 5;
            }

            return modifier;
        }

        // 显示生辰八字图表
        function showBirthChart() {
            const container = document.getElementById('detailedAnalysis');
            
            let chartHTML = `
                <div class="birth-chart">
                    <h3>🎋 生辰八字</h3>
                    <div class="bazi-display">
                        <div class="bazi-pillar">
                            <h4>年柱</h4>
                            <div class="tiangan">${userBirthData.bazi.year.gan}</div>
                            <div class="dizhi">${userBirthData.bazi.year.zhi}</div>
                        </div>
                        <div class="bazi-pillar">
                            <h4>月柱</h4>
                            <div class="tiangan">${userBirthData.bazi.month.gan}</div>
                            <div class="dizhi">${userBirthData.bazi.month.zhi}</div>
                        </div>
                        <div class="bazi-pillar">
                            <h4>日柱</h4>
                            <div class="tiangan">${userBirthData.bazi.day.gan}</div>
                            <div class="dizhi">${userBirthData.bazi.day.zhi}</div>
                        </div>
                        <div class="bazi-pillar">
                            <h4>时柱</h4>
                            <div class="tiangan">${userBirthData.bazi.hour.gan}</div>
                            <div class="dizhi">${userBirthData.bazi.hour.zhi}</div>
                        </div>
                    </div>
                </div>

                <div class="birth-chart">
                    <h3>🌟 五行分析</h3>
                    <div class="wuxing-analysis">
                        <div class="wuxing-item wuxing-jin">
                            <div>金</div>
                            <div>${userBirthData.wuxing['金']}%</div>
                        </div>
                        <div class="wuxing-item wuxing-mu">
                            <div>木</div>
                            <div>${userBirthData.wuxing['木']}%</div>
                        </div>
                        <div class="wuxing-item wuxing-shui">
                            <div>水</div>
                            <div>${userBirthData.wuxing['水']}%</div>
                        </div>
                        <div class="wuxing-item wuxing-huo">
                            <div>火</div>
                            <div>${userBirthData.wuxing['火']}%</div>
                        </div>
                        <div class="wuxing-item wuxing-tu">
                            <div>土</div>
                            <div>${userBirthData.wuxing['土']}%</div>
                        </div>
                    </div>
                    <p style="text-align: center; margin-top: 15px;">
                        <strong>五行特点：</strong>${getWuXingCharacteristics()}
                    </p>
                </div>

                <div class="birth-chart">
                    <h3>🐲 生肖运势</h3>
                    <div class="zodiac-display">
                        <div class="zodiac-animal">${userBirthData.zodiac.emoji}</div>
                        <h2>${userBirthData.zodiac.name}年生人</h2>
                        <p><strong>姓名五行：</strong>${userBirthData.nameWuxing}</p>
                        <p><strong>本年运势：</strong>${getZodiacYearlyFortune()}</p>
                    </div>
                </div>
            `;
            
            container.innerHTML = chartHTML;
        }

        // 获取五行特点描述
        function getWuXingCharacteristics() {
            const wuxing = userBirthData.wuxing;
            const dominant = Object.keys(wuxing).reduce((a, b) => wuxing[a] > wuxing[b] ? a : b);
            const weak = Object.keys(wuxing).reduce((a, b) => wuxing[a] < wuxing[b] ? a : b);
            
            const characteristics = {
                '金': '果断坚毅，善于决策，适合管理工作',
                '木': '生机勃勃，富有创造力，善于成长发展',
                '水': '智慧灵活，善于变通，具有很强的适应性',
                '火': '热情活跃，富有激情，具有强烈的表现欲',
                '土': '稳重踏实，具有很强的包容性和责任感'
            };
            
            return `以${dominant}为主导（${wuxing[dominant]}%），${characteristics[dominant]}。需补充${weak}行（${wuxing[weak]}%）`;
        }

        // 获取生肖年度运势
        function getZodiacYearlyFortune() {
            const currentYear = new Date().getFullYear();
            const currentZodiacIndex = (currentYear - 1900) % 12;
            const userZodiacIndex = userBirthData.zodiac.index;
            
            if (userZodiacIndex === currentZodiacIndex) {
                return '本命年，需要特别注意，建议佩戴红色饰品化解太岁';
            }
            
            const harmoniousZodiacs = getHarmoniousZodiacs(userZodiacIndex);
            const conflictZodiacs = getConflictZodiacs(userZodiacIndex);
            
            if (harmoniousZodiacs.includes(currentZodiacIndex)) {
                return '运势颇佳，贵人相助，各方面发展顺利';
            } else if (conflictZodiacs.includes(currentZodiacIndex)) {
                return '需要谨慎行事，避免冲突，可请专业化解';
            } else {
                return '运势平稳，按部就班，稳中求进';
            }
        }
    </script>
</body>
</html> 