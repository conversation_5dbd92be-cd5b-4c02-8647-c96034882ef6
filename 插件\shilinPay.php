<?php
/*
 * @Plugin Name: 诗林支付(ShilinPay)
 * @Plugin URI: https://www.coolao.com/shilinpay.html
 * @Description: Wordpress支付整合插件，集支付宝、微信支付、易支付(彩虹)为一体的支付类插件。 
 * @Author: 诗林工作室
 * @Version: 1.0
 * @AuthorUri: https://shilin.studio
 * @Text Domain: shilinpay,诗林支付,alipay,wechatpay,epay,支付宝,微信支付,易支付,集成支付
 * @Date: 2024-11-27 00:31:29
 * @LastEditTime: 2025-03-15 17:11:45
 * Copyright (c) 2024 by Shilin Studio All Rights Reserved. 
 */
// 禁止直接访问
if (!defined('ABSPATH')) {
    die;
}

//定义常量
require_once rtrim(WP_PLUGIN_DIR . '/ShilinPay', '/') . '/Includes/shilinPay.Const.php';
//载入核心文件
require_once rtrim(WP_PLUGIN_DIR . '/ShilinPay', '/') . '/Includes/shilinPay.Core.php';
//数据库操作
register_activation_hook(__FILE__, 'shilinPaySqlData');
register_deactivation_hook(__FILE__, 'shilinPaySqlDataDel');
//重写规则
// register_activation_hook(__FILE__, 'flush_rewrite_rules');
// register_deactivation_hook(__FILE__, 'flush_rewrite_rules');

