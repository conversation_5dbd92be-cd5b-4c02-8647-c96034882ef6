/**
 *
 * ---------------------------------------------------------
 * SHILIN FRAMEWORK RTL CSS MAP
 * <AUTHOR>
 * @<PERSON><PERSON> https://shilin.studio
 * ---------------------------------------------------------
 *
 * 01. Base
 *     01. 01. Header
 *     01. 02. <PERSON><PERSON>
 *     01. 03. Navigation
 *     01. 04. Content
 *     01. 05. Section
 *     01. 06. Show All Settings
 *     01. 07. Search Input
 *     01. 08. Copyright
 *     01. 09. Metabox
 * 02. Fields
 *     02. 01. Field: typography
 *     02. 02. Field: checkbox, radio
 *     02. 03. Field: switcher
 *     02. 04. Field: upload
 *     02. 05. Field: group
 *     02. 06. Field: repeater
 *     02. 07. Field: help
 *     02. 08. Field: icon
 *     02. 09. Field: gallery
 *     02. 10. Field: sorter
 *     02. 11. Field: tabbed
 *     02. 12. Field: media
 *     02. 13. Field: notice
 *     02. 14. Field: border, spacing, dimensions
 *     02. 15. Field: background
 *     02. 16. Field: spinner
 *     02. 17. Field: slider
 *     02. 18. Field: button_set
 *     02. 19. Field: link_color
 *     02. 20. Field: color_group
 *     02. 21. Field: palette
 *     02. 22. Field: accordion
 *     02. 23. Field: date
 *     02. 24. Field: map
 *     02. 25. Field: sortable
 *     02. 26. Field: number
 * 03. Taxonomy
 * 04. Profile
 * 05. Nav Menu
 * 06. Modal
 * 07. Customizer
 * 08. Responsive
 * 09. Others
 *
 * ---------------------------------------------------------
 *
 */
/**
 * 01. Base
 */
.shilin-options {
  margin-left: 20px;
  margin-right: 0;
}

/**
 * 01. 01. Header
 */
.shilin-header h1 {
  float: right;
}
.shilin-header fieldset {
  float: left;
}

/**
 * 01. 02. Header Buttons
 */
.shilin-buttons {
  float: left;
  direction: ltr;
}

.shilin-header-left {
  float: right;
}

.shilin-header-right {
  float: left;
}

/**
 * 01. 03. Navigation
 */
.shilin-nav {
  float: right;
}
.shilin-nav ul {
  clear: right;
}
.shilin-nav ul li .shilin-active:after {
  right: auto;
  left: 0;
  border-left-color: #fff;
  border-right-color: transparent;
}
.shilin-nav ul li .shilin-arrow:after {
  content: "\f053";
  right: auto;
  left: 10px;
}
.shilin-nav ul li.shilin-tab-expanded .shilin-arrow:after {
  transform: rotate(-90deg);
}
.shilin-nav ul ul li a {
  padding-right: 25px;
  padding-left: 15px;
}
.shilin-nav ul ul:before {
  left: auto;
  right: 15px;
}
.shilin-nav .shilin-tab-icon {
  margin-left: 5px;
  margin-right: 0;
}

.shilin-nav-background {
  left: auto;
  right: 0;
}

/**
 * 01. 04. Content
 */
.shilin-content {
  margin-left: 0;
  margin-right: 225px;
}

/**
 * 01. 05. Section
 */
.shilin-sections {
  float: right;
}

/**
 * 01. 06. Show all options
 */
.shilin-show-all .shilin-content {
  margin-right: 0;
  overflow: hidden;
}

.shilin-expand-all {
  float: right;
  right: auto;
  left: 40px;
  margin-right: 0;
  margin-left: 4px;
}

/**
 * 01. 07. Search Input
 */
.shilin-search {
  float: right;
}
.shilin-search input {
  margin: 0 0 0 5px;
}

/**
 * 01. 08. Copyright
 */
.shilin-copyright {
  float: right;
}

/**
 * 01. 09. Metabox
 */
.shilin-metabox {
  margin: -6px -12px -12px -12px;
}
.shilin-metabox .shilin-section-title {
  padding: 20px;
}

.shilin-section-title .shilin-section-icon {
  margin-left: 5px;
  margin-right: 0;
}

/**
 * 02. Fields
 */
.shilin-field .shilin-title {
  float: right;
}
.shilin-field .shilin-fieldset {
  float: left;
}

.shilin-pseudo-field {
  padding: 0 0 0 5px !important;
}

/**
 * 02. 01. Field: typography
 */
.shilin-field-typography select {
  margin: 0;
  width: 100%;
}
.shilin-field-typography .shilin--blocks-inputs .shilin--blocks {
  flex-direction: row-reverse;
}
.shilin-field-typography .shilin--unit {
  left: 4px;
  right: auto;
}

/**
 * 02. 02. Field: checkbox, radio
 */
.shilin-field-checkbox ul ul li,
.shilin-field-radio ul ul li {
  margin-left: 0;
  margin-right: 8px;
}
.shilin-field-checkbox ul ul li:first-child,
.shilin-field-radio ul ul li:first-child {
  margin-right: 0;
}
.shilin-field-checkbox .shilin--inline-list li,
.shilin-field-radio .shilin--inline-list li {
  margin-right: 0;
  margin-left: 15px;
}
.shilin-field-checkbox .shilin--text,
.shilin-field-radio .shilin--text {
  margin-left: 0;
  margin-right: 5px;
}

/**
 * 02. 03. Field: switcher
 */
.shilin-field-switcher .shilin--switcher {
  float: right;
}
.shilin-field-switcher .shilin--label {
  float: right;
  margin-left: 0;
  margin-right: 5px;
}

/**
 * 02. 04. Field: upload
 */
.shilin-field-upload .shilin--remove,
.shilin-field-upload .shilin--button {
  margin-left: 0;
  margin-right: 5px;
}

/**
 * 02. 05. Field: group
 */
.shilin-field-group .shilin-cloneable-title {
  padding: 15px 10px 15px 65px;
}
.shilin-field-group .shilin-cloneable-helper {
  right: auto;
  left: 10px;
}

/**
 * 02. 06. Field: repeater
 */
.shilin-field-repeater .shilin-repeater-helper {
  border-left: 0;
  border-right: 1px solid #eee;
}

/**
 * 02. 07. Field: help
 */
.shilin-help {
  right: auto;
  left: 5px;
}

/**
 * 02. 08. Field: icon
 */
.shilin-field-icon .button {
  margin-right: 0;
  margin-left: 5px;
}
.shilin-field-icon .shilin-icon-preview i {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 09. Field: gallery
 */
.shilin-field-gallery ul li {
  margin-right: 0;
  margin-left: 5px;
}
.shilin-field-gallery .button {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 11. Field: tabbed
 */
.shilin-field-tabbed .shilin-tabbed-nav .shilin--icon {
  padding-right: 0;
  padding-left: 5px;
}
.shilin-field-tabbed .shilin-tabbed-nav a {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 12. Field: media
 */
.shilin-field-media .button {
  margin-left: 0;
  margin-right: 7px;
}
.shilin-field-media .hidden + .button {
  margin-right: 0;
}

/**
 * 02. 13. Field: notice
 */
.shilin-notice {
  border-left: none;
  border-right-style: solid;
  border-right-width: 4px;
}

/**
 * 02. 14. Field: border, spacing, dimensions
 */
.shilin-field-dimensions .shilin--input,
.shilin-field-dimensions .shilin-fieldset,
.shilin-field-spacing .shilin--input,
.shilin-field-spacing .shilin-fieldset,
.shilin-field-border .shilin--input,
.shilin-field-border .shilin-fieldset {
  direction: ltr;
}
.shilin-field-dimensions .shilin--inputs,
.shilin-field-dimensions .shilin--color,
.shilin-field-spacing .shilin--inputs,
.shilin-field-spacing .shilin--color,
.shilin-field-border .shilin--inputs,
.shilin-field-border .shilin--color {
  float: right;
}
.shilin-field-dimensions .shilin--color,
.shilin-field-spacing .shilin--color,
.shilin-field-border .shilin--color {
  margin-right: 4px;
  direction: rtl;
}

/**
 * 02. 15. Field: background
 */
.shilin-field-background .shilin--block {
  float: right;
}
.shilin-field-background .shilin--select,
.shilin-field-background .shilin--media {
  padding-right: 0;
}
.shilin-field-background .shilin--title {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 02. 16. Field: spinner
 */
.shilin-field-spinner .shilin--spin {
  float: right;
  direction: ltr;
}

/**
 * 02. 17. Field: slider
 */
.shilin-field-slider .shilin-slider-ui {
  direction: ltr;
  margin-right: 0;
  margin-left: 15px;
}
.shilin-field-slider .shilin--input {
  direction: ltr;
}

/**
 * 02. 18. Field: button_set
 */
.shilin-field-button_set .shilin--button-group {
  float: right;
}

/**
 * 02. 19. Field: link_color
 */
.shilin-field-link_color .shilin--left {
  float: right;
  margin-right: 0;
  margin-left: 10px;
}

/**
 * 02. 20. Field: color_group
 */
.shilin-field-color_group .shilin--left {
  float: right;
  margin-right: 0;
  margin-left: 10px;
}

/**
 * 02. 21. Field: palette
 */
.shilin-field-palette .shilin--palette {
  margin-right: 0;
  margin-left: 10px;
}

/**
 * 02. 22. Field: accordion
 */
.shilin-field-accordion .shilin--icon {
  margin-right: 0;
  margin-left: 2px;
}

/**
 * 02. 23. Field: date
 */
.shilin-field-date .shilin--to {
  margin-left: 0;
  margin-right: 7px;
}

/**
 * 02. 24. Field: map
 */
.shilin-field-map .shilin--map-input:last-child {
  padding-left: 0;
  padding-right: 10px;
}

/**
 * 02. 25. Field: sortable
 */
.shilin-field-sortable .shilin-sortable-helper {
  border-left: none;
  border-right: 1px solid #eee;
}

/**
 * 02. 26. Field: number
 */
.shilin-field-number .shilin--wrap {
  float: right;
}
.shilin-field-number .shilin--unit {
  left: 4px;
  right: auto;
}

/**
 * 03. Taxonomy
 */
.shilin-taxonomy-add-fields {
  margin-right: 0;
  margin-left: 30px;
}
.shilin-taxonomy-add-fields .shilin-field > .shilin-fieldset > .shilin-help {
  left: -5px;
  right: auto;
}

.shilin-taxonomy-edit-fields {
  margin-right: 0;
  margin-left: 35px;
}
.shilin-taxonomy-edit-fields .shilin-field > .shilin-fieldset > .shilin-help {
  right: auto;
  left: -5px;
}

/**
 * 04. Profile
 */
.shilin-profile-options > h2 > .fa {
  padding-right: 0;
  padding-left: 7px;
}
.shilin-profile-options > .shilin-field > .shilin-fieldset > .shilin-help {
  left: -5px;
  right: auto;
}

/**
 * 05. Nav Menu
 */
.shilin-nav-menu-options > .shilin-fields {
  margin-left: 0;
  margin-right: -10px;
}

.shilin-nav-menu-title {
  padding-left: 14px;
  padding-right: 12px;
}

.shilin-nav-menu-icon {
  margin-right: 0;
  margin-left: 5px;
}

/**
 * 06. Modal
 */
.shilin-modal-content .shilin-field {
  padding: 15px 15px 15px 30px;
}

.shilin-modal-title {
  padding: 0 16px 0 36px;
}

.shilin-modal-close {
  right: auto;
  left: 0;
}

/**
 * 07. Customizer
 */
.control-section .shilin-field .shilin-fieldset {
  margin-right: 0;
}

/**
 * 08. Responsive
 */
@media only screen and (max-width: 1200px) {
  .shilin-metabox .shilin-field .shilin-fieldset {
    margin-left: 0;
  }
}
@media only screen and (max-width: 782px) {
  .shilinPanel .shilin-fieldset,
  .shilinPanel .shilin-content {
    margin-right: 0;
  }
}
/**
 * 09. Others
 */
.shilin-field .shilin--transparent-slider {
  margin-left: 0;
  margin-right: 2px;
}
.shilin-field .shilin--transparent-slider .ui-slider-handle {
  margin: 0 -11px;
}
.shilin-field .shilin--transparent-offset {
  background-position: center right;
}
.shilin-field .shilin--transparent-text {
  right: auto;
  left: 10px;
}
