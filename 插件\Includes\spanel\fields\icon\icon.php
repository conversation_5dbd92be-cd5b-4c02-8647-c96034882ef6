<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.
/**
 *
 * Field: icon
 *
 * <AUTHOR> Studio
 * @Uri https://shilin.studio
 *
 */
if ( ! class_exists( 'Shilin_Field_icon' ) ) {
  class Shilin_Field_icon extends Shilin_Fields {

    public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
      parent::__construct( $field, $value, $unique, $where, $parent );
    }

    public function render() {

      $args = wp_parse_args( $this->field, array(
        'button_title' => esc_html__( 'Add Icon', 'shilin' ),
        'remove_title' => esc_html__( 'Remove Icon', 'shilin' ),
      ) );

      echo $this->field_before();

      $nonce  = wp_create_nonce( 'shilin_icon_nonce' );
      $hidden = ( empty( $this->value ) ) ? ' hidden' : '';

      echo '<div class="shilin-icon-select">';
      echo '<span class="shilin-icon-preview'. esc_attr( $hidden ) .'"><i class="'. esc_attr( $this->value ) .'"></i></span>';
      echo '<a href="#" class="button button-primary shilin-icon-add" data-nonce="'. esc_attr( $nonce ) .'">'. $args['button_title'] .'</a>';
      echo '<a href="#" class="button shilin-warning-primary shilin-icon-remove'. esc_attr( $hidden ) .'">'. $args['remove_title'] .'</a>';
      echo '<input type="hidden" name="'. esc_attr( $this->field_name() ) .'" value="'. esc_attr( $this->value ) .'" class="shilin-icon-value"'. $this->field_attributes() .' />';
      echo '</div>';

      echo $this->field_after();

    }

    public function enqueue() {
      add_action( 'admin_footer', array( 'Shilin_Field_icon', 'add_footer_modal_icon' ) );
      add_action( 'customize_controls_print_footer_scripts', array( 'Shilin_Field_icon', 'add_footer_modal_icon' ) );
    }

    public static function add_footer_modal_icon() {
    ?>
      <div id="shilin-modal-icon" class="shilin-modal shilin-modal-icon hidden">
        <div class="shilin-modal-table">
          <div class="shilin-modal-table-cell">
            <div class="shilin-modal-overlay"></div>
            <div class="shilin-modal-inner">
              <div class="shilin-modal-title">
                <?php esc_html_e( 'Add Icon', 'shilin' ); ?>
                <div class="shilin-modal-close shilin-icon-close"></div>
              </div>
              <div class="shilin-modal-header">
                <input type="text" placeholder="<?php esc_html_e( 'Search...', 'shilin' ); ?>" class="shilin-icon-search" />
              </div>
              <div class="shilin-modal-content">
                <div class="shilin-modal-loading"><div class="shilin-loading"></div></div>
                <div class="shilin-modal-load"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <?php
    }

  }
}
