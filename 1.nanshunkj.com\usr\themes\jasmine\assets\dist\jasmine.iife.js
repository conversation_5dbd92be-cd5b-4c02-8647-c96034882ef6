var jasmine=function(P){"use strict";const le="",ue="";/**
  * Sticky Sidebar JavaScript Plugin.
  * @version 3.3.1
  * <AUTHOR> <a<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>
  * @license The MIT License (MIT)
  */const G=(()=>{const c=".stickySidebar",m={topSpacing:0,bottomSpacing:0,containerSelector:!1,innerWrapperSelector:".inner-wrapper-sticky",stickyClass:"is-affixed",resizeSensor:!0,minWidth:!1};class l{constructor(e,r={}){if(this.options=l.extend(m,r),this.sidebar=typeof e=="string"?document.querySelector(e):e,typeof this.sidebar>"u")throw new Error("There is no specific sidebar element.");this.sidebarInner=!1,this.container=this.sidebar.parentElement,this.affixedType="STATIC",this.direction="down",this.support={transform:!1,transform3d:!1},this._initialized=!1,this._reStyle=!1,this._breakpoint=!1,this._resizeListeners=[],this.dimensions={translateY:0,topSpacing:0,lastTopSpacing:0,bottomSpacing:0,lastBottomSpacing:0,sidebarHeight:0,sidebarWidth:0,containerTop:0,containerHeight:0,viewportHeight:0,viewportTop:0,lastViewportTop:0},["handleEvent"].forEach(n=>{this[n]=this[n].bind(this)}),this.initialize()}initialize(){if(this._setSupportFeatures(),this.options.innerWrapperSelector&&(this.sidebarInner=this.sidebar.querySelector(this.options.innerWrapperSelector),this.sidebarInner===null&&(this.sidebarInner=!1)),!this.sidebarInner){let e=document.createElement("div");for(e.setAttribute("class","inner-wrapper-sticky"),this.sidebar.appendChild(e);this.sidebar.firstChild!=e;)e.appendChild(this.sidebar.firstChild);this.sidebarInner=this.sidebar.querySelector(".inner-wrapper-sticky")}if(this.options.containerSelector){let e=document.querySelectorAll(this.options.containerSelector);if(e=Array.prototype.slice.call(e),e.forEach((r,n)=>{r.contains(this.sidebar)&&(this.container=r)}),!e.length)throw new Error("The container does not contains on the sidebar.")}typeof this.options.topSpacing!="function"&&(this.options.topSpacing=parseInt(this.options.topSpacing)||0),typeof this.options.bottomSpacing!="function"&&(this.options.bottomSpacing=parseInt(this.options.bottomSpacing)||0),this._widthBreakpoint(),this.calcDimensions(),this.stickyPosition(),this.bindEvents(),this._initialized=!0}bindEvents(){window.addEventListener("resize",this,{passive:!0,capture:!1}),window.addEventListener("scroll",this,{passive:!0,capture:!1}),this.sidebar.addEventListener("update"+c,this),this.options.resizeSensor&&typeof ResizeSensor<"u"&&(new ResizeSensor(this.sidebarInner,this.handleEvent),new ResizeSensor(this.container,this.handleEvent))}handleEvent(e){this.updateSticky(e)}calcDimensions(){if(!this._breakpoint){var e=this.dimensions;e.containerTop=l.offsetRelative(this.container).top,e.containerHeight=this.container.clientHeight,e.containerBottom=e.containerTop+e.containerHeight,e.sidebarHeight=this.sidebarInner.offsetHeight,e.sidebarWidth=this.sidebar.offsetWidth,e.viewportHeight=window.innerHeight,this._calcDimensionsWithScroll()}}_calcDimensionsWithScroll(){var e=this.dimensions;e.sidebarLeft=l.offsetRelative(this.sidebar).left,e.viewportTop=document.documentElement.scrollTop||document.body.scrollTop,e.viewportBottom=e.viewportTop+e.viewportHeight,e.viewportLeft=document.documentElement.scrollLeft||document.body.scrollLeft,e.topSpacing=this.options.topSpacing,e.bottomSpacing=this.options.bottomSpacing,typeof e.topSpacing=="function"&&(e.topSpacing=parseInt(e.topSpacing(this.sidebar))||0),typeof e.bottomSpacing=="function"&&(e.bottomSpacing=parseInt(e.bottomSpacing(this.sidebar))||0),this.affixedType==="VIEWPORT-TOP"?e.topSpacing<e.lastTopSpacing&&(e.translateY+=e.lastTopSpacing-e.topSpacing,this._reStyle=!0):this.affixedType==="VIEWPORT-BOTTOM"&&e.bottomSpacing<e.lastBottomSpacing&&(e.translateY+=e.lastBottomSpacing-e.bottomSpacing,this._reStyle=!0),e.lastTopSpacing=e.topSpacing,e.lastBottomSpacing=e.bottomSpacing}isSidebarFitsViewport(){return this.dimensions.sidebarHeight<this.dimensions.viewportHeight}observeScrollDir(){var e=this.dimensions;if(e.lastViewportTop!==e.viewportTop){var r=this.direction==="down"?Math.min:Math.max;e.viewportTop===r(e.viewportTop,e.lastViewportTop)&&(this.direction=this.direction==="down"?"up":"down")}}getAffixType(){var e=this.dimensions,r=!1;this._calcDimensionsWithScroll();var n=e.sidebarHeight+e.containerTop,t=e.viewportTop+e.topSpacing,s=e.viewportBottom-e.bottomSpacing;return this.direction==="up"?t<=e.containerTop?(e.translateY=0,r="STATIC"):t<=e.translateY+e.containerTop?(e.translateY=t-e.containerTop,r="VIEWPORT-TOP"):!this.isSidebarFitsViewport()&&e.containerTop<=t&&(r="VIEWPORT-UNBOTTOM"):this.isSidebarFitsViewport()?e.sidebarHeight+t>=e.containerBottom?(e.translateY=e.containerBottom-n,r="CONTAINER-BOTTOM"):t>=e.containerTop&&(e.translateY=t-e.containerTop,r="VIEWPORT-TOP"):e.containerBottom<=s?(e.translateY=e.containerBottom-n,r="CONTAINER-BOTTOM"):n+e.translateY<=s?(e.translateY=s-n,r="VIEWPORT-BOTTOM"):e.containerTop+e.translateY<=t&&(r="VIEWPORT-UNBOTTOM"),e.translateY=Math.max(0,e.translateY),e.translateY=Math.min(e.containerHeight,e.translateY),e.lastViewportTop=e.viewportTop,r}_getStyle(e){if(!(typeof e>"u")){var r={inner:{},outer:{}},n=this.dimensions;switch(e){case"VIEWPORT-TOP":r.inner={position:"fixed",top:n.topSpacing,left:n.sidebarLeft-n.viewportLeft,width:n.sidebarWidth};break;case"VIEWPORT-BOTTOM":r.inner={position:"fixed",top:"auto",left:n.sidebarLeft,bottom:n.bottomSpacing,width:n.sidebarWidth};break;case"CONTAINER-BOTTOM":case"VIEWPORT-UNBOTTOM":let t=this._getTranslate(0,n.translateY+"px");t?r.inner={transform:t}:r.inner={position:"absolute",top:n.translateY,width:n.sidebarWidth};break}switch(e){case"VIEWPORT-TOP":case"VIEWPORT-BOTTOM":case"VIEWPORT-UNBOTTOM":case"CONTAINER-BOTTOM":r.outer={height:n.sidebarHeight,position:"relative"};break}return r.outer=l.extend({height:"",position:""},r.outer),r.inner=l.extend({position:"relative",top:"",left:"",bottom:"",width:"",transform:this._getTranslate()},r.inner),r}}stickyPosition(e){if(!this._breakpoint){e=this._reStyle||e||!1,this.options.topSpacing,this.options.bottomSpacing;var r=this.getAffixType(),n=this._getStyle(r);if((this.affixedType!=r||e)&&r){let t="affix."+r.toLowerCase().replace("viewport-","")+c;l.eventTrigger(this.sidebar,t),r==="STATIC"?l.removeClass(this.sidebar,this.options.stickyClass):l.addClass(this.sidebar,this.options.stickyClass);for(let d in n.outer)n.outer[d],this.sidebar.style[d]=n.outer[d];for(let d in n.inner){let g=typeof n.inner[d]=="number"?"px":"";this.sidebarInner.style[d]=n.inner[d]+g}let s="affixed."+r.toLowerCase().replace("viewport-","")+c;l.eventTrigger(this.sidebar,s)}else this._initialized&&(this.sidebarInner.style.left=n.inner.left);this.affixedType=r}}_widthBreakpoint(){window.innerWidth<=this.options.minWidth?(this._breakpoint=!0,this.affixedType="STATIC",this.sidebar.removeAttribute("style"),l.removeClass(this.sidebar,this.options.stickyClass),this.sidebarInner.removeAttribute("style")):this._breakpoint=!1}updateSticky(e={}){this._running||(this._running=!0,(r=>{requestAnimationFrame(()=>{switch(r){case"scroll":this._calcDimensionsWithScroll(),this.observeScrollDir(),this.stickyPosition();break;case"resize":default:this._widthBreakpoint(),this.calcDimensions(),this.stickyPosition(!0);break}this._running=!1})})(e.type))}_setSupportFeatures(){var e=this.support;e.transform=l.supportTransform(),e.transform3d=l.supportTransform(!0)}_getTranslate(e=0,r=0,n=0){return this.support.transform3d?"translate3d("+e+", "+r+", "+n+")":this.support.translate?"translate("+e+", "+r+")":!1}destroy(){window.removeEventListener("resize",this,{caption:!1}),window.removeEventListener("scroll",this,{caption:!1}),this.sidebar.classList.remove(this.options.stickyClass),this.sidebar.style.minHeight="",this.sidebar.removeEventListener("update"+c,this);var e={inner:{},outer:{}};e.inner={position:"",top:"",left:"",bottom:"",width:"",transform:""},e.outer={height:"",position:""};for(let r in e.outer)this.sidebar.style[r]=e.outer[r];for(let r in e.inner)this.sidebarInner.style[r]=e.inner[r];this.options.resizeSensor&&typeof ResizeSensor<"u"&&(ResizeSensor.detach(this.sidebarInner,this.handleEvent),ResizeSensor.detach(this.container,this.handleEvent))}static supportTransform(e){var r=!1,n=e?"perspective":"transform",t=n.charAt(0).toUpperCase()+n.slice(1),s=["Webkit","Moz","O","ms"],d=document.createElement("support"),g=d.style;return(n+" "+s.join(t+" ")+t).split(" ").forEach(function(E,y){if(g[E]!==void 0)return r=E,!1}),r}static eventTrigger(e,r,n){try{var t=new CustomEvent(r,{detail:n})}catch{var t=document.createEvent("CustomEvent");t.initCustomEvent(r,!0,!0,n)}e.dispatchEvent(t)}static extend(e,r){var n={};for(let t in e)typeof r[t]<"u"?n[t]=r[t]:n[t]=e[t];return n}static offsetRelative(e){var r={left:0,top:0};do{let n=e.offsetTop,t=e.offsetLeft;isNaN(n)||(r.top+=n),isNaN(t)||(r.left+=t),e=e.tagName==="BODY"?e.parentElement:e.offsetParent}while(e);return r}static addClass(e,r){l.hasClass(e,r)||(e.classList?e.classList.add(r):e.className+=" "+r)}static removeClass(e,r){l.hasClass(e,r)&&(e.classList?e.classList.remove(r):e.className=e.className.replace(new RegExp("(^|\\b)"+r.split(" ").join("|")+"(\\b|$)","gi")," "))}static hasClass(e,r){return e.classList?e.classList.contains(r):new RegExp("(^| )"+r+"( |$)","gi").test(e.className)}}return l})();window.StickySidebar=G;var j=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Z(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var q={exports:{}};(function(c){var m=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
  * Prism: Lightweight, robust, elegant syntax highlighting
  *
  * @license MIT <https://opensource.org/licenses/MIT>
  * <AUTHOR> Verou <https://lea.verou.me>
  * @namespace
  * @public
  */var l=function(f){var e=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,r=0,n={},t={manual:f.Prism&&f.Prism.manual,disableWorkerMessageHandler:f.Prism&&f.Prism.disableWorkerMessageHandler,util:{encode:function a(i){return i instanceof s?new s(i.type,a(i.content),i.alias):Array.isArray(i)?i.map(a):i.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(a){return Object.prototype.toString.call(a).slice(8,-1)},objId:function(a){return a.__id||Object.defineProperty(a,"__id",{value:++r}),a.__id},clone:function a(i,o){o=o||{};var u,p;switch(t.util.type(i)){case"Object":if(p=t.util.objId(i),o[p])return o[p];u={},o[p]=u;for(var h in i)i.hasOwnProperty(h)&&(u[h]=a(i[h],o));return u;case"Array":return p=t.util.objId(i),o[p]?o[p]:(u=[],o[p]=u,i.forEach(function(S,b){u[b]=a(S,o)}),u);default:return i}},getLanguage:function(a){for(;a;){var i=e.exec(a.className);if(i)return i[1].toLowerCase();a=a.parentElement}return"none"},setLanguage:function(a,i){a.className=a.className.replace(RegExp(e,"gi"),""),a.classList.add("language-"+i)},currentScript:function(){if(typeof document>"u")return null;if("currentScript"in document&&1<2)return document.currentScript;try{throw new Error}catch(u){var a=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(u.stack)||[])[1];if(a){var i=document.getElementsByTagName("script");for(var o in i)if(i[o].src==a)return i[o]}return null}},isActive:function(a,i,o){for(var u="no-"+i;a;){var p=a.classList;if(p.contains(i))return!0;if(p.contains(u))return!1;a=a.parentElement}return!!o}},languages:{plain:n,plaintext:n,text:n,txt:n,extend:function(a,i){var o=t.util.clone(t.languages[a]);for(var u in i)o[u]=i[u];return o},insertBefore:function(a,i,o,u){u=u||t.languages;var p=u[a],h={};for(var S in p)if(p.hasOwnProperty(S)){if(S==i)for(var b in o)o.hasOwnProperty(b)&&(h[b]=o[b]);o.hasOwnProperty(S)||(h[S]=p[S])}var T=u[a];return u[a]=h,t.languages.DFS(t.languages,function(v,N){N===T&&v!=a&&(this[v]=h)}),h},DFS:function a(i,o,u,p){p=p||{};var h=t.util.objId;for(var S in i)if(i.hasOwnProperty(S)){o.call(i,S,i[S],u||S);var b=i[S],T=t.util.type(b);T==="Object"&&!p[h(b)]?(p[h(b)]=!0,a(b,o,null,p)):T==="Array"&&!p[h(b)]&&(p[h(b)]=!0,a(b,o,S,p))}}},plugins:{},highlightAll:function(a,i){t.highlightAllUnder(document,a,i)},highlightAllUnder:function(a,i,o){var u={callback:o,container:a,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};t.hooks.run("before-highlightall",u),u.elements=Array.prototype.slice.apply(u.container.querySelectorAll(u.selector)),t.hooks.run("before-all-elements-highlight",u);for(var p=0,h;h=u.elements[p++];)t.highlightElement(h,i===!0,u.callback)},highlightElement:function(a,i,o){var u=t.util.getLanguage(a),p=t.languages[u];t.util.setLanguage(a,u);var h=a.parentElement;h&&h.nodeName.toLowerCase()==="pre"&&t.util.setLanguage(h,u);var S=a.textContent,b={element:a,language:u,grammar:p,code:S};function T(N){b.highlightedCode=N,t.hooks.run("before-insert",b),b.element.innerHTML=b.highlightedCode,t.hooks.run("after-highlight",b),t.hooks.run("complete",b),o&&o.call(b.element)}if(t.hooks.run("before-sanity-check",b),h=b.element.parentElement,h&&h.nodeName.toLowerCase()==="pre"&&!h.hasAttribute("tabindex")&&h.setAttribute("tabindex","0"),!b.code){t.hooks.run("complete",b),o&&o.call(b.element);return}if(t.hooks.run("before-highlight",b),!b.grammar){T(t.util.encode(b.code));return}if(i&&f.Worker){var v=new Worker(t.filename);v.onmessage=function(N){T(N.data)},v.postMessage(JSON.stringify({language:b.language,code:b.code,immediateClose:!0}))}else T(t.highlight(b.code,b.grammar,b.language))},highlight:function(a,i,o){var u={code:a,grammar:i,language:o};if(t.hooks.run("before-tokenize",u),!u.grammar)throw new Error('The language "'+u.language+'" has no grammar.');return u.tokens=t.tokenize(u.code,u.grammar),t.hooks.run("after-tokenize",u),s.stringify(t.util.encode(u.tokens),u.language)},tokenize:function(a,i){var o=i.rest;if(o){for(var u in o)i[u]=o[u];delete i.rest}var p=new E;return y(p,p.head,a),g(a,p,i,p.head,0),L(p)},hooks:{all:{},add:function(a,i){var o=t.hooks.all;o[a]=o[a]||[],o[a].push(i)},run:function(a,i){var o=t.hooks.all[a];if(!(!o||!o.length))for(var u=0,p;p=o[u++];)p(i)}},Token:s};f.Prism=t;function s(a,i,o,u){this.type=a,this.content=i,this.alias=o,this.length=(u||"").length|0}s.stringify=function a(i,o){if(typeof i=="string")return i;if(Array.isArray(i)){var u="";return i.forEach(function(T){u+=a(T,o)}),u}var p={type:i.type,content:a(i.content,o),tag:"span",classes:["token",i.type],attributes:{},language:o},h=i.alias;h&&(Array.isArray(h)?Array.prototype.push.apply(p.classes,h):p.classes.push(h)),t.hooks.run("wrap",p);var S="";for(var b in p.attributes)S+=" "+b+'="'+(p.attributes[b]||"").replace(/"/g,"&quot;")+'"';return"<"+p.tag+' class="'+p.classes.join(" ")+'"'+S+">"+p.content+"</"+p.tag+">"};function d(a,i,o,u){a.lastIndex=i;var p=a.exec(o);if(p&&u&&p[1]){var h=p[1].length;p.index+=h,p[0]=p[0].slice(h)}return p}function g(a,i,o,u,p,h){for(var S in o)if(!(!o.hasOwnProperty(S)||!o[S])){var b=o[S];b=Array.isArray(b)?b:[b];for(var T=0;T<b.length;++T){if(h&&h.cause==S+","+T)return;var v=b[T],N=v.inside,Y=!!v.lookbehind,K=!!v.greedy,ie=v.alias;if(K&&!v.pattern.global){var ae=v.pattern.toString().match(/[imsuy]*$/)[0];v.pattern=RegExp(v.pattern.source,ae+"g")}for(var X=v.pattern||v,A=u.next,C=p;A!==i.tail&&!(h&&C>=h.reach);C+=A.value.length,A=A.next){var R=A.value;if(i.length>a.length)return;if(!(R instanceof s)){var D=1,I;if(K){if(I=d(X,C,a,Y),!I||I.index>=a.length)break;var M=I.index,se=I.index+I[0].length,x=C;for(x+=A.value.length;M>=x;)A=A.next,x+=A.value.length;if(x-=A.value.length,C=x,A.value instanceof s)continue;for(var F=A;F!==i.tail&&(x<se||typeof F.value=="string");F=F.next)D++,x+=F.value.length;D--,R=a.slice(C,x),I.index-=C}else if(I=d(X,0,R,Y),!I)continue;var M=I.index,B=I[0],$=R.slice(0,M),Q=R.slice(M+B.length),H=C+R.length;h&&H>h.reach&&(h.reach=H);var z=A.prev;$&&(z=y(i,z,$),C+=$.length),w(i,z,D);var oe=new s(S,N?t.tokenize(B,N):B,ie,B);if(A=y(i,z,oe),Q&&y(i,A,Q),D>1){var W={cause:S+","+T,reach:H};g(a,i,o,A.prev,C,W),h&&W.reach>h.reach&&(h.reach=W.reach)}}}}}}function E(){var a={value:null,prev:null,next:null},i={value:null,prev:a,next:null};a.next=i,this.head=a,this.tail=i,this.length=0}function y(a,i,o){var u=i.next,p={value:o,prev:i,next:u};return i.next=p,u.prev=p,a.length++,p}function w(a,i,o){for(var u=i.next,p=0;p<o&&u!==a.tail;p++)u=u.next;i.next=u,u.prev=i,a.length-=p}function L(a){for(var i=[],o=a.head.next;o!==a.tail;)i.push(o.value),o=o.next;return i}if(!f.document)return f.addEventListener&&(t.disableWorkerMessageHandler||f.addEventListener("message",function(a){var i=JSON.parse(a.data),o=i.language,u=i.code,p=i.immediateClose;f.postMessage(t.highlight(u,t.languages[o],o)),p&&f.close()},!1)),t;var k=t.util.currentScript();k&&(t.filename=k.src,k.hasAttribute("data-manual")&&(t.manual=!0));function O(){t.manual||t.highlightAll()}if(!t.manual){var _=document.readyState;_==="loading"||_==="interactive"&&k&&k.defer?document.addEventListener("DOMContentLoaded",O):window.requestAnimationFrame?window.requestAnimationFrame(O):window.setTimeout(O,16)}return t}(m);c.exports&&(c.exports=l),typeof j<"u"&&(j.Prism=l)})(q);var J=q.exports;const ee=Z(J);Prism.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},function(c){var m=/\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\s*[(){}[\]<>=%~.:,;?+\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\b/,l=/(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,f={pattern:RegExp(/(^|[^\w.])/.source+l+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}},punctuation:/\./}};c.languages.java=c.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"/,lookbehind:!0,greedy:!0},"class-name":[f,{pattern:RegExp(/(^|[^\w.])/.source+l+/[A-Z]\w*(?=\s+\w+\s*[;,=()]|\s*(?:\[[\s,]*\]\s*)?::\s*new\b)/.source),lookbehind:!0,inside:f.inside},{pattern:RegExp(/(\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\s+)/.source+l+/[A-Z]\w*\b/.source),lookbehind:!0,inside:f.inside}],keyword:m,function:[c.languages.clike.function,{pattern:/(::\s*)[a-z_]\w*/,lookbehind:!0}],number:/\b0b[01][01_]*L?\b|\b0x(?:\.[\da-f_p+-]+|[\da-f_]+(?:\.[\da-f_p+-]+)?)\b|(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?\d[\d_]*)?[dfl]?/i,operator:{pattern:/(^|[^.])(?:<<=?|>>>?=?|->|--|\+\+|&&|\|\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,lookbehind:!0},constant:/\b[A-Z][A-Z_\d]+\b/}),c.languages.insertBefore("java","string",{"triple-quoted-string":{pattern:/"""[ \t]*[\r\n](?:(?:"|"")?(?:\\.|[^"\\]))*"""/,greedy:!0,alias:"string"},char:{pattern:/'(?:\\.|[^'\\\r\n]){1,6}'/,greedy:!0}}),c.languages.insertBefore("java","class-name",{annotation:{pattern:/(^|[^.])@\w+(?:\s*\.\s*\w+)*/,lookbehind:!0,alias:"punctuation"},generics:{pattern:/<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&))*>)*>)*>)*>/,inside:{"class-name":f,keyword:m,punctuation:/[<>(),.:]/,operator:/[?&|]/}},import:[{pattern:RegExp(/(\bimport\s+)/.source+l+/(?:[A-Z]\w*|\*)(?=\s*;)/.source),lookbehind:!0,inside:{namespace:f.inside.namespace,punctuation:/\./,operator:/\*/,"class-name":/\w+/}},{pattern:RegExp(/(\bimport\s+static\s+)/.source+l+/(?:\w+|\*)(?=\s*;)/.source),lookbehind:!0,alias:"static",inside:{namespace:f.inside.namespace,static:/\b\w+$/,punctuation:/\./,operator:/\*/,"class-name":/\w+/}}],namespace:{pattern:RegExp(/(\b(?:exports|import(?:\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\s+)(?!<keyword>)[a-z]\w*(?:\.[a-z]\w*)*\.?/.source.replace(/<keyword>/g,function(){return m.source})),lookbehind:!0,inside:{punctuation:/\./}}})}(Prism),Prism.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},Prism.languages.markup.tag.inside["attr-value"].inside.entity=Prism.languages.markup.entity,Prism.languages.markup.doctype.inside["internal-subset"].inside=Prism.languages.markup,Prism.hooks.add("wrap",function(c){c.type==="entity"&&(c.attributes.title=c.content.replace(/&amp;/,"&"))}),Object.defineProperty(Prism.languages.markup.tag,"addInlined",{value:function(m,l){var f={};f["language-"+l]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:Prism.languages[l]},f.cdata=/^<!\[CDATA\[|\]\]>$/i;var e={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:f}};e["language-"+l]={pattern:/[\s\S]+/,inside:Prism.languages[l]};var r={};r[m]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return m}),"i"),lookbehind:!0,greedy:!0,inside:e},Prism.languages.insertBefore("markup","cdata",r)}}),Object.defineProperty(Prism.languages.markup.tag,"addAttribute",{value:function(c,m){Prism.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+c+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[m,"language-"+m],inside:Prism.languages[m]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),Prism.languages.html=Prism.languages.markup,Prism.languages.mathml=Prism.languages.markup,Prism.languages.svg=Prism.languages.markup,Prism.languages.xml=Prism.languages.extend("markup",{}),Prism.languages.ssml=Prism.languages.xml,Prism.languages.atom=Prism.languages.xml,Prism.languages.rss=Prism.languages.xml,Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),Prism.languages.js=Prism.languages.javascript,function(c){var m=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;c.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+m.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+m.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+m.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+m.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:m,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},c.languages.css.atrule.inside.rest=c.languages.css;var l=c.languages.markup;l&&(l.tag.addInlined("style","css"),l.tag.addAttribute("style","css"))}(Prism),function(c){function m(l,f){return"___"+l.toUpperCase()+f+"___"}Object.defineProperties(c.languages["markup-templating"]={},{buildPlaceholders:{value:function(l,f,e,r){if(l.language===f){var n=l.tokenStack=[];l.code=l.code.replace(e,function(t){if(typeof r=="function"&&!r(t))return t;for(var s=n.length,d;l.code.indexOf(d=m(f,s))!==-1;)++s;return n[s]=t,d}),l.grammar=c.languages.markup}}},tokenizePlaceholders:{value:function(l,f){if(l.language!==f||!l.tokenStack)return;l.grammar=c.languages[f];var e=0,r=Object.keys(l.tokenStack);function n(t){for(var s=0;s<t.length&&!(e>=r.length);s++){var d=t[s];if(typeof d=="string"||d.content&&typeof d.content=="string"){var g=r[e],E=l.tokenStack[g],y=typeof d=="string"?d:d.content,w=m(f,g),L=y.indexOf(w);if(L>-1){++e;var k=y.substring(0,L),O=new c.Token(f,c.tokenize(E,l.grammar),"language-"+f,E),_=y.substring(L+w.length),a=[];k&&a.push.apply(a,n([k])),a.push(O),_&&a.push.apply(a,n([_])),typeof d=="string"?t.splice.apply(t,[s,1].concat(a)):d.content=a}}else d.content&&n(d.content)}return t}n(l.tokens)}}})}(Prism),function(c){var m=/\/\*[\s\S]*?\*\/|\/\/.*|#(?!\[).*/,l=[{pattern:/\b(?:false|true)\b/i,alias:"boolean"},{pattern:/(::\s*)\b[a-z_]\w*\b(?!\s*\()/i,greedy:!0,lookbehind:!0},{pattern:/(\b(?:case|const)\s+)\b[a-z_]\w*(?=\s*[;=])/i,greedy:!0,lookbehind:!0},/\b(?:null)\b/i,/\b[A-Z_][A-Z0-9_]*\b(?!\s*\()/],f=/\b0b[01]+(?:_[01]+)*\b|\b0o[0-7]+(?:_[0-7]+)*\b|\b0x[\da-f]+(?:_[\da-f]+)*\b|(?:\b\d+(?:_\d+)*\.?(?:\d+(?:_\d+)*)?|\B\.\d+)(?:e[+-]?\d+)?/i,e=/<?=>|\?\?=?|\.{3}|\??->|[!=]=?=?|::|\*\*=?|--|\+\+|&&|\|\||<<|>>|[?~]|[/^|%*&<>.+-]=?/,r=/[{}\[\](),:;]/;c.languages.php={delimiter:{pattern:/\?>$|^<\?(?:php(?=\s)|=)?/i,alias:"important"},comment:m,variable:/\$+(?:\w+\b|(?=\{))/,package:{pattern:/(namespace\s+|use\s+(?:function\s+)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,lookbehind:!0,inside:{punctuation:/\\/}},"class-name-definition":{pattern:/(\b(?:class|enum|interface|trait)\s+)\b[a-z_]\w*(?!\\)\b/i,lookbehind:!0,alias:"class-name"},"function-definition":{pattern:/(\bfunction\s+)[a-z_]\w*(?=\s*\()/i,lookbehind:!0,alias:"function"},keyword:[{pattern:/(\(\s*)\b(?:array|bool|boolean|float|int|integer|object|string)\b(?=\s*\))/i,alias:"type-casting",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|object|self|static|string)\b(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|never|object|self|static|string|void)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/\b(?:array(?!\s*\()|bool|float|int|iterable|mixed|object|string|void)\b/i,alias:"type-declaration",greedy:!0},{pattern:/(\|\s*)(?:false|null)\b|\b(?:false|null)(?=\s*\|)/i,alias:"type-declaration",greedy:!0,lookbehind:!0},{pattern:/\b(?:parent|self|static)(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(\byield\s+)from\b/i,lookbehind:!0},/\bclass\b/i,{pattern:/((?:^|[^\s>:]|(?:^|[^-])>|(?:^|[^:]):)\s*)\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\b/i,lookbehind:!0}],"argument-name":{pattern:/([(,]\s*)\b[a-z_]\w*(?=\s*:(?!:))/i,lookbehind:!0},"class-name":[{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self|\s+static))\s+|\bcatch\s*\()\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/(\|\s*)\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/\b[a-z_]\w*(?!\\)\b(?=\s*\|)/i,greedy:!0},{pattern:/(\|\s*)(?:\\?\b[a-z_]\w*)+\b/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(?:\\?\b[a-z_]\w*)+\b(?=\s*\|)/i,alias:"class-name-fully-qualified",greedy:!0,inside:{punctuation:/\\/}},{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self\b|\s+static\b))\s+|\bcatch\s*\()(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*\$)/i,alias:"type-declaration",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-declaration"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*::)/i,alias:["class-name-fully-qualified","static-context"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/([(,?]\s*)[a-z_]\w*(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-hint"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b[a-z_]\w*(?!\\)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:["class-name-fully-qualified","return-type"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:l,function:{pattern:/(^|[^\\\w])\\?[a-z_](?:[\w\\]*\w)?(?=\s*\()/i,lookbehind:!0,inside:{punctuation:/\\/}},property:{pattern:/(->\s*)\w+/,lookbehind:!0},number:f,operator:e,punctuation:r};var n={pattern:/\{\$(?:\{(?:\{[^{}]+\}|[^{}]+)\}|[^{}])+\}|(^|[^\\{])\$+(?:\w+(?:\[[^\r\n\[\]]+\]|->\w+)?)/,lookbehind:!0,inside:c.languages.php},t=[{pattern:/<<<'([^']+)'[\r\n](?:.*[\r\n])*?\1;/,alias:"nowdoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<'[^']+'|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<'?|[';]$/}}}},{pattern:/<<<(?:"([^"]+)"[\r\n](?:.*[\r\n])*?\1;|([a-z_]\w*)[\r\n](?:.*[\r\n])*?\2;)/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<(?:"[^"]+"|[a-z_]\w*)|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<"?|[";]$/}},interpolation:n}},{pattern:/`(?:\\[\s\S]|[^\\`])*`/,alias:"backtick-quoted-string",greedy:!0},{pattern:/'(?:\\[\s\S]|[^\\'])*'/,alias:"single-quoted-string",greedy:!0},{pattern:/"(?:\\[\s\S]|[^\\"])*"/,alias:"double-quoted-string",greedy:!0,inside:{interpolation:n}}];c.languages.insertBefore("php","variable",{string:t,attribute:{pattern:/#\[(?:[^"'\/#]|\/(?![*/])|\/\/.*$|#(?!\[).*$|\/\*(?:[^*]|\*(?!\/))*\*\/|"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*')+\](?=\s*[a-z$#])/im,greedy:!0,inside:{"attribute-content":{pattern:/^(#\[)[\s\S]+(?=\]$)/,lookbehind:!0,inside:{comment:m,string:t,"attribute-class-name":[{pattern:/([^:]|^)\b[a-z_]\w*(?!\\)\b/i,alias:"class-name",greedy:!0,lookbehind:!0},{pattern:/([^:]|^)(?:\\?\b[a-z_]\w*)+/i,alias:["class-name","class-name-fully-qualified"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:l,number:f,operator:e,punctuation:r}},delimiter:{pattern:/^#\[|\]$/,alias:"punctuation"}}}}),c.hooks.add("before-tokenize",function(s){if(/<\?/.test(s.code)){var d=/<\?(?:[^"'/#]|\/(?![*/])|("|')(?:\\[\s\S]|(?!\1)[^\\])*\1|(?:\/\/|#(?!\[))(?:[^?\n\r]|\?(?!>))*(?=$|\?>|[\r\n])|#\[|\/\*(?:[^*]|\*(?!\/))*(?:\*\/|$))*?(?:\?>|$)/g;c.languages["markup-templating"].buildPlaceholders(s,"php",d)}}),c.hooks.add("after-tokenize",function(s){c.languages["markup-templating"].tokenizePlaceholders(s,"php")})}(Prism),Prism.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},Prism.languages.c=Prism.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/}),Prism.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}}),Prism.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},Prism.languages.c.string],char:Prism.languages.c.char,comment:Prism.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:Prism.languages.c}}}}),Prism.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/}),delete Prism.languages.c.boolean,Prism.languages.c=Prism.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/}),Prism.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}}),Prism.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},Prism.languages.c.string],char:Prism.languages.c.char,comment:Prism.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:Prism.languages.c}}}}),Prism.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/}),delete Prism.languages.c.boolean,function(c){var m=/[*&][^\s[\]{},]+/,l=/!(?:<[\w\-%#;/?:@&=+$,.!~*'()[\]]+>|(?:[a-zA-Z\d-]*!)?[\w\-%#;/?:@&=+$.~*'()]+)?/,f="(?:"+l.source+"(?:[ 	]+"+m.source+")?|"+m.source+"(?:[ 	]+"+l.source+")?)",e=/(?:[^\s\x00-\x08\x0e-\x1f!"#%&'*,\-:>?@[\]`{|}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]|[?:-]<PLAIN>)(?:[ \t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,function(){return/[^\s\x00-\x08\x0e-\x1f,[\]{}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]/.source}),r=/"(?:[^"\\\r\n]|\\.)*"|'(?:[^'\\\r\n]|\\.)*'/.source;function n(t,s){s=(s||"").replace(/m/g,"")+"m";var d=/([:\-,[{]\s*(?:\s<<prop>>[ \t]+)?)(?:<<value>>)(?=[ \t]*(?:$|,|\]|\}|(?:[\r\n]\s*)?#))/.source.replace(/<<prop>>/g,function(){return f}).replace(/<<value>>/g,function(){return t});return RegExp(d,s)}c.languages.yaml={scalar:{pattern:RegExp(/([\-:]\s*(?:\s<<prop>>[ \t]+)?[|>])[ \t]*(?:((?:\r?\n|\r)[ \t]+)\S[^\r\n]*(?:\2[^\r\n]+)*)/.source.replace(/<<prop>>/g,function(){return f})),lookbehind:!0,alias:"string"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\-,[{\r\n?])[ \t]*(?:<<prop>>[ \t]+)?)<<key>>(?=\s*:\s)/.source.replace(/<<prop>>/g,function(){return f}).replace(/<<key>>/g,function(){return"(?:"+e+"|"+r+")"})),lookbehind:!0,greedy:!0,alias:"atrule"},directive:{pattern:/(^[ \t]*)%.+/m,lookbehind:!0,alias:"important"},datetime:{pattern:n(/\d{4}-\d\d?-\d\d?(?:[tT]|[ \t]+)\d\d?:\d{2}:\d{2}(?:\.\d*)?(?:[ \t]*(?:Z|[-+]\d\d?(?::\d{2})?))?|\d{4}-\d{2}-\d{2}|\d\d?:\d{2}(?::\d{2}(?:\.\d*)?)?/.source),lookbehind:!0,alias:"number"},boolean:{pattern:n(/false|true/.source,"i"),lookbehind:!0,alias:"important"},null:{pattern:n(/null|~/.source,"i"),lookbehind:!0,alias:"important"},string:{pattern:n(r),lookbehind:!0,greedy:!0},number:{pattern:n(/[+-]?(?:0x[\da-f]+|0o[0-7]+|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?|\.inf|\.nan)/.source,"i"),lookbehind:!0},tag:l,important:m,punctuation:/---|[:[\]{}\-,|>?]|\.\.\./},c.languages.yml=c.languages.yaml}(Prism),Prism.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},Prism.languages.webmanifest=Prism.languages.json;const ce="";(function(){if(typeof Prism>"u"||typeof document>"u")return;var c="line-numbers",m=/\n(?!$)/g,l=Prism.plugins.lineNumbers={getLine:function(n,t){if(!(n.tagName!=="PRE"||!n.classList.contains(c))){var s=n.querySelector(".line-numbers-rows");if(s){var d=parseInt(n.getAttribute("data-start"),10)||1,g=d+(s.children.length-1);t<d&&(t=d),t>g&&(t=g);var E=t-d;return s.children[E]}}},resize:function(n){f([n])},assumeViewportIndependence:!0};function f(n){if(n=n.filter(function(s){var d=e(s),g=d["white-space"];return g==="pre-wrap"||g==="pre-line"}),n.length!=0){var t=n.map(function(s){var d=s.querySelector("code"),g=s.querySelector(".line-numbers-rows");if(!(!d||!g)){var E=s.querySelector(".line-numbers-sizer"),y=d.textContent.split(m);E||(E=document.createElement("span"),E.className="line-numbers-sizer",d.appendChild(E)),E.innerHTML="0",E.style.display="block";var w=E.getBoundingClientRect().height;return E.innerHTML="",{element:s,lines:y,lineHeights:[],oneLinerHeight:w,sizer:E}}}).filter(Boolean);t.forEach(function(s){var d=s.sizer,g=s.lines,E=s.lineHeights,y=s.oneLinerHeight;E[g.length-1]=void 0,g.forEach(function(w,L){if(w&&w.length>1){var k=d.appendChild(document.createElement("span"));k.style.display="block",k.textContent=w}else E[L]=y})}),t.forEach(function(s){for(var d=s.sizer,g=s.lineHeights,E=0,y=0;y<g.length;y++)g[y]===void 0&&(g[y]=d.children[E++].getBoundingClientRect().height)}),t.forEach(function(s){var d=s.sizer,g=s.element.querySelector(".line-numbers-rows");d.style.display="none",d.innerHTML="",s.lineHeights.forEach(function(E,y){g.children[y].style.height=E+"px"})})}}function e(n){return n?window.getComputedStyle?getComputedStyle(n):n.currentStyle||null:null}var r=void 0;window.addEventListener("resize",function(){l.assumeViewportIndependence&&r===window.innerWidth||(r=window.innerWidth,f(Array.prototype.slice.call(document.querySelectorAll("pre."+c))))}),Prism.hooks.add("complete",function(n){if(n.code){var t=n.element,s=t.parentNode;if(!(!s||!/pre/i.test(s.nodeName))&&!t.querySelector(".line-numbers-rows")&&Prism.util.isActive(t,c)){t.classList.remove(c),s.classList.add(c);var d=n.code.match(m),g=d?d.length+1:1,E,y=new Array(g+1).join("<span></span>");E=document.createElement("span"),E.setAttribute("aria-hidden","true"),E.className="line-numbers-rows",E.innerHTML=y,s.hasAttribute("data-start")&&(s.style.counterReset="linenumber "+(parseInt(s.getAttribute("data-start"),10)-1)),n.element.appendChild(E),f([s]),Prism.hooks.run("line-numbers",n)}}}),Prism.hooks.add("line-numbers",function(n){n.plugins=n.plugins||{},n.plugins.lineNumbers=!0})})();const de="";(function(){if(typeof Prism>"u"||typeof document>"u")return;var c=[],m={},l=function(){};Prism.plugins.toolbar={};var f=Prism.plugins.toolbar.registerButton=function(n,t){var s;if(typeof t=="function"?s=t:s=function(d){var g;return typeof t.onClick=="function"?(g=document.createElement("button"),g.type="button",g.addEventListener("click",function(){t.onClick.call(this,d)})):typeof t.url=="string"?(g=document.createElement("a"),g.href=t.url):g=document.createElement("span"),t.className&&g.classList.add(t.className),g.textContent=t.text,g},n in m){console.warn('There is a button with the key "'+n+'" registered already.');return}c.push(m[n]=s)};function e(n){for(;n;){var t=n.getAttribute("data-toolbar-order");if(t!=null)return t=t.trim(),t.length?t.split(/\s*,\s*/g):[];n=n.parentElement}}var r=Prism.plugins.toolbar.hook=function(n){var t=n.element.parentNode;if(!(!t||!/pre/i.test(t.nodeName))&&!t.parentNode.classList.contains("code-toolbar")){var s=document.createElement("div");s.classList.add("code-toolbar"),t.parentNode.insertBefore(s,t),s.appendChild(t);var d=document.createElement("div");d.classList.add("toolbar");var g=c,E=e(n.element);E&&(g=E.map(function(y){return m[y]||l})),g.forEach(function(y){var w=y(n);if(w){var L=document.createElement("div");L.classList.add("toolbar-item"),L.appendChild(w),d.appendChild(L)}}),s.appendChild(d)}};f("label",function(n){var t=n.element.parentNode;if(!(!t||!/pre/i.test(t.nodeName))&&t.hasAttribute("data-label")){var s,d,g=t.getAttribute("data-label");try{d=document.querySelector("template#"+g)}catch{}return d?s=d.content:(t.hasAttribute("data-url")?(s=document.createElement("a"),s.href=t.getAttribute("data-url")):s=document.createElement("span"),s.textContent=g),s}}),Prism.hooks.add("complete",r)})(),function(){if(!(typeof Prism>"u"||typeof document>"u")){if(!Prism.plugins.toolbar){console.warn("Show Languages plugin loaded before Toolbar plugin.");return}var c={none:"Plain text",plain:"Plain text",plaintext:"Plain text",text:"Plain text",txt:"Plain text",html:"HTML",xml:"XML",svg:"SVG",mathml:"MathML",ssml:"SSML",rss:"RSS",css:"CSS",clike:"C-like",js:"JavaScript",abap:"ABAP",abnf:"ABNF",al:"AL",antlr4:"ANTLR4",g4:"ANTLR4",apacheconf:"Apache Configuration",apl:"APL",aql:"AQL",ino:"Arduino",arff:"ARFF",armasm:"ARM Assembly","arm-asm":"ARM Assembly",art:"Arturo",asciidoc:"AsciiDoc",adoc:"AsciiDoc",aspnet:"ASP.NET (C#)",asm6502:"6502 Assembly",asmatmel:"Atmel AVR Assembly",autohotkey:"AutoHotkey",autoit:"AutoIt",avisynth:"AviSynth",avs:"AviSynth","avro-idl":"Avro IDL",avdl:"Avro IDL",awk:"AWK",gawk:"GAWK",sh:"Shell",basic:"BASIC",bbcode:"BBcode",bbj:"BBj",bnf:"BNF",rbnf:"RBNF",bqn:"BQN",bsl:"BSL (1C:Enterprise)",oscript:"OneScript",csharp:"C#",cs:"C#",dotnet:"C#",cpp:"C++",cfscript:"CFScript",cfc:"CFScript",cil:"CIL",cilkc:"Cilk/C","cilk-c":"Cilk/C",cilkcpp:"Cilk/C++","cilk-cpp":"Cilk/C++",cilk:"Cilk/C++",cmake:"CMake",cobol:"COBOL",coffee:"CoffeeScript",conc:"Concurnas",csp:"Content-Security-Policy","css-extras":"CSS Extras",csv:"CSV",cue:"CUE",dataweave:"DataWeave",dax:"DAX",django:"Django/Jinja2",jinja2:"Django/Jinja2","dns-zone-file":"DNS zone file","dns-zone":"DNS zone file",dockerfile:"Docker",dot:"DOT (Graphviz)",gv:"DOT (Graphviz)",ebnf:"EBNF",editorconfig:"EditorConfig",ejs:"EJS",etlua:"Embedded Lua templating",erb:"ERB","excel-formula":"Excel Formula",xlsx:"Excel Formula",xls:"Excel Formula",fsharp:"F#","firestore-security-rules":"Firestore security rules",ftl:"FreeMarker Template Language",gml:"GameMaker Language",gamemakerlanguage:"GameMaker Language",gap:"GAP (CAS)",gcode:"G-code",gdscript:"GDScript",gedcom:"GEDCOM",gettext:"gettext",po:"gettext",glsl:"GLSL",gn:"GN",gni:"GN","linker-script":"GNU Linker Script",ld:"GNU Linker Script","go-module":"Go module","go-mod":"Go module",graphql:"GraphQL",hbs:"Handlebars",hs:"Haskell",hcl:"HCL",hlsl:"HLSL",http:"HTTP",hpkp:"HTTP Public-Key-Pins",hsts:"HTTP Strict-Transport-Security",ichigojam:"IchigoJam","icu-message-format":"ICU Message Format",idr:"Idris",ignore:".ignore",gitignore:".gitignore",hgignore:".hgignore",npmignore:".npmignore",inform7:"Inform 7",javadoc:"JavaDoc",javadoclike:"JavaDoc-like",javastacktrace:"Java stack trace",jq:"JQ",jsdoc:"JSDoc","js-extras":"JS Extras",json:"JSON",webmanifest:"Web App Manifest",json5:"JSON5",jsonp:"JSONP",jsstacktrace:"JS stack trace","js-templates":"JS Templates",keepalived:"Keepalived Configure",kts:"Kotlin Script",kt:"Kotlin",kumir:"KuMir (КуМир)",kum:"KuMir (КуМир)",latex:"LaTeX",tex:"TeX",context:"ConTeXt",lilypond:"LilyPond",ly:"LilyPond",emacs:"Lisp",elisp:"Lisp","emacs-lisp":"Lisp",llvm:"LLVM IR",log:"Log file",lolcode:"LOLCODE",magma:"Magma (CAS)",md:"Markdown","markup-templating":"Markup templating",matlab:"MATLAB",maxscript:"MAXScript",mel:"MEL",metafont:"METAFONT",mongodb:"MongoDB",moon:"MoonScript",n1ql:"N1QL",n4js:"N4JS",n4jsd:"N4JS","nand2tetris-hdl":"Nand To Tetris HDL",naniscript:"Naninovel Script",nani:"Naninovel Script",nasm:"NASM",neon:"NEON",nginx:"nginx",nsis:"NSIS",objectivec:"Objective-C",objc:"Objective-C",ocaml:"OCaml",opencl:"OpenCL",openqasm:"OpenQasm",qasm:"OpenQasm",parigp:"PARI/GP",objectpascal:"Object Pascal",psl:"PATROL Scripting Language",pcaxis:"PC-Axis",px:"PC-Axis",peoplecode:"PeopleCode",pcode:"PeopleCode",php:"PHP",phpdoc:"PHPDoc","php-extras":"PHP Extras","plant-uml":"PlantUML",plantuml:"PlantUML",plsql:"PL/SQL",powerquery:"PowerQuery",pq:"PowerQuery",mscript:"PowerQuery",powershell:"PowerShell",promql:"PromQL",properties:".properties",protobuf:"Protocol Buffers",purebasic:"PureBasic",pbfasm:"PureBasic",purs:"PureScript",py:"Python",qsharp:"Q#",qs:"Q#",q:"Q (kdb+ database)",qml:"QML",rkt:"Racket",cshtml:"Razor C#",razor:"Razor C#",jsx:"React JSX",tsx:"React TSX",renpy:"Ren'py",rpy:"Ren'py",res:"ReScript",rest:"reST (reStructuredText)",robotframework:"Robot Framework",robot:"Robot Framework",rb:"Ruby",sas:"SAS",sass:"Sass (Sass)",scss:"Sass (SCSS)","shell-session":"Shell session","sh-session":"Shell session",shellsession:"Shell session",sml:"SML",smlnj:"SML/NJ",solidity:"Solidity (Ethereum)",sol:"Solidity (Ethereum)","solution-file":"Solution file",sln:"Solution file",soy:"Soy (Closure Template)",sparql:"SPARQL",rq:"SPARQL","splunk-spl":"Splunk SPL",sqf:"SQF: Status Quo Function (Arma 3)",sql:"SQL",stata:"Stata Ado",iecst:"Structured Text (IEC 61131-3)",supercollider:"SuperCollider",sclang:"SuperCollider",systemd:"Systemd configuration file","t4-templating":"T4 templating","t4-cs":"T4 Text Templates (C#)",t4:"T4 Text Templates (C#)","t4-vb":"T4 Text Templates (VB)",tap:"TAP",tt2:"Template Toolkit 2",toml:"TOML",trickle:"trickle",troy:"troy",trig:"TriG",ts:"TypeScript",tsconfig:"TSConfig",uscript:"UnrealScript",uc:"UnrealScript",uorazor:"UO Razor Script",uri:"URI",url:"URL",vbnet:"VB.Net",vhdl:"VHDL",vim:"vim","visual-basic":"Visual Basic",vba:"VBA",vb:"Visual Basic",wasm:"WebAssembly","web-idl":"Web IDL",webidl:"Web IDL",wgsl:"WGSL",wiki:"Wiki markup",wolfram:"Wolfram language",nb:"Mathematica Notebook",wl:"Wolfram language",xeoracube:"XeoraCube","xml-doc":"XML doc (.net)",xojo:"Xojo (REALbasic)",xquery:"XQuery",yaml:"YAML",yml:"YAML",yang:"YANG"};Prism.plugins.toolbar.registerButton("show-language",function(m){var l=m.element.parentNode;if(!l||!/pre/i.test(l.nodeName))return;function f(n){return n&&(n.substring(0,1).toUpperCase()+n.substring(1)).replace(/s(?=cript)/,"S")}var e=l.getAttribute("data-language")||c[m.language]||f(m.language);if(e){var r=document.createElement("span");return r.textContent=e,r}})}}(),function(){if(typeof Prism>"u"||typeof document>"u")return;if(!Prism.plugins.toolbar){console.warn("Copy to Clipboard plugin loaded before Toolbar plugin.");return}function c(r,n){r.addEventListener("click",function(){l(n)})}function m(r){var n=document.createElement("textarea");n.value=r.getText(),n.style.top="0",n.style.left="0",n.style.position="fixed",document.body.appendChild(n),n.focus(),n.select();try{var t=document.execCommand("copy");setTimeout(function(){t?r.success():r.error()},1)}catch(s){setTimeout(function(){r.error(s)},1)}document.body.removeChild(n)}function l(r){navigator.clipboard?navigator.clipboard.writeText(r.getText()).then(r.success,function(){m(r)}):m(r)}function f(r){window.getSelection().selectAllChildren(r)}function e(r){var n={copy:"Copy","copy-error":"Press Ctrl+C to copy","copy-success":"Copied!","copy-timeout":5e3},t="data-prismjs-";for(var s in n){for(var d=t+s,g=r;g&&!g.hasAttribute(d);)g=g.parentElement;g&&(n[s]=g.getAttribute(d))}return n}Prism.plugins.toolbar.registerButton("copy-to-clipboard",function(r){var n=r.element,t=e(n),s=document.createElement("button");s.className="copy-to-clipboard-button",s.setAttribute("type","button");var d=document.createElement("span");return s.appendChild(d),E("copy"),c(s,{getText:function(){return n.textContent},success:function(){E("copy-success"),g()},error:function(){E("copy-error"),setTimeout(function(){f(n)},1),g()}}),s;function g(){setTimeout(function(){E("copy")},t["copy-timeout"])}function E(y){d.textContent=t[y],s.setAttribute("data-copy-state",y)}})}();const pe="";function te(){var c;(c=document.getElementById("search-input"))==null||c.blur()}function ne(){localStorage.theme==="light"?localStorage.theme="dark":localStorage.theme==="dark"?localStorage.theme="light":localStorage.theme="dark",U()}function U(){localStorage.theme==="dark"||!("theme"in localStorage)&&window.matchMedia("(prefers-color-scheme: dark)").matches?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}U(),ee.highlightAll(),console.log("%c Jasmine ","background:#000;color:#fff","https://8ww.fun/"),window.onload=()=>{var c;new G("#sidebar-right",{innerWrapperSelector:".sidebar__right__inner"}),Array.from(document.getElementsByClassName("nav-li")).forEach(m=>{m.addEventListener("mouseover",()=>{m.getElementsByTagName("span")[0].classList.add("!block")}),m.addEventListener("mouseout",()=>{m.getElementsByTagName("span")[0].classList.remove("!block")})}),(c=document.querySelector("#mobile-menus-bg"))==null||c.addEventListener("click",()=>{V()})};function re(){document.body.scrollTop=0,document.documentElement.scrollTop=0}function V(){var m,l,f,e;document.querySelector("#mobile-menus").classList.contains("!translate-x-0")?((m=document.querySelector("#mobile-menus-bg"))==null||m.classList.add("hidden"),(l=document.querySelector("#mobile-menus"))==null||l.classList.remove("!translate-x-0")):((f=document.querySelector("#mobile-menus-bg"))==null||f.classList.remove("hidden"),(e=document.querySelector("#mobile-menus"))==null||e.classList.add("!translate-x-0"))}return P.backtop=re,P.clickSearch=te,P.loadTheme=U,P.switchDark=ne,P.toggleMobileMenu=V,Object.defineProperty(P,Symbol.toStringTag,{value:"Module"}),P}({});
 