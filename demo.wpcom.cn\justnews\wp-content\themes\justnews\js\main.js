!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";var e,t,a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function i(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function n(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var a=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};a.prototype=t.prototype}else a={};return Object.defineProperty(a,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var i=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(a,t,i.get?i:{enumerable:!0,get:function(){return e[t]}})})),a}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function o(e,t,a,i,n,r,o){try{var s=e[r](o),l=s.value}catch(e){return void a(e)}s.done?t(l):Promise.resolve(l).then(i,n)}function s(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,f(i.key),i)}}function l(e,t,a){return(t=f(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function c(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,i)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?c(Object(a),!0).forEach((function(t){l(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function u(){u=function(){return t};var e,t={},a=Object.prototype,i=a.hasOwnProperty,n=Object.defineProperty||function(e,t,a){e[t]=a.value},r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",s=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,a){return e[t]=a}}function d(e,t,a,i){var r=t&&t.prototype instanceof w?t:w,o=Object.create(r.prototype),s=new M(i||[]);return n(o,"_invoke",{value:k(e,a,s)}),o}function p(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",f="suspendedYield",m="executing",v="completed",g={};function w(){}function y(){}function b(){}var x={};c(x,o,(function(){return this}));var E=Object.getPrototypeOf,T=E&&E(E(O([])));T&&T!==a&&i.call(T,o)&&(x=T);var _=b.prototype=w.prototype=Object.create(x);function S(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function a(n,r,o,s){var l=p(e[n],e,r);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==typeof d&&i.call(d,"__await")?t.resolve(d.__await).then((function(e){a("next",e,o,s)}),(function(e){a("throw",e,o,s)})):t.resolve(d).then((function(e){c.value=e,o(c)}),(function(e){return a("throw",e,o,s)}))}s(l.arg)}var r;n(this,"_invoke",{value:function(e,i){function n(){return new t((function(t,n){a(e,i,t,n)}))}return r=r?r.then(n,n):n()}})}function k(t,a,i){var n=h;return function(r,o){if(n===m)throw Error("Generator is already running");if(n===v){if("throw"===r)throw o;return{value:e,done:!0}}for(i.method=r,i.arg=o;;){var s=i.delegate;if(s){var l=P(s,i);if(l){if(l===g)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===h)throw n=v,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=m;var c=p(t,a,i);if("normal"===c.type){if(n=i.done?v:f,c.arg===g)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(n=v,i.method="throw",i.arg=c.arg)}}}function P(t,a){var i=a.method,n=t.iterator[i];if(n===e)return a.delegate=null,"throw"===i&&t.iterator.return&&(a.method="return",a.arg=e,P(t,a),"throw"===a.method)||"return"!==i&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+i+"' method")),g;var r=p(n,t.iterator,a.arg);if("throw"===r.type)return a.method="throw",a.arg=r.arg,a.delegate=null,g;var o=r.arg;return o?o.done?(a[t.resultName]=o.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,g):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function O(t){if(t||""===t){var a=t[o];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,r=function a(){for(;++n<t.length;)if(i.call(t,n))return a.value=t[n],a.done=!1,a;return a.value=e,a.done=!0,a};return r.next=r}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=b,n(_,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,l,"GeneratorFunction")),e.prototype=Object.create(_),e},t.awrap=function(e){return{__await:e}},S(C.prototype),c(C.prototype,s,(function(){return this})),t.AsyncIterator=C,t.async=function(e,a,i,n,r){void 0===r&&(r=Promise);var o=new C(d(e,a,i,n),r);return t.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(_),c(_,l,"Generator"),c(_,o,(function(){return this})),c(_,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var i in t)a.push(i);return a.reverse(),function e(){for(;a.length;){var i=a.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},t.values=O,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var a in this)"t"===a.charAt(0)&&i.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function n(i,n){return s.type="throw",s.arg=t,a.next=i,n&&(a.method="next",a.arg=e),!!n}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var l=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var r=n;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var o=r?r.completion:{};return o.type=e,o.arg=t,r?(this.method="next",this.next=r.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),I(a),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var i=a.completion;if("throw"===i.type){var n=i.arg;I(a)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,a,i){return this.delegate={iterator:O(t),resultName:a,nextLoc:i},"next"===this.method&&(this.arg=e),g}},t}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var i,n,r,o,s=[],l=!0,c=!1;try{if(r=(a=a.call(e)).next,0===t);else for(;!(l=(i=r.call(a)).done)&&(s.push(i.value),s.length!==t);l=!0);}catch(e){c=!0,n=e}finally{try{if(!l&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw n}}return s}}(e,t)||v(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||v(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,t);if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof t?t:t+""}function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function v(e,t){if(e){if("string"==typeof e)return r(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?r(e,t):void 0}}function g(){var e=this.getMeta("url");e.match(/^http/i)||(e=location.href),this.defaults={url:e,origin:location.origin,source:this.getMeta("site_name")||document.title,title:this.getMeta("title")||document.title,description:this.getMeta("description")||"",image:this.getMeta("image")}}(e=jQuery).fn.emulateTransitionEnd=function(t){var a=!1,i=this;return e(this).one("wpcomTransitionEnd",(function(){a=!0})),setTimeout((function(){a||e(i).trigger(e.__transition.end)}),t),this},e((function(){e.__transition=function(){var e=document.createElement("wpcom"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var a in t)if(void 0!==e.style[a])return{end:t[a]};return!1}(),e.__transition&&(e.event.special.wpcomTransitionEnd={bindType:e.__transition.end,delegateType:e.__transition.end,handle:function(t){if(e(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}})})),function(e){var t='[data-dismiss="alert"], [data-wpcom-dismiss="alert"]',a=function(a){e(a).on("click",t,this.close)};a.TRANSITION_DURATION=150,a.prototype.close=function(t){var i=e(this),n=i.attr("data-target");n||(n=(n=i.attr("href"))&&n.replace(/.*(?=#[^\s]*$)/,"")),n="#"===n?[]:n;var r=e(document).find(n);function o(){r.detach().trigger("closed.wpcom.alert").remove()}t&&t.preventDefault(),r.length||(r=i.closest(".wpcom-alert")),r.trigger(t=e.Event("close.wpcom.alert")),t.isDefaultPrevented()||(r.removeClass("in"),e.__transition&&r.hasClass("fade")?r.one("wpcomTransitionEnd",o).emulateTransitionEnd(a.TRANSITION_DURATION):o())};var i=e.fn._alert;e.fn._alert=function(t){return this.each((function(){var i=e(this),n=i.data("wpcom.alert");n||i.data("wpcom.alert",n=new a(this)),"string"==typeof t&&n[t].call(i)}))},e.fn._alert.Constructor=a,e.fn._alert.noConflict=function(){return e.fn._alert=i,this},e(document).on("click.wpcom.alert.data-api",t,a.prototype.close)}(jQuery),function(e){var t=function t(a,i){this.$element=e(a),this.options=e.extend({},t.DEFAULTS,i),this.$trigger=e('[data-toggle="collapse"][href="#'+a.id+'"],[data-toggle="collapse"][data-target="#'+a.id+'"]'),this.transitioning=null,this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};function a(t){var a,i=t.attr("data-target")||(a=t.attr("href"))&&a.replace(/.*(?=#[^\s]+$)/,"");return e(document).find(i)}function i(a){return this.each((function(){var i=e(this),n=i.data("bs.collapse"),r=e.extend({},t.DEFAULTS,i.data(),"object"==m(a)&&a);!n&&r.toggle&&/show|hide/.test(a)&&(r.toggle=!1),n||i.data("bs.collapse",n=new t(this,r)),"string"==typeof a&&n[a]()}))}t.VERSION="3.4.1",t.TRANSITION_DURATION=350,t.DEFAULTS={toggle:!0},t.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},t.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var a,n=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(n&&n.length&&(a=n.data("bs.collapse"))&&a.transitioning)){var r=e.Event("show.bs.collapse");if(this.$element.trigger(r),!r.isDefaultPrevented()){n&&n.length&&(i.call(n,"hide"),a||n.data("bs.collapse",null));var o=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[o](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var s=function(){this.$element.removeClass("collapsing").addClass("collapse in")[o](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!e.support.transition)return s.call(this);var l=e.camelCase(["scroll",o].join("-"));this.$element.one("bsTransitionEnd",e.proxy(s,this)).emulateTransitionEnd(t.TRANSITION_DURATION)[o](this.$element[0][l])}}}},t.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var a=e.Event("hide.bs.collapse");if(this.$element.trigger(a),!a.isDefaultPrevented()){var i=this.dimension();this.$element[i](this.$element[i]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var n=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};if(!e.support.transition)return n.call(this);this.$element[i](0).one("bsTransitionEnd",e.proxy(n,this)).emulateTransitionEnd(t.TRANSITION_DURATION)}}},t.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},t.prototype.getParent=function(){return e(document).find(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(e.proxy((function(t,i){var n=e(i);this.addAriaAndCollapsedClass(a(n),n)}),this)).end()},t.prototype.addAriaAndCollapsedClass=function(e,t){var a=e.hasClass("in");e.attr("aria-expanded",a),t.toggleClass("collapsed",!a).attr("aria-expanded",a)};var n=e.fn.collapse;e.fn.collapse=i,e.fn.collapse.Constructor=t,e.fn.collapse.noConflict=function(){return e.fn.collapse=n,this},e(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',(function(t){var n=e(this);n.attr("data-target")||t.preventDefault();var r=a(n),o=r.data("bs.collapse")?"toggle":n.data();i.call(r,o)}))}(jQuery),function(e){var t=function(t,a){this.options=a,this.$body=e(document.body),this.$element=e(t),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.fixedContent=".navbar-fixed-top, .navbar-fixed-bottom",this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,e.proxy((function(){this.$element.trigger("loaded.wpcom.modal")}),this))};function a(a,i){return this.each((function(){var n=e(this),r=n.data("wpcom.modal"),o=e.extend({},t.DEFAULTS,n.data(),"object"==m(a)&&a);r||n.data("wpcom.modal",r=new t(this,o)),"string"==typeof a?r[a](i):o.show&&r.show(i)}))}t.TRANSITION_DURATION=250,t.BACKDROP_TRANSITION_DURATION=120,t.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},t.prototype.toggle=function(e){return this.isShown?this.hide():this.show(e)},t.prototype.show=function(a){var i=this,n=e.Event("show.wpcom.modal",{relatedTarget:a});this.$element.trigger(n),this.isShown||n.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.wpcom.modal",'[data-dismiss="modal"], [data-wpcom-dismiss="modal"]',e.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.wpcom.modal",(function(){i.$element.one("mouseup.dismiss.wpcom.modal",(function(t){e(t.target).is(i.$element)&&(i.ignoreBackdropClick=!0)}))})),this.backdrop((function(){var n=e.__transition&&i.$element.hasClass("fade");i.$element.parent().length||i.$element.appendTo(i.$body),i.$element.show().scrollTop(0),i.adjustDialog(),n&&i.$element[0].offsetWidth,i.$element.addClass("in"),i.enforceFocus();var r=e.Event("shown.wpcom.modal",{relatedTarget:a});n?i.$dialog.one("wpcomTransitionEnd",(function(){i.$element.trigger("focus").trigger(r)})).emulateTransitionEnd(t.TRANSITION_DURATION):i.$element.trigger("focus").trigger(r)})))},t.prototype.hide=function(a){a&&a.preventDefault(),a=e.Event("hide.wpcom.modal"),this.$element.trigger(a),this.isShown&&!a.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),e(document).off("focusin.wpcom.modal"),this.$element.removeClass("in").off("click.dismiss.wpcom.modal").off("mouseup.dismiss.wpcom.modal"),this.$dialog.off("mousedown.dismiss.wpcom.modal"),e.__transition&&this.$element.hasClass("fade")?this.$element.one("wpcomTransitionEnd",e.proxy(this.hideModal,this)).emulateTransitionEnd(t.TRANSITION_DURATION):this.hideModal())},t.prototype.enforceFocus=function(){e(document).off("focusin.wpcom.modal").on("focusin.wpcom.modal",e.proxy((function(e){document===e.target||this.$element[0]===e.target||this.$element.has(e.target).length||this.$element.trigger("focus")}),this))},t.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.wpcom.modal",e.proxy((function(e){27==e.which&&this.hide()}),this)):this.isShown||this.$element.off("keydown.dismiss.wpcom.modal")},t.prototype.resize=function(){this.isShown?e(window).on("resize.wpcom.modal",e.proxy(this.handleUpdate,this)):e(window).off("resize.wpcom.modal")},t.prototype.hideModal=function(){var e=this;this.$element.hide(),this.backdrop((function(){e.$body.removeClass("modal-open"),e.resetAdjustments(),e.resetScrollbar(),e.$element.trigger("hidden.wpcom.modal")}))},t.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},t.prototype.backdrop=function(a){var i=this,n=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var r=e.__transition&&n;if(this.$backdrop=e(document.createElement("div")).addClass("wpcom-modal-backdrop "+n).appendTo(this.$body),this.$element.on("click.dismiss.wpcom.modal",e.proxy((function(e){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide())}),this)),r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!a)return;r?this.$backdrop.one("wpcomTransitionEnd",a).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):a()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var o=function(){i.removeBackdrop(),a&&a()};e.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("wpcomTransitionEnd",o).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):o()}else a&&a()},t.prototype.handleUpdate=function(){this.adjustDialog()},t.prototype.adjustDialog=function(){var e=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&e?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!e?this.scrollbarWidth:""})},t.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},t.prototype.checkScrollbar=function(){var e=window.innerWidth;if(!e){var t=document.documentElement.getBoundingClientRect();e=t.right-Math.abs(t.left)}this.bodyIsOverflowing=document.body.clientWidth<e,this.scrollbarWidth=this.measureScrollbar()},t.prototype.setScrollbar=function(){var t=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||0;var a=this.scrollbarWidth;this.bodyIsOverflowing&&(this.$body.css("--scrollbar-width",t+a+"px"),e(this.fixedContent).each((function(t,i){var n=i.style.paddingRight,r=e(i).css("padding-right");e(i).data("padding-right",n).css("padding-right",parseFloat(r)+a+"px")})))},t.prototype.resetScrollbar=function(){this.$body.css("--scrollbar-width",this.originalBodyPad+"px"),e(this.fixedContent).each((function(t,a){var i=e(a).data("padding-right");e(a).removeData("padding-right"),a.style.paddingRight=i||""}))},t.prototype.measureScrollbar=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",this.$body.append(e);var t=e.offsetWidth-e.clientWidth;return this.$body[0].removeChild(e),t};var i=e.fn._modal,n=e.fn.modal;e.fn._modal=a,e.fn._modal.Constructor=t,e.fn.modal=a,e.fn.modal.Constructor=t,e.fn._modal.noConflict=function(){return e.fn._modal=i,this},e.fn.modal.noConflict=function(){return e.fn.modal=n,this},e(document).on("click.wpcom.modal.data-api",'[data-toggle="modal"]',(function(t){var i=e(this),n=i.attr("href"),r=i.attr("data-target")||n&&n.replace(/.*(?=#[^\s]+$)/,""),o=e(document).find(r),s=o.data("wpcom.modal")?"toggle":e.extend({remote:!/#/.test(n)&&n},o.data(),i.data());i.is("a")&&t.preventDefault(),o.one("show.wpcom.modal",(function(e){e.isDefaultPrevented()||o.one("hidden.wpcom.modal",(function(){i.is(":visible")&&i.trigger("focus")}))})),a.call(o,s,this)}))}(jQuery),function(e){var t=["sanitize","whiteList","sanitizeFn"],a=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],i={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},n=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,r=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function o(t,i){var o=t.nodeName.toLowerCase();if(-1!==e.inArray(o,i))return-1===e.inArray(o,a)||Boolean(t.nodeValue.match(n)||t.nodeValue.match(r));for(var s=e(i).filter((function(e,t){return t instanceof RegExp})),l=0,c=s.length;l<c;l++)if(o.match(s[l]))return!0;return!1}function s(t,a,i){if(0===t.length)return t;if(i&&"function"==typeof i)return i(t);if(!document.implementation||!document.implementation.createHTMLDocument)return t;var n=document.implementation.createHTMLDocument("sanitization");n.body.innerHTML=t;for(var r=e.map(a,(function(e,t){return t})),s=e(n.body).find("*"),l=0,c=s.length;l<c;l++){var d=s[l],u=d.nodeName.toLowerCase();if(-1!==e.inArray(u,r))for(var p=e.map(d.attributes,(function(e){return e})),h=[].concat(a["*"]||[],a[u]||[]),f=0,m=p.length;f<m;f++)o(p[f],h)||d.removeAttribute(p[f].nodeName);else d.parentNode.removeChild(d)}return n.body.innerHTML}var l=function(e,t){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",e,t)};l.VERSION="3.4.1",l.TRANSITION_DURATION=150,l.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0},sanitize:!0,sanitizeFn:null,whiteList:i},l.prototype.init=function(t,a,i){if(this.enabled=!0,this.type=t,this.$element=e(a),this.options=this.getOptions(i),this.$viewport=this.options.viewport&&e(document).find(e.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var n=this.options.trigger.split(" "),r=n.length;r--;){var o=n[r];if("click"==o)this.$element.on("click."+this.type,this.options.selector,e.proxy(this.toggle,this));else if("manual"!=o){var s="hover"==o?"mouseenter":"focusin",l="hover"==o?"mouseleave":"focusout";this.$element.on(s+"."+this.type,this.options.selector,e.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,e.proxy(this.leave,this))}}this.options.selector?this._options=e.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},l.prototype.getDefaults=function(){return l.DEFAULTS},l.prototype.getOptions=function(a){var i=this.$element.data();for(var n in i)i.hasOwnProperty(n)&&-1!==e.inArray(n,t)&&delete i[n];return(a=e.extend({},this.getDefaults(),i,a)).delay&&"number"==typeof a.delay&&(a.delay={show:a.delay,hide:a.delay}),a.sanitize&&(a.template=s(a.template,a.whiteList,a.sanitizeFn)),a},l.prototype.getDelegateOptions=function(){var t={},a=this.getDefaults();return this._options&&e.each(this._options,(function(e,i){a[e]!=i&&(t[e]=i)})),t},l.prototype.enter=function(t){var a=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);if(a||(a=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,a)),t instanceof e.Event&&(a.inState["focusin"==t.type?"focus":"hover"]=!0),a.tip().hasClass("in")||"in"==a.hoverState)a.hoverState="in";else{if(clearTimeout(a.timeout),a.hoverState="in",!a.options.delay||!a.options.delay.show)return a.show();a.timeout=setTimeout((function(){"in"==a.hoverState&&a.show()}),a.options.delay.show)}},l.prototype.isInStateTrue=function(){for(var e in this.inState)if(this.inState[e])return!0;return!1},l.prototype.leave=function(t){var a=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);if(a||(a=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,a)),t instanceof e.Event&&(a.inState["focusout"==t.type?"focus":"hover"]=!1),!a.isInStateTrue()){if(clearTimeout(a.timeout),a.hoverState="out",!a.options.delay||!a.options.delay.hide)return a.hide();a.timeout=setTimeout((function(){"out"==a.hoverState&&a.hide()}),a.options.delay.hide)}},l.prototype.show=function(){var t=e.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(t);var a=e.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(t.isDefaultPrevented()||!a)return;var i=this,n=this.tip(),r=this.getUID(this.type);this.setContent(),n.attr("id",r),this.$element.attr("aria-describedby",r),this.options.animation&&n.addClass("fade");var o="function"==typeof this.options.placement?this.options.placement.call(this,n[0],this.$element[0]):this.options.placement,s=/\s?auto?\s?/i,c=s.test(o);c&&(o=o.replace(s,"")||"top"),n.detach().css({top:0,left:0,display:"block"}).addClass(o).data("bs."+this.type,this),this.options.container?n.appendTo(e(document).find(this.options.container)):n.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var d=this.getPosition(),u=n[0].offsetWidth,p=n[0].offsetHeight;if(c){var h=o,f=this.getPosition(this.$viewport);o="bottom"==o&&d.bottom+p>f.bottom?"top":"top"==o&&d.top-p<f.top?"bottom":"right"==o&&d.right+u>f.width?"left":"left"==o&&d.left-u<f.left?"right":o,n.removeClass(h).addClass(o)}var m=this.getCalculatedOffset(o,d,u,p);this.applyPlacement(m,o);var v=function(){var e=i.hoverState;i.$element.trigger("shown.bs."+i.type),i.hoverState=null,"out"==e&&i.leave(i)};e.__transition&&this.$tip.hasClass("fade")?n.one("wpcomTransitionEnd",v).emulateTransitionEnd(l.TRANSITION_DURATION):v()}},l.prototype.applyPlacement=function(t,a){var i=this.tip(),n=i[0].offsetWidth,r=i[0].offsetHeight,o=parseInt(i.css("margin-top"),10),s=parseInt(i.css("margin-left"),10);isNaN(o)&&(o=0),isNaN(s)&&(s=0),t.top+=o,t.left+=s,e.offset.setOffset(i[0],e.extend({using:function(e){i.css({top:Math.round(e.top),left:Math.round(e.left)})}},t),0),i.addClass("in");var l=i[0].offsetWidth,c=i[0].offsetHeight;"top"==a&&c!=r&&(t.top=t.top+r-c);var d=this.getViewportAdjustedDelta(a,t,l,c);d.left?t.left+=d.left:t.top+=d.top;var u=/top|bottom/.test(a),p=u?2*d.left-n+l:2*d.top-r+c,h=u?"offsetWidth":"offsetHeight";i.offset(t),this.replaceArrow(p,i[0][h],u)},l.prototype.replaceArrow=function(e,t,a){this.arrow().css(a?"left":"top",50*(1-e/t)+"%").css(a?"top":"left","")},l.prototype.setContent=function(){var e=this.tip(),t=this.getTitle();this.options.html?(this.options.sanitize&&(t=s(t,this.options.whiteList,this.options.sanitizeFn)),e.find(".tooltip-inner").html(t)):e.find(".tooltip-inner").text(t),e.removeClass("fade in top bottom left right")},l.prototype.hide=function(t){var a=this,i=e(this.$tip),n=e.Event("hide.bs."+this.type);function r(){"in"!=a.hoverState&&i.detach(),a.$element&&a.$element.removeAttr("aria-describedby").trigger("hidden.bs."+a.type),t&&t()}if(this.$element.trigger(n),!n.isDefaultPrevented())return i.removeClass("in"),e.__transition&&i.hasClass("fade")?i.one("wpcomTransitionEnd",r).emulateTransitionEnd(l.TRANSITION_DURATION):r(),this.hoverState=null,this},l.prototype.fixTitle=function(){var e=this.$element;(e.attr("title")||"string"!=typeof e.attr("data-original-title"))&&e.attr("data-original-title",e.attr("title")||"").attr("title","")},l.prototype.hasContent=function(){return this.getTitle()},l.prototype.getPosition=function(t){var a=(t=t||this.$element)[0],i="BODY"==a.tagName,n=a.getBoundingClientRect();null==n.width&&(n=e.extend({},n,{width:n.right-n.left,height:n.bottom-n.top}));var r=window.SVGElement&&a instanceof window.SVGElement,o=i?{top:0,left:0}:r?null:t.offset(),s={scroll:i?document.documentElement.scrollTop||document.body.scrollTop:t.scrollTop()},l=i?{width:e(window).width(),height:e(window).height()}:null;return e.extend({},n,s,l,o)},l.prototype.getCalculatedOffset=function(e,t,a,i){return"bottom"==e?{top:t.top+t.height,left:t.left+t.width/2-a/2}:"top"==e?{top:t.top-i,left:t.left+t.width/2-a/2}:"left"==e?{top:t.top+t.height/2-i/2,left:t.left-a}:{top:t.top+t.height/2-i/2,left:t.left+t.width}},l.prototype.getViewportAdjustedDelta=function(e,t,a,i){var n={top:0,left:0};if(!this.$viewport)return n;var r=this.options.viewport&&this.options.viewport.padding||0,o=this.getPosition(this.$viewport);if(/right|left/.test(e)){var s=t.top-r-o.scroll,l=t.top+r-o.scroll+i;s<o.top?n.top=o.top-s:l>o.top+o.height&&(n.top=o.top+o.height-l)}else{var c=t.left-r,d=t.left+r+a;c<o.left?n.left=o.left-c:d>o.right&&(n.left=o.left+o.width-d)}return n},l.prototype.getTitle=function(){var e=this.$element,t=this.options;return e.attr("data-original-title")||("function"==typeof t.title?t.title.call(e[0]):t.title)},l.prototype.getUID=function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e},l.prototype.tip=function(){if(!this.$tip&&(this.$tip=e(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},l.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},l.prototype.enable=function(){this.enabled=!0},l.prototype.disable=function(){this.enabled=!1},l.prototype.toggleEnabled=function(){this.enabled=!this.enabled},l.prototype.toggle=function(t){var a=this;t&&((a=e(t.currentTarget).data("bs."+this.type))||(a=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,a))),t?(a.inState.click=!a.inState.click,a.isInStateTrue()?a.enter(a):a.leave(a)):a.tip().hasClass("in")?a.leave(a):a.enter(a)},l.prototype.destroy=function(){var e=this;clearTimeout(this.timeout),this.hide((function(){e.$element.off("."+e.type).removeData("bs."+e.type),e.$tip&&e.$tip.detach(),e.$tip=null,e.$arrow=null,e.$viewport=null,e.$element=null}))},l.prototype.sanitizeHtml=function(e){return s(e,this.options.whiteList,this.options.sanitizeFn)};var c=e.fn.tooltip;e.fn.tooltip=function(t){return this.each((function(){var a=e(this),i=a.data("bs.tooltip"),n="object"==m(t)&&t;!i&&/destroy|hide/.test(t)||(i||a.data("bs.tooltip",i=new l(this,n)),"string"==typeof t&&i[t]())}))},e.fn.tooltip.Constructor=l,e.fn.tooltip.noConflict=function(){return e.fn.tooltip=c,this}}(jQuery),function(e){var t=function(t){this.element=e(t)};function a(a){return this.each((function(){var i=e(this),n=i.data("bs.tab");n||i.data("bs.tab",n=new t(this)),"string"==typeof a&&n[a]()}))}t.VERSION="3.4.1",t.TRANSITION_DURATION=150,t.prototype.show=function(){var t=this.element,a=t.closest("ul:not(.dropdown-menu)"),i=t.data("target");if(i||(i=(i=t.attr("href"))&&i.replace(/.*(?=#[^\s]*$)/,"")),!t.parent("li").hasClass("active")){var n=a.find(".active:last a"),r=e.Event("hide.bs.tab",{relatedTarget:t[0]}),o=e.Event("show.bs.tab",{relatedTarget:n[0]});if(n.trigger(r),t.trigger(o),!o.isDefaultPrevented()&&!r.isDefaultPrevented()){var s=e(document).find(i);this.activate(t.closest("li"),a),this.activate(s,s.parent(),(function(){n.trigger({type:"hidden.bs.tab",relatedTarget:t[0]}),t.trigger({type:"shown.bs.tab",relatedTarget:n[0]})}))}}},t.prototype.activate=function(a,i,n){var r=i.find("> .active"),o=n&&e.__transition&&(r.length&&r.hasClass("fade")||!!i.find("> .fade").length);function s(){r.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),a.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),o?(a[0].offsetWidth,a.addClass("in")):a.removeClass("fade"),a.parent(".dropdown-menu").length&&a.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),n&&n()}r.length&&o?r.one("wpcomTransitionEnd",s).emulateTransitionEnd(t.TRANSITION_DURATION):s(),r.removeClass("in")};var i=e.fn.tab;e.fn.tab=a,e.fn.tab.Constructor=t,e.fn.tab.noConflict=function(){return e.fn.tab=i,this};var n=function(t){t.preventDefault(),a.call(e(this),"show")};e(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',n).on("click.bs.tab.data-api",'[data-toggle="pill"]',n)}(jQuery),(t=jQuery).fn.qrcode=function(e){var a;function i(e){this.mode=a,this.data=e}function n(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}function r(e,t){if(null==e.length)throw Error(e.length+"/"+t);for(var a=0;a<e.length&&0==e[a];)a++;this.num=Array(e.length-a+t);for(var i=0;i<e.length-a;i++)this.num[i]=e[i+a]}function o(e,t){this.totalCount=e,this.dataCount=t}function s(){this.buffer=[],this.length=0}i.prototype={getLength:function(){return this.data.length},write:function(e){for(var t=0;t<this.data.length;t++)e.put(this.data.charCodeAt(t),8)}},n.prototype={addData:function(e){this.dataList.push(new i(e)),this.dataCache=null},isDark:function(e,t){if(0>e||this.moduleCount<=e||0>t||this.moduleCount<=t)throw Error(e+","+t);return this.modules[e][t]},getModuleCount:function(){return this.moduleCount},make:function(){if(1>this.typeNumber){var e=1;for(e=1;40>e;e++){for(var t=o.getRSBlocks(e,this.errorCorrectLevel),a=new s,i=0,n=0;n<t.length;n++)i+=t[n].dataCount;for(n=0;n<this.dataList.length;n++)t=this.dataList[n],a.put(t.mode,4),a.put(t.getLength(),l.getLengthInBits(t.mode,e)),t.write(a);if(a.getLengthInBits()<=8*i)break}this.typeNumber=e}this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=Array(this.moduleCount);for(var a=0;a<this.moduleCount;a++){this.modules[a]=Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++)this.modules[a][i]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),7<=this.typeNumber&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=n.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},setupPositionProbePattern:function(e,t){for(var a=-1;7>=a;a++)if(!(-1>=e+a||this.moduleCount<=e+a))for(var i=-1;7>=i;i++)-1>=t+i||this.moduleCount<=t+i||(this.modules[e+a][t+i]=0<=a&&6>=a&&(0==i||6==i)||0<=i&&6>=i&&(0==a||6==a)||2<=a&&4>=a&&2<=i&&4>=i)},getBestMaskPattern:function(){for(var e=0,t=0,a=0;8>a;a++){this.makeImpl(!0,a);var i=l.getLostPoint(this);(0==a||e>i)&&(e=i,t=a)}return t},createMovieClip:function(e,t,a){for(e=e.createEmptyMovieClip(t,a),this.make(),t=0;t<this.modules.length;t++){a=1*t;for(var i=0;i<this.modules[t].length;i++){var n=1*i;this.modules[t][i]&&(e.beginFill(0,100),e.moveTo(n,a),e.lineTo(n+1,a),e.lineTo(n+1,a+1),e.lineTo(n,a+1),e.endFill())}}return e},setupTimingPattern:function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=0==e%2);for(e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=0==e%2)},setupPositionAdjustPattern:function(){for(var e=l.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var a=0;a<e.length;a++){var i=e[t],n=e[a];if(null==this.modules[i][n])for(var r=-2;2>=r;r++)for(var o=-2;2>=o;o++)this.modules[i+r][n+o]=-2==r||2==r||-2==o||2==o||0==r&&0==o}},setupTypeNumber:function(e){for(var t=l.getBCHTypeNumber(this.typeNumber),a=0;18>a;a++){var i=!e&&1==(t>>a&1);this.modules[Math.floor(a/3)][a%3+this.moduleCount-8-3]=i}for(a=0;18>a;a++)i=!e&&1==(t>>a&1),this.modules[a%3+this.moduleCount-8-3][Math.floor(a/3)]=i},setupTypeInfo:function(e,t){for(var a=l.getBCHTypeInfo(this.errorCorrectLevel<<3|t),i=0;15>i;i++){var n=!e&&1==(a>>i&1);6>i?this.modules[i][8]=n:8>i?this.modules[i+1][8]=n:this.modules[this.moduleCount-15+i][8]=n}for(i=0;15>i;i++)n=!e&&1==(a>>i&1),8>i?this.modules[8][this.moduleCount-i-1]=n:9>i?this.modules[8][15-i-1+1]=n:this.modules[8][15-i-1]=n;this.modules[this.moduleCount-8][8]=!e},mapData:function(e,t){for(var a=-1,i=this.moduleCount-1,n=7,r=0,o=this.moduleCount-1;0<o;o-=2)for(6==o&&o--;;){for(var s=0;2>s;s++)if(null==this.modules[i][o-s]){var c=!1;r<e.length&&(c=1==(e[r]>>>n&1)),l.getMask(t,i,o-s)&&(c=!c),this.modules[i][o-s]=c,-1==--n&&(r++,n=7)}if(0>(i+=a)||this.moduleCount<=i){i-=a,a=-a;break}}}},n.PAD0=236,n.PAD1=17,n.createData=function(e,t,a){t=o.getRSBlocks(e,t);for(var i=new s,r=0;r<a.length;r++){var c=a[r];i.put(c.mode,4),i.put(c.getLength(),l.getLengthInBits(c.mode,e)),c.write(i)}for(r=e=0;r<t.length;r++)e+=t[r].dataCount;if(i.getLengthInBits()>8*e)throw Error("code length overflow. ("+i.getLengthInBits()+">"+8*e+")");for(i.getLengthInBits()+4<=8*e&&i.put(0,4);0!=i.getLengthInBits()%8;)i.putBit(!1);for(;!(i.getLengthInBits()>=8*e||(i.put(n.PAD0,8),i.getLengthInBits()>=8*e));)i.put(n.PAD1,8);return n.createBytes(i,t)},n.createBytes=function(e,t){for(var a=0,i=0,n=0,o=Array(t.length),s=Array(t.length),c=0;c<t.length;c++){var d=t[c].dataCount,u=t[c].totalCount-d;i=Math.max(i,d),n=Math.max(n,u),o[c]=Array(d);for(var p=0;p<o[c].length;p++)o[c][p]=255&e.buffer[p+a];for(a+=d,p=l.getErrorCorrectPolynomial(u),d=new r(o[c],p.getLength()-1).mod(p),s[c]=Array(p.getLength()-1),p=0;p<s[c].length;p++)u=p+d.getLength()-s[c].length,s[c][p]=0<=u?d.get(u):0}for(p=c=0;p<t.length;p++)c+=t[p].totalCount;for(a=Array(c),p=d=0;p<i;p++)for(c=0;c<t.length;c++)p<o[c].length&&(a[d++]=o[c][p]);for(p=0;p<n;p++)for(c=0;c<t.length;c++)p<s[c].length&&(a[d++]=s[c][p]);return a},a=4;for(var l={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;0<=l.getBCHDigit(t)-l.getBCHDigit(l.G15);)t^=l.G15<<l.getBCHDigit(t)-l.getBCHDigit(l.G15);return(e<<10|t)^l.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;0<=l.getBCHDigit(t)-l.getBCHDigit(l.G18);)t^=l.G18<<l.getBCHDigit(t)-l.getBCHDigit(l.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;0!=e;)t++,e>>>=1;return t},getPatternPosition:function(e){return l.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,a){switch(e){case 0:return 0==(t+a)%2;case 1:return 0==t%2;case 2:return 0==a%3;case 3:return 0==(t+a)%3;case 4:return 0==(Math.floor(t/2)+Math.floor(a/3))%2;case 5:return 0==t*a%2+t*a%3;case 6:return 0==(t*a%2+t*a%3)%2;case 7:return 0==(t*a%3+(t+a)%2)%2;default:throw Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new r([1],0),a=0;a<e;a++)t=t.multiply(new r([1,c.gexp(a)],0));return t},getLengthInBits:function(e,t){if(1<=t&&10>t)switch(e){case 1:return 10;case 2:return 9;case a:case 8:return 8;default:throw Error("mode:"+e)}else if(27>t)switch(e){case 1:return 12;case 2:return 11;case a:return 16;case 8:return 10;default:throw Error("mode:"+e)}else{if(!(41>t))throw Error("type:"+t);switch(e){case 1:return 14;case 2:return 13;case a:return 16;case 8:return 12;default:throw Error("mode:"+e)}}},getLostPoint:function(e){for(var t=e.getModuleCount(),a=0,i=0;i<t;i++)for(var n=0;n<t;n++){for(var r=0,o=e.isDark(i,n),s=-1;1>=s;s++)if(!(0>i+s||t<=i+s))for(var l=-1;1>=l;l++)0>n+l||t<=n+l||0==s&&0==l||o==e.isDark(i+s,n+l)&&r++;5<r&&(a+=3+r-5)}for(i=0;i<t-1;i++)for(n=0;n<t-1;n++)r=0,e.isDark(i,n)&&r++,e.isDark(i+1,n)&&r++,e.isDark(i,n+1)&&r++,e.isDark(i+1,n+1)&&r++,(0==r||4==r)&&(a+=3);for(i=0;i<t;i++)for(n=0;n<t-6;n++)e.isDark(i,n)&&!e.isDark(i,n+1)&&e.isDark(i,n+2)&&e.isDark(i,n+3)&&e.isDark(i,n+4)&&!e.isDark(i,n+5)&&e.isDark(i,n+6)&&(a+=40);for(n=0;n<t;n++)for(i=0;i<t-6;i++)e.isDark(i,n)&&!e.isDark(i+1,n)&&e.isDark(i+2,n)&&e.isDark(i+3,n)&&e.isDark(i+4,n)&&!e.isDark(i+5,n)&&e.isDark(i+6,n)&&(a+=40);for(n=r=0;n<t;n++)for(i=0;i<t;i++)e.isDark(i,n)&&r++;return a+10*(e=Math.abs(100*r/t/t-50)/5)}},c={glog:function(e){if(1>e)throw Error("glog("+e+")");return c.LOG_TABLE[e]},gexp:function(e){for(;0>e;)e+=255;for(;256<=e;)e-=255;return c.EXP_TABLE[e]},EXP_TABLE:Array(256),LOG_TABLE:Array(256)},d=0;8>d;d++)c.EXP_TABLE[d]=1<<d;for(d=8;256>d;d++)c.EXP_TABLE[d]=c.EXP_TABLE[d-4]^c.EXP_TABLE[d-5]^c.EXP_TABLE[d-6]^c.EXP_TABLE[d-8];for(d=0;255>d;d++)c.LOG_TABLE[c.EXP_TABLE[d]]=d;return r.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=Array(this.getLength()+e.getLength()-1),a=0;a<this.getLength();a++)for(var i=0;i<e.getLength();i++)t[a+i]^=c.gexp(c.glog(this.get(a))+c.glog(e.get(i)));return new r(t,0)},mod:function(e){if(0>this.getLength()-e.getLength())return this;for(var t=c.glog(this.get(0))-c.glog(e.get(0)),a=Array(this.getLength()),i=0;i<this.getLength();i++)a[i]=this.get(i);for(i=0;i<e.getLength();i++)a[i]^=c.gexp(c.glog(e.get(i))+t);return new r(a,0).mod(e)}},o.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],o.getRSBlocks=function(e,t){var a=o.getRsBlockTable(e,t);if(null==a)throw Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var i=a.length/3,n=[],r=0;r<i;r++)for(var s=a[3*r+0],l=a[3*r+1],c=a[3*r+2],d=0;d<s;d++)n.push(new o(l,c));return n},o.getRsBlockTable=function(e,t){switch(t){case 1:return o.RS_BLOCK_TABLE[4*(e-1)+0];case 0:return o.RS_BLOCK_TABLE[4*(e-1)+1];case 3:return o.RS_BLOCK_TABLE[4*(e-1)+2];case 2:return o.RS_BLOCK_TABLE[4*(e-1)+3]}},s.prototype={get:function(e){return 1==(this.buffer[Math.floor(e/8)]>>>7-e%8&1)},put:function(e,t){for(var a=0;a<t;a++)this.putBit(1==(e>>>t-a-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},"string"==typeof e&&(e={text:e}),e=t.extend({},{render:"canvas",width:256,height:256,typeNumber:-1,correctLevel:2,background:"#ffffff",foreground:"#000000"},e),this.each((function(){var a;if("canvas"==e.render){(a=new n(e.typeNumber,e.correctLevel)).addData(e.text),a.make();var i=document.createElement("canvas");i.width=e.width,i.height=e.height;for(var r=i.getContext("2d"),o=e.width/a.getModuleCount(),s=e.height/a.getModuleCount(),l=0;l<a.getModuleCount();l++)for(var c=0;c<a.getModuleCount();c++){r.fillStyle=a.isDark(l,c)?e.foreground:e.background;var d=Math.ceil((c+1)*o)-Math.floor(c*o),u=Math.ceil((l+1)*o)-Math.floor(l*o);r.fillRect(Math.round(c*o),Math.round(l*s),d,u)}}else for((a=new n(e.typeNumber,e.correctLevel)).addData(e.text),a.make(),i=t("<table></table>").css("width",e.width+"px").css("height",e.height+"px").css("border","0px").css("border-collapse","collapse").css("background-color",e.background),r=e.width/a.getModuleCount(),o=e.height/a.getModuleCount(),s=0;s<a.getModuleCount();s++)for(l=t("<tr></tr>").css("height",o+"px").appendTo(i),c=0;c<a.getModuleCount();c++)t("<td></td>").css("width",r+"px").css("background-color",a.isDark(s,c)?e.foreground:e.background).appendTo(l);a=i,jQuery(a).appendTo(this)}))},"function"!=typeof Object.assign&&(Object.assign=function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");e=Object(e);for(var t=1;t<arguments.length;t++){var a=arguments[t];if(null!=a)for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e}),g.prototype={getMeta:function(e){var t=document.querySelector('meta[property="og:'+e+'"]');return t?t.getAttribute("content"):""},templates:{qzone:"https://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url={{URL}}&title={{TITLE}}&desc={{DESCRIPTION}}&summary={{SUMMARY}}&site={{SOURCE}}&pics={{IMAGE}}",qq:'https://connect.qq.com/widget/shareqq/index.html?url={{URL}}&title={{TITLE}}&source={{SOURCE}}&desc={{DESCRIPTION}}&pics={{IMAGE}}&summary="{{SUMMARY}}"',weibo:"https://service.weibo.com/share/share.php?url={{URL}}&title={{TITLE}}&pic={{IMAGE}}&appkey={{WEIBOKEY}}&searchPic=true",wechat:"javascript:",douban:"https://www.douban.com/recommend/?title={{TITLE}}&url={{URL}}",linkedin:"https://www.linkedin.com/shareArticle?mini=true&ro=true&title={{TITLE}}&url={{URL}}&summary={{SUMMARY}}&source={{SOURCE}}&armin=armin",facebook:"https://www.facebook.com/sharer/sharer.php?u={{URL}}",twitter:"https://twitter.com/intent/tweet?text={{TITLE}}&url={{URL}}&via={{ORIGIN}}",x:"https://twitter.com/intent/tweet?text={{TITLE}}&url={{URL}}&via={{ORIGIN}}",mail:"mailto:?subject={{TITLE}}&body={{URL}}",tumblr:"https://www.tumblr.com/share?t={{TITLE}}&u={{URL}}&v=3",whatsapp:"https://web.whatsapp.com/send?text={{URL}}",pinterest:"https://www.pinterest.com/pin/create/button/?description={{TITLE}}&media=&url={{URL}}",line:"https://lineit.line.me/share/ui?url={{URL}}&text={{TITLE}}",telegram:"https://t.me/share/url?url={{URL}}&text={{TITLE}}&to="},makeUrl:function(e,t){t=t||this.defaults;var a=this.subString(t.description,236);return t.description=a&&a!==t.description?a+"...":t.description,t.summary||(t.summary=t.description),this.templates[e].replace(/\{\{(\w)(\w*)\}\}/g,(function(a,i,n){var r=e+i+n.toLowerCase();return n=(i+n).toLowerCase(),encodeURIComponent((void 0===t[r]?t[n]:t[r])||"")}))},init:function(){var e=this;jQuery("a[data-share]").each((function(){var t=jQuery(this),a=t.data("share");if(a&&e.templates[a]){var i=Object.assign({},e.defaults);t.data("share-callback")&&window[t.data("share-callback")]&&(i=Object.assign(i,window[t.data("share-callback")].apply(window,[this]))),t.attr("href",e.makeUrl(a,i)),"wechat"===a&&0===t.find(".share-wx-wrap").length&&(t.attr("target",""),t.append('<span class="share-wx-wrap"><span class="j-share-qrcode"></span><span>\u5fae\u4fe1\u626b\u7801\u5206\u4eab</span></span>'),t.find(".j-share-qrcode").qrcode({text:i.url}))}}))},subString:function(e,t){var a=/[^\x00-\xff]/g;if(e.replace(a,"aa").length<=t)return e;for(var i=Math.floor(t/2),n=e.length;i<n;i++)if(e.substring(0,i).replace(a,"aa").length>=t)return e.substring(0,i);return e}};var w,y,b=new g;
/*!
	 * Lazy Load - JavaScript plugin for lazy loading images
	 *
	 * Copyright (c) 2007-2019 Mika Tuupola
	 *
	 * Licensed under the MIT license:
	 *   http://www.opensource.org/licenses/mit-license.php
	 *
	 * Project home:
	 *   https://appelsiini.net/projects/lazyload
	 *
	 * Version: 2.0.0-rc.2
	 *
	 */
w={exports:{}},y=void 0!==a?a:window||a.global,w.exports=function(e){function t(){try{var e=navigator.userAgent.match(/Firefox\/([0-9]+)\./),t=navigator.userAgent.match(/Chrome\/([0-9]+)\./);return e&&e[1]>=65||t&&t[1]>=32||0===document.createElement("canvas").toDataURL("image/webp").indexOf("data:image/webp")}catch(e){return!1}}var a=t();function i(e,t){return e&&e.split("?").length>1?e.match(/([&?]+)x-oss-process=/i)?e=e.replace(/([&?]+)x-oss-process=/i,"$1x-oss-process=image/format,webp,"):e.match(/([&?]+)imageMogr2/i)?e=e.replace(/([&?]+)imageMogr2\//i,"$1imageMogr2/format/webp/"):e+=t.replace("?","&"):e&&(e+=t),e}function i(e,t){if(!e)return e;var a=["jpg","jpeg","png","gif","bmp","tiff","tif","webp"],i=e.split("?")[0].split(".").pop().toLowerCase();return a.includes(i)?(e.split("?").length>1?e.match(/([&?]+)x-oss-process=/i)?e=e.replace(/([&?]+)x-oss-process=/i,"$1x-oss-process=image/format,webp,"):e.match(/([&?]+)imageMogr2/i)?e=e.replace(/([&?]+)imageMogr2\//i,"$1imageMogr2/format/webp/"):e+=t.replace("?","&"):e+=t,e):e}var n={src:"data-src",srcset:"data-srcset",selector:".j-lazy",root:null,rootMargin:"150px",threshold:0},r=function e(){var t={},a=!1,i=0,n=arguments.length;"[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(a=arguments[0],i++);for(var r=function(i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(a&&"[object Object]"===Object.prototype.toString.call(i[n])?t[n]=e(!0,t[n],i[n]):t[n]=i[n])};i<n;i++)r(arguments[i]);return t};function o(e,t){this.settings=r(n,t||{}),this.images=e||document.querySelectorAll(this.settings.selector),this.observer=null,this.init()}if(o.prototype={init:function(){if(e.IntersectionObserver){var t=this,n={root:this.settings.root,rootMargin:this.settings.rootMargin,threshold:this.settings.threshold};this.observer=new IntersectionObserver((function(e){Array.prototype.forEach.call(e,(function(e){if(e.isIntersecting){t.observer.unobserve(e.target);var n=e.target.getAttribute(t.settings.src),r=e.target.getAttribute(t.settings.srcset);n&&a&&t.settings.webp&&(n=i(n,t.settings.webp)),r&&a&&t.settings.webp&&(r=i(r,t.settings.webp));var o=jQuery(e.target);"img"===e.target.tagName.toLowerCase()?(n&&(e.target.src=n),r&&(e.target.srcset=r),e.target.onerror=function(e){o.trigger("lazy_loaded")}):e.target.style.backgroundImage="url("+n+")",o.one("load",(function(){o.trigger("lazy_loaded")}))}}))}),n),Array.prototype.forEach.call(this.images,(function(e){t.observer.observe(e)}))}else this.loadImages()},loadAndDestroy:function(){this.settings&&(this.loadImages(),this.destroy())},loadImages:function(){if(this.settings){var e=this;Array.prototype.forEach.call(this.images,(function(t){var a=t.getAttribute(e.settings.src),i=t.getAttribute(e.settings.srcset);"img"===t.tagName.toLowerCase()?(a&&(t.src=a),i&&(t.srcset=i)):t.style.backgroundImage="url('"+a+"')"}))}},destroy:function(){this.settings&&(this.observer.disconnect(),this.settings=null)}},e.jQuery){var s=e.jQuery;s.fn.lazyload=function(e){(e=e||{}).attribute=e.attribute||"data-original",e.src=e.attribute||"data-original";var t=s.makeArray(this),a="";if(t&&t.length){var i=jQuery(t[0]);i.is("img")?a=i.attr("src"):(a=window.getComputedStyle(t[0]).getPropertyValue("background-image"))&&(a=a.slice(4,-1).replace(/['"]/g,""))}var n=document.createElement("img");return n.src=a,n.onload=function(){new o(t,e)},n.onerror=function(){new o(t,e)},this}}return o}(y);var x={exports:{}},E={exports:{}};Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(e){if(null==this)throw new TypeError("this is null or not defined");var t=Object(this),a=t.length>>>0;if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var i=arguments[1],n=0;n<a;){var r=t[n];if(e.call(i,r,n,t))return r;n++}}});var T;var _="http://www.w3.org/1999/xhtml",S="undefined"==typeof document?void 0:document,C=!!S&&"content"in S.createElement("template"),k=!!S&&S.createRange&&"createContextualFragment"in S.createRange();function P(e){return e=e.trim(),C?function(e){var t=S.createElement("template");return t.innerHTML=e,t.content.childNodes[0]}(e):k?function(e){return T||(T=S.createRange()).selectNode(S.body),T.createContextualFragment(e).childNodes[0]}(e):function(e){var t=S.createElement("body");return t.innerHTML=e,t.childNodes[0]}(e)}function j(e,t){var a,i,n=e.nodeName,r=t.nodeName;return n===r||(a=n.charCodeAt(0),i=r.charCodeAt(0),a<=90&&i>=97?n===r.toUpperCase():i<=90&&a>=97&&r===n.toUpperCase())}function I(e,t,a){e[a]!==t[a]&&(e[a]=t[a],e[a]?e.setAttribute(a,""):e.removeAttribute(a))}var M={OPTION:function(e,t){var a=e.parentNode;if(a){var i=a.nodeName.toUpperCase();"OPTGROUP"===i&&(i=(a=a.parentNode)&&a.nodeName.toUpperCase()),"SELECT"!==i||a.hasAttribute("multiple")||(e.hasAttribute("selected")&&!t.selected&&(e.setAttribute("selected","selected"),e.removeAttribute("selected")),a.selectedIndex=-1)}I(e,t,"selected")},INPUT:function(e,t){I(e,t,"checked"),I(e,t,"disabled"),e.value!==t.value&&(e.value=t.value),t.hasAttribute("value")||e.removeAttribute("value")},TEXTAREA:function(e,t){var a=t.value;e.value!==a&&(e.value=a);var i=e.firstChild;if(i){var n=i.nodeValue;if(n==a||!a&&n==e.placeholder)return;i.nodeValue=a}},SELECT:function(e,t){if(!t.hasAttribute("multiple")){for(var a,i,n=-1,r=0,o=e.firstChild;o;)if("OPTGROUP"===(i=o.nodeName&&o.nodeName.toUpperCase()))o=(a=o).firstChild;else{if("OPTION"===i){if(o.hasAttribute("selected")){n=r;break}r++}!(o=o.nextSibling)&&a&&(o=a.nextSibling,a=null)}e.selectedIndex=n}}};function O(){}function A(e){if(e)return e.getAttribute&&e.getAttribute("id")||e.id}var L=function(e){return function(t,a,i){if(i||(i={}),"string"==typeof a)if("#document"===t.nodeName||"HTML"===t.nodeName||"BODY"===t.nodeName){var n=a;(a=S.createElement("html")).innerHTML=n}else a=P(a);else 11===a.nodeType&&(a=a.firstElementChild);var r=i.getNodeKey||A,o=i.onBeforeNodeAdded||O,s=i.onNodeAdded||O,l=i.onBeforeElUpdated||O,c=i.onElUpdated||O,d=i.onBeforeNodeDiscarded||O,u=i.onNodeDiscarded||O,p=i.onBeforeElChildrenUpdated||O,h=i.skipFromChildren||O,f=i.addChild||function(e,t){return e.appendChild(t)},m=!0===i.childrenOnly,v=Object.create(null),g=[];function w(e){g.push(e)}function y(e,t){if(1===e.nodeType)for(var a=e.firstChild;a;){var i=void 0;t&&(i=r(a))?w(i):(u(a),a.firstChild&&y(a,t)),a=a.nextSibling}}function b(e,t,a){!1!==d(e)&&(t&&t.removeChild(e),u(e),y(e,a))}function x(e){s(e);for(var t=e.firstChild;t;){var a=t.nextSibling,i=r(t);if(i){var n=v[i];n&&j(t,n)?(t.parentNode.replaceChild(n,t),E(n,t)):x(t)}else x(t);t=a}}function E(t,a,i){var n=r(a);if(n&&delete v[n],!i){if(!1===l(t,a))return;if(e(t,a),c(t),!1===p(t,a))return}"TEXTAREA"!==t.nodeName?function(e,t){var a,i,n,s,l,c=h(e,t),d=t.firstChild,u=e.firstChild;e:for(;d;){for(s=d.nextSibling,a=r(d);!c&&u;){if(n=u.nextSibling,d.isSameNode&&d.isSameNode(u)){d=s,u=n;continue e}i=r(u);var p=u.nodeType,m=void 0;if(p===d.nodeType&&(1===p?(a?a!==i&&((l=v[a])?n===l?m=!1:(e.insertBefore(l,u),i?w(i):b(u,e,!0),i=r(u=l)):m=!1):i&&(m=!1),(m=!1!==m&&j(u,d))&&E(u,d)):3!==p&&8!=p||(m=!0,u.nodeValue!==d.nodeValue&&(u.nodeValue=d.nodeValue))),m){d=s,u=n;continue e}i?w(i):b(u,e,!0),u=n}if(a&&(l=v[a])&&j(l,d))c||f(e,l),E(l,d);else{var g=o(d);!1!==g&&(g&&(d=g),d.actualize&&(d=d.actualize(e.ownerDocument||S)),f(e,d),x(d))}d=s,u=n}!function(e,t,a){for(;t;){var i=t.nextSibling;(a=r(t))?w(a):b(t,e,!0),t=i}}(e,u,i);var y=M[e.nodeName];y&&y(e,t)}(t,a):M.TEXTAREA(t,a)}!function e(t){if(1===t.nodeType||11===t.nodeType)for(var a=t.firstChild;a;){var i=r(a);i&&(v[i]=a),e(a),a=a.nextSibling}}(t);var T,C,k=t,I=k.nodeType,L=a.nodeType;if(!m)if(1===I)1===L?j(t,a)||(u(t),k=function(e,t){for(var a=e.firstChild;a;){var i=a.nextSibling;t.appendChild(a),a=i}return t}(t,(T=a.nodeName,(C=a.namespaceURI)&&C!==_?S.createElementNS(C,T):S.createElement(T)))):k=a;else if(3===I||8===I){if(L===I)return k.nodeValue!==a.nodeValue&&(k.nodeValue=a.nodeValue),k;k=a}if(k===a)u(t);else{if(a.isSameNode&&a.isSameNode(k))return;if(E(k,a,m),g)for(var N=0,D=g.length;N<D;N++){var B=v[g[N]];B&&b(B,B.parentNode,!1)}}return!m&&k!==t&&t.parentNode&&(k.actualize&&(k=k.actualize(t.ownerDocument||S)),t.parentNode.replaceChild(k,t)),k}}((function(e,t){var a,i,n,r,o=t.attributes;if(11!==t.nodeType&&11!==e.nodeType){for(var s=o.length-1;s>=0;s--)i=(a=o[s]).name,n=a.namespaceURI,r=a.value,n?(i=a.localName||i,e.getAttributeNS(n,i)!==r&&("xmlns"===a.prefix&&(i=a.name),e.setAttributeNS(n,i,r))):e.getAttribute(i)!==r&&e.setAttribute(i,r);for(var l=e.attributes,c=l.length-1;c>=0;c--)i=(a=l[c]).name,(n=a.namespaceURI)?(i=a.localName||i,t.hasAttributeNS(n,i)||e.removeAttributeNS(n,i)):t.hasAttribute(i)||e.removeAttribute(i)}})),N=n(Object.freeze({__proto__:null,default:L})),D={};Object.defineProperty(D,"__esModule",{value:!0});var B=D.matches=function(e,t){for(var a=(e.document||e.ownerDocument).querySelectorAll(t),i=a.length;--i>=0&&a.item(i)!==e;);return i>-1};D.selector=function(e){return document.querySelector(e)};var z=D.findAncestor=function(e,t){if("function"==typeof e.closest)return e.closest(t)||null;for(;e&&e!==document;){if(B(e,t))return e;e=e.parentElement}return null},G=[];D.on=function(e,t,a,i){var n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];a.split(" ").forEach((function(a){var r=function(e){var a=z(e.target,t);a&&(e.delegateTarget=a,i(e))};G.push({listener:r,element:e,query:t,event:a,capture:n}),e.addEventListener(a,r,n)}))},D.off=function(e,t,a){a.split(" ").forEach((function(a){G.forEach((function(i,n){i.element===e&&i.query===t&&i.event===a&&(e.removeEventListener(a,i.listener,i.capture),G.splice(n,1))}))}))},function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a,i=function(){function e(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,a,i){return a&&e(t.prototype,a),i&&e(t,i),t}}(),n=(a=N)&&a.__esModule?a:{default:a},r=D;function o(e){if(Array.isArray(e)){for(var t=0,a=Array(e.length);t<e.length;t++)a[t]=e[t];return a}return Array.from(e)}var s="input paste copy click change keydown keyup keypress contextmenu mouseup mousedown mousemove touchstart touchend touchmove compositionstart compositionend focus",l="input change click",c=s.replace(/([a-z]+)/g,"[data-action-$1],")+"[data-action]",d=function(){function e(t){var a=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.atemplate=[],this.events=[],t&&Object.keys(t).forEach((function(e){a[e]=t[e]})),this.data||(this.data={}),this.templates||(this.templates=[]);for(var i=0,n=this.templates.length;i<n;i+=1){var o=this.templates[i],s=(0,r.selector)("#"+o).innerHTML;this.atemplate.push({id:o,html:s,binded:!1})}}return i(e,[{key:"addDataBind",value:function(e){var t=this;(0,r.on)(e,"[data-bind]",l,(function(e){var a,i,n=e.delegateTarget,r=n.getAttribute("data-bind"),o=n.getAttribute("href"),s=n.value;o&&(s=s.replace("#","")),"checkbox"===n.getAttribute("type")?(a=[],i=document.querySelectorAll('[data-bind="'+r+'"]'),[].forEach.call(i,(function(e){e.checked&&a.push(e.value)}))):"radio"!==n.getAttribute("type")&&t.updateDataByString(r,s)})),this.events.push({element:e,selector:"[data-bind]",event:l})}},{key:"addActionBind",value:function(e){var t=this;(0,r.on)(e,c,s,(function(e){var a=e.delegateTarget,i=s.split(" "),n="action";i.forEach((function(t){a.getAttribute("data-action-"+t)&&e.type===t&&(n+="-"+t)}));var r=a.getAttribute("data-"+n);if(r){var l,c=r.replace(/\(.*?\);?/,""),d=r.replace(/(.*?)\((.*?)\);?/,"$2").split(",");if(t.e=e,t.method&&t.method[c])(l=t.method)[c].apply(l,o(d));else t[c]&&t[c].apply(t,o(d))}})),this.events.push({element:e,selector:c,event:l})}},{key:"removeTemplateEvents",value:function(){this.events.forEach((function(e){(0,r.off)(e.element,e.selector,e.event)}))}},{key:"addTemplate",value:function(e,t){this.atemplate.push({id:e,html:t,binded:!1}),this.templates.push(e)}},{key:"getData",value:function(){return JSON.parse(JSON.stringify(this.data))}},{key:"saveData",value:function(e){var t=JSON.stringify(this.data);localStorage.setItem(e,t)}},{key:"setData",value:function(e){var t=this;Object.keys(e).forEach((function(a){"function"!=typeof e[a]&&(t.data[a]=e[a])}))}},{key:"loadData",value:function(e){var t=JSON.parse(localStorage.getItem(e));t&&this.setData(t)}},{key:"getRand",value:function(e,t){return~~(Math.random()*(t-e+1))+e}},{key:"getRandText",value:function(e){for(var t="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i=0;i<e;i+=1)t+=a.charAt(Math.floor(this.getRand(0,62)));return t}},{key:"getDataFromObj",value:function(e,t){for(var a=(e=(e=e.replace(/\[([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+)\]/g,".$1")).replace(/^\./,"")).split(".");a.length;){var i=a.shift();if(!(i in t))return null;t=t[i]}return t}},{key:"getDataByString",value:function(e){var t=this.data;return this.getDataFromObj(e,t)}},{key:"updateDataByString",value:function(e,t){for(var a=this.data,i=e.split(".");i.length>1;)a=a[i.shift()];a[i.shift()]=t}},{key:"removeDataByString",value:function(e){for(var t=this.data,a=e.split(".");a.length>1;)t=t[a.shift()];var i=a.shift();i.match(/^\d+$/)?t.splice(Number(i),1):delete t[i]}},{key:"resolveBlock",value:function(e,t,a){var i=this,n=e.match(/<!-- BEGIN ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):touch#([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+) -->/g),r=e.match(/<!-- BEGIN ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):touchnot#([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+) -->/g),o=e.match(/<!-- BEGIN ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):exist -->/g),s=e.match(/<!-- BEGIN ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):empty -->/g);if(n)for(var l=0,c=n.length;l<c;l+=1){var d=n[l],u=(d=d.replace(/([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):touch#([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+)/,"($1):touch#($2)")).replace(/BEGIN/,"END"),p=new RegExp(d+"(([\\n\\r\\t]|.)*?)"+u,"g");e=e.replace(p,(function(e,a,n,r){return""+("function"==typeof t[a]?t[a].apply(i):i.getDataFromObj(a,t))===n?r:""}))}if(r)for(var h=0,f=r.length;h<f;h+=1){var m=r[h],v=(m=m.replace(/([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):touchnot#([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+)/,"($1):touchnot#($2)")).replace(/BEGIN/,"END"),g=new RegExp(m+"(([\\n\\r\\t]|.)*?)"+v,"g");e=e.replace(g,(function(e,a,n,r){return""+("function"==typeof t[a]?t[a].apply(i):i.getDataFromObj(a,t))!==n?r:""}))}if(o)for(var w=0,y=o.length;w<y;w+=1){var b=o[w],x=(b=b.replace(/([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):exist/,"($1):exist")).replace(/BEGIN/,"END"),E=new RegExp(b+"(([\\n\\r\\t]|.)*?)"+x,"g");e=e.replace(E,(function(e,a,n){var r="function"==typeof t[a]?t[a].apply(i):i.getDataFromObj(a,t);return r||0===r?n:""}))}if(s)for(var T=0,_=s.length;T<_;T+=1){var S=s[T],C=(S=S.replace(/([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):empty/,"($1):empty")).replace(/BEGIN/,"END"),k=new RegExp(S+"(([\\n\\r\\t]|.)*?)"+C,"g");e=e.replace(k,(function(e,a,n){var r="function"==typeof t[a]?t[a].apply(i):i.getDataFromObj(a,t);return r||0===r?"":n}))}return e=e.replace(/{([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+)}(\[([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+)\])*/g,(function(e,n,r,o){var s=void 0;if(""+n=="i")s=a;else{if(!t[n]&&0!==t[n])return o&&i.convert&&i.convert[o]?i.convert[o].call(i,""):"";s="function"==typeof t[n]?t[n].apply(i):t[n]}return o&&i.convert&&i.convert[o]?i.convert[o].call(i,s):s}))}},{key:"resolveAbsBlock",value:function(e){var t=this;return e=e.replace(/{(.*?)}/g,(function(e,a){var i=t.getDataByString(a);return void 0!==i?"function"==typeof i?i.apply(t):i:e}))}},{key:"resolveInclude",value:function(e){return e=e.replace(/<!-- #include id="(.*?)" -->/g,(function(e,t){return(0,r.selector)("#"+t).innerHTML}))}},{key:"resolveWith",value:function(e){return e=e.replace(/<!-- BEGIN ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):with -->(([\n\r\t]|.)*?)<!-- END ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+):with -->/g,(function(e,t){return e=e.replace(/data\-bind=['"](.*?)['"]/g,"data-bind='"+t+".$1'")}))}},{key:"resolveLoop",value:function(e){var t=this;return e=e.replace(/<!-- BEGIN ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+?):loop -->(([\n\r\t]|.)*?)<!-- END ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+?):loop -->/g,(function(e,a,i){var n=t.getDataByString(a),r=[],o="";if((r="function"==typeof n?n.apply(t):n)instanceof Array)for(var s=0,l=r.length;s<l;s+=1)o+=t.resolveBlock(i,r[s],s);return o=o.replace(/\\([^\\])/g,"$1")}))}},{key:"removeData",value:function(e){var t=this.data;return Object.keys(t).forEach((function(a){for(var i=0,n=e.length;i<n;i+=1)a===e[i]&&delete t[a]})),this}},{key:"hasLoop",value:function(e){return!!e.match(/<!-- BEGIN ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+?):loop -->(([\n\r\t]|.)*?)<!-- END ([\w\-\.\u3041-\u3093\u30a1-\u30f6\u4e9c-\u7199]+?):loop -->/g)}},{key:"getHtml",value:function(e,t){var a=this.atemplate.find((function(t){return t.id===e})),i="";if(a&&a.html&&(i=a.html),t&&(i=e),!i)return"";var n=this.data;for(i=this.resolveInclude(i),i=this.resolveWith(i);this.hasLoop(i);)i=this.resolveLoop(i);return i=(i=this.resolveBlock(i,n)).replace(/\\([^\\])/g,"$1"),(i=this.resolveAbsBlock(i)).replace(/^([\t ])*\n/gm,"")}},{key:"update",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"html",a=arguments[1],i=this.templates;this.beforeUpdated&&this.beforeUpdated();for(var o=function(o,s){var l=i[o],c="#"+l,d=e.getHtml(l),u=(0,r.selector)("[data-id='"+l+"']");if(u)if("text"===t)u.innerText=d;else if(a){var p=document.createElement("div");p.innerHTML=d;var h=p.querySelector(a).outerHTML;(0,n.default)(u.querySelector(a),h)}else(0,n.default)(u,"<div data-id='"+l+"'>"+d+"</div>");else(0,r.selector)(c).insertAdjacentHTML("afterend",'<div data-id="'+l+'"></div>'),"text"===t?(0,r.selector)("[data-id='"+l+"']").innerText=d:(0,r.selector)("[data-id='"+l+"']").innerHTML=d;var f=e.atemplate.find((function(e){return e.id===l}));f.binded||(f.binded=!0,e.addDataBind((0,r.selector)("[data-id='"+l+"']")),e.addActionBind((0,r.selector)("[data-id='"+l+"']")))},s=0,l=i.length;s<l;s+=1)o(s);return this.updateBindingData(a),this.onUpdated&&this.onUpdated(a),this}},{key:"updateBindingData",value:function(e){for(var t=this,a=this.templates,i=0,n=a.length;i<n;i+=1){var o=a[i],s=(0,r.selector)("[data-id='"+o+"']");e&&(s=s.querySelector(e));var l=s.querySelectorAll("[data-bind]");[].forEach.call(l,(function(e){var a=t.getDataByString(e.getAttribute("data-bind"));"checkbox"===e.getAttribute("type")||"radio"===e.getAttribute("type")?a===e.value&&(e.checked=!0):e.value=a}));var c=s.querySelectorAll("[data-bind-oneway]");[].forEach.call(c,(function(e){var a=t.getDataByString(e.getAttribute("data-bind-oneway"));"checkbox"===e.getAttribute("type")||"radio"===e.getAttribute("type")?a===e.value&&(e.checked=!0):e.value=a}))}return this}},{key:"applyMethod",value:function(e){for(var t,a=arguments.length,i=Array(a>1?a-1:0),n=1;n<a;n++)i[n-1]=arguments[n];return(t=this.method)[e].apply(t,i)}},{key:"getComputedProp",value:function(e){return this.data[e].apply(this)}},{key:"remove",value:function(e){for(var t=this.data,a=e.split(".");a.length>1;)t=t[a.shift()];var i=a.shift();return i.match(/^\d+$/)?t.splice(Number(i),1):delete t[i],this}}]),e}();t.default=d,e.exports=t.default}(E,E.exports);var R=E.exports;try{var q=new window.CustomEvent("test");if(q.preventDefault(),!0!==q.defaultPrevented)throw new Error("Could not prevent default")}catch(e){var H=function(e,t){var a,i;return t=t||{bubbles:!1,cancelable:!1,detail:void 0},(a=document.createEvent("CustomEvent")).initCustomEvent(e,t.bubbles,t.cancelable,t.detail),i=a.preventDefault,a.preventDefault=function(){i.call(this);try{Object.defineProperty(this,"defaultPrevented",{get:function(){return!0}})}catch(e){this.defaultPrevented=!0}},a};H.prototype=window.Event.prototype,window.CustomEvent=H}var $={};function F(e){return F="function"==typeof Symbol&&"symbol"===m(Symbol.iterator)?function(e){return m(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":m(e)},F(e)}Object.defineProperty($,"__esModule",{value:!0}),$.isOldIE=$.getBrowser=$.removeClass=$.addClass=$.append=$.removeElement=$.getViewPos=$.parseQuery=$.triggerEvent=$.extend=$.isSmartPhone=void 0;$.isSmartPhone=function(){var e=navigator.userAgent;return e.indexOf("iPhone")>0||e.indexOf("iPad")>0||e.indexOf("ipod")>0||e.indexOf("Android")>0};var V=function e(t){t=t||{};for(var a=1;a<arguments.length;a++){var i=arguments[a];if(i)for(var n in i)i.hasOwnProperty(n)&&("object"===F(i[n])?t[n]=e(t[n],i[n]):t[n]=i[n])}return t};$.extend=V;$.triggerEvent=function(e,t,a){var i;window.CustomEvent?i=new CustomEvent(t,{cancelable:!0}):(i=document.createEvent("CustomEvent")).initCustomEvent(t,!1,!1,a),e.dispatchEvent(i)};$.parseQuery=function(e){for(var t,a,i,n=e.split("&"),r={},o=0,s=n.length;o<s;o++)void 0!==(t=n[o].split("="))[0]&&(a=t[0],i=void 0!==t[1]?t.slice(1).join("="):a,r[a]=decodeURIComponent(i));return r};$.getViewPos=function(e){return{left:e.getBoundingClientRect().left,top:e.getBoundingClientRect().top}};$.removeElement=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)};$.append=function(e,t){var a=document.createElement("div");for(a.innerHTML=t;a.children.length>0;)e.appendChild(a.children[0])};$.addClass=function(e,t){e.classList?e.classList.add(t):e.className+=" ".concat(t)};$.removeClass=function(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp("(^|\\b)"+t.split(" ").join("|")+"(\\b|$)","gi")," ")};var U=function(){var e=window.navigator.userAgent.toLowerCase(),t=window.navigator.appVersion.toLowerCase(),a="unknown";return-1!=e.indexOf("msie")?a=-1!=t.indexOf("msie 6.")?"ie6":-1!=t.indexOf("msie 7.")?"ie7":-1!=t.indexOf("msie 8.")?"ie8":-1!=t.indexOf("msie 9.")?"ie9":-1!=t.indexOf("msie 10.")?"ie10":"ie":-1!=e.indexOf("trident/7")?a="ie11":-1!=e.indexOf("chrome")?a="chrome":-1!=e.indexOf("safari")?a="safari":-1!=e.indexOf("opera")?a="opera":-1!=e.indexOf("firefox")&&(a="firefox"),a};$.getBrowser=U;$.isOldIE=function(){var e=U();return-1!==e.indexOf("ie")&&parseInt(e.replace(/[^0-9]/g,""))<=10};var W,Q={};W=Q,function(e){var t=e.Promise,a=t&&"resolve"in t&&"reject"in t&&"all"in t&&"race"in t&&function(){var e;return new t((function(t){e=t})),"function"==typeof e}();W?(W.Promise=a?t:T,W.Polyfill=T):a||(e.Promise=T);var i="pending",n="sealed",r="fulfilled",o="rejected",s=function(){};function l(e){return"[object Array]"===Object.prototype.toString.call(e)}var c,d="undefined"!=typeof setImmediate?setImmediate:setTimeout,u=[];function p(){for(var e=0;e<u.length;e++)u[e][0](u[e][1]);u=[],c=!1}function h(e,t){u.push([e,t]),c||(c=!0,d(p,0))}function f(e){var t=e.owner,a=t.state_,i=t.data_,n=e[a],s=e.then;if("function"==typeof n){a=r;try{i=n(i)}catch(e){y(s,e)}}v(s,i)||(a===r&&g(s,i),a===o&&y(s,i))}function v(e,t){var a;try{if(e===t)throw new TypeError("A promises callback cannot return that same promise.");if(t&&("function"==typeof t||"object"===m(t))){var i=t.then;if("function"==typeof i)return i.call(t,(function(i){a||(a=!0,t!==i?g(e,i):w(e,i))}),(function(t){a||(a=!0,y(e,t))})),!0}}catch(t){return a||y(e,t),!0}return!1}function g(e,t){e!==t&&v(e,t)||w(e,t)}function w(e,t){e.state_===i&&(e.state_=n,e.data_=t,h(x,e))}function y(e,t){e.state_===i&&(e.state_=n,e.data_=t,h(E,e))}function b(e){var t=e.then_;e.then_=void 0;for(var a=0;a<t.length;a++)f(t[a])}function x(e){e.state_=r,b(e)}function E(e){e.state_=o,b(e)}function T(e){if("function"!=typeof e)throw new TypeError("Promise constructor takes a function argument");if(this instanceof T==0)throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this.then_=[],function(e,t){function a(e){y(t,e)}try{e((function(e){g(t,e)}),a)}catch(e){a(e)}}(e,this)}T.prototype={constructor:T,state_:i,then_:null,data_:void 0,then:function(e,t){var a={owner:this,then:new this.constructor(s),fulfilled:e,rejected:t};return this.state_===r||this.state_===o?h(f,a):this.then_.push(a),a.then},catch:function(e){return this.then(null,e)}},T.all=function(e){if(!l(e))throw new TypeError("You must pass an array to Promise.all().");return new this((function(t,a){var i=[],n=0;function r(e){return n++,function(a){i[e]=a,--n||t(i)}}for(var o,s=0;s<e.length;s++)(o=e[s])&&"function"==typeof o.then?o.then(r(s),a):i[s]=o;n||t(i)}))},T.race=function(e){if(!l(e))throw new TypeError("You must pass an array to Promise.race().");return new this((function(t,a){for(var i,n=0;n<e.length;n++)(i=e[n])&&"function"==typeof i.then?i.then(t,a):t(i)}))},T.resolve=function(e){return e&&"object"===m(e)&&e.constructor===this?e:new this((function(t){t(e)}))},T.reject=function(e){return new this((function(t,a){a(e)}))}}("undefined"!=typeof window?window:void 0!==a?a:"undefined"!=typeof self?self:a),function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a,i=(a=R)&&a.__esModule?a:{default:a};function n(e){return n="function"==typeof Symbol&&"symbol"===m(Symbol.iterator)?function(e){return m(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":m(e)},n(e)}function r(e,t,a){return t&&function(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e.prototype,t),e}function o(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function s(e){return s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},s(e)}function l(e,t){return l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},l(e,t)}var c=$,d=Q.Promise,u={classNames:{smartPhoto:"smartphoto",smartPhotoClose:"smartphoto-close",smartPhotoBody:"smartphoto-body",smartPhotoInner:"smartphoto-inner",smartPhotoContent:"smartphoto-content",smartPhotoImg:"smartphoto-img",smartPhotoImgOnMove:"smartphoto-img-onmove",smartPhotoImgElasticMove:"smartphoto-img-elasticmove",smartPhotoImgWrap:"smartphoto-img-wrap",smartPhotoArrows:"smartphoto-arrows",smartPhotoNav:"smartphoto-nav",smartPhotoArrowRight:"smartphoto-arrow-right",smartPhotoArrowLeft:"smartphoto-arrow-left",smartPhotoArrowHideIcon:"smartphoto-arrow-hide",smartPhotoImgLeft:"smartphoto-img-left",smartPhotoImgRight:"smartphoto-img-right",smartPhotoList:"smartphoto-list",smartPhotoListOnMove:"smartphoto-list-onmove",smartPhotoHeader:"smartphoto-header",smartPhotoCount:"smartphoto-count",smartPhotoCaption:"smartphoto-caption",smartPhotoDismiss:"smartphoto-dismiss",smartPhotoLoader:"smartphoto-loader",smartPhotoLoaderWrap:"smartphoto-loader-wrap",smartPhotoImgClone:"smartphoto-img-clone"},message:{gotoNextImage:"go to the next image",gotoPrevImage:"go to the previous image",closeDialog:"close the image dialog"},arrows:!0,nav:!0,showAnimation:!0,verticalGravity:!1,useOrientationApi:!1,useHistoryApi:!0,swipeTopToClose:!1,swipeBottomToClose:!0,swipeOffset:100,headerHeight:60,footerHeight:60,forceInterval:10,registance:.5,loadOffset:2,resizeStyle:"fit",lazyAttribute:"data-src"},p=function(e){function t(e,a){var i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(i=o(this,s(t).call(this))).data=c.extend({},u,a),i.data.currentIndex=0,i.data.oldIndex=0,i.data.hide=!0,i.data.group={},i.data.scaleSize=1,i.data.scale=!1,i.pos={x:0,y:0},i.data.photoPosX=0,i.data.photoPosY=0,i.handlers=[],i.convert={increment:i.increment,virtualPos:i.virtualPos,round:i.round},i.data.groupItems=i.groupItems,i.elements="string"==typeof e?document.querySelectorAll(e):e;var n=new Date;i.tapSecond=n.getTime(),i.onListMove=!1,i.clicked=!1,i.id=i._getUniqId(),i.vx=0,i.vy=0,i.data.appearEffect=null,i.addTemplate(i.id,'<div class="\\{classNames.smartPhoto\\}"\x3c!-- BEGIN hide:exist --\x3e aria-hidden="true"\x3c!-- END hide:exist --\x3e\x3c!-- BEGIN hide:empty --\x3e aria-hidden="false"\x3c!-- END hide:empty --\x3e role="dialog">\n\t<div class="\\{classNames.smartPhotoBody\\}">\n\t\t<div class="\\{classNames.smartPhotoInner\\}">\n\t\t\t   <div class="\\{classNames.smartPhotoHeader\\}">\n\t\t\t\t\t<span class="\\{classNames.smartPhotoCount\\}">{currentIndex}[increment]/{total}</span>\n\t\t\t\t\t<span class="\\{classNames.smartPhotoCaption\\}" aria-live="polite" tabindex="-1">\x3c!-- BEGIN groupItems:loop --\x3e\x3c!-- \\BEGIN currentIndex:touch#{index} --\x3e{caption}\x3c!-- \\END currentIndex:touch#{index} --\x3e\x3c!-- END groupItems:loop --\x3e</span>\n\t\t\t\t\t<button class="\\{classNames.smartPhotoDismiss\\}" data-action-click="hidePhoto()"><span class="smartphoto-sr-only">\\{message.closeDialog\\}</span></button>\n\t\t\t\t</div>\n\t\t\t\t<div class="\\{classNames.smartPhotoContent\\}"\x3c!-- BEGIN isSmartPhone:exist --\x3e data-action-touchstart="beforeDrag" data-action-touchmove="onDrag" data-action-touchend="afterDrag(false)"\x3c!-- END isSmartPhone:exist --\x3e\x3c!-- BEGIN isSmartPhone:empty --\x3e data-action-click="hidePhoto()"\x3c!-- END isSmartPhone:empty --\x3e>\n\t\t\t\t</div>\n\t\t\t\t<ul style="transform:translate({translateX}[round]px,{translateY}[round]px);" class="\\{classNames.smartPhotoList\\}\x3c!-- BEGIN onMoveClass:exist --\x3e \\{classNames.smartPhotoListOnMove\\}\x3c!-- END onMoveClass:exist --\x3e">\n\t\t\t\t\t\x3c!-- BEGIN groupItems:loop --\x3e\n\t\t\t\t\t<li style="transform:translate({translateX}[round]px,{translateY}[round]px);" class="\x3c!-- \\BEGIN currentIndex:touch#{index} --\x3ecurrent\x3c!-- \\END currentIndex:touch#{index} --\x3e">\n\t\t\t\t\t\t\x3c!-- BEGIN processed:exist --\x3e\n\t\t\t\t\t\t<div style="transform:translate({x}[round]px,{y}[round]px) scale({scale});" class="\\\\{classNames.smartPhotoImgWrap\\\\}"\x3c!-- \\BEGIN isSmartPhone:empty --\x3e data-action-mousemove="onDrag" data-action-mousedown="beforeDrag" data-action-mouseup="afterDrag"\x3c!-- \\END isSmartPhone:empty --\x3e\x3c!-- \\BEGIN isSmartPhone:exist --\x3e data-action-touchstart="beforeDrag" data-action-touchmove="onDrag" data-action-touchend="afterDrag"\x3c!-- \\END isSmartPhone:exist --\x3e>\n\t\t\t\t\t\t\t<img style="\x3c!-- \\BEGIN currentIndex:touch#{index} --\x3etransform:translate(\\{photoPosX\\}[virtualPos]px,\\{photoPosY\\}[virtualPos]px) scale(\\{scaleSize\\});\x3c!-- \\END currentIndex:touch#{index} --\x3ewidth:{width}px;" src="{src}" class="\\\\{classNames.smartPhotoImg\\\\}\x3c!-- \\BEGIN scale:exist --\x3e  \\\\{classNames.smartPhotoImgOnMove\\\\}\x3c!-- \\END scale:exist --\x3e\x3c!-- \\BEGIN elastic:exist --\x3e \\\\{classNames.smartPhotoImgElasticMove\\\\}\x3c!-- \\END elastic:exist --\x3e\x3c!-- \\BEGIN appear:exist --\x3e active\x3c!-- \\END appear:exist --\x3e" ondragstart="return false;">\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\x3c!-- END processed:exist --\x3e\n\t\t\t\t\t\t\x3c!-- BEGIN processed:empty --\x3e\n\t\t\t\t\t\t<div class="\\\\{classNames.smartPhotoLoaderWrap\\\\}">\n\t\t\t\t\t\t\t<span class="\\\\{classNames.smartPhotoLoader\\\\}"></span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\x3c!-- END processed:empty --\x3e\n\t\t\t\t\t</li>\n\t\t\t\t\t\x3c!-- END groupItems:loop --\x3e\n\t\t\t\t</ul>\n\t\t\t\t\x3c!-- BEGIN arrows:exist --\x3e\n\t\t\t\t<ul class="\\{classNames.smartPhotoArrows\\}"\x3c!-- BEGIN hideUi:exist --\x3e aria-hidden="true"\x3c!-- END hideUi:exist --\x3e\x3c!-- BEGIN hideUi:exist --\x3e aria-hidden="false"\x3c!-- END hideUi:exist --\x3e>\n\t\t\t\t\t<li class="\\{classNames.smartPhotoArrowLeft\\}\x3c!-- BEGIN isSmartPhone:exist --\x3e \\{classNames.smartPhotoArrowHideIcon\\}\x3c!-- END isSmartPhone:exist --\x3e"\x3c!-- BEGIN showPrevArrow:empty --\x3e aria-hidden="true"\x3c!-- END showPrevArrow:empty --\x3e><a href="#" data-action-click="gotoSlide({prev})" role="button"><span class="smartphoto-sr-only">\\{message.gotoPrevImage\\}</span></a></li>\n\t\t\t\t\t<li class="\\{classNames.smartPhotoArrowRight\\}\x3c!-- BEGIN isSmartPhone:exist --\x3e \\{classNames.smartPhotoArrowHideIcon\\}\x3c!-- END isSmartPhone:exist --\x3e"\x3c!-- BEGIN showNextArrow:empty --\x3e aria-hidden="true"\x3c!-- END showNextArrow:empty --\x3e><a href="#" data-action-click="gotoSlide({next})" role="button"><span class="smartphoto-sr-only">\\{message.gotoNextImage\\}</span></a></li>\n\t\t\t\t</ul>\n\t\t\t\t\x3c!-- END arrows:exist --\x3e\n\t\t\t\t\x3c!-- BEGIN nav:exist --\x3e\n\t\t\t\t<nav class="\\{classNames.smartPhotoNav\\}"\x3c!-- BEGIN hideUi:exist --\x3e aria-hidden="true"\x3c!-- END hideUi:exist --\x3e\x3c!-- BEGIN hideUi:exist --\x3e aria-hidden="false"\x3c!-- END hideUi:exist --\x3e>\n\t\t\t\t\t<ul>\n\t\t\t\t\t\t\x3c!-- BEGIN groupItems:loop --\x3e\n\t\t\t\t\t\t<li><a href="#" data-action-click="gotoSlide({index})" class="\x3c!-- \\BEGIN currentIndex:touch#{index} --\x3ecurrent\x3c!-- \\END currentIndex:touch#{index} --\x3e" style="background-image:url(\'{thumb}\');" role="button"><span class="smartphoto-sr-only">go to {caption}</span></a></li>\n\t\t\t\t\t\t\x3c!-- END groupItems:loop --\x3e\n\t\t\t\t\t</ul>\n\t\t\t\t</nav>\n\t\t\t\t\x3c!-- END nav:exist --\x3e\n\t\t</div>\n\t\t\x3c!-- BEGIN appearEffect:exist --\x3e\n\t\t<img src=\\{appearEffect.img\\}\n\t\tclass="\\{classNames.smartPhotoImgClone\\}"\n\t\tstyle="width:\\{appearEffect.width\\}px;height:\\{appearEffect.height\\}px;transform:translate(\\{appearEffect.left\\}px,\\{appearEffect.top\\}px) scale(1)" />\n\t\t\x3c!-- END appearEffect:exist --\x3e\n\t</div>\n</div>\n'),i.data.isSmartPhone=i._isSmartPhone();var r=document.querySelector("body");c.append(r,"<div data-id='".concat(i.id,"'></div>")),[].forEach.call(i.elements,(function(e){i.addNewItem(e)})),i.update();var l=i._getCurrentItemByHash();if(l&&c.triggerEvent(l.element,"click"),i.interval=setInterval((function(){i._doAnim()}),i.data.forceInterval),!i.data.isSmartPhone){var p=function(){i.groupItems()&&(i._resetTranslate(),i._setPosByCurrentIndex(),i._setSizeByScreen(),i.update())},h=function(e){var t=e.keyCode||e.which;!0!==i.data.hide&&(37===t?i.gotoSlide(i.data.prev):39===t?i.gotoSlide(i.data.next):27===t&&i.hidePhoto())};return window.addEventListener("resize",p),window.addEventListener("keydown",h),i._registerRemoveEvent(window,"resize",p),i._registerRemoveEvent(window,"keydown",h),o(i)}var f=function(){if(i.groupItems()){i._resetTranslate(),i._setPosByCurrentIndex(),i._setHashByCurrentIndex(),i._setSizeByScreen(),i.update();var e=i._getWindowWidth();!function t(a){new d((function(e){setTimeout((function(){e()}),25)})).then((function(){e!==i._getWindowWidth()?(i._resetTranslate(),i._setPosByCurrentIndex(),i._setHashByCurrentIndex(),i._setSizeByScreen(),i.update()):a<=500&&t(a+25)}))}(0)}};if(window.addEventListener("orientationchange",f),i._registerRemoveEvent(window,"orientationchange",f),!i.data.useOrientationApi)return o(i);var m=function(e){var t=window.orientation;e&&e.gamma&&!i.data.appearEffect&&(i.isBeingZoomed||i.photoSwipable||i.data.elastic||!i.data.scale||(0===t?i._calcGravity(e.gamma,e.beta):90===t?i._calcGravity(e.beta,e.gamma):-90===t?i._calcGravity(-e.beta,-e.gamma):180===t&&i._calcGravity(-e.gamma,-e.beta)))};return window.addEventListener("deviceorientation",m),i._registerRemoveEvent(window,"deviceorientation",m),i}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&l(e,t)}(t,e),r(t,[{key:"on",value:function(e,t){var a=this._getElementByClass(this.data.classNames.smartPhoto),i=function(e){t.call(a,e)};a.addEventListener(e,i),this._registerRemoveEvent(a,e,i)}},{key:"_registerRemoveEvent",value:function(e,t,a){this.handlers.push({target:e,event:t,handler:a})}},{key:"destroy",value:function(){this.handlers.forEach((function(e){e.target.removeEventListener(e.event,e.handler)}));var e=document.querySelector('[data-id="'.concat(this.id,'"]'));c.removeElement(e),clearInterval(this.interval),this.removeTemplateEvents()}},{key:"increment",value:function(e){return e+1}},{key:"round",value:function(e){return Math.round(e)}},{key:"virtualPos",value:function(e){return(e=parseInt(e,10))/this._getSelectedItem().scale/this.data.scaleSize}},{key:"groupItems",value:function(){return this.data.group[this.data.currentGroup]}},{key:"_resetTranslate",value:function(){var e=this;this.groupItems().forEach((function(t,a){t.translateX=e._getWindowWidth()*a}))}},{key:"addNewItem",value:function(e){var t=this,a=e.getAttribute("data-group")||"nogroup",i=this.data.group;"nogroup"===a&&e.setAttribute("data-group","nogroup"),i[a]||(i[a]=[]);var n=i[a].length,r=document.querySelector("body"),o=e.getAttribute("href"),s=e.querySelector("img"),l=o;s&&(l=s.getAttribute(this.data.lazyAttribute)?s.getAttribute(this.data.lazyAttribute):s.currentSrc?s.currentSrc:s.src);var c={src:o,thumb:l,caption:e.getAttribute("data-caption"),groupId:a,translateX:this._getWindowWidth()*n,index:n,translateY:0,width:50,height:50,id:e.getAttribute("data-id")||n,loaded:!1,processed:!1,element:e};i[a].push(c),this.data.currentGroup=a,e.getAttribute("data-id")||e.setAttribute("data-id",n),e.setAttribute("data-index",n);var d=function(a){a.preventDefault(),t.data.currentGroup=e.getAttribute("data-group"),t.data.currentIndex=parseInt(e.getAttribute("data-index"),10),t._setHashByCurrentIndex();var i=t._getSelectedItem();i.loaded?(t._initPhoto(),t.addAppearEffect(e,i),t.clicked=!0,t.update(),r.style.overflow="hidden",t._fireEvent("open")):t._loadItem(i).then((function(){t._initPhoto(),t.addAppearEffect(e,i),t.clicked=!0,t.update(),r.style.overflow="hidden",t._fireEvent("open")}))};e.addEventListener("click",d),this._registerRemoveEvent(e,"click",d)}},{key:"_initPhoto",value:function(){this.data.total=this.groupItems().length,this.data.hide=!1,this.data.photoPosX=0,this.data.photoPosY=0,this._setPosByCurrentIndex(),this._setSizeByScreen(),this.setArrow(),"fill"===this.data.resizeStyle&&this.data.isSmartPhone&&(this.data.scale=!0,this.data.hideUi=!0,this.data.scaleSize=this._getScaleBoarder())}},{key:"onUpdated",value:function(){var e=this;if(this.data.appearEffect&&this.data.appearEffect.once&&(this.data.appearEffect.once=!1,this.execEffect().then((function(){e.data.appearEffect=null,e.data.appear=!0,e.update()}))),this.clicked){this.clicked=!1;var t=this.data.classNames;this._getElementByClass(t.smartPhotoCaption).focus()}}},{key:"execEffect",value:function(){var e=this;return new d((function(t){c.isOldIE()&&t();var a=e.data,i=a.appearEffect,n=a.classNames,r=e._getElementByClass(n.smartPhotoImgClone);r.addEventListener("transitionend",(function e(){r.removeEventListener("transitionend",e,!0),t()}),!0),setTimeout((function(){r.style.transform="translate(".concat(i.afterX,"px, ").concat(i.afterY,"px) scale(").concat(i.scale,")")}),10)}))}},{key:"addAppearEffect",value:function(e,t){if(!1!==this.data.showAnimation){var a=e.querySelector("img"),i=c.getViewPos(a),n={},r=1;n.width=a.offsetWidth,n.height=a.offsetHeight,n.top=i.top,n.left=i.left,n.once=!0,a.getAttribute(this.data.lazyAttribute)?n.img=a.getAttribute(this.data.lazyAttribute):n.img=t.src;var o=this._getWindowWidth(),s=this._getWindowHeight(),l=s-this.data.headerHeight-this.data.footerHeight;"fill"===this.data.resizeStyle&&this.data.isSmartPhone?r=a.offsetWidth>a.offsetHeight?s/a.offsetHeight:o/a.offsetWidth:(n.width>=n.height?r=t.height<l?t.width/n.width:l/n.height:n.height>n.width&&(r=t.height<l?t.height/n.height:l/n.height),n.width*r>o&&(r=o/n.width));var d=(r-1)/2*a.offsetWidth+(o-a.offsetWidth*r)/2,u=(r-1)/2*a.offsetHeight+(s-a.offsetHeight*r)/2;n.afterX=d,n.afterY=u,n.scale=r,this.data.appearEffect=n}else this.data.appear=!0}},{key:"hidePhoto",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"bottom";this.data.hide=!0,this.data.appear=!1,this.data.appearEffect=null,this.data.hideUi=!1,this.data.scale=!1,this.data.scaleSize=1;var a=void 0!==window.pageXOffset?window.pageXOffset:(document.documentElement||document.body.parentNode||document.body).scrollLeft,i=void 0!==window.pageYOffset?window.pageYOffset:(document.documentElement||document.body.parentNode||document.body).scrollTop,n=document.querySelector("body");window.location.hash&&this._setHash(""),window.scroll(a,i),this._doHideEffect(t).then((function(){e.update(),n.style.overflow="",e._fireEvent("close")}))}},{key:"_doHideEffect",value:function(e){var t=this;return new d((function(a){c.isOldIE()&&a();var i=t.data.classNames,n=t._getElementByClass(i.smartPhoto),r=t._getElementByQuery(".current .".concat(i.smartPhotoImg)),o=t._getWindowHeight();n.style.opacity=0,"bottom"===e?r.style.transform="translateY(".concat(o,"px)"):"top"===e&&(r.style.transform="translateY(-".concat(o,"px)")),n.addEventListener("transitionend",(function e(){n.removeEventListener("transitionend",e,!0),a()}),!0)}))}},{key:"_getElementByClass",value:function(e){return document.querySelector('[data-id="'.concat(this.id,'"] .').concat(e))}},{key:"_getElementByQuery",value:function(e){return document.querySelector('[data-id="'.concat(this.id,'"] ').concat(e))}},{key:"_getTouchPos",value:function(){var e=0,t=0,a="undefined"==typeof event?this.e:event;return this._isTouched(a)?(e=a.touches[0].pageX,t=a.touches[0].pageY):a.pageX&&(e=a.pageX,t=a.pageY),{x:e,y:t}}},{key:"_getGesturePos",value:function(e){var t=e.touches;return[{x:t[0].pageX,y:t[0].pageY},{x:t[1].pageX,y:t[1].pageY}]}},{key:"_setPosByCurrentIndex",value:function(){var e=this,t=-1*this.groupItems()[this.data.currentIndex].translateX;this.pos.x=t,setTimeout((function(){e.data.translateX=t,e.data.translateY=0,e._listUpdate()}),1)}},{key:"_setHashByCurrentIndex",value:function(){var e=void 0!==window.pageXOffset?window.pageXOffset:(document.documentElement||document.body.parentNode||document.body).scrollLeft,t=void 0!==window.pageYOffset?window.pageYOffset:(document.documentElement||document.body.parentNode||document.body).scrollTop,a=this.groupItems()[this.data.currentIndex].id,i=this.data.currentGroup,n="group=".concat(i,"&photo=").concat(a);this._setHash(n),window.scroll(e,t)}},{key:"_setHash",value:function(e){window.history&&window.history.pushState&&this.data.useHistoryApi&&(e?window.history.replaceState(null,null,"".concat(location.pathname).concat(location.search,"#").concat(e)):window.history.replaceState(null,null,"".concat(location.pathname).concat(location.search)))}},{key:"_getCurrentItemByHash",value:function(){var e=this.data.group,t=location.hash.substr(1),a=c.parseQuery(t),i=null,n=function(e){a.group===e.groupId&&a.photo===e.id&&(i=e)};return Object.keys(e).forEach((function(t){e[t].forEach(n)})),i}},{key:"_loadItem",value:function(e){return new d((function(t){var a=new Image;a.onload=function(){e.width=a.width,e.height=a.height,e.loaded=!0,t()},a.onerror=function(){t()},a.src=e.src}))}},{key:"_getItemByIndex",value:function(e){var t=this.data;return t.group[t.currentGroup][e]?t.group[t.currentGroup][e]:null}},{key:"_loadNeighborItems",value:function(){for(var e=this,t=this.data.currentIndex,a=this.data.loadOffset,i=t+a,n=[],r=t-a;r<i;r++){var o=this._getItemByIndex(r);o&&!o.loaded&&n.push(this._loadItem(o))}n.length&&d.all(n).then((function(){e._initPhoto(),e.update()}))}},{key:"_setSizeByScreen",value:function(){var e=this._getWindowWidth(),t=this._getWindowHeight(),a=this.data.headerHeight,i=this.data.footerHeight,n=t-(a+i);this.groupItems().forEach((function(a){a.loaded&&(a.processed=!0,a.scale=n/a.height,a.height<n&&(a.scale=1),a.x=(a.scale-1)/2*a.width+(e-a.width*a.scale)/2,a.y=(a.scale-1)/2*a.height+(t-a.height*a.scale)/2,a.width*a.scale>e&&(a.scale=e/a.width,a.x=(a.scale-1)/2*a.width))}))}},{key:"_slideList",value:function(){var e=this;this.data.scaleSize=1,this.isBeingZoomed=!1,this.data.hideUi=!1,this.data.scale=!1,this.data.photoPosX=0,this.data.photoPosY=0,this.data.onMoveClass=!0,this._setPosByCurrentIndex(),this._setHashByCurrentIndex(),this._setSizeByScreen(),setTimeout((function(){var t=e._getSelectedItem();e.data.onMoveClass=!1,e.setArrow(),e.update(),e.data.oldIndex!==e.data.currentIndex&&e._fireEvent("change"),e.data.oldIndex=e.data.currentIndex,e._loadNeighborItems(),t.loaded||e._loadItem(t).then((function(){e._initPhoto(),e.update()}))}),200)}},{key:"gotoSlide",value:function(e){this.e&&this.e.preventDefault&&this.e.preventDefault(),this.data.currentIndex=parseInt(e,10),this.data.currentIndex||(this.data.currentIndex=0),this._slideList()}},{key:"setArrow",value:function(){var e=this.groupItems().length,t=this.data.currentIndex+1,a=this.data.currentIndex-1;this.data.showNextArrow=!1,this.data.showPrevArrow=!1,t!==e&&(this.data.next=t,this.data.showNextArrow=!0),-1!==a&&(this.data.prev=a,this.data.showPrevArrow=!0)}},{key:"beforeDrag",value:function(){if(this._isGestured(this.e))this.beforeGesture();else if(this.isBeingZoomed=!1,this.data.scale)this.beforePhotoDrag();else{var e=this._getTouchPos();this.isSwipable=!0,this.dragStart=!0,this.firstPos=e,this.oldPos=e}}},{key:"afterDrag",value:function(){var e=this.groupItems(),t=(new Date).getTime(),a=this.tapSecond-t,i=0,n=0;if(this.isSwipable=!1,this.onListMove=!1,this.oldPos&&(i=this.oldPos.x-this.firstPos.x,n=this.oldPos.y-this.firstPos.y),this.isBeingZoomed)this.afterGesture();else if(this.data.scale)this.afterPhotoDrag();else if(c.isSmartPhone()||0!==i||0!==n){if(Math.abs(a)<=500&&0===i&&0===n)return this.e.preventDefault(),void this.zoomPhoto();this.tapSecond=t,this._fireEvent("swipeend"),"horizontal"===this.moveDir&&(i>=this.data.swipeOffset&&0!==this.data.currentIndex?this.data.currentIndex-=1:i<=-this.data.swipeOffset&&this.data.currentIndex!==e.length-1&&(this.data.currentIndex+=1),this._slideList()),"vertical"===this.moveDir&&(this.data.swipeBottomToClose&&n>=this.data.swipeOffset?this.hidePhoto("bottom"):this.data.swipeTopToClose&&n<=-this.data.swipeOffset?this.hidePhoto("top"):(this.data.translateY=0,this._slideList()))}else this.zoomPhoto()}},{key:"onDrag",value:function(){if(this.e.preventDefault(),this._isGestured(this.e)&&!1===this.onListMove)this.onGesture();else if(!this.isBeingZoomed)if(this.data.scale)this.onPhotoDrag();else if(this.isSwipable){var e=this._getTouchPos(),t=e.x-this.oldPos.x,a=e.y-this.firstPos.y;this.dragStart&&(this._fireEvent("swipestart"),this.dragStart=!1,Math.abs(t)>Math.abs(a)?this.moveDir="horizontal":this.moveDir="vertical"),"horizontal"===this.moveDir?(this.pos.x+=t,this.data.translateX=this.pos.x):this.data.translateY=a,this.onListMove=!0,this.oldPos=e,this._listUpdate()}}},{key:"zoomPhoto",value:function(){var e=this;this.data.hideUi=!0,this.data.scaleSize=this._getScaleBoarder(),this.data.scaleSize<=1||(this.data.photoPosX=0,this.data.photoPosY=0,this._photoUpdate(),setTimeout((function(){e.data.scale=!0,e._photoUpdate(),e._fireEvent("zoomin")}),300))}},{key:"zoomOutPhoto",value:function(){this.data.scaleSize=1,this.isBeingZoomed=!1,this.data.hideUi=!1,this.data.scale=!1,this.data.photoPosX=0,this.data.photoPosY=0,this._photoUpdate(),this._fireEvent("zoomout")}},{key:"beforePhotoDrag",value:function(){var e=this._getTouchPos();this.photoSwipable=!0,this.data.photoPosX||(this.data.photoPosX=0),this.data.photoPosY||(this.data.photoPosY=0),this.oldPhotoPos=e,this.firstPhotoPos=e}},{key:"onPhotoDrag",value:function(){if(this.photoSwipable){this.e.preventDefault();var e=this._getTouchPos(),t=e.x-this.oldPhotoPos.x,a=e.y-this.oldPhotoPos.y,i=this._round(this.data.scaleSize*t,6),n=this._round(this.data.scaleSize*a,6);"number"==typeof i&&(this.data.photoPosX+=i,this.photoVX=i),"number"==typeof n&&(this.data.photoPosY+=n,this.photoVY=n),this.oldPhotoPos=e,this._photoUpdate()}}},{key:"afterPhotoDrag",value:function(){if(this.oldPhotoPos.x===this.firstPhotoPos.x&&this.photoSwipable)this.photoSwipable=!1,this.zoomOutPhoto();else{this.photoSwipable=!1;var e=this._getSelectedItem(),t=this._makeBound(e),a=this.data.swipeOffset*this.data.scaleSize,i=0,n=0;if(this.data.photoPosX>t.maxX?i=-1:this.data.photoPosX<t.minX&&(i=1),this.data.photoPosY>t.maxY?n=-1:this.data.photoPosY<t.minY&&(n=1),this.data.photoPosX-t.maxX>a&&0!==this.data.currentIndex)return void this.gotoSlide(this.data.prev);if(t.minX-this.data.photoPosX>a&&this.data.currentIndex+1!==this.data.total)return void this.gotoSlide(this.data.next);0===i&&0===n?(this.vx=this.photoVX/5,this.vy=this.photoVY/5):this._registerElasticForce(i,n)}}},{key:"beforeGesture",value:function(){this._fireEvent("gesturestart");var e=this._getGesturePos(this.e),t=this._getDistance(e[0],e[1]);this.isBeingZoomed=!0,this.oldDistance=t,this.data.scale=!0,this.e.preventDefault()}},{key:"onGesture",value:function(){var e=this._getGesturePos(this.e),t=this._getDistance(e[0],e[1]),a=(t-this.oldDistance)/100,i=this.data.scaleSize,n=this.data.photoPosX,r=this.data.photoPosY;this.isBeingZoomed=!0,this.data.scaleSize+=this._round(a,6),this.data.scaleSize<.2&&(this.data.scaleSize=.2),this.data.scaleSize<i&&(this.data.photoPosX=(1+this.data.scaleSize-i)*n,this.data.photoPosY=(1+this.data.scaleSize-i)*r),this.data.scaleSize<1||this.data.scaleSize>this._getScaleBoarder()?this.data.hideUi=!0:this.data.hideUi=!1,this.oldDistance=t,this.e.preventDefault(),this._photoUpdate()}},{key:"afterGesture",value:function(){this.data.scaleSize>this._getScaleBoarder()||(this.data.photoPosX=0,this.data.photoPosY=0,this.data.scale=!1,this.data.scaleSize=1,this.data.hideUi=!1,this._fireEvent("gestureend"),this._photoUpdate())}},{key:"_getForceAndTheta",value:function(e,t){return{force:Math.sqrt(e*e+t*t),theta:Math.atan2(t,e)}}},{key:"_getScaleBoarder",value:function(){var e=this._getSelectedItem(),t=this._getWindowWidth(),a=this._getWindowHeight();return c.isSmartPhone()?e.width>e.height?a/(e.height*e.scale):t/(e.width*e.scale):1/e.scale}},{key:"_makeBound",value:function(e){var t,a,i,n,r=e.width*e.scale*this.data.scaleSize,o=e.height*e.scale*this.data.scaleSize,s=this._getWindowWidth(),l=this._getWindowHeight();return t=s>r?-1*(i=(s-r)/2):-1*(i=(r-s)/2),a=l>o?-1*(n=(l-o)/2):-1*(n=(o-l)/2),{minX:this._round(t,6)*this.data.scaleSize,minY:this._round(a,6)*this.data.scaleSize,maxX:this._round(i,6)*this.data.scaleSize,maxY:this._round(n,6)*this.data.scaleSize}}},{key:"_registerElasticForce",value:function(e,t){var a=this,i=this._getSelectedItem(),n=this._makeBound(i);this.data.elastic=!0,1===e?this.data.photoPosX=n.minX:-1===e&&(this.data.photoPosX=n.maxX),1===t?this.data.photoPosY=n.minY:-1===t&&(this.data.photoPosY=n.maxY),this._photoUpdate(),setTimeout((function(){a.data.elastic=!1,a._photoUpdate()}),300)}},{key:"_getSelectedItem",value:function(){var e=this.data,t=e.currentIndex;return e.group[e.currentGroup][t]}},{key:"_getUniqId",value:function(){return(Date.now().toString(36)+Math.random().toString(36).substr(2,5)).toUpperCase()}},{key:"_getDistance",value:function(e,t){var a=e.x-t.x,i=e.y-t.y;return Math.sqrt(a*a+i*i)}},{key:"_round",value:function(e,t){var a=Math.pow(10,t);return e*=a,e=Math.round(e),e/=a}},{key:"_isTouched",value:function(e){return!(!e||!e.touches)}},{key:"_isGestured",value:function(e){return!!(e&&e.touches&&e.touches.length>1)}},{key:"_isSmartPhone",value:function(){var e=navigator.userAgent;return e.indexOf("iPhone")>0||e.indexOf("iPad")>0||e.indexOf("ipod")>0||e.indexOf("Android")>0}},{key:"_calcGravity",value:function(e,t){(e>5||e<-5)&&(this.vx+=.05*e),!1!==this.data.verticalGravity&&(t>5||t<-5)&&(this.vy+=.05*t)}},{key:"_photoUpdate",value:function(){var e=this.data.classNames,t=this._getElementByQuery(".current").querySelector(".".concat(e.smartPhotoImg)),a=this._getElementByQuery(".".concat(e.smartPhotoNav)),i=this._getElementByQuery(".".concat(e.smartPhotoArrows)),n=this.virtualPos(this.data.photoPosX),r=this.virtualPos(this.data.photoPosY),o=this.data.scaleSize,s="translate(".concat(n,"px,").concat(r,"px) scale(").concat(o,")");t.style.transform=s,this.data.scale?c.addClass(t,e.smartPhotoImgOnMove):c.removeClass(t,e.smartPhotoImgOnMove),this.data.elastic?c.addClass(t,e.smartPhotoImgElasticMove):c.removeClass(t,e.smartPhotoImgElasticMove),this.data.hideUi?(a&&a.setAttribute("aria-hidden","true"),i&&i.setAttribute("aria-hidden","true")):(a&&a.setAttribute("aria-hidden","false"),i&&i.setAttribute("aria-hidden","false"))}},{key:"_getWindowWidth",value:function(){return document&&document.documentElement?document.documentElement.clientWidth:window&&window.innerWidth?window.innerWidth:0}},{key:"_getWindowHeight",value:function(){return document&&document.documentElement?document.documentElement.clientHeight:window&&window.innerHeight?window.innerHeight:0}},{key:"_listUpdate",value:function(){var e=this.data.classNames,t=this._getElementByQuery(".".concat(e.smartPhotoList)),a="translate(".concat(this.data.translateX,"px,").concat(this.data.translateY,"px)");t.style.transform=a,this.data.onMoveClass?c.addClass(t,e.smartPhotoListOnMove):c.removeClass(t,e.smartPhotoListOnMove)}},{key:"_fireEvent",value:function(e){var t=this._getElementByClass(this.data.classNames.smartPhoto);c.triggerEvent(t,e)}},{key:"_doAnim",value:function(){if(!(this.isBeingZoomed||this.isSwipable||this.photoSwipable||this.data.elastic)&&this.data.scale){this.data.photoPosX+=this.vx,this.data.photoPosY+=this.vy;var e=this._getSelectedItem(),t=this._makeBound(e);this.data.photoPosX<t.minX?(this.data.photoPosX=t.minX,this.vx*=-.2):this.data.photoPosX>t.maxX&&(this.data.photoPosX=t.maxX,this.vx*=-.2),this.data.photoPosY<t.minY?(this.data.photoPosY=t.minY,this.vy*=-.2):this.data.photoPosY>t.maxY&&(this.data.photoPosY=t.maxY,this.vy*=-.2);var a=this._getForceAndTheta(this.vx,this.vy),i=a.force,n=a.theta;i-=this.data.registance,Math.abs(i)<.5||(this.vx=Math.cos(n)*i,this.vy=Math.sin(n)*i,this._photoUpdate())}}}]),t}(i.default);t.default=p,e.exports=t.default}(x,x.exports);var X=i(x.exports),Y={init:function(){var e=this;jQuery("pre.wp-block-code").each((function(e,t){var a=jQuery(t),i=a.removeClass("wp-block-code").prop("outerHTML");a.replaceWith('<div class="wp-block-wpcom-hljs">'+i+"</div>")}));var t=jQuery(".wp-block-wpcom-hljs > pre");t.length&&this.load(t),jQuery(document).on("hljs.wpcom",(function(t){t.target&&e.load(jQuery(t.target))}))},load:function(e){if("undefined"==typeof hljs){var t=this,a=void 0!==_wpcom_js.framework_url?_wpcom_js.framework_url:_wpcom_js.theme_url+"/themer",i=a+"/assets/js/highlight-11.9.0.min.js",n=a+"/assets/css/highlight-11.9.0.min.css";jQuery.ajax({url:i,dataType:"script",cache:!0,success:function(){var a=document.createElement("link");a.href=n,a.rel="stylesheet",a.type="text/css",document.body.appendChild(a),hljs.configure({ignoreUnescapedHTML:!0}),t.render(e)}})}else this.render(e)},render:function(e){e.length&&(this.copyBtn(e),e.each((function(e,t){hljs.highlightElement(t)})))},copyBtn:function(e){void 0!==document.execCommand&&(e.parent().each((function(e,t){var a=jQuery(t);a.data("value",a.find(">pre").text())})),e.parent().append('<div class="copy-btn"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-copy"></use></svg></i></div>'),e.parent().on("click",".copy-btn",(function(){var e=jQuery(this),t=e.closest(".wp-block-wpcom-hljs").data("value"),a=document.createElement("textarea");a.value=t,jQuery("body").append(a),a.style.position="fixed",a.style.height=0,a.select(),document.execCommand("copy"),a.remove(),e.html('<i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-dagou"></use></svg></i>').addClass("success"),setTimeout((function(){e.html('<i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-copy"></use></svg></i>').removeClass("success")}),2e3)})))}};function K(e){return null!==e&&"object"===m(e)&&"constructor"in e&&e.constructor===Object}function Z(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach((function(a){void 0===e[a]?e[a]=t[a]:K(t[a])&&K(e[a])&&Object.keys(t[a]).length>0&&Z(e[a],t[a])}))}var J={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function ee(){var e="undefined"!=typeof document?document:{};return Z(e,J),e}var te,ae,ie,ne={document:J,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(e){return"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0)},cancelAnimationFrame:function(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function re(){var e="undefined"!=typeof window?window:{};return Z(e,ne),e}function oe(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function se(){return Date.now()}function le(e,t){void 0===t&&(t="x");var a,i,n,r=re(),o=function(e){var t,a=re();return a.getComputedStyle&&(t=a.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return r.WebKitCSSMatrix?((i=o.transform||o.webkitTransform).split(",").length>6&&(i=i.split(", ").map((function(e){return e.replace(",",".")})).join(", ")),n=new r.WebKitCSSMatrix("none"===i?"":i)):a=(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(i=r.WebKitCSSMatrix?n.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===t&&(i=r.WebKitCSSMatrix?n.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),i||0}function ce(e){return"object"===m(e)&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function de(){for(var e,t=Object(arguments.length<=0?void 0:arguments[0]),a=["__proto__","constructor","prototype"],i=1;i<arguments.length;i+=1){var n=i<0||arguments.length<=i?void 0:arguments[i];if(null!=n&&(e=n,!("undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType))))for(var r=Object.keys(Object(n)).filter((function(e){return a.indexOf(e)<0})),o=0,s=r.length;o<s;o+=1){var l=r[o],c=Object.getOwnPropertyDescriptor(n,l);void 0!==c&&c.enumerable&&(ce(t[l])&&ce(n[l])?n[l].__swiper__?t[l]=n[l]:de(t[l],n[l]):!ce(t[l])&&ce(n[l])?(t[l]={},n[l].__swiper__?t[l]=n[l]:de(t[l],n[l])):t[l]=n[l])}}return t}function ue(e,t,a){e.style.setProperty(t,a)}function pe(e){var t,a=e.swiper,i=e.targetPosition,n=e.side,r=re(),o=-a.translate,s=null,c=a.params.speed;a.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(a.cssModeFrameID);var d=i>o?"next":"prev",u=function(e,t){return"next"===d&&e>=t||"prev"===d&&e<=t};!function e(){t=(new Date).getTime(),null===s&&(s=t);var d=Math.max(Math.min((t-s)/c,1),0),p=.5-Math.cos(d*Math.PI)/2,h=o+p*(i-o);if(u(h,i)&&(h=i),a.wrapperEl.scrollTo(l({},n,h)),u(h,i))return a.wrapperEl.style.overflow="hidden",a.wrapperEl.style.scrollSnapType="",setTimeout((function(){a.wrapperEl.style.overflow="",a.wrapperEl.scrollTo(l({},n,h))})),void r.cancelAnimationFrame(a.cssModeFrameID);a.cssModeFrameID=r.requestAnimationFrame(e)}()}function he(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function fe(e,t){void 0===t&&(t="");var a=h(e.children);return e instanceof HTMLSlotElement&&a.push.apply(a,h(e.assignedElements())),t?a.filter((function(e){return e.matches(t)})):a}function me(e){try{return void console.warn(e)}catch(e){}}function ve(e,t){var a;void 0===t&&(t=[]);var i=document.createElement(e);return(a=i.classList).add.apply(a,h(Array.isArray(t)?t:function(e){return void 0===e&&(e=""),e.trim().split(" ").filter((function(e){return!!e.trim()}))}(t))),i}function ge(e,t){return re().getComputedStyle(e,null).getPropertyValue(t)}function we(e){var t,a=e;if(a){for(t=0;null!==(a=a.previousSibling);)1===a.nodeType&&(t+=1);return t}}function ye(e,t){for(var a=[],i=e.parentElement;i;)t?i.matches(t)&&a.push(i):a.push(i),i=i.parentElement;return a}function be(e,t,a){var i=re();return e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom"))}function xe(e){return(Array.isArray(e)?e:[e]).filter((function(e){return!!e}))}function Ee(e){return function(t){return Math.abs(t)>0&&e.browser&&e.browser.need3dFix&&Math.abs(t)%90==0?t+.001:t}}function Te(){return te||(te=function(){var e=re(),t=ee();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),te}function _e(e){return void 0===e&&(e={}),ae||(ae=function(e){var t=(void 0===e?{}:e).userAgent,a=Te(),i=re(),n=i.navigator.platform,r=t||i.navigator.userAgent,o={ios:!1,android:!1},s=i.screen.width,l=i.screen.height,c=r.match(/(Android);?[\s\/]+([\d.]+)?/),d=r.match(/(iPad).*OS\s([\d_]+)/),u=r.match(/(iPod)(.*OS\s([\d_]+))?/),p=!d&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h="Win32"===n,f="MacIntel"===n;return!d&&f&&a.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf("".concat(s,"x").concat(l))>=0&&((d=r.match(/(Version)\/([\d.]+)/))||(d=[0,1,"13_0_0"]),f=!1),c&&!h&&(o.os="android",o.android=!0),(d||p||u)&&(o.os="ios",o.ios=!0),o}(e)),ae}function Se(){return ie||(ie=function(){var e=re(),t=_e(),a=!1;function i(){var t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(i()){var n=String(e.navigator.userAgent);if(n.includes("Version/")){var r=p(n.split("Version/")[1].split(" ")[0].split(".").map((function(e){return Number(e)})),2),o=r[0],s=r[1];a=o<16||16===o&&s<2}}var l=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),c=i();return{isSafari:a||c,needPerspectiveFix:a,need3dFix:c||l&&t.ios,isWebView:l}}()),ie}var Ce={on:function(e,t,a){var i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof t)return i;var n=a?"unshift":"push";return e.split(" ").forEach((function(e){i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][n](t)})),i},once:function(e,t,a){var i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof t)return i;function n(){i.off(e,n),n.__emitterProxy&&delete n.__emitterProxy;for(var a=arguments.length,r=new Array(a),o=0;o<a;o++)r[o]=arguments[o];t.apply(i,r)}return n.__emitterProxy=t,i.on(e,n,a)},onAny:function(e,t){var a=this;if(!a.eventsListeners||a.destroyed)return a;if("function"!=typeof e)return a;var i=t?"unshift":"push";return a.eventsAnyListeners.indexOf(e)<0&&a.eventsAnyListeners[i](e),a},offAny:function(e){var t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;var a=t.eventsAnyListeners.indexOf(e);return a>=0&&t.eventsAnyListeners.splice(a,1),t},off:function(e,t){var a=this;return!a.eventsListeners||a.destroyed?a:a.eventsListeners?(e.split(" ").forEach((function(e){void 0===t?a.eventsListeners[e]=[]:a.eventsListeners[e]&&a.eventsListeners[e].forEach((function(i,n){(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&a.eventsListeners[e].splice(n,1)}))})),a):a},emit:function(){var e,t,a,i=this;if(!i.eventsListeners||i.destroyed)return i;if(!i.eventsListeners)return i;for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return"string"==typeof r[0]||Array.isArray(r[0])?(e=r[0],t=r.slice(1,r.length),a=i):(e=r[0].events,t=r[0].data,a=r[0].context||i),t.unshift(a),(Array.isArray(e)?e:e.split(" ")).forEach((function(e){i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach((function(i){i.apply(a,[e].concat(h(t)))})),i.eventsListeners&&i.eventsListeners[e]&&i.eventsListeners[e].forEach((function(e){e.apply(a,t)}))})),i}};var ke=function(e,t,a){t&&!e.classList.contains(a)?e.classList.add(a):!t&&e.classList.contains(a)&&e.classList.remove(a)};var Pe=function(e,t,a){t&&!e.classList.contains(a)?e.classList.add(a):!t&&e.classList.contains(a)&&e.classList.remove(a)};var je=function(e,t){if(e&&!e.destroyed&&e.params){var a=t.closest(e.isElement?"swiper-slide":".".concat(e.params.slideClass));if(a){var i=a.querySelector(".".concat(e.params.lazyPreloaderClass));!i&&e.isElement&&(a.shadowRoot?i=a.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)):requestAnimationFrame((function(){a.shadowRoot&&(i=a.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)))&&i.remove()}))),i&&i.remove()}}},Ie=function(e,t){if(e.slides[t]){var a=e.slides[t].querySelector('[loading="lazy"]');a&&a.removeAttribute("loading")}},Me=function(e){if(e&&!e.destroyed&&e.params){var t=e.params.lazyPreloadPrevNext,a=e.slides.length;if(a&&t&&!(t<0)){t=Math.min(t,a);var i="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),n=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){var r=n,o=[r-t];return o.push.apply(o,h(Array.from({length:t}).map((function(e,t){return r+i+t})))),void e.slides.forEach((function(t,a){o.includes(t.column)&&Ie(e,a)}))}var s=n+i-1;if(e.params.rewind||e.params.loop)for(var l=n-t;l<=s+t;l+=1){var c=(l%a+a)%a;(c<n||c>s)&&Ie(e,c)}else for(var d=Math.max(n-t,0);d<=Math.min(s+t,a-1);d+=1)d!==n&&(d>s||d<n)&&Ie(e,d)}}};var Oe={updateSize:function(){var e,t,a=this,i=a.el;e=void 0!==a.params.width&&null!==a.params.width?a.params.width:i.clientWidth,t=void 0!==a.params.height&&null!==a.params.height?a.params.height:i.clientHeight,0===e&&a.isHorizontal()||0===t&&a.isVertical()||(e=e-parseInt(ge(i,"padding-left")||0,10)-parseInt(ge(i,"padding-right")||0,10),t=t-parseInt(ge(i,"padding-top")||0,10)-parseInt(ge(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(a,{width:e,height:t,size:a.isHorizontal()?e:t}))},updateSlides:function(){var e=this;function t(t,a){return parseFloat(t.getPropertyValue(e.getDirectionLabel(a))||0)}var a=e.params,i=e.wrapperEl,n=e.slidesEl,r=e.size,o=e.rtlTranslate,s=e.wrongRTL,l=e.virtual&&a.virtual.enabled,c=l?e.virtual.slides.length:e.slides.length,d=fe(n,".".concat(e.params.slideClass,", swiper-slide")),u=l?e.virtual.slides.length:d.length,p=[],h=[],f=[],m=a.slidesOffsetBefore;"function"==typeof m&&(m=a.slidesOffsetBefore.call(e));var v=a.slidesOffsetAfter;"function"==typeof v&&(v=a.slidesOffsetAfter.call(e));var g=e.snapGrid.length,w=e.slidesGrid.length,y=a.spaceBetween,b=-m,x=0,E=0;if(void 0!==r){"string"==typeof y&&y.indexOf("%")>=0?y=parseFloat(y.replace("%",""))/100*r:"string"==typeof y&&(y=parseFloat(y)),e.virtualSize=-y,d.forEach((function(e){o?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""})),a.centeredSlides&&a.cssMode&&(ue(i,"--swiper-centered-offset-before",""),ue(i,"--swiper-centered-offset-after",""));var T,_=a.grid&&a.grid.rows>1&&e.grid;_?e.grid.initSlides(d):e.grid&&e.grid.unsetSlides();for(var S="auto"===a.slidesPerView&&a.breakpoints&&Object.keys(a.breakpoints).filter((function(e){return void 0!==a.breakpoints[e].slidesPerView})).length>0,C=0;C<u;C+=1){T=0;var k=void 0;if(d[C]&&(k=d[C]),_&&e.grid.updateSlide(C,k,d),!d[C]||"none"!==ge(k,"display")){if("auto"===a.slidesPerView){S&&(d[C].style[e.getDirectionLabel("width")]="");var P=getComputedStyle(k),j=k.style.transform,I=k.style.webkitTransform;if(j&&(k.style.transform="none"),I&&(k.style.webkitTransform="none"),a.roundLengths)T=e.isHorizontal()?be(k,"width"):be(k,"height");else{var M=t(P,"width"),O=t(P,"padding-left"),A=t(P,"padding-right"),L=t(P,"margin-left"),N=t(P,"margin-right"),D=P.getPropertyValue("box-sizing");if(D&&"border-box"===D)T=M+L+N;else{var B=k,z=B.clientWidth;T=M+O+A+L+N+(B.offsetWidth-z)}}j&&(k.style.transform=j),I&&(k.style.webkitTransform=I),a.roundLengths&&(T=Math.floor(T))}else T=(r-(a.slidesPerView-1)*y)/a.slidesPerView,a.roundLengths&&(T=Math.floor(T)),d[C]&&(d[C].style[e.getDirectionLabel("width")]="".concat(T,"px"));d[C]&&(d[C].swiperSlideSize=T),f.push(T),a.centeredSlides?(b=b+T/2+x/2+y,0===x&&0!==C&&(b=b-r/2-y),0===C&&(b=b-r/2-y),Math.abs(b)<.001&&(b=0),a.roundLengths&&(b=Math.floor(b)),E%a.slidesPerGroup==0&&p.push(b),h.push(b)):(a.roundLengths&&(b=Math.floor(b)),(E-Math.min(e.params.slidesPerGroupSkip,E))%e.params.slidesPerGroup==0&&p.push(b),h.push(b),b=b+T+y),e.virtualSize+=T+y,x=T,E+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+v,o&&s&&("slide"===a.effect||"coverflow"===a.effect)&&(i.style.width="".concat(e.virtualSize+y,"px")),a.setWrapperSize&&(i.style[e.getDirectionLabel("width")]="".concat(e.virtualSize+y,"px")),_&&e.grid.updateWrapperSize(T,p),!a.centeredSlides){for(var G=[],R=0;R<p.length;R+=1){var q=p[R];a.roundLengths&&(q=Math.floor(q)),p[R]<=e.virtualSize-r&&G.push(q)}p=G,Math.floor(e.virtualSize-r)-Math.floor(p[p.length-1])>1&&p.push(e.virtualSize-r)}if(l&&a.loop){var H=f[0]+y;if(a.slidesPerGroup>1)for(var $=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/a.slidesPerGroup),F=H*a.slidesPerGroup,V=0;V<$;V+=1)p.push(p[p.length-1]+F);for(var U=0;U<e.virtual.slidesBefore+e.virtual.slidesAfter;U+=1)1===a.slidesPerGroup&&p.push(p[p.length-1]+H),h.push(h[h.length-1]+H),e.virtualSize+=H}if(0===p.length&&(p=[0]),0!==y){var W=e.isHorizontal()&&o?"marginLeft":e.getDirectionLabel("marginRight");d.filter((function(e,t){return!(a.cssMode&&!a.loop)||t!==d.length-1})).forEach((function(e){e.style[W]="".concat(y,"px")}))}if(a.centeredSlides&&a.centeredSlidesBounds){var Q=0;f.forEach((function(e){Q+=e+(y||0)}));var X=(Q-=y)>r?Q-r:0;p=p.map((function(e){return e<=0?-m:e>X?X+v:e}))}if(a.centerInsufficientSlides){var Y=0;f.forEach((function(e){Y+=e+(y||0)})),Y-=y;var K=(a.slidesOffsetBefore||0)+(a.slidesOffsetAfter||0);if(Y+K<r){var Z=(r-Y-K)/2;p.forEach((function(e,t){p[t]=e-Z})),h.forEach((function(e,t){h[t]=e+Z}))}}if(Object.assign(e,{slides:d,snapGrid:p,slidesGrid:h,slidesSizesGrid:f}),a.centeredSlides&&a.cssMode&&!a.centeredSlidesBounds){ue(i,"--swiper-centered-offset-before","".concat(-p[0],"px")),ue(i,"--swiper-centered-offset-after","".concat(e.size/2-f[f.length-1]/2,"px"));var J=-e.snapGrid[0],ee=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((function(e){return e+J})),e.slidesGrid=e.slidesGrid.map((function(e){return e+ee}))}if(u!==c&&e.emit("slidesLengthChange"),p.length!==g&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),h.length!==w&&e.emit("slidesGridLengthChange"),a.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(l||a.cssMode||"slide"!==a.effect&&"fade"!==a.effect)){var te="".concat(a.containerModifierClass,"backface-hidden"),ae=e.el.classList.contains(te);u<=a.maxBackfaceHiddenSlides?ae||e.el.classList.add(te):ae&&e.el.classList.remove(te)}}},updateAutoHeight:function(e){var t,a=this,i=[],n=a.virtual&&a.params.virtual.enabled,r=0;"number"==typeof e?a.setTransition(e):!0===e&&a.setTransition(a.params.speed);var o=function(e){return n?a.slides[a.getSlideIndexByData(e)]:a.slides[e]};if("auto"!==a.params.slidesPerView&&a.params.slidesPerView>1)if(a.params.centeredSlides)(a.visibleSlides||[]).forEach((function(e){i.push(e)}));else for(t=0;t<Math.ceil(a.params.slidesPerView);t+=1){var s=a.activeIndex+t;if(s>a.slides.length&&!n)break;i.push(o(s))}else i.push(o(a.activeIndex));for(t=0;t<i.length;t+=1)if(void 0!==i[t]){var l=i[t].offsetHeight;r=l>r?l:r}(r||0===r)&&(a.wrapperEl.style.height="".concat(r,"px"))},updateSlidesOffset:function(){for(var e=this,t=e.slides,a=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0,i=0;i<t.length;i+=1)t[i].swiperSlideOffset=(e.isHorizontal()?t[i].offsetLeft:t[i].offsetTop)-a-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);var t=this,a=t.params,i=t.slides,n=t.rtlTranslate,r=t.snapGrid;if(0!==i.length){void 0===i[0].swiperSlideOffset&&t.updateSlidesOffset();var o=-e;n&&(o=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];var s=a.spaceBetween;"string"==typeof s&&s.indexOf("%")>=0?s=parseFloat(s.replace("%",""))/100*t.size:"string"==typeof s&&(s=parseFloat(s));for(var l=0;l<i.length;l+=1){var c=i[l],d=c.swiperSlideOffset;a.cssMode&&a.centeredSlides&&(d-=i[0].swiperSlideOffset);var u=(o+(a.centeredSlides?t.minTranslate():0)-d)/(c.swiperSlideSize+s),p=(o-r[0]+(a.centeredSlides?t.minTranslate():0)-d)/(c.swiperSlideSize+s),h=-(o-d),f=h+t.slidesSizesGrid[l],m=h>=0&&h<=t.size-t.slidesSizesGrid[l],v=h>=0&&h<t.size-1||f>1&&f<=t.size||h<=0&&f>=t.size;v&&(t.visibleSlides.push(c),t.visibleSlidesIndexes.push(l)),ke(c,v,a.slideVisibleClass),ke(c,m,a.slideFullyVisibleClass),c.progress=n?-u:u,c.originalProgress=n?-p:p}}},updateProgress:function(e){var t=this;if(void 0===e){var a=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*a||0}var i=t.params,n=t.maxTranslate()-t.minTranslate(),r=t.progress,o=t.isBeginning,s=t.isEnd,l=t.progressLoop,c=o,d=s;if(0===n)r=0,o=!0,s=!0;else{r=(e-t.minTranslate())/n;var u=Math.abs(e-t.minTranslate())<1,p=Math.abs(e-t.maxTranslate())<1;o=u||r<=0,s=p||r>=1,u&&(r=0),p&&(r=1)}if(i.loop){var h=t.getSlideIndexByData(0),f=t.getSlideIndexByData(t.slides.length-1),m=t.slidesGrid[h],v=t.slidesGrid[f],g=t.slidesGrid[t.slidesGrid.length-1],w=Math.abs(e);(l=w>=m?(w-m)/g:(w+g-v)/g)>1&&(l-=1)}Object.assign(t,{progress:r,progressLoop:l,isBeginning:o,isEnd:s}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&t.updateSlidesProgress(e),o&&!c&&t.emit("reachBeginning toEdge"),s&&!d&&t.emit("reachEnd toEdge"),(c&&!o||d&&!s)&&t.emit("fromEdge"),t.emit("progress",r)},updateSlidesClasses:function(){var e,t,a,i=this,n=i.slides,r=i.params,o=i.slidesEl,s=i.activeIndex,l=i.virtual&&r.virtual.enabled,c=i.grid&&r.grid&&r.grid.rows>1,d=function(e){return fe(o,".".concat(r.slideClass).concat(e,", swiper-slide").concat(e))[0]};if(l)if(r.loop){var u=s-i.virtual.slidesBefore;u<0&&(u=i.virtual.slides.length+u),u>=i.virtual.slides.length&&(u-=i.virtual.slides.length),e=d('[data-swiper-slide-index="'.concat(u,'"]'))}else e=d('[data-swiper-slide-index="'.concat(s,'"]'));else c?(e=n.filter((function(e){return e.column===s}))[0],a=n.filter((function(e){return e.column===s+1}))[0],t=n.filter((function(e){return e.column===s-1}))[0]):e=n[s];e&&(c||(a=function(e,t){for(var a=[];e.nextElementSibling;){var i=e.nextElementSibling;t?i.matches(t)&&a.push(i):a.push(i),e=i}return a}(e,".".concat(r.slideClass,", swiper-slide"))[0],r.loop&&!a&&(a=n[0]),t=function(e,t){for(var a=[];e.previousElementSibling;){var i=e.previousElementSibling;t?i.matches(t)&&a.push(i):a.push(i),e=i}return a}(e,".".concat(r.slideClass,", swiper-slide"))[0],r.loop&&0===!t&&(t=n[n.length-1]))),n.forEach((function(i){Pe(i,i===e,r.slideActiveClass),Pe(i,i===a,r.slideNextClass),Pe(i,i===t,r.slidePrevClass)})),i.emitSlidesClasses()},updateActiveIndex:function(e){var t,a=this,i=a.rtlTranslate?a.translate:-a.translate,n=a.snapGrid,r=a.params,o=a.activeIndex,s=a.realIndex,l=a.snapIndex,c=e,d=function(e){var t=e-a.virtual.slidesBefore;return t<0&&(t=a.virtual.slides.length+t),t>=a.virtual.slides.length&&(t-=a.virtual.slides.length),t};if(void 0===c&&(c=function(e){for(var t,a=e.slidesGrid,i=e.params,n=e.rtlTranslate?e.translate:-e.translate,r=0;r<a.length;r+=1)void 0!==a[r+1]?n>=a[r]&&n<a[r+1]-(a[r+1]-a[r])/2?t=r:n>=a[r]&&n<a[r+1]&&(t=r+1):n>=a[r]&&(t=r);return i.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(a)),n.indexOf(i)>=0)t=n.indexOf(i);else{var u=Math.min(r.slidesPerGroupSkip,c);t=u+Math.floor((c-u)/r.slidesPerGroup)}if(t>=n.length&&(t=n.length-1),c!==o||a.params.loop)if(c===o&&a.params.loop&&a.virtual&&a.params.virtual.enabled)a.realIndex=d(c);else{var p,h=a.grid&&r.grid&&r.grid.rows>1;if(a.virtual&&r.virtual.enabled&&r.loop)p=d(c);else if(h){var f=a.slides.filter((function(e){return e.column===c}))[0],m=parseInt(f.getAttribute("data-swiper-slide-index"),10);Number.isNaN(m)&&(m=Math.max(a.slides.indexOf(f),0)),p=Math.floor(m/r.grid.rows)}else if(a.slides[c]){var v=a.slides[c].getAttribute("data-swiper-slide-index");p=v?parseInt(v,10):c}else p=c;Object.assign(a,{previousSnapIndex:l,snapIndex:t,previousRealIndex:s,realIndex:p,previousIndex:o,activeIndex:c}),a.initialized&&Me(a),a.emit("activeIndexChange"),a.emit("snapIndexChange"),(a.initialized||a.params.runCallbacksOnInit)&&(s!==p&&a.emit("realIndexChange"),a.emit("slideChange"))}else t!==l&&(a.snapIndex=t,a.emit("snapIndexChange"))},updateClickedSlide:function(e,t){var a=this,i=a.params,n=e.closest(".".concat(i.slideClass,", swiper-slide"));!n&&a.isElement&&t&&t.length>1&&t.includes(e)&&h(t.slice(t.indexOf(e)+1,t.length)).forEach((function(e){!n&&e.matches&&e.matches(".".concat(i.slideClass,", swiper-slide"))&&(n=e)}));var r,o=!1;if(n)for(var s=0;s<a.slides.length;s+=1)if(a.slides[s]===n){o=!0,r=s;break}if(!n||!o)return a.clickedSlide=void 0,void(a.clickedIndex=void 0);a.clickedSlide=n,a.virtual&&a.params.virtual.enabled?a.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):a.clickedIndex=r,i.slideToClickedSlide&&void 0!==a.clickedIndex&&a.clickedIndex!==a.activeIndex&&a.slideToClickedSlide()}};var Ae={getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var t=this,a=t.params,i=t.rtlTranslate,n=t.translate,r=t.wrapperEl;if(a.virtualTranslate)return i?-n:n;if(a.cssMode)return n;var o=le(r,e);return o+=t.cssOverflowAdjustment(),i&&(o=-o),o||0},setTranslate:function(e,t){var a=this,i=a.rtlTranslate,n=a.params,r=a.wrapperEl,o=a.progress,s=0,l=0;a.isHorizontal()?s=i?-e:e:l=e,n.roundLengths&&(s=Math.floor(s),l=Math.floor(l)),a.previousTranslate=a.translate,a.translate=a.isHorizontal()?s:l,n.cssMode?r[a.isHorizontal()?"scrollLeft":"scrollTop"]=a.isHorizontal()?-s:-l:n.virtualTranslate||(a.isHorizontal()?s-=a.cssOverflowAdjustment():l-=a.cssOverflowAdjustment(),r.style.transform="translate3d(".concat(s,"px, ").concat(l,"px, ").concat(0,"px)"));var c=a.maxTranslate()-a.minTranslate();(0===c?0:(e-a.minTranslate())/c)!==o&&a.updateProgress(e),a.emit("setTranslate",a.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,a,i,n){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0),void 0===i&&(i=!0);var r=this,o=r.params,s=r.wrapperEl;if(r.animating&&o.preventInteractionOnTransition)return!1;var c,d=r.minTranslate(),u=r.maxTranslate();if(c=i&&e>d?d:i&&e<u?u:e,r.updateProgress(c),o.cssMode){var p=r.isHorizontal();if(0===t)s[p?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return pe({swiper:r,targetPosition:-c,side:p?"left":"top"}),!0;s.scrollTo(l(l({},p?"left":"top",-c),"behavior","smooth"))}return!0}return 0===t?(r.setTransition(0),r.setTranslate(c),a&&(r.emit("beforeTransitionStart",t,n),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(c),a&&(r.emit("beforeTransitionStart",t,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,a&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}};function Le(e){var t=e.swiper,a=e.runCallbacks,i=e.direction,n=e.step,r=t.activeIndex,o=t.previousIndex,s=i;if(s||(s=r>o?"next":r<o?"prev":"reset"),t.emit("transition".concat(n)),a&&r!==o){if("reset"===s)return void t.emit("slideResetTransition".concat(n));t.emit("slideChangeTransition".concat(n)),"next"===s?t.emit("slideNextTransition".concat(n)):t.emit("slidePrevTransition".concat(n))}}var Ne={slideTo:function(e,t,a,i,n){void 0===e&&(e=0),void 0===a&&(a=!0),"string"==typeof e&&(e=parseInt(e,10));var r=this,o=e;o<0&&(o=0);var s=r.params,c=r.snapGrid,d=r.slidesGrid,u=r.previousIndex,p=r.activeIndex,h=r.rtlTranslate,f=r.wrapperEl;if(!r.enabled&&!i&&!n||r.destroyed||r.animating&&s.preventInteractionOnTransition)return!1;void 0===t&&(t=r.params.speed);var m=Math.min(r.params.slidesPerGroupSkip,o),v=m+Math.floor((o-m)/r.params.slidesPerGroup);v>=c.length&&(v=c.length-1);var g,w=-c[v];if(s.normalizeSlideIndex)for(var y=0;y<d.length;y+=1){var b=-Math.floor(100*w),x=Math.floor(100*d[y]),E=Math.floor(100*d[y+1]);void 0!==d[y+1]?b>=x&&b<E-(E-x)/2?o=y:b>=x&&b<E&&(o=y+1):b>=x&&(o=y)}if(r.initialized&&o!==p){if(!r.allowSlideNext&&(h?w>r.translate&&w>r.minTranslate():w<r.translate&&w<r.minTranslate()))return!1;if(!r.allowSlidePrev&&w>r.translate&&w>r.maxTranslate()&&(p||0)!==o)return!1}o!==(u||0)&&a&&r.emit("beforeSlideChangeStart"),r.updateProgress(w),g=o>p?"next":o<p?"prev":"reset";var T=r.virtual&&r.params.virtual.enabled;if(!(T&&n)&&(h&&-w===r.translate||!h&&w===r.translate))return r.updateActiveIndex(o),s.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==s.effect&&r.setTranslate(w),"reset"!==g&&(r.transitionStart(a,g),r.transitionEnd(a,g)),!1;if(s.cssMode){var _=r.isHorizontal(),S=h?w:-w;if(0===t)T&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),T&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame((function(){f[_?"scrollLeft":"scrollTop"]=S}))):f[_?"scrollLeft":"scrollTop"]=S,T&&requestAnimationFrame((function(){r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1}));else{if(!r.support.smoothScroll)return pe({swiper:r,targetPosition:S,side:_?"left":"top"}),!0;f.scrollTo(l(l({},_?"left":"top",S),"behavior","smooth"))}return!0}return r.setTransition(t),r.setTranslate(w),r.updateActiveIndex(o),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,i),r.transitionStart(a,g),0===t?r.transitionEnd(a,g):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(a,g))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,a,i){void 0===e&&(e=0),void 0===a&&(a=!0),"string"==typeof e&&(e=parseInt(e,10));var n=this;if(!n.destroyed){void 0===t&&(t=n.params.speed);var r=n.grid&&n.params.grid&&n.params.grid.rows>1,o=e;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)o+=n.virtual.slidesBefore;else{var s;if(r){var l=o*n.params.grid.rows;s=n.slides.filter((function(e){return 1*e.getAttribute("data-swiper-slide-index")===l}))[0].column}else s=n.getSlideIndexByData(o);var c=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,d=n.params.centeredSlides,u=n.params.slidesPerView;"auto"===u?u=n.slidesPerViewDynamic():(u=Math.ceil(parseFloat(n.params.slidesPerView,10)),d&&u%2==0&&(u+=1));var p=c-s<u;if(d&&(p=p||s<Math.ceil(u/2)),i&&d&&"auto"!==n.params.slidesPerView&&!r&&(p=!1),p){var h=d?s<n.activeIndex?"prev":"next":s-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:h,slideTo:!0,activeSlideIndex:"next"===h?s+1:s-c+1,slideRealIndex:"next"===h?n.realIndex:void 0})}if(r){var f=o*n.params.grid.rows;o=n.slides.filter((function(e){return 1*e.getAttribute("data-swiper-slide-index")===f}))[0].column}else o=n.getSlideIndexByData(o)}return requestAnimationFrame((function(){n.slideTo(o,t,a,i)})),n}},slideNext:function(e,t,a){void 0===t&&(t=!0);var i=this,n=i.enabled,r=i.params,o=i.animating;if(!n||i.destroyed)return i;void 0===e&&(e=i.params.speed);var s=r.slidesPerGroup;"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(s=Math.max(i.slidesPerViewDynamic("current",!0),1));var l=i.activeIndex<r.slidesPerGroupSkip?1:s,c=i.virtual&&r.virtual.enabled;if(r.loop){if(o&&!c&&r.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&r.cssMode)return requestAnimationFrame((function(){i.slideTo(i.activeIndex+l,e,t,a)})),!0}return r.rewind&&i.isEnd?i.slideTo(0,e,t,a):i.slideTo(i.activeIndex+l,e,t,a)},slidePrev:function(e,t,a){void 0===t&&(t=!0);var i=this,n=i.params,r=i.snapGrid,o=i.slidesGrid,s=i.rtlTranslate,l=i.enabled,c=i.animating;if(!l||i.destroyed)return i;void 0===e&&(e=i.params.speed);var d=i.virtual&&n.virtual.enabled;if(n.loop){if(c&&!d&&n.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}var p,h=u(s?i.translate:-i.translate),f=r.map((function(e){return u(e)})),m=r[f.indexOf(h)-1];void 0===m&&n.cssMode&&(r.forEach((function(e,t){h>=e&&(p=t)})),void 0!==p&&(m=r[p>0?p-1:p]));var v=0;if(void 0!==m&&((v=o.indexOf(m))<0&&(v=i.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(v=v-i.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),n.rewind&&i.isBeginning){var g=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(g,e,t,a)}return n.loop&&0===i.activeIndex&&n.cssMode?(requestAnimationFrame((function(){i.slideTo(v,e,t,a)})),!0):i.slideTo(v,e,t,a)},slideReset:function(e,t,a){void 0===t&&(t=!0);var i=this;if(!i.destroyed)return void 0===e&&(e=i.params.speed),i.slideTo(i.activeIndex,e,t,a)},slideToClosest:function(e,t,a,i){void 0===t&&(t=!0),void 0===i&&(i=.5);var n=this;if(!n.destroyed){void 0===e&&(e=n.params.speed);var r=n.activeIndex,o=Math.min(n.params.slidesPerGroupSkip,r),s=o+Math.floor((r-o)/n.params.slidesPerGroup),l=n.rtlTranslate?n.translate:-n.translate;if(l>=n.snapGrid[s]){var c=n.snapGrid[s];l-c>(n.snapGrid[s+1]-c)*i&&(r+=n.params.slidesPerGroup)}else{var d=n.snapGrid[s-1];l-d<=(n.snapGrid[s]-d)*i&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,e,t,a)}},slideToClickedSlide:function(){var e=this;if(!e.destroyed){var t,a=e.params,i=e.slidesEl,n="auto"===a.slidesPerView?e.slidesPerViewDynamic():a.slidesPerView,r=e.clickedIndex,o=e.isElement?"swiper-slide":".".concat(a.slideClass);if(a.loop){if(e.animating)return;t=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),a.centeredSlides?r<e.loopedSlides-n/2||r>e.slides.length-e.loopedSlides+n/2?(e.loopFix(),r=e.getSlideIndex(fe(i,"".concat(o,'[data-swiper-slide-index="').concat(t,'"]'))[0]),oe((function(){e.slideTo(r)}))):e.slideTo(r):r>e.slides.length-n?(e.loopFix(),r=e.getSlideIndex(fe(i,"".concat(o,'[data-swiper-slide-index="').concat(t,'"]'))[0]),oe((function(){e.slideTo(r)}))):e.slideTo(r)}else e.slideTo(r)}}};var De={loopCreate:function(e){var t=this,a=t.params,i=t.slidesEl;if(!(!a.loop||t.virtual&&t.params.virtual.enabled)){var n=function(){fe(i,".".concat(a.slideClass,", swiper-slide")).forEach((function(e,t){e.setAttribute("data-swiper-slide-index",t)}))},r=t.grid&&a.grid&&a.grid.rows>1,o=a.slidesPerGroup*(r?a.grid.rows:1),s=t.slides.length%o!=0,l=r&&t.slides.length%a.grid.rows!=0,c=function(e){for(var i=0;i<e;i+=1){var n=t.isElement?ve("swiper-slide",[a.slideBlankClass]):ve("div",[a.slideClass,a.slideBlankClass]);t.slidesEl.append(n)}};if(s){if(a.loopAddBlankSlides)c(o-t.slides.length%o),t.recalcSlides(),t.updateSlides();else me("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else if(l){if(a.loopAddBlankSlides)c(a.grid.rows-t.slides.length%a.grid.rows),t.recalcSlides(),t.updateSlides();else me("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else n();t.loopFix({slideRealIndex:e,direction:a.centeredSlides?void 0:"next"})}},loopFix:function(e){var t=void 0===e?{}:e,a=t.slideRealIndex,i=t.slideTo,n=void 0===i||i,r=t.direction,o=t.setTranslate,s=t.activeSlideIndex,l=t.byController,c=t.byMousewheel,u=this;if(u.params.loop){u.emit("beforeLoopFix");var p=u.slides,h=u.allowSlidePrev,f=u.allowSlideNext,m=u.slidesEl,v=u.params,g=v.centeredSlides;if(u.allowSlidePrev=!0,u.allowSlideNext=!0,u.virtual&&v.virtual.enabled)return n&&(v.centeredSlides||0!==u.snapIndex?v.centeredSlides&&u.snapIndex<v.slidesPerView?u.slideTo(u.virtual.slides.length+u.snapIndex,0,!1,!0):u.snapIndex===u.snapGrid.length-1&&u.slideTo(u.virtual.slidesBefore,0,!1,!0):u.slideTo(u.virtual.slides.length,0,!1,!0)),u.allowSlidePrev=h,u.allowSlideNext=f,void u.emit("loopFix");var w=v.slidesPerView;"auto"===w?w=u.slidesPerViewDynamic():(w=Math.ceil(parseFloat(v.slidesPerView,10)),g&&w%2==0&&(w+=1));var y=v.slidesPerGroupAuto?w:v.slidesPerGroup,b=y;b%y!=0&&(b+=y-b%y),b+=v.loopAdditionalSlides,u.loopedSlides=b;var x=u.grid&&v.grid&&v.grid.rows>1;p.length<w+b?me("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):x&&"row"===v.grid.fill&&me("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");var E=[],T=[],_=u.activeIndex;void 0===s?s=u.getSlideIndex(p.filter((function(e){return e.classList.contains(v.slideActiveClass)}))[0]):_=s;var S="next"===r||!r,C="prev"===r||!r,k=0,P=0,j=x?Math.ceil(p.length/v.grid.rows):p.length,I=(x?p[s].column:s)+(g&&void 0===o?-w/2+.5:0);if(I<b){k=Math.max(b-I,y);for(var M=0;M<b-I;M+=1){var O=M-Math.floor(M/j)*j;if(x)for(var A=j-O-1,L=p.length-1;L>=0;L-=1)p[L].column===A&&E.push(L);else E.push(j-O-1)}}else if(I+w>j-b){P=Math.max(I-(j-2*b),y);for(var N=function(){var e=D-Math.floor(D/j)*j;x?p.forEach((function(t,a){t.column===e&&T.push(a)})):T.push(e)},D=0;D<P;D+=1)N()}if(u.__preventObserver__=!0,requestAnimationFrame((function(){u.__preventObserver__=!1})),C&&E.forEach((function(e){p[e].swiperLoopMoveDOM=!0,m.prepend(p[e]),p[e].swiperLoopMoveDOM=!1})),S&&T.forEach((function(e){p[e].swiperLoopMoveDOM=!0,m.append(p[e]),p[e].swiperLoopMoveDOM=!1})),u.recalcSlides(),"auto"===v.slidesPerView?u.updateSlides():x&&(E.length>0&&C||T.length>0&&S)&&u.slides.forEach((function(e,t){u.grid.updateSlide(t,e,u.slides)})),v.watchSlidesProgress&&u.updateSlidesOffset(),n)if(E.length>0&&C){if(void 0===a){var B=u.slidesGrid[_],z=u.slidesGrid[_+k]-B;c?u.setTranslate(u.translate-z):(u.slideTo(_+Math.ceil(k),0,!1,!0),o&&(u.touchEventsData.startTranslate=u.touchEventsData.startTranslate-z,u.touchEventsData.currentTranslate=u.touchEventsData.currentTranslate-z))}else if(o){var G=x?E.length/v.grid.rows:E.length;u.slideTo(u.activeIndex+G,0,!1,!0),u.touchEventsData.currentTranslate=u.translate}}else if(T.length>0&&S)if(void 0===a){var R=u.slidesGrid[_],q=u.slidesGrid[_-P]-R;c?u.setTranslate(u.translate-q):(u.slideTo(_-P,0,!1,!0),o&&(u.touchEventsData.startTranslate=u.touchEventsData.startTranslate-q,u.touchEventsData.currentTranslate=u.touchEventsData.currentTranslate-q))}else{var H=x?T.length/v.grid.rows:T.length;u.slideTo(u.activeIndex-H,0,!1,!0)}if(u.allowSlidePrev=h,u.allowSlideNext=f,u.controller&&u.controller.control&&!l){var $={slideRealIndex:a,direction:r,setTranslate:o,activeSlideIndex:s,byController:!0};Array.isArray(u.controller.control)?u.controller.control.forEach((function(e){!e.destroyed&&e.params.loop&&e.loopFix(d(d({},$),{},{slideTo:e.params.slidesPerView===v.slidesPerView&&n}))})):u.controller.control instanceof u.constructor&&u.controller.control.params.loop&&u.controller.control.loopFix(d(d({},$),{},{slideTo:u.controller.control.params.slidesPerView===v.slidesPerView&&n}))}u.emit("loopFix")}},loopDestroy:function(){var e=this,t=e.params,a=e.slidesEl;if(!(!t.loop||e.virtual&&e.params.virtual.enabled)){e.recalcSlides();var i=[];e.slides.forEach((function(e){var t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;i[t]=e})),e.slides.forEach((function(e){e.removeAttribute("data-swiper-slide-index")})),i.forEach((function(e){a.append(e)})),e.recalcSlides(),e.slideTo(e.realIndex,0)}}};function Be(e,t,a){var i=re(),n=e.params,r=n.edgeSwipeDetection,o=n.edgeSwipeThreshold;return!r||!(a<=o||a>=i.innerWidth-o)||"prevent"===r&&(t.preventDefault(),!0)}function ze(e){var t=this,a=ee(),i=e;i.originalEvent&&(i=i.originalEvent);var n=t.touchEventsData;if("pointerdown"===i.type){if(null!==n.pointerId&&n.pointerId!==i.pointerId)return;n.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(n.touchId=i.targetTouches[0].identifier);if("touchstart"!==i.type){var r=t.params,o=t.touches;if(t.enabled&&(r.simulateTouch||"mouse"!==i.pointerType)&&(!t.animating||!r.preventInteractionOnTransition)){!t.animating&&r.cssMode&&r.loop&&t.loopFix();var s,l,c,d=i.target;if("wrapper"!==r.touchEventsTarget||(s=d,l=t.wrapperEl,!(c=l.contains(s))&&l instanceof HTMLSlotElement?h(l.assignedElements()).includes(s):c))if(!("which"in i&&3===i.which||"button"in i&&i.button>0||n.isTouched&&n.isMoved)){var u=!!r.noSwipingClass&&""!==r.noSwipingClass,p=i.composedPath?i.composedPath():i.path;u&&i.target&&i.target.shadowRoot&&p&&(d=p[0]);var f=r.noSwipingSelector?r.noSwipingSelector:".".concat(r.noSwipingClass),m=!(!i.target||!i.target.shadowRoot);if(r.noSwiping&&(m?function(e,t){return void 0===t&&(t=this),function t(a){if(!a||a===ee()||a===re())return null;a.assignedSlot&&(a=a.assignedSlot);var i=a.closest(e);return i||a.getRootNode?i||t(a.getRootNode().host):null}(t)}(f,d):d.closest(f)))t.allowClick=!0;else if(!r.swipeHandler||d.closest(r.swipeHandler)){o.currentX=i.pageX,o.currentY=i.pageY;var v=o.currentX,g=o.currentY;if(Be(t,i,v)){Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=v,o.startY=g,n.touchStartTime=se(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);var w=!0;d.matches(n.focusableElements)&&(w=!1,"SELECT"===d.nodeName&&(n.isTouched=!1)),a.activeElement&&a.activeElement.matches(n.focusableElements)&&a.activeElement!==d&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!d.matches(n.focusableElements))&&a.activeElement.blur();var y=w&&t.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!y||d.isContentEditable||i.preventDefault(),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.animating&&!r.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",i)}}}}}else Be(t,i,i.targetTouches[0].pageX)}function Ge(e){var t=ee(),a=this,i=a.touchEventsData,n=a.params,r=a.touches,o=a.rtlTranslate;if(a.enabled&&(n.simulateTouch||"mouse"!==e.pointerType)){var s,l=e;if(l.originalEvent&&(l=l.originalEvent),"pointermove"===l.type){if(null!==i.touchId)return;if(l.pointerId!==i.pointerId)return}if("touchmove"===l.type){if(!(s=h(l.changedTouches).filter((function(e){return e.identifier===i.touchId}))[0])||s.identifier!==i.touchId)return}else s=l;if(i.isTouched){var c=s.pageX,d=s.pageY;if(l.preventedByNestedSwiper)return r.startX=c,void(r.startY=d);if(!a.allowTouchMove)return l.target.matches(i.focusableElements)||(a.allowClick=!1),void(i.isTouched&&(Object.assign(r,{startX:c,startY:d,currentX:c,currentY:d}),i.touchStartTime=se()));if(n.touchReleaseOnEdges&&!n.loop)if(a.isVertical()){if(d<r.startY&&a.translate<=a.maxTranslate()||d>r.startY&&a.translate>=a.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else if(c<r.startX&&a.translate<=a.maxTranslate()||c>r.startX&&a.translate>=a.minTranslate())return;if(t.activeElement&&t.activeElement.matches(i.focusableElements)&&t.activeElement!==l.target&&"mouse"!==l.pointerType&&t.activeElement.blur(),t.activeElement&&l.target===t.activeElement&&l.target.matches(i.focusableElements))return i.isMoved=!0,void(a.allowClick=!1);i.allowTouchCallbacks&&a.emit("touchMove",l),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=c,r.currentY=d;var u=r.currentX-r.startX,p=r.currentY-r.startY;if(!(a.params.threshold&&Math.sqrt(Math.pow(u,2)+Math.pow(p,2))<a.params.threshold)){var f;if(void 0===i.isScrolling)a.isHorizontal()&&r.currentY===r.startY||a.isVertical()&&r.currentX===r.startX?i.isScrolling=!1:u*u+p*p>=25&&(f=180*Math.atan2(Math.abs(p),Math.abs(u))/Math.PI,i.isScrolling=a.isHorizontal()?f>n.touchAngle:90-f>n.touchAngle);if(i.isScrolling&&a.emit("touchMoveOpposite",l),void 0===i.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(i.startMoving=!0)),i.isScrolling||"touchmove"===l.type&&i.preventTouchMoveFromPointerMove)i.isTouched=!1;else if(i.startMoving){a.allowClick=!1,!n.cssMode&&l.cancelable&&l.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&l.stopPropagation();var m=a.isHorizontal()?u:p,v=a.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(m=Math.abs(m)*(o?1:-1),v=Math.abs(v)*(o?1:-1)),r.diff=m,m*=n.touchRatio,o&&(m=-m,v=-v);var g=a.touchesDirection;a.swipeDirection=m>0?"prev":"next",a.touchesDirection=v>0?"prev":"next";var w=a.params.loop&&!n.cssMode,y="next"===a.touchesDirection&&a.allowSlideNext||"prev"===a.touchesDirection&&a.allowSlidePrev;if(!i.isMoved){if(w&&y&&a.loopFix({direction:a.swipeDirection}),i.startTranslate=a.getTranslate(),a.setTransition(0),a.animating){var b=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});a.wrapperEl.dispatchEvent(b)}i.allowMomentumBounce=!1,!n.grabCursor||!0!==a.allowSlideNext&&!0!==a.allowSlidePrev||a.setGrabCursor(!0),a.emit("sliderFirstMove",l)}if((new Date).getTime(),i.isMoved&&i.allowThresholdMove&&g!==a.touchesDirection&&w&&y&&Math.abs(m)>=1)return Object.assign(r,{startX:c,startY:d,currentX:c,currentY:d,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,void(i.startTranslate=i.currentTranslate);a.emit("sliderMove",l),i.isMoved=!0,i.currentTranslate=m+i.startTranslate;var x=!0,E=n.resistanceRatio;if(n.touchReleaseOnEdges&&(E=0),m>0?(w&&y&&i.allowThresholdMove&&i.currentTranslate>(n.centeredSlides?a.minTranslate()-a.slidesSizesGrid[a.activeIndex+1]-("auto"!==n.slidesPerView&&a.slides.length-n.slidesPerView>=2?a.slidesSizesGrid[a.activeIndex+1]+a.params.spaceBetween:0)-a.params.spaceBetween:a.minTranslate())&&a.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>a.minTranslate()&&(x=!1,n.resistance&&(i.currentTranslate=a.minTranslate()-1+Math.pow(-a.minTranslate()+i.startTranslate+m,E)))):m<0&&(w&&y&&i.allowThresholdMove&&i.currentTranslate<(n.centeredSlides?a.maxTranslate()+a.slidesSizesGrid[a.slidesSizesGrid.length-1]+a.params.spaceBetween+("auto"!==n.slidesPerView&&a.slides.length-n.slidesPerView>=2?a.slidesSizesGrid[a.slidesSizesGrid.length-1]+a.params.spaceBetween:0):a.maxTranslate())&&a.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:a.slides.length-("auto"===n.slidesPerView?a.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),i.currentTranslate<a.maxTranslate()&&(x=!1,n.resistance&&(i.currentTranslate=a.maxTranslate()+1-Math.pow(a.maxTranslate()-i.startTranslate-m,E)))),x&&(l.preventedByNestedSwiper=!0),!a.allowSlideNext&&"next"===a.swipeDirection&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!a.allowSlidePrev&&"prev"===a.swipeDirection&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),a.allowSlidePrev||a.allowSlideNext||(i.currentTranslate=i.startTranslate),n.threshold>0){if(!(Math.abs(m)>n.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,i.currentTranslate=i.startTranslate,void(r.diff=a.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&a.freeMode||n.watchSlidesProgress)&&(a.updateActiveIndex(),a.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&a.freeMode&&a.freeMode.onTouchMove(),a.updateProgress(i.currentTranslate),a.setTranslate(i.currentTranslate))}}}else i.startMoving&&i.isScrolling&&a.emit("touchMoveOpposite",l)}}function Re(e){var t,a=this,i=a.touchEventsData,n=e;if(n.originalEvent&&(n=n.originalEvent),"touchend"===n.type||"touchcancel"===n.type){if(!(t=h(n.changedTouches).filter((function(e){return e.identifier===i.touchId}))[0])||t.identifier!==i.touchId)return}else{if(null!==i.touchId)return;if(n.pointerId!==i.pointerId)return;t=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)&&!(["pointercancel","contextmenu"].includes(n.type)&&(a.browser.isSafari||a.browser.isWebView)))return;i.pointerId=null,i.touchId=null;var r=a.params,o=a.touches,s=a.rtlTranslate,l=a.slidesGrid;if(a.enabled&&(r.simulateTouch||"mouse"!==n.pointerType)){if(i.allowTouchCallbacks&&a.emit("touchEnd",n),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&r.grabCursor&&a.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);r.grabCursor&&i.isMoved&&i.isTouched&&(!0===a.allowSlideNext||!0===a.allowSlidePrev)&&a.setGrabCursor(!1);var c,d=se(),u=d-i.touchStartTime;if(a.allowClick){var p=n.path||n.composedPath&&n.composedPath();a.updateClickedSlide(p&&p[0]||n.target,p),a.emit("tap click",n),u<300&&d-i.lastClickTime<300&&a.emit("doubleTap doubleClick",n)}if(i.lastClickTime=se(),oe((function(){a.destroyed||(a.allowClick=!0)})),!i.isTouched||!i.isMoved||!a.swipeDirection||0===o.diff&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,c=r.followFinger?s?a.translate:-a.translate:-i.currentTranslate,!r.cssMode)if(r.freeMode&&r.freeMode.enabled)a.freeMode.onTouchEnd({currentPos:c});else{for(var f=c>=-a.maxTranslate()&&!a.params.loop,m=0,v=a.slidesSizesGrid[0],g=0;g<l.length;g+=g<r.slidesPerGroupSkip?1:r.slidesPerGroup){var w=g<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==l[g+w]?(f||c>=l[g]&&c<l[g+w])&&(m=g,v=l[g+w]-l[g]):(f||c>=l[g])&&(m=g,v=l[l.length-1]-l[l.length-2])}var y=null,b=null;r.rewind&&(a.isBeginning?b=r.virtual&&r.virtual.enabled&&a.virtual?a.virtual.slides.length-1:a.slides.length-1:a.isEnd&&(y=0));var x=(c-l[m])/v,E=m<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(u>r.longSwipesMs){if(!r.longSwipes)return void a.slideTo(a.activeIndex);"next"===a.swipeDirection&&(x>=r.longSwipesRatio?a.slideTo(r.rewind&&a.isEnd?y:m+E):a.slideTo(m)),"prev"===a.swipeDirection&&(x>1-r.longSwipesRatio?a.slideTo(m+E):null!==b&&x<0&&Math.abs(x)>r.longSwipesRatio?a.slideTo(b):a.slideTo(m))}else{if(!r.shortSwipes)return void a.slideTo(a.activeIndex);a.navigation&&(n.target===a.navigation.nextEl||n.target===a.navigation.prevEl)?n.target===a.navigation.nextEl?a.slideTo(m+E):a.slideTo(m):("next"===a.swipeDirection&&a.slideTo(null!==y?y:m+E),"prev"===a.swipeDirection&&a.slideTo(null!==b?b:m))}}}}function qe(){var e=this,t=e.params,a=e.el;if(!a||0!==a.offsetWidth){t.breakpoints&&e.setBreakpoint();var i=e.allowSlideNext,n=e.allowSlidePrev,r=e.snapGrid,o=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();var s=o&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||s?e.params.loop&&!o?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout((function(){e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()}),500)),e.allowSlidePrev=n,e.allowSlideNext=i,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}}function He(e){var t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function $e(){var e=this,t=e.wrapperEl,a=e.rtlTranslate;if(e.enabled){e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();var i=e.maxTranslate()-e.minTranslate();(0===i?0:(e.translate-e.minTranslate())/i)!==e.progress&&e.updateProgress(a?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}}function Fe(e){var t=this;je(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function Ve(){var e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}var Ue=function(e,t){var a=ee(),i=e.params,n=e.el,r=e.wrapperEl,o=e.device,s=!!i.nested,l="on"===t?"addEventListener":"removeEventListener",c=t;n&&"string"!=typeof n&&(a[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:s}),n[l]("touchstart",e.onTouchStart,{passive:!1}),n[l]("pointerdown",e.onTouchStart,{passive:!1}),a[l]("touchmove",e.onTouchMove,{passive:!1,capture:s}),a[l]("pointermove",e.onTouchMove,{passive:!1,capture:s}),a[l]("touchend",e.onTouchEnd,{passive:!0}),a[l]("pointerup",e.onTouchEnd,{passive:!0}),a[l]("pointercancel",e.onTouchEnd,{passive:!0}),a[l]("touchcancel",e.onTouchEnd,{passive:!0}),a[l]("pointerout",e.onTouchEnd,{passive:!0}),a[l]("pointerleave",e.onTouchEnd,{passive:!0}),a[l]("contextmenu",e.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&n[l]("click",e.onClick,!0),i.cssMode&&r[l]("scroll",e.onScroll),i.updateOnWindowResize?e[c](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",qe,!0):e[c]("observerUpdate",qe,!0),n[l]("load",e.onLoad,{capture:!0}))};var We=function(e,t){return e.grid&&t.grid&&t.grid.rows>1};var Qe={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Xe(e,t){return function(a){void 0===a&&(a={});var i=Object.keys(a)[0],n=a[i];"object"===m(n)&&null!==n?(!0===e[i]&&(e[i]={enabled:!0}),"navigation"===i&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),i in e&&"enabled"in n?("object"!==m(e[i])||"enabled"in e[i]||(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),de(t,a)):de(t,a)):de(t,a)}}var Ye={eventsEmitter:Ce,update:Oe,translate:Ae,transition:{setTransition:function(e,t){var a=this;a.params.cssMode||(a.wrapperEl.style.transitionDuration="".concat(e,"ms"),a.wrapperEl.style.transitionDelay=0===e?"0ms":""),a.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);var a=this,i=a.params;i.cssMode||(i.autoHeight&&a.updateAutoHeight(),Le({swiper:a,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);var a=this,i=a.params;a.animating=!1,i.cssMode||(a.setTransition(0),Le({swiper:a,runCallbacks:e,direction:t,step:"End"}))}},slide:Ne,loop:De,grabCursor:{setGrabCursor:function(e){var t=this;if(!(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)){var a="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),a.style.cursor="move",a.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame((function(){t.__preventObserver__=!1}))}},unsetGrabCursor:function(){var e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame((function(){e.__preventObserver__=!1})))}},events:{attachEvents:function(){var e=this,t=e.params;e.onTouchStart=ze.bind(e),e.onTouchMove=Ge.bind(e),e.onTouchEnd=Re.bind(e),e.onDocumentTouchStart=Ve.bind(e),t.cssMode&&(e.onScroll=$e.bind(e)),e.onClick=He.bind(e),e.onLoad=Fe.bind(e),Ue(e,"on")},detachEvents:function(){Ue(this,"off")}},breakpoints:{setBreakpoint:function(){var e=this,t=e.realIndex,a=e.initialized,i=e.params,n=e.el,r=i.breakpoints;if(r&&(!r||0!==Object.keys(r).length)){var o=e.getBreakpoint(r,e.params.breakpointsBase,e.el);if(o&&e.currentBreakpoint!==o){var s=(o in r?r[o]:void 0)||e.originalParams,l=We(e,i),c=We(e,s),d=e.params.grabCursor,u=s.grabCursor,p=i.enabled;l&&!c?(n.classList.remove("".concat(i.containerModifierClass,"grid"),"".concat(i.containerModifierClass,"grid-column")),e.emitContainerClasses()):!l&&c&&(n.classList.add("".concat(i.containerModifierClass,"grid")),(s.grid.fill&&"column"===s.grid.fill||!s.grid.fill&&"column"===i.grid.fill)&&n.classList.add("".concat(i.containerModifierClass,"grid-column")),e.emitContainerClasses()),d&&!u?e.unsetGrabCursor():!d&&u&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach((function(t){if(void 0!==s[t]){var a=i[t]&&i[t].enabled,n=s[t]&&s[t].enabled;a&&!n&&e[t].disable(),!a&&n&&e[t].enable()}}));var h=s.direction&&s.direction!==i.direction,f=i.loop&&(s.slidesPerView!==i.slidesPerView||h),m=i.loop;h&&a&&e.changeDirection(),de(e.params,s);var v=e.params.enabled,g=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),p&&!v?e.disable():!p&&v&&e.enable(),e.currentBreakpoint=o,e.emit("_beforeBreakpoint",s),a&&(f?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!m&&g?(e.loopCreate(t),e.updateSlides()):m&&!g&&e.loopDestroy()),e.emit("breakpoint",s)}}},getBreakpoint:function(e,t,a){if(void 0===t&&(t="window"),e&&("container"!==t||a)){var i=!1,n=re(),r="window"===t?n.innerHeight:a.clientHeight,o=Object.keys(e).map((function(e){if("string"==typeof e&&0===e.indexOf("@")){var t=parseFloat(e.substr(1));return{value:r*t,point:e}}return{value:e,point:e}}));o.sort((function(e,t){return parseInt(e.value,10)-parseInt(t.value,10)}));for(var s=0;s<o.length;s+=1){var l=o[s],c=l.point,d=l.value;"window"===t?n.matchMedia("(min-width: ".concat(d,"px)")).matches&&(i=c):d<=a.clientWidth&&(i=c)}return i||"max"}}},checkOverflow:{checkOverflow:function(){var e=this,t=e.isLocked,a=e.params,i=a.slidesOffsetBefore;if(i){var n=e.slides.length-1,r=e.slidesGrid[n]+e.slidesSizesGrid[n]+2*i;e.isLocked=e.size>r}else e.isLocked=1===e.snapGrid.length;!0===a.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===a.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){var e,t,a,i,n=this,r=n.classNames,o=n.params,s=n.rtl,l=n.el,c=n.device,d=(t=["initialized",o.direction,{"free-mode":n.params.freeMode&&o.freeMode.enabled},{autoheight:o.autoHeight},{rtl:s},{grid:o.grid&&o.grid.rows>1},{"grid-column":o.grid&&o.grid.rows>1&&"column"===o.grid.fill},{android:c.android},{ios:c.ios},{"css-mode":o.cssMode},{centered:o.cssMode&&o.centeredSlides},{"watch-progress":o.watchSlidesProgress}],a=o.containerModifierClass,i=[],t.forEach((function(e){"object"===m(e)?Object.keys(e).forEach((function(t){e[t]&&i.push(a+t)})):"string"==typeof e&&i.push(a+e)})),i);r.push.apply(r,h(d)),(e=l.classList).add.apply(e,h(r)),n.emitContainerClasses()},removeClasses:function(){var e,t=this,a=t.el,i=t.classNames;a&&"string"!=typeof a&&((e=a.classList).remove.apply(e,h(i)),t.emitContainerClasses())}}},Ke={},Ze=function(){function e(){var t,a;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];1===n.length&&n[0].constructor&&"Object"===Object.prototype.toString.call(n[0]).slice(8,-1)?a=n[0]:(t=n[0],a=n[1]),a||(a={}),a=de({},a),t&&!a.el&&(a.el=t);var o=ee();if(a.el&&"string"==typeof a.el&&o.querySelectorAll(a.el).length>1){var s=[];return o.querySelectorAll(a.el).forEach((function(t){var i=de({},a,{el:t});s.push(new e(i))})),s}var l,c=this;(c.__swiper__=!0,c.support=Te(),c.device=_e({userAgent:a.userAgent}),c.browser=Se(),c.eventsListeners={},c.eventsAnyListeners=[],c.modules=h(c.__modules__),a.modules&&Array.isArray(a.modules))&&(l=c.modules).push.apply(l,h(a.modules));var d={};c.modules.forEach((function(e){e({params:a,swiper:c,extendParams:Xe(a,d),on:c.on.bind(c),once:c.once.bind(c),off:c.off.bind(c),emit:c.emit.bind(c)})}));var u=de({},Qe,d);return c.params=de({},u,Ke,a),c.originalParams=de({},c.params),c.passedParams=de({},a),c.params&&c.params.on&&Object.keys(c.params.on).forEach((function(e){c.on(e,c.params.on[e])})),c.params&&c.params.onAny&&c.onAny(c.params.onAny),Object.assign(c,{enabled:c.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return"horizontal"===c.params.direction},isVertical:function(){return"vertical"===c.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment:function(){return Math.trunc(this.translate/Math.pow(2,23))*Math.pow(2,23)},allowSlideNext:c.params.allowSlideNext,allowSlidePrev:c.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:c.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:c.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),c.emit("_swiper"),c.params.init&&c.init(),c}return function(e,t,a){return t&&s(e.prototype,t),a&&s(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}(e,[{key:"getDirectionLabel",value:function(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}},{key:"getSlideIndex",value:function(e){var t=this.slidesEl,a=this.params,i=we(fe(t,".".concat(a.slideClass,", swiper-slide"))[0]);return we(e)-i}},{key:"getSlideIndexByData",value:function(e){return this.getSlideIndex(this.slides.filter((function(t){return 1*t.getAttribute("data-swiper-slide-index")===e}))[0])}},{key:"recalcSlides",value:function(){var e=this,t=e.slidesEl,a=e.params;e.slides=fe(t,".".concat(a.slideClass,", swiper-slide"))}},{key:"enable",value:function(){var e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}},{key:"disable",value:function(){var e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}},{key:"setProgress",value:function(e,t){var a=this;e=Math.min(Math.max(e,0),1);var i=a.minTranslate(),n=(a.maxTranslate()-i)*e+i;a.translateTo(n,void 0===t?0:t),a.updateActiveIndex(),a.updateSlidesClasses()}},{key:"emitContainerClasses",value:function(){var e=this;if(e.params._emitClasses&&e.el){var t=e.el.className.split(" ").filter((function(t){return 0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)}));e.emit("_containerClasses",t.join(" "))}}},{key:"getSlideClasses",value:function(e){var t=this;return t.destroyed?"":e.className.split(" ").filter((function(e){return 0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)})).join(" ")}},{key:"emitSlidesClasses",value:function(){var e=this;if(e.params._emitClasses&&e.el){var t=[];e.slides.forEach((function(a){var i=e.getSlideClasses(a);t.push({slideEl:a,classNames:i}),e.emit("_slideClass",a,i)})),e.emit("_slideClasses",t)}}},{key:"slidesPerViewDynamic",value:function(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);var a=this,i=a.params,n=a.slides,r=a.slidesGrid,o=a.slidesSizesGrid,s=a.size,l=a.activeIndex,c=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){for(var d,u=n[l]?Math.ceil(n[l].swiperSlideSize):0,p=l+1;p<n.length;p+=1)n[p]&&!d&&(c+=1,(u+=Math.ceil(n[p].swiperSlideSize))>s&&(d=!0));for(var h=l-1;h>=0;h-=1)n[h]&&!d&&(c+=1,(u+=n[h].swiperSlideSize)>s&&(d=!0))}else if("current"===e)for(var f=l+1;f<n.length;f+=1){(t?r[f]+o[f]-r[l]<s:r[f]-r[l]<s)&&(c+=1)}else for(var m=l-1;m>=0;m-=1){r[l]-r[m]<s&&(c+=1)}return c}},{key:"update",value:function(){var e=this;if(e&&!e.destroyed){var t,a=e.snapGrid,i=e.params;if(i.breakpoints&&e.setBreakpoint(),h(e.el.querySelectorAll('[loading="lazy"]')).forEach((function(t){t.complete&&je(e,t)})),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),i.freeMode&&i.freeMode.enabled&&!i.cssMode)r(),i.autoHeight&&e.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){var n=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;t=e.slideTo(n.length-1,0,!1,!0)}else t=e.slideTo(e.activeIndex,0,!1,!0);t||r()}i.watchOverflow&&a!==e.snapGrid&&e.checkOverflow(),e.emit("update")}function r(){var t=e.rtlTranslate?-1*e.translate:e.translate,a=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}}},{key:"changeDirection",value:function(e,t){void 0===t&&(t=!0);var a=this,i=a.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(a.el.classList.remove("".concat(a.params.containerModifierClass).concat(i)),a.el.classList.add("".concat(a.params.containerModifierClass).concat(e)),a.emitContainerClasses(),a.params.direction=e,a.slides.forEach((function(t){"vertical"===e?t.style.width="":t.style.height=""})),a.emit("changeDirection"),t&&a.update()),a}},{key:"changeLanguageDirection",value:function(e){var t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="rtl"):(t.el.classList.remove("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="ltr"),t.update())}},{key:"mount",value:function(e){var t=this;if(t.mounted)return!0;var a=e||t.params.el;if("string"==typeof a&&(a=document.querySelector(a)),!a)return!1;a.swiper=t,a.parentNode&&a.parentNode.host&&a.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);var i=function(){return".".concat((t.params.wrapperClass||"").trim().split(" ").join("."))},n=a&&a.shadowRoot&&a.shadowRoot.querySelector?a.shadowRoot.querySelector(i()):fe(a,i())[0];return!n&&t.params.createElements&&(n=ve("div",t.params.wrapperClass),a.append(n),fe(a,".".concat(t.params.slideClass)).forEach((function(e){n.append(e)}))),Object.assign(t,{el:a,wrapperEl:n,slidesEl:t.isElement&&!a.parentNode.host.slideSlots?a.parentNode.host:n,hostEl:t.isElement?a.parentNode.host:a,mounted:!0,rtl:"rtl"===a.dir.toLowerCase()||"rtl"===ge(a,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===a.dir.toLowerCase()||"rtl"===ge(a,"direction")),wrongRTL:"-webkit-box"===ge(n,"display")}),!0}},{key:"init",value:function(e){var t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();var a=h(t.el.querySelectorAll('[loading="lazy"]'));return t.isElement&&a.push.apply(a,h(t.hostEl.querySelectorAll('[loading="lazy"]'))),a.forEach((function(e){e.complete?je(t,e):e.addEventListener("load",(function(e){je(t,e.target)}))})),Me(t),t.initialized=!0,Me(t),t.emit("init"),t.emit("afterInit"),t}},{key:"destroy",value:function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var a,i=this,n=i.params,r=i.el,o=i.wrapperEl,s=i.slides;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),n.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),o&&o.removeAttribute("style"),s&&s.length&&s.forEach((function(e){e.classList.remove(n.slideVisibleClass,n.slideFullyVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}))),i.emit("destroy"),Object.keys(i.eventsListeners).forEach((function(e){i.off(e)})),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),a=i,Object.keys(a).forEach((function(e){try{a[e]=null}catch(e){}try{delete a[e]}catch(e){}}))),i.destroyed=!0),null}}],[{key:"extendDefaults",value:function(e){de(Ke,e)}},{key:"extendedDefaults",get:function(){return Ke}},{key:"defaults",get:function(){return Qe}},{key:"installModule",value:function(t){e.prototype.__modules__||(e.prototype.__modules__=[]);var a=e.prototype.__modules__;"function"==typeof t&&a.indexOf(t)<0&&a.push(t)}},{key:"use",value:function(t){return Array.isArray(t)?(t.forEach((function(t){return e.installModule(t)})),e):(e.installModule(t),e)}}])}();function Je(e,t,a,i){return e.params.createElements&&Object.keys(i).forEach((function(n){if(!a[n]&&!0===a.auto){var r=fe(e.el,".".concat(i[n]))[0];r||((r=ve("div",i[n])).className=i[n],e.el.append(r)),a[n]=r,t[n]=r}})),a}function et(e){var t=e.swiper,a=e.extendParams,i=e.on,n=e.emit;function r(e){var a;return e&&"string"==typeof e&&t.isElement&&(a=t.el.querySelector(e)||t.hostEl.querySelector(e))?a:(e&&("string"==typeof e&&(a=h(document.querySelectorAll(e))),t.params.uniqueNavElements&&"string"==typeof e&&a&&a.length>1&&1===t.el.querySelectorAll(e).length?a=t.el.querySelector(e):a&&1===a.length&&(a=a[0])),e&&!a?e:a)}function o(e,a){var i=t.params.navigation;(e=xe(e)).forEach((function(e){var n;e&&((n=e.classList)[a?"add":"remove"].apply(n,h(i.disabledClass.split(" "))),"BUTTON"===e.tagName&&(e.disabled=a),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](i.lockClass))}))}function s(){var e=t.navigation,a=e.nextEl,i=e.prevEl;if(t.params.loop)return o(i,!1),void o(a,!1);o(i,t.isBeginning&&!t.params.rewind),o(a,t.isEnd&&!t.params.rewind)}function l(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),n("navigationPrev"))}function c(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),n("navigationNext"))}function d(){var e=t.params.navigation;if(t.params.navigation=Je(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),e.nextEl||e.prevEl){var a=r(e.nextEl),i=r(e.prevEl);Object.assign(t.navigation,{nextEl:a,prevEl:i}),a=xe(a),i=xe(i);var n=function(a,i){var n;(a&&a.addEventListener("click","next"===i?c:l),!t.enabled&&a)&&(n=a.classList).add.apply(n,h(e.lockClass.split(" ")))};a.forEach((function(e){return n(e,"next")})),i.forEach((function(e){return n(e,"prev")}))}}function u(){var e=t.navigation,a=e.nextEl,i=e.prevEl;a=xe(a),i=xe(i);var n=function(e,a){var i;e.removeEventListener("click","next"===a?c:l),(i=e.classList).remove.apply(i,h(t.params.navigation.disabledClass.split(" ")))};a.forEach((function(e){return n(e,"next")})),i.forEach((function(e){return n(e,"prev")}))}a({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},i("init",(function(){!1===t.params.navigation.enabled?p():(d(),s())})),i("toEdge fromEdge lock unlock",(function(){s()})),i("destroy",(function(){u()})),i("enable disable",(function(){var e=t.navigation,a=e.nextEl,i=e.prevEl;a=xe(a),i=xe(i),t.enabled?s():[].concat(h(a),h(i)).filter((function(e){return!!e})).forEach((function(e){return e.classList.add(t.params.navigation.lockClass)}))})),i("click",(function(e,a){var i=t.navigation,r=i.nextEl,o=i.prevEl;r=xe(r),o=xe(o);var s=a.target,l=o.includes(s)||r.includes(s);if(t.isElement&&!l){var c=a.path||a.composedPath&&a.composedPath();c&&(l=c.find((function(e){return r.includes(e)||o.includes(e)})))}if(t.params.navigation.hideOnClick&&!l){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===s||t.pagination.el.contains(s)))return;var d;r.length?d=r[0].classList.contains(t.params.navigation.hiddenClass):o.length&&(d=o[0].classList.contains(t.params.navigation.hiddenClass)),n(!0===d?"navigationShow":"navigationHide"),[].concat(h(r),h(o)).filter((function(e){return!!e})).forEach((function(e){return e.classList.toggle(t.params.navigation.hiddenClass)}))}}));var p=function(){var e;(e=t.el.classList).add.apply(e,h(t.params.navigation.navigationDisabledClass.split(" "))),u()};Object.assign(t.navigation,{enable:function(){var e;(e=t.el.classList).remove.apply(e,h(t.params.navigation.navigationDisabledClass.split(" "))),d(),s()},disable:p,update:s,init:d,destroy:u})}function tt(e){return void 0===e&&(e=""),".".concat(e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,"."))}function at(e){var t,a=e.swiper,i=e.extendParams,n=e.on,r=e.emit,o="swiper-pagination";i({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:"".concat(o,"-bullet"),bulletActiveClass:"".concat(o,"-bullet-active"),modifierClass:"".concat(o,"-"),currentClass:"".concat(o,"-current"),totalClass:"".concat(o,"-total"),hiddenClass:"".concat(o,"-hidden"),progressbarFillClass:"".concat(o,"-progressbar-fill"),progressbarOppositeClass:"".concat(o,"-progressbar-opposite"),clickableClass:"".concat(o,"-clickable"),lockClass:"".concat(o,"-lock"),horizontalClass:"".concat(o,"-horizontal"),verticalClass:"".concat(o,"-vertical"),paginationDisabledClass:"".concat(o,"-disabled")}}),a.pagination={el:null,bullets:[]};var s=0;function l(){return!a.params.pagination.el||!a.pagination.el||Array.isArray(a.pagination.el)&&0===a.pagination.el.length}function c(e,t){var i=a.params.pagination.bulletActiveClass;e&&(e=e["".concat("prev"===t?"previous":"next","ElementSibling")])&&(e.classList.add("".concat(i,"-").concat(t)),(e=e["".concat("prev"===t?"previous":"next","ElementSibling")])&&e.classList.add("".concat(i,"-").concat(t,"-").concat(t)))}function d(e){var t=e.target.closest(tt(a.params.pagination.bulletClass));if(t){e.preventDefault();var i,n,r,o=we(t)*a.params.slidesPerGroup;if(a.params.loop){if(a.realIndex===o)return;var s=(i=a.realIndex,n=o,r=a.slides.length,(n%=r)==1+(i%=r)?"next":n===i-1?"previous":void 0);"next"===s?a.slideNext():"previous"===s?a.slidePrev():a.slideToLoop(o)}else a.slideTo(o)}}function u(){var e=a.rtl,i=a.params.pagination;if(!l()){var n,o,d=a.pagination.el;d=xe(d);var u=a.virtual&&a.params.virtual.enabled?a.virtual.slides.length:a.slides.length,p=a.params.loop?Math.ceil(u/a.params.slidesPerGroup):a.snapGrid.length;if(a.params.loop?(o=a.previousRealIndex||0,n=a.params.slidesPerGroup>1?Math.floor(a.realIndex/a.params.slidesPerGroup):a.realIndex):void 0!==a.snapIndex?(n=a.snapIndex,o=a.previousSnapIndex):(o=a.previousIndex||0,n=a.activeIndex||0),"bullets"===i.type&&a.pagination.bullets&&a.pagination.bullets.length>0){var f,m,v,g=a.pagination.bullets;if(i.dynamicBullets&&(t=be(g[0],a.isHorizontal()?"width":"height"),d.forEach((function(e){e.style[a.isHorizontal()?"width":"height"]="".concat(t*(i.dynamicMainBullets+4),"px")})),i.dynamicMainBullets>1&&void 0!==o&&((s+=n-(o||0))>i.dynamicMainBullets-1?s=i.dynamicMainBullets-1:s<0&&(s=0)),f=Math.max(n-s,0),v=((m=f+(Math.min(g.length,i.dynamicMainBullets)-1))+f)/2),g.forEach((function(e){var t,a=h(["","-next","-next-next","-prev","-prev-prev","-main"].map((function(e){return"".concat(i.bulletActiveClass).concat(e)}))).map((function(e){return"string"==typeof e&&e.includes(" ")?e.split(" "):e})).flat();(t=e.classList).remove.apply(t,h(a))})),d.length>1)g.forEach((function(e){var t,r=we(e);r===n?(t=e.classList).add.apply(t,h(i.bulletActiveClass.split(" "))):a.isElement&&e.setAttribute("part","bullet");if(i.dynamicBullets){var o;if(r>=f&&r<=m)(o=e.classList).add.apply(o,h("".concat(i.bulletActiveClass,"-main").split(" ")));r===f&&c(e,"prev"),r===m&&c(e,"next")}}));else{var w,y=g[n];if(y)(w=y.classList).add.apply(w,h(i.bulletActiveClass.split(" ")));if(a.isElement&&g.forEach((function(e,t){e.setAttribute("part",t===n?"bullet-active":"bullet")})),i.dynamicBullets){for(var b=g[f],x=g[m],E=f;E<=m;E+=1){var T;if(g[E])(T=g[E].classList).add.apply(T,h("".concat(i.bulletActiveClass,"-main").split(" ")))}c(b,"prev"),c(x,"next")}}if(i.dynamicBullets){var _=Math.min(g.length,i.dynamicMainBullets+4),S=(t*_-t)/2-v*t,C=e?"right":"left";g.forEach((function(e){e.style[a.isHorizontal()?C:"top"]="".concat(S,"px")}))}}d.forEach((function(e,t){if("fraction"===i.type&&(e.querySelectorAll(tt(i.currentClass)).forEach((function(e){e.textContent=i.formatFractionCurrent(n+1)})),e.querySelectorAll(tt(i.totalClass)).forEach((function(e){e.textContent=i.formatFractionTotal(p)}))),"progressbar"===i.type){var o;o=i.progressbarOpposite?a.isHorizontal()?"vertical":"horizontal":a.isHorizontal()?"horizontal":"vertical";var s=(n+1)/p,l=1,c=1;"horizontal"===o?l=s:c=s,e.querySelectorAll(tt(i.progressbarFillClass)).forEach((function(e){e.style.transform="translate3d(0,0,0) scaleX(".concat(l,") scaleY(").concat(c,")"),e.style.transitionDuration="".concat(a.params.speed,"ms")}))}"custom"===i.type&&i.renderCustom?(e.innerHTML=i.renderCustom(a,n+1,p),0===t&&r("paginationRender",e)):(0===t&&r("paginationRender",e),r("paginationUpdate",e)),a.params.watchOverflow&&a.enabled&&e.classList[a.isLocked?"add":"remove"](i.lockClass)}))}}function p(){var e=a.params.pagination;if(!l()){var t=a.virtual&&a.params.virtual.enabled?a.virtual.slides.length:a.grid&&a.params.grid.rows>1?a.slides.length/Math.ceil(a.params.grid.rows):a.slides.length,i=a.pagination.el;i=xe(i);var n="";if("bullets"===e.type){var o=a.params.loop?Math.ceil(t/a.params.slidesPerGroup):a.snapGrid.length;a.params.freeMode&&a.params.freeMode.enabled&&o>t&&(o=t);for(var s=0;s<o;s+=1)e.renderBullet?n+=e.renderBullet.call(a,s,e.bulletClass):n+="<".concat(e.bulletElement," ").concat(a.isElement?'part="bullet"':"",' class="').concat(e.bulletClass,'"></').concat(e.bulletElement,">")}"fraction"===e.type&&(n=e.renderFraction?e.renderFraction.call(a,e.currentClass,e.totalClass):'<span class="'.concat(e.currentClass,'"></span>')+" / "+'<span class="'.concat(e.totalClass,'"></span>')),"progressbar"===e.type&&(n=e.renderProgressbar?e.renderProgressbar.call(a,e.progressbarFillClass):'<span class="'.concat(e.progressbarFillClass,'"></span>')),a.pagination.bullets=[],i.forEach((function(t){var i;("custom"!==e.type&&(t.innerHTML=n||""),"bullets"===e.type)&&(i=a.pagination.bullets).push.apply(i,h(t.querySelectorAll(tt(e.bulletClass))))})),"custom"!==e.type&&r("paginationRender",i[0])}}function f(){a.params.pagination=Je(a,a.originalParams.pagination,a.params.pagination,{el:"swiper-pagination"});var e,t=a.params.pagination;t.el&&("string"==typeof t.el&&a.isElement&&(e=a.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=h(document.querySelectorAll(t.el))),e||(e=t.el),e&&0!==e.length&&(a.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=h(a.el.querySelectorAll(t.el))).length>1&&(e=e.filter((function(e){return ye(e,".swiper")[0]===a.el}))[0]),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(a.pagination,{el:e}),(e=xe(e)).forEach((function(e){var i;"bullets"===t.type&&t.clickable&&(i=e.classList).add.apply(i,h((t.clickableClass||"").split(" ")));e.classList.add(t.modifierClass+t.type),e.classList.add(a.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add("".concat(t.modifierClass).concat(t.type,"-dynamic")),s=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",d),a.enabled||e.classList.add(t.lockClass)}))))}function m(){var e=a.params.pagination;if(!l()){var t=a.pagination.el;t&&(t=xe(t)).forEach((function(t){var i;(t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(a.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable)&&((i=t.classList).remove.apply(i,h((e.clickableClass||"").split(" "))),t.removeEventListener("click",d))})),a.pagination.bullets&&a.pagination.bullets.forEach((function(t){var a;return(a=t.classList).remove.apply(a,h(e.bulletActiveClass.split(" ")))}))}}n("changeDirection",(function(){if(a.pagination&&a.pagination.el){var e=a.params.pagination,t=a.pagination.el;(t=xe(t)).forEach((function(t){t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(a.isHorizontal()?e.horizontalClass:e.verticalClass)}))}})),n("init",(function(){!1===a.params.pagination.enabled?v():(f(),p(),u())})),n("activeIndexChange",(function(){void 0===a.snapIndex&&u()})),n("snapIndexChange",(function(){u()})),n("snapGridLengthChange",(function(){p(),u()})),n("destroy",(function(){m()})),n("enable disable",(function(){var e=a.pagination.el;e&&(e=xe(e)).forEach((function(e){return e.classList[a.enabled?"remove":"add"](a.params.pagination.lockClass)}))})),n("lock unlock",(function(){u()})),n("click",(function(e,t){var i=t.target,n=xe(a.pagination.el);if(a.params.pagination.el&&a.params.pagination.hideOnClick&&n&&n.length>0&&!i.classList.contains(a.params.pagination.bulletClass)){if(a.navigation&&(a.navigation.nextEl&&i===a.navigation.nextEl||a.navigation.prevEl&&i===a.navigation.prevEl))return;var o=n[0].classList.contains(a.params.pagination.hiddenClass);r(!0===o?"paginationShow":"paginationHide"),n.forEach((function(e){return e.classList.toggle(a.params.pagination.hiddenClass)}))}}));var v=function(){a.el.classList.add(a.params.pagination.paginationDisabledClass);var e=a.pagination.el;e&&(e=xe(e)).forEach((function(e){return e.classList.add(a.params.pagination.paginationDisabledClass)})),m()};Object.assign(a.pagination,{enable:function(){a.el.classList.remove(a.params.pagination.paginationDisabledClass);var e=a.pagination.el;e&&(e=xe(e)).forEach((function(e){return e.classList.remove(a.params.pagination.paginationDisabledClass)})),f(),p(),u()},disable:v,render:p,update:u,init:f,destroy:m})}function it(e){var t,a,i=e.swiper,n=e.extendParams,r=e.on,o=e.emit,s=e.params;i.autoplay={running:!1,paused:!1,timeLeft:0},n({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});var l,c,d,u,p,h,f,m,v=s&&s.autoplay?s.autoplay.delay:3e3,g=s&&s.autoplay?s.autoplay.delay:3e3,w=(new Date).getTime();function y(e){i&&!i.destroyed&&i.wrapperEl&&e.target===i.wrapperEl&&(i.wrapperEl.removeEventListener("transitionend",y),m||e.detail&&e.detail.bySwiperTouchMove||S())}var b=function e(){if(!i.destroyed&&i.autoplay.running){i.autoplay.paused?c=!0:c&&(g=l,c=!1);var t=i.autoplay.paused?l:w+g-(new Date).getTime();i.autoplay.timeLeft=t,o("autoplayTimeLeft",t,t/v),a=requestAnimationFrame((function(){e()}))}},x=function e(n){if(!i.destroyed&&i.autoplay.running){cancelAnimationFrame(a),b();var r=void 0===n?i.params.autoplay.delay:n;v=i.params.autoplay.delay,g=i.params.autoplay.delay;var s=function(){var e;if(e=i.virtual&&i.params.virtual.enabled?i.slides.filter((function(e){return e.classList.contains("swiper-slide-active")}))[0]:i.slides[i.activeIndex])return parseInt(e.getAttribute("data-swiper-autoplay"),10)}();!Number.isNaN(s)&&s>0&&void 0===n&&(r=s,v=s,g=s),l=r;var c=i.params.speed,d=function(){i&&!i.destroyed&&(i.params.autoplay.reverseDirection?!i.isBeginning||i.params.loop||i.params.rewind?(i.slidePrev(c,!0,!0),o("autoplay")):i.params.autoplay.stopOnLastSlide||(i.slideTo(i.slides.length-1,c,!0,!0),o("autoplay")):!i.isEnd||i.params.loop||i.params.rewind?(i.slideNext(c,!0,!0),o("autoplay")):i.params.autoplay.stopOnLastSlide||(i.slideTo(0,c,!0,!0),o("autoplay")),i.params.cssMode&&(w=(new Date).getTime(),requestAnimationFrame((function(){e()}))))};return r>0?(clearTimeout(t),t=setTimeout((function(){d()}),r)):requestAnimationFrame((function(){d()})),r}},E=function(){w=(new Date).getTime(),i.autoplay.running=!0,x(),o("autoplayStart")},T=function(){i.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(a),o("autoplayStop")},_=function(e,a){if(!i.destroyed&&i.autoplay.running){clearTimeout(t),e||(f=!0);var n=function(){o("autoplayPause"),i.params.autoplay.waitForTransition?i.wrapperEl.addEventListener("transitionend",y):S()};if(i.autoplay.paused=!0,a)return h&&(l=i.params.autoplay.delay),h=!1,void n();var r=l||i.params.autoplay.delay;l=r-((new Date).getTime()-w),i.isEnd&&l<0&&!i.params.loop||(l<0&&(l=0),n())}},S=function(){i.isEnd&&l<0&&!i.params.loop||i.destroyed||!i.autoplay.running||(w=(new Date).getTime(),f?(f=!1,x(l)):x(),i.autoplay.paused=!1,o("autoplayResume"))},C=function(){if(!i.destroyed&&i.autoplay.running){var e=ee();"hidden"===e.visibilityState&&(f=!0,_(!0)),"visible"===e.visibilityState&&S()}},k=function(e){"mouse"===e.pointerType&&(f=!0,m=!0,i.animating||i.autoplay.paused||_(!0))},P=function(e){"mouse"===e.pointerType&&(m=!1,i.autoplay.paused&&S())};r("init",(function(){i.params.autoplay.enabled&&(i.params.autoplay.pauseOnMouseEnter&&(i.el.addEventListener("pointerenter",k),i.el.addEventListener("pointerleave",P)),ee().addEventListener("visibilitychange",C),E())})),r("destroy",(function(){i.el&&"string"!=typeof i.el&&(i.el.removeEventListener("pointerenter",k),i.el.removeEventListener("pointerleave",P)),ee().removeEventListener("visibilitychange",C),i.autoplay.running&&T()})),r("_freeModeStaticRelease",(function(){(u||f)&&S()})),r("_freeModeNoMomentumRelease",(function(){i.params.autoplay.disableOnInteraction?T():_(!0,!0)})),r("beforeTransitionStart",(function(e,t,a){!i.destroyed&&i.autoplay.running&&(a||!i.params.autoplay.disableOnInteraction?_(!0,!0):T())})),r("sliderFirstMove",(function(){!i.destroyed&&i.autoplay.running&&(i.params.autoplay.disableOnInteraction?T():(d=!0,u=!1,f=!1,p=setTimeout((function(){f=!0,u=!0,_(!0)}),200)))})),r("touchEnd",(function(){if(!i.destroyed&&i.autoplay.running&&d){if(clearTimeout(p),clearTimeout(t),i.params.autoplay.disableOnInteraction)return u=!1,void(d=!1);u&&i.params.cssMode&&S(),u=!1,d=!1}})),r("slideChange",(function(){!i.destroyed&&i.autoplay.running&&(h=!0)})),Object.assign(i.autoplay,{start:E,stop:T,pause:_,resume:S})}function nt(e){var t=e.swiper,a=e.extendParams,i=e.on;a({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});var n=!1,r=!1;function o(){var e=t.thumbs.swiper;if(e&&!e.destroyed){var a,i=e.clickedIndex,n=e.clickedSlide;if(!n||!n.classList.contains(t.params.thumbs.slideThumbActiveClass))if(null!=i)a=e.params.loop?parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10):i,t.params.loop?t.slideToLoop(a):t.slideTo(a)}}function s(){var e=t.params.thumbs;if(n)return!1;n=!0;var a=t.constructor;if(e.swiper instanceof a)t.thumbs.swiper=e.swiper,Object.assign(t.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(t.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper.update();else if(ce(e.swiper)){var i=Object.assign({},e.swiper);Object.assign(i,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper=new a(i),r=!0}return t.thumbs.swiper.el.classList.add(t.params.thumbs.thumbsContainerClass),t.thumbs.swiper.on("tap",o),!0}function l(e){var a=t.thumbs.swiper;if(a&&!a.destroyed){var i="auto"===a.params.slidesPerView?a.slidesPerViewDynamic():a.params.slidesPerView,n=1,r=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(n=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(n=1),n=Math.floor(n),a.slides.forEach((function(e){return e.classList.remove(r)})),a.params.loop||a.params.virtual&&a.params.virtual.enabled)for(var o=0;o<n;o+=1)fe(a.slidesEl,'[data-swiper-slide-index="'.concat(t.realIndex+o,'"]')).forEach((function(e){e.classList.add(r)}));else for(var s=0;s<n;s+=1)a.slides[t.realIndex+s]&&a.slides[t.realIndex+s].classList.add(r);var l=t.params.thumbs.autoScrollOffset,c=l&&!a.params.loop;if(t.realIndex!==a.realIndex||c){var d,u,p=a.activeIndex;if(a.params.loop){var h=a.slides.filter((function(e){return e.getAttribute("data-swiper-slide-index")==="".concat(t.realIndex)}))[0];d=a.slides.indexOf(h),u=t.activeIndex>t.previousIndex?"next":"prev"}else u=(d=t.realIndex)>t.previousIndex?"next":"prev";c&&(d+="next"===u?l:-1*l),a.visibleSlidesIndexes&&a.visibleSlidesIndexes.indexOf(d)<0&&(a.params.centeredSlides?d=d>p?d-Math.floor(i/2)+1:d+Math.floor(i/2)-1:d>p&&a.params.slidesPerGroup,a.slideTo(d,e?0:void 0))}}}t.thumbs={swiper:null},i("beforeInit",(function(){var e=t.params.thumbs;if(e&&e.swiper)if("string"==typeof e.swiper||e.swiper instanceof HTMLElement){var a=ee();requestAnimationFrame((function i(){t.destroyed||(function(){var i="string"==typeof e.swiper?a.querySelector(e.swiper):e.swiper;if(i&&i.swiper)e.swiper=i.swiper,s(),l(!0);else if(i){var n="".concat(t.params.eventsPrefix,"init");i.addEventListener(n,(function a(r){e.swiper=r.detail[0],i.removeEventListener(n,a),s(),l(!0),e.swiper.update(),t.update()}))}return i}()||requestAnimationFrame(i))}))}else s(),l(!0)})),i("slideChange update resize observerUpdate",(function(){l()})),i("setTransition",(function(e,a){var i=t.thumbs.swiper;i&&!i.destroyed&&i.setTransition(a)})),i("beforeDestroy",(function(){var e=t.thumbs.swiper;e&&!e.destroyed&&r&&e.destroy()})),Object.assign(t.thumbs,{init:s,update:l})}function rt(e){var t=this,a=t.params,i=t.slidesEl;a.loop&&t.loopDestroy();var n=function(e){if("string"==typeof e){var t=document.createElement("div");t.innerHTML=e,i.append(t.children[0]),t.innerHTML=""}else i.append(e)};if("object"===m(e)&&"length"in e)for(var r=0;r<e.length;r+=1)e[r]&&n(e[r]);else n(e);t.recalcSlides(),a.loop&&t.loopCreate(),a.observer&&!t.isElement||t.update()}function ot(e){var t=this,a=t.params,i=t.activeIndex,n=t.slidesEl;a.loop&&t.loopDestroy();var r=i+1,o=function(e){if("string"==typeof e){var t=document.createElement("div");t.innerHTML=e,n.prepend(t.children[0]),t.innerHTML=""}else n.prepend(e)};if("object"===m(e)&&"length"in e){for(var s=0;s<e.length;s+=1)e[s]&&o(e[s]);r=i+e.length}else o(e);t.recalcSlides(),a.loop&&t.loopCreate(),a.observer&&!t.isElement||t.update(),t.slideTo(r,0,!1)}function st(e,t){var a=this,i=a.params,n=a.activeIndex,r=a.slidesEl,o=n;i.loop&&(o-=a.loopedSlides,a.loopDestroy(),a.recalcSlides());var s=a.slides.length;if(e<=0)a.prependSlide(t);else if(e>=s)a.appendSlide(t);else{for(var l=o>e?o+1:o,c=[],d=s-1;d>=e;d-=1){var u=a.slides[d];u.remove(),c.unshift(u)}if("object"===m(t)&&"length"in t){for(var p=0;p<t.length;p+=1)t[p]&&r.append(t[p]);l=o>e?o+t.length:o}else r.append(t);for(var h=0;h<c.length;h+=1)r.append(c[h]);a.recalcSlides(),i.loop&&a.loopCreate(),i.observer&&!a.isElement||a.update(),i.loop?a.slideTo(l+a.loopedSlides,0,!1):a.slideTo(l,0,!1)}}function lt(e){var t=this,a=t.params,i=t.activeIndex;a.loop&&(i-=t.loopedSlides,t.loopDestroy());var n,r=i;if("object"===m(e)&&"length"in e){for(var o=0;o<e.length;o+=1)n=e[o],t.slides[n]&&t.slides[n].remove(),n<r&&(r-=1);r=Math.max(r,0)}else n=e,t.slides[n]&&t.slides[n].remove(),n<r&&(r-=1),r=Math.max(r,0);t.recalcSlides(),a.loop&&t.loopCreate(),a.observer&&!t.isElement||t.update(),a.loop?t.slideTo(r+t.loopedSlides,0,!1):t.slideTo(r,0,!1)}function ct(){for(var e=[],t=0;t<this.slides.length;t+=1)e.push(t);this.removeSlide(e)}function dt(e){var t=e.swiper;Object.assign(t,{appendSlide:rt.bind(t),prependSlide:ot.bind(t),addSlide:st.bind(t),removeSlide:lt.bind(t),removeAllSlides:ct.bind(t)})}function ut(e){var t,a=e.effect,i=e.swiper,n=e.on,r=e.setTranslate,o=e.setTransition,s=e.overwriteParams,l=e.perspective,c=e.recreateShadows,d=e.getEffectParams;n("beforeInit",(function(){if(i.params.effect===a){i.classNames.push("".concat(i.params.containerModifierClass).concat(a)),l&&l()&&i.classNames.push("".concat(i.params.containerModifierClass,"3d"));var e=s?s():{};Object.assign(i.params,e),Object.assign(i.originalParams,e)}})),n("setTranslate",(function(){i.params.effect===a&&r()})),n("setTransition",(function(e,t){i.params.effect===a&&o(t)})),n("transitionEnd",(function(){if(i.params.effect===a&&c){if(!d||!d().slideShadows)return;i.slides.forEach((function(e){e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((function(e){return e.remove()}))})),c()}})),n("virtualUpdate",(function(){i.params.effect===a&&(i.slides.length||(t=!0),requestAnimationFrame((function(){t&&i.slides&&i.slides.length&&(r(),t=!1)})))}))}function pt(e,t){var a=he(t);return a!==t&&(a.style.backfaceVisibility="hidden",a.style["-webkit-backface-visibility"]="hidden"),a}function ht(e){var t=e.swiper,a=e.duration,i=e.transformElements,n=e.allSlides,r=t.activeIndex;if(t.params.virtualTranslate&&0!==a){var o=!1;(n?i:i.filter((function(e){var a=e.classList.contains("swiper-slide-transform")?function(e){return e.parentElement?e.parentElement:t.slides.filter((function(t){return t.shadowRoot&&t.shadowRoot===e.parentNode}))[0]}(e):e;return t.getSlideIndex(a)===r}))).forEach((function(e){!function(e,t){t&&e.addEventListener("transitionend",(function a(i){i.target===e&&(t.call(e,i),e.removeEventListener("transitionend",a))}))}(e,(function(){if(!o&&t&&!t.destroyed){o=!0,t.animating=!1;var e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});t.wrapperEl.dispatchEvent(e)}}))}))}}function ft(e){var t=e.swiper,a=e.extendParams,i=e.on;a({fadeEffect:{crossFade:!1}});ut({effect:"fade",swiper:t,on:i,setTranslate:function(){for(var e=t.slides,a=(t.params.fadeEffect,0);a<e.length;a+=1){var i=t.slides[a],n=-i.swiperSlideOffset;t.params.virtualTranslate||(n-=t.translate);var r=0;t.isHorizontal()||(r=n,n=0);var o=t.params.fadeEffect.crossFade?Math.max(1-Math.abs(i.progress),0):1+Math.min(Math.max(i.progress,-1),0),s=pt(0,i);s.style.opacity=o,s.style.transform="translate3d(".concat(n,"px, ").concat(r,"px, 0px)")}},setTransition:function(e){var a=t.slides.map((function(e){return he(e)}));a.forEach((function(t){t.style.transitionDuration="".concat(e,"ms")})),ht({swiper:t,duration:e,transformElements:a,allSlides:!0})},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode}}})}function mt(e){var t=e.swiper,a=e.extendParams,i=e.on;a({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});var n=function(e,t,a){var i=a?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),n=a?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");i||(i=ve("div","swiper-slide-shadow-cube swiper-slide-shadow-".concat(a?"left":"top").split(" ")),e.append(i)),n||(n=ve("div","swiper-slide-shadow-cube swiper-slide-shadow-".concat(a?"right":"bottom").split(" ")),e.append(n)),i&&(i.style.opacity=Math.max(-t,0)),n&&(n.style.opacity=Math.max(t,0))};ut({effect:"cube",swiper:t,on:i,setTranslate:function(){var e,a=t.el,i=t.wrapperEl,r=t.slides,o=t.width,s=t.height,l=t.rtlTranslate,c=t.size,d=t.browser,u=Ee(t),p=t.params.cubeEffect,h=t.isHorizontal(),f=t.virtual&&t.params.virtual.enabled,m=0;p.shadow&&(h?((e=t.wrapperEl.querySelector(".swiper-cube-shadow"))||(e=ve("div","swiper-cube-shadow"),t.wrapperEl.append(e)),e.style.height="".concat(o,"px")):(e=a.querySelector(".swiper-cube-shadow"))||(e=ve("div","swiper-cube-shadow"),a.append(e)));for(var v=0;v<r.length;v+=1){var g=r[v],w=v;f&&(w=parseInt(g.getAttribute("data-swiper-slide-index"),10));var y=90*w,b=Math.floor(y/360);l&&(y=-y,b=Math.floor(-y/360));var x=Math.max(Math.min(g.progress,1),-1),E=0,T=0,_=0;w%4==0?(E=4*-b*c,_=0):(w-1)%4==0?(E=0,_=4*-b*c):(w-2)%4==0?(E=c+4*b*c,_=c):(w-3)%4==0&&(E=-c,_=3*c+4*c*b),l&&(E=-E),h||(T=E,E=0);var S="rotateX(".concat(u(h?0:-y),"deg) rotateY(").concat(u(h?y:0),"deg) translate3d(").concat(E,"px, ").concat(T,"px, ").concat(_,"px)");x<=1&&x>-1&&(m=90*w+90*x,l&&(m=90*-w-90*x)),g.style.transform=S,p.slideShadows&&n(g,x,h)}if(i.style.transformOrigin="50% 50% -".concat(c/2,"px"),i.style["-webkit-transform-origin"]="50% 50% -".concat(c/2,"px"),p.shadow)if(h)e.style.transform="translate3d(0px, ".concat(o/2+p.shadowOffset,"px, ").concat(-o/2,"px) rotateX(89.99deg) rotateZ(0deg) scale(").concat(p.shadowScale,")");else{var C=Math.abs(m)-90*Math.floor(Math.abs(m)/90),k=1.5-(Math.sin(2*C*Math.PI/360)/2+Math.cos(2*C*Math.PI/360)/2),P=p.shadowScale,j=p.shadowScale/k,I=p.shadowOffset;e.style.transform="scale3d(".concat(P,", 1, ").concat(j,") translate3d(0px, ").concat(s/2+I,"px, ").concat(-s/2/j,"px) rotateX(-89.99deg)")}var M=(d.isSafari||d.isWebView)&&d.needPerspectiveFix?-c/2:0;i.style.transform="translate3d(0px,0,".concat(M,"px) rotateX(").concat(u(t.isHorizontal()?0:m),"deg) rotateY(").concat(u(t.isHorizontal()?-m:0),"deg)"),i.style.setProperty("--swiper-cube-translate-z","".concat(M,"px"))},setTransition:function(e){var a=t.el;if(t.slides.forEach((function(t){t.style.transitionDuration="".concat(e,"ms"),t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((function(t){t.style.transitionDuration="".concat(e,"ms")}))})),t.params.cubeEffect.shadow&&!t.isHorizontal()){var i=a.querySelector(".swiper-cube-shadow");i&&(i.style.transitionDuration="".concat(e,"ms"))}},recreateShadows:function(){var e=t.isHorizontal();t.slides.forEach((function(t){var a=Math.max(Math.min(t.progress,1),-1);n(t,a,e)}))},getEffectParams:function(){return t.params.cubeEffect},perspective:function(){return!0},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0}}})}function vt(e,t,a){var i="swiper-slide-shadow".concat(a?"-".concat(a):"").concat(e?" swiper-slide-shadow-".concat(e):""),n=he(t),r=n.querySelector(".".concat(i.split(" ").join(".")));return r||(r=ve("div",i.split(" ")),n.append(r)),r}function gt(e){var t=e.swiper,a=e.extendParams,i=e.on;a({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}});ut({effect:"coverflow",swiper:t,on:i,setTranslate:function(){for(var e=t.width,a=t.height,i=t.slides,n=t.slidesSizesGrid,r=t.params.coverflowEffect,o=t.isHorizontal(),s=t.translate,l=o?e/2-s:a/2-s,c=o?r.rotate:-r.rotate,d=r.depth,u=Ee(t),p=0,h=i.length;p<h;p+=1){var f=i[p],m=n[p],v=(l-f.swiperSlideOffset-m/2)/m,g="function"==typeof r.modifier?r.modifier(v):v*r.modifier,w=o?c*g:0,y=o?0:c*g,b=-d*Math.abs(g),x=r.stretch;"string"==typeof x&&-1!==x.indexOf("%")&&(x=parseFloat(r.stretch)/100*m);var E=o?0:x*g,T=o?x*g:0,_=1-(1-r.scale)*Math.abs(g);Math.abs(T)<.001&&(T=0),Math.abs(E)<.001&&(E=0),Math.abs(b)<.001&&(b=0),Math.abs(w)<.001&&(w=0),Math.abs(y)<.001&&(y=0),Math.abs(_)<.001&&(_=0);var S="translate3d(".concat(T,"px,").concat(E,"px,").concat(b,"px)  rotateX(").concat(u(y),"deg) rotateY(").concat(u(w),"deg) scale(").concat(_,")");if(pt(0,f).style.transform=S,f.style.zIndex=1-Math.abs(Math.round(g)),r.slideShadows){var C=o?f.querySelector(".swiper-slide-shadow-left"):f.querySelector(".swiper-slide-shadow-top"),k=o?f.querySelector(".swiper-slide-shadow-right"):f.querySelector(".swiper-slide-shadow-bottom");C||(C=vt("coverflow",f,o?"left":"top")),k||(k=vt("coverflow",f,o?"right":"bottom")),C&&(C.style.opacity=g>0?g:0),k&&(k.style.opacity=-g>0?-g:0)}}},setTransition:function(e){t.slides.map((function(e){return he(e)})).forEach((function(t){t.style.transitionDuration="".concat(e,"ms"),t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((function(t){t.style.transitionDuration="".concat(e,"ms")}))}))},perspective:function(){return!0},overwriteParams:function(){return{watchSlidesProgress:!0}}})}function wt(e){var t=e.swiper,a=e.extendParams,i=e.on;a({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}});ut({effect:"cards",swiper:t,on:i,setTranslate:function(){for(var e=t.slides,a=t.activeIndex,i=t.rtlTranslate,n=t.params.cardsEffect,r=t.touchEventsData,o=r.startTranslate,s=r.isTouched,l=i?-t.translate:t.translate,c=0;c<e.length;c+=1){var d=e[c],u=d.progress,p=Math.min(Math.max(u,-4),4),h=d.swiperSlideOffset;t.params.centeredSlides&&!t.params.cssMode&&(t.wrapperEl.style.transform="translateX(".concat(t.minTranslate(),"px)")),t.params.centeredSlides&&t.params.cssMode&&(h-=e[0].swiperSlideOffset);var f=t.params.cssMode?-h-t.translate:-h,m=0,v=-100*Math.abs(p),g=1,w=-n.perSlideRotate*p,y=n.perSlideOffset-.75*Math.abs(p),b=t.virtual&&t.params.virtual.enabled?t.virtual.from+c:c,x=(b===a||b===a-1)&&p>0&&p<1&&(s||t.params.cssMode)&&l<o,E=(b===a||b===a+1)&&p<0&&p>-1&&(s||t.params.cssMode)&&l>o;if(x||E){var T=Math.pow(1-Math.abs((Math.abs(p)-.5)/.5),.5);w+=-28*p*T,g+=-.5*T,y+=96*T,m="".concat(-25*T*Math.abs(p),"%")}if(f=p<0?"calc(".concat(f,"px ").concat(i?"-":"+"," (").concat(y*Math.abs(p),"%))"):p>0?"calc(".concat(f,"px ").concat(i?"-":"+"," (-").concat(y*Math.abs(p),"%))"):"".concat(f,"px"),!t.isHorizontal()){var _=m;m=f,f=_}var S="".concat(p<0?1+(1-g)*p:1-(1-g)*p),C="\n        translate3d(".concat(f,", ").concat(m,", ").concat(v,"px)\n        rotateZ(").concat(n.rotate?i?-w:w:0,"deg)\n        scale(").concat(S,")\n      ");if(n.slideShadows){var k=d.querySelector(".swiper-slide-shadow");k||(k=vt("cards",d)),k&&(k.style.opacity=Math.min(Math.max((Math.abs(p)-.5)/.5,0),1))}d.style.zIndex=-Math.abs(Math.round(u))+e.length,pt(0,d).style.transform=C}},setTransition:function(e){var a=t.slides.map((function(e){return he(e)}));a.forEach((function(t){t.style.transitionDuration="".concat(e,"ms"),t.querySelectorAll(".swiper-slide-shadow").forEach((function(t){t.style.transitionDuration="".concat(e,"ms")}))})),ht({swiper:t,duration:e,transformElements:a})},perspective:function(){return!0},overwriteParams:function(){return{watchSlidesProgress:!0,virtualTranslate:!t.params.cssMode}}})}Object.keys(Ye).forEach((function(e){Object.keys(Ye[e]).forEach((function(t){Ze.prototype[t]=Ye[e][t]}))})),Ze.use([function(e){var t=e.swiper,a=e.on,i=e.emit,n=re(),r=null,o=null,s=function(){t&&!t.destroyed&&t.initialized&&(i("beforeResize"),i("resize"))},l=function(){t&&!t.destroyed&&t.initialized&&i("orientationchange")};a("init",(function(){t.params.resizeObserver&&void 0!==n.ResizeObserver?t&&!t.destroyed&&t.initialized&&(r=new ResizeObserver((function(e){o=n.requestAnimationFrame((function(){var a=t.width,i=t.height,n=a,r=i;e.forEach((function(e){var a=e.contentBoxSize,i=e.contentRect,o=e.target;o&&o!==t.el||(n=i?i.width:(a[0]||a).inlineSize,r=i?i.height:(a[0]||a).blockSize)})),n===a&&r===i||s()}))}))).observe(t.el):(n.addEventListener("resize",s),n.addEventListener("orientationchange",l))})),a("destroy",(function(){o&&n.cancelAnimationFrame(o),r&&r.unobserve&&t.el&&(r.unobserve(t.el),r=null),n.removeEventListener("resize",s),n.removeEventListener("orientationchange",l)}))},function(e){var t=e.swiper,a=e.extendParams,i=e.on,n=e.emit,r=[],o=re(),s=function(e,a){void 0===a&&(a={});var i=new(o.MutationObserver||o.WebkitMutationObserver)((function(e){if(!t.__preventObserver__)if(1!==e.length){var a=function(){n("observerUpdate",e[0])};o.requestAnimationFrame?o.requestAnimationFrame(a):o.setTimeout(a,0)}else n("observerUpdate",e[0])}));i.observe(e,{attributes:void 0===a.attributes||a.attributes,childList:t.isElement||(void 0===a.childList||a).childList,characterData:void 0===a.characterData||a.characterData}),r.push(i)};a({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",(function(){if(t.params.observer){if(t.params.observeParents)for(var e=ye(t.hostEl),a=0;a<e.length;a+=1)s(e[a]);s(t.hostEl,{childList:t.params.observeSlideChildren}),s(t.wrapperEl,{attributes:!1})}})),i("destroy",(function(){r.forEach((function(e){e.disconnect()})),r.splice(0,r.length)}))}]);var yt={player_js:null,hls_js:null,init:function(){var e=this;jQuery(document.body).on("player",function(){var t,a=(t=u().mark((function t(a,i){var n,r,o,s,l,c;return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=Object.assign({theme:"var(--theme-color)",playbackRate:!0,aspectRatio:!0,setting:!0,pip:!0,fullscreen:!0,flip:!0,autoPlayback:!0},i.args?i.args:{}),t.prev=1,t.next=4,e.load_js("player");case 4:if(Artplayer.CONTROL_HIDE_TIME=1500,Artplayer.CONTEXTMENU=!1,r=jQuery(i.el),o=(o=i.args&&i.args.url?i.args.url:r.attr("src"))||r.find("source").attr("src"),s=(s=i.args&&i.args.poster?i.args.poster:"")||r.attr("poster"),l=Object.assign(n,{container:r.parent().get(0),url:o,poster:s||""}),!(o.search(/(\.m3u8|m3u8\?)/i)>-1)){t.next=17;break}return t.next=15,e.load_js("hls");case 15:l.type="m3u8",l.customType={m3u8:e.playM3u8};case 17:c=new Artplayer(l),i.callback&&i.callback(c),t.next=24;break;case 21:t.prev=21,t.t0=t.catch(1),console.error("Error loading player script:",t.t0);case 24:case"end":return t.stop()}}),t,null,[[1,21]])})),function(){var e=this,a=arguments;return new Promise((function(i,n){var r=t.apply(e,a);function s(e){o(r,i,n,s,l,"next",e)}function l(e){o(r,i,n,s,l,"throw",e)}s(void 0)}))});return function(e,t){return a.apply(this,arguments)}}())},load_js:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"player";if("player"===e&&this.player_js)return this.player_js;if("hls"===e&&this.hls_js)return this.hls_js;var t=void 0!==_wpcom_js.framework_url?_wpcom_js.framework_url:_wpcom_js.theme_url+"/themer",a="player"===e?t+"/assets/js/artplayer-5.1.1.min.js":t+"/assets/js/hls-1.5.8.min.js",i=jQuery.ajax({url:a,dataType:"script",cache:!0});return"player"===e?this.player_js=i:this.hls_js=i,i},playM3u8:function(e,t,a){if(Hls.isSupported()){a.hls&&a.hls.destroy();var i=new Hls;i.loadSource(t),i.attachMedia(e),a.hls=i,a.on("destroy",(function(){return i.destroy()}))}else e.canPlayType("application/vnd.apple.mpegurl")?e.src=t:a.notice.show="Unsupported playback format: m3u8"}};({init:function(){var e=this;jQuery(document.body).on("swiper",(function(t,a){var i=Object.assign({on:{},loop:!0,effect:"slide",autoplay:{delay:_wpcom_js.slide_speed?_wpcom_js.slide_speed:5e3,pauseOnMouseEnter:!0}},a.args),n=[it,dt];i.pagination&&n.push(at),i.navigation&&n.push(et),"fade"===i.effect&&n.push(ft),"cube"===i.effect&&n.push(mt),"coverflow"===i.effect&&n.push(gt),"cards"===i.effect&&n.push(wt),i.thumbs&&n.push(nt),i.modules=n,a.args.on&&a.args.on.init||(i.on.init=e.onInit),i.on.slidesLengthChange=e.checkSize;var r=new Ze(a.el,i);a.args._callback&&a.args._callback(r)}))},checkSize:function(e){if(e.slides.length<2)e.params.autoplay=!1,e.params.touchRatio=0,e.autoplay.stop();else{function t(e,t){if(e.params.slidesPerView&&e.slides.length<=e.params.slidesPerView){var a=[];e.slides.map((function(e){a.push(e.outerHTML)})),e.appendSlide(a),t&&t()}}t(e,(function(){return t(e)}))}},onInit:function(e){var t=jQuery(e.el);e.params.navigation||t.on("click",".swiper-button-next",(function(){e.slideNext()})).on("click",".swiper-button-prev",(function(){e.slidePrev()})),t.find(".j-lazy").lazyload({webp:void 0!==_wpcom_js.webp&&_wpcom_js.webp?_wpcom_js.webp:null}),t.find("img").on("load",(function(){e.update()})),setTimeout((function(){e.params.autoHeight&&e.updateAutoHeight(200)}),500)}}).init(),yt.init(),jQuery((function(e){var t=e(window),a=e(document.body),i=navigator.userAgent.toLowerCase(),n=1,r=[],o=void 0!==_wpcom_js.webp&&_wpcom_js.webp?_wpcom_js.webp:null;(e(".wpcom-user-list").length||e(".wpcom-member").length)&&(n=0),e('[data-toggle="tooltip"]').tooltip(),z(),e.fn.loading||e.fn.extend({loading:function(t){var a=e(this);t?a.addClass("loading").prepend('<i class="wpcom-icon wi wi-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'):a.removeClass("loading").find(".wi-loader").remove()}}),e("#wpcom-video").length&&a.trigger("player",[{el:"#wpcom-video",callback:function(e){e.on("view",(function(t){e.mini=!t&&e.playing&&!e.pip}))}}]);var s=e(".j-wpcom-video, .wp-block-video video, .wp-block-wpcom-video-code video, .wp-video video");if(s.length)for(var l=0;l<s.length;l++)a.trigger("player",[{el:s[l]}]);var c=e(".modules-video-player");if(c.length)for(var d=0;d<c.length;d++)a.trigger("player",[{el:c[d],args:{autoPlayback:!1,icons:{state:'<i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-play"></use></svg></i>'}},callback:function(e){e.on("ready",(function(){e.controls.show=!1})),jQuery(e.template.$player).on("mouseenter",(function(){e.controls.show=!0})).on("mouseleave",(function(){e.controls.show=!1}))}}]);Y.init(),e(".entry-content-slider").each((function(e,t){var i={autoHeight:!0,pagination:{el:t.querySelector(".swiper-pagination"),clickable:!0}};a.trigger("swiper",{el:t,args:i})}));var u,p=0,h=0;function f(){var e=window.innerWidth;if(!e){var t=document.documentElement.getBoundingClientRect();e=t.right-Math.abs(t.left)}u=document.body.clientWidth<e,h=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",a.append(e);var t=e.offsetWidth-e.clientWidth;return document.body.removeChild(e),t}()}if(e(document).on("smartphoto",(function(e,t){var i=new X(t,{nav:!1});i.on("open",(function(){var e;e=parseInt(a.css("padding-right")||0,10),p=document.body.style.paddingRight||0,u&&a.css("--scrollbar-width",e+h+"px"),a.addClass("modal-open")})),i.on("close",(function(){a.removeClass("modal-open"),a.css("--scrollbar-width",p+"px"),f()})),t.off("update").on("update",(function(e,t){t&&i.data.group[t.group][t.index]&&(i.data.group[t.group][t.index].src=t.src,i.data.group[t.group][t.index].thumb=t.src)}))})).off("copy_text").on("copy_text",(function(e,t,a,i){if(navigator.clipboard)navigator.clipboard.writeText(t).then((function(){a&&a()}),(function(){console.error("Failed to copy"),i&&i()}));else if(void 0!==document.execCommand){var n=document.createElement("textarea");document.body.appendChild(n),n.style.position="fixed",n.style.height=0,n.value=t,n.select(),document.execCommand("copy",!0),document.body.removeChild(n),a&&a()}else i&&i()})),"baiduboxapp"==i.match(/baiduboxapp/i))e(document).on("click","a.j-wpcom-lightbox",(function(t){t.preventDefault();var a="baiduboxapp://v19/utils/previewImage?params="+encodeURIComponent(JSON.stringify({urls:r,current:e(this).attr("href")})),i=document.createElement("iframe");i.style.display="none",i.src=a;var n=document.body;n.appendChild(i),setTimeout((function(){n.removeChild(i),i=null}),0)}));else{f();var m=e(".entry-content .j-wpcom-lightbox, .modules-gutenberg .j-wpcom-lightbox");m.length&&(m.find("noscript").remove(),e(document).trigger("smartphoto",[m]))}window.location.hash&&N(window.location.hash),e(".j-lazy").lazyload({webp:o}),e('a[href^="http"]').not('a[href*="'+location.hostname+'"]').each((function(t,a){var i=e(a),n=i.attr("rel");n&&!/noopener/i.test(n)?n+=" noopener":n=n||"noopener",i.attr("rel",n)})),/(iPhone|iPad|iPod|iOS|Android|Mobile|HarmonyOS)/i.test(i)&&(e("body").addClass("is-mobile"),e(".modules-fullwidth > .module-bg-video").remove()),B(),e(".entry-content.text-indent > p > img, .entry-content.text-indent > p > .j-wpcom-lightbox, .entry-content.text-indent .q-entry > p > img, .entry-content.text-indent .q-entry > p > .j-wpcom-lightbox").each((function(){var t=e(this).parent(),a=t.children(),i=0;(1===a.length&&""===e.trim(t.prop("outerText"))||2===a.length&&"NOSCRIPT"===a.eq(0).prop("tagName")&&""===e.trim(t.prop("outerText")))&&(i=1),i&&t.css("text-indent",0)}));var v=!1;if(window.localStorage){var g=localStorage.getItem("hideTopNews");(g=g?JSON.parse(g):null)&&g.value&&g.expires>Date.parse(new Date)?v=!0:g&&localStorage.removeItem("hideTopNews")}var w=e(".top-news");function y(t,a){var i='<div class="wpcom-modal fade" id="footer-bar">\n            <div class="modal-dialog modal-sm">\n                <div class="modal-content">\n                    <div class="modal-header">\n                        <div class="wpcom-close" data-wpcom-dismiss="modal" aria-label="Close"><span aria-hidden="true"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-close"></use></svg></i></span></div><h4 class="modal-title">'+(a||"")+'</h4>\n                    </div>\n                    <div class="modal-body">\n'+t+"                    </div>\n                </div>\n            </div>\n        </div>";0===e("#footer-bar").length?e("body").append(i):e("#footer-bar").replaceWith(i),e("#footer-bar")._modal()}function x(e){var t=e,a=t.closest(".modules-search").find('input[name="cat"]'),i=t.find("option:selected").data("cat");"post"===t.val()&&i?a.length?a.val(i):t.after('<input type="hidden" name="cat" value="'+i+'">'):a.length&&a.remove()}!v&&w.length&&(w.slideDown(),e("body:not(.member-login,.member-register)").css("padding-top",60),e(".wpcom-member .btn-home").css("top",90)),e(".multi-filter .multi-filter-item").each((function(t,a){var i=e(a);i.find(".multi-filter-ul").outerHeight()>80&&i.addClass("has-more")})).on("click",".multi-filter-more",(function(){e(this).closest(".multi-filter-item").toggleClass("open")})),e(document).on("click",'a[href^="#"]',(function(t){var a=e(this),i=a.attr("role");"tab"!=i&&"button"!=i&&(t.preventDefault(),a.hasClass("ez-toc-link")&&this.hash&&"undefined"!=typeof ezTOC&&ezTOC.scroll_offset?N(this.hash,ezTOC.scroll_offset):this.hash&&N(this.hash))})).on("click",".j-footer-bar-qrcode,.j-footer-bar-text",(function(t){t.preventDefault();var a=e(this),i=a.find("> span").text();return y(a.hasClass("j-footer-bar-qrcode")?'<img src="'+a.attr("href")+'">':a.find("script").html(),i||_wpcom_js.js_lang.qrcode),!1})).on("click",".j-footer-bar-copy",(function(t){var a=e(this),i=a.find("script.j-copy-text").text(),n=a.find("script.j-copy-callback").html();e(document).trigger("copy_text",[i,function(){n&&""!==n.trim()?y(n='<div class="copy-callback-success">'+_wpcom_js.js_lang.copy_done+"</div>"+n,a.find("> span").text()):wpcom_notice({message:_wpcom_js.js_lang.copy_done,show:1500})},function(){var e=_wpcom_js.js_lang.copy_fail;wpcom_alert(e+="<br />"+i)}])})).on("shown.bs.collapse",".wp-block-wpcom-accordion",(function(){e(this).find(".panel-collapse.in").closest(".panel").find(".panel-title a").attr("aria-expanded","true")})).on("change",".modules-search select[name=post_type]",(function(){x(e(this))})).on("click",".modules-search .modules-search-type-item",(function(){var t=e(this),a=t.closest(".modules-search");if(t.hasClass("active"))return!1;a.find(".modules-search-type-item").removeClass("active"),t.addClass("active"),t.data("search-url")?(a.find("form.modules-search-from").data("search-url",t.data("search-url")),a.find(".modules-search-post").hide()):(a.find("form.modules-search-from").data("search-url",""),a.find(".modules-search-post").show())})).on("submit",".modules-search form.modules-search-from",(function(){var t=e(this),a=t.data("search-url");if(a){var i=t.find("input[name=s]").val();return a=a.replace("%KEYWORDS%",i),"_blank"===t.attr("target")?window.open(a,"_blank"):window.location.href=a,!1}})).on("click",".tabs-switch-click > .tabs-switch-item",(function(){var t=e(this);if(t.hasClass("active"))return!1;var a=t.index();t.parent().find(".tabs-switch-item").removeClass("active"),t.addClass("active"),t.closest(".modules-tabs").find(" > .tabs-content > .tabs-content-item").removeClass("active").eq(a).addClass("active")})).on("mouseenter",".tabs-switch-hover > .tabs-switch-item",(function(){var t=e(this);if(t.hasClass("active"))return!1;var a=t.index();t.parent().find(".tabs-switch-item").removeClass("active"),t.addClass("active"),t.closest(".modules-tabs").find(" > .tabs-content > .tabs-content-item").removeClass("active").eq(a).addClass("active")})),e(".modules-search select[name=post_type]").each((function(){x(e(this))})),e(".modules-search .modules-search-type").each((function(){e(this).find(".modules-search-type-item").eq(0).trigger("click")})),e(".wp-block-wpcom-accordion").collapse("show"),window.location.base="NjU2",e(".wpcom-modal.modal-video").on("shown.wpcom.modal",(function(t){var i=e(this).closest(".modules-video").find(".video-wrap");e(".modal-body",this).html(i.find(".video-code").html());var n=e(this).find(".j-wpcom-video");n.length&&a.trigger("player",[{el:n,args:{autoplay:!0}}])})).on("hidden.wpcom.modal",(function(t){e(".modal-body",this).html("")}));var E,T=new MutationObserver((function(t){t.forEach((function(t){Array.prototype.slice.call(t.addedNodes).forEach((function(t){!t.className||"widget_shopping_cart_content"!==t.className&&"woocommerce-cart-form"!==t.className||e(t).find(".j-lazy").lazyload({webp:o})}))}))})),_={childList:!0,subtree:!0,attributes:!1,characterData:!1},S=document.querySelector(".shopping-cart"),C=document.querySelector(".woocommerce-cart-form-wrap"),k=document.querySelector(".widget_shopping_cart");S&&T.observe(S,_),C&&T.observe(C,_),k&&T.observe(k,_);var P,j=new MutationObserver((function(e){e.forEach((function(e){("attributes"===e.type&&"UL"!==e.target.nodeName&&"id"!==e.attributeName||"childList"===e.type)&&(clearTimeout(E),E=setTimeout((function(){return D()}),50))}))})),I=document.querySelector("header.header");I&&j.observe(I,{childList:!0,subtree:!0,attributes:!0,characterData:!1}),e(document).on("reset_adv_menu","header.header",(function(){D()})).on("wpcom.map",(function(){B()})).on("wpcom.lightbox",(function(t,a){if(z(),a&&"baiduboxapp"!==i.match(/baiduboxapp/i)){var n=a.find(".j-wpcom-lightbox");n.length&&(n.find("noscript").remove(),e(document).trigger("smartphoto",[n]))}})).on("change",".woocommerce input.qty",(function(){void 0!==P&&clearTimeout(P),P=setTimeout((function(){e("[name='update_cart']").trigger("click")}),600)})),a.on("click",".navbar-toggle",(function(){a.hasClass("navbar-on")?a.removeClass("navbar-on navbar-ing"):(a.addClass("navbar-on navbar-ing"),setTimeout((function(){a.removeClass("navbar-ing")}),500)),0==e(".navbar-on-shadow").length&&e("#wrap").append('<div class="navbar-on-shadow"></div>')})).on("click",".m-dropdown",(function(){var t=e(this).parent();return t.find("> .dropdown-menu").slideToggle("fast"),t.toggleClass("dropdown-open"),!1})).on("click",".top-news-close",(function(){var t={value:1,expires:Date.parse(new Date)+864e5};window.localStorage&&localStorage.setItem("hideTopNews",JSON.stringify(t)),w.slideUp(),a.css("padding-top",0),e(".wpcom-member .btn-home").css("top",30)})).on("click",".action .j-top",(function(){e("html, body").animate({scrollTop:0},"slow")})),e("#wrap").on("click",".navbar-on-shadow",(function(){a.hasClass("navbar-ing")||e(".navbar-toggle").trigger("click")})),e(".woocommerce").off("click.quantity").on("click.quantity",".qty-down,.qty-up",(function(){var t=e(this).hasClass("qty-down")?0:1,a=e(this).parent().find(".qty"),i=a.val();i=t?++i:--i,i=a.attr("min")&&i<a.attr("min")?a.attr("min"):i,i=a.attr("max")&&i>a.attr("max")?a.attr("max"):i,a.val(i).trigger("change")})).off("blur.quantity").on("blur.quantity",".qty",(function(){var t=e(this),a=t.val();a=t.attr("min")&&a<t.attr("min")?t.attr("min"):a,a=t.attr("max")&&a>t.attr("max")?t.attr("max"):a,t.val(a)}));var M,O,A=e(".j-top"),L=e(".action");function N(t,a){var i=e(t).length?e(t).offset().top:0;(a=a||10)&&(i-=a);var n=e("header.header");if(n.length){var r=getComputedStyle(n[0]);r&&r.position&&"fixed"===r.position&&(i-=n.outerHeight())}i=e("#wpadminbar").length?i-e("#wpadminbar").outerHeight():i,i=(i=e(".top-news").length?i-e(".top-news").outerHeight():i)<0?0:i,e("html, body").animate({scrollTop:i},400)}function D(){e(".wpcom-adv-menu").each((function(t,a){var n=e(a),r=e("body").width(),o=n.closest(".container").width();o=o||r-64;var s=e("#wrap > .container").width(),l=e("footer.footer > .container").width();s=(s=!s||l&&l<s?l:s)&&s<o?s:o,n.css({"--menu-margin-left":(r-s)/2+"px"});var c=/(iPhone|iPad|iPod|iOS|Android|Mobile|HarmonyOS)/i.test(i);n.find(" > .menu-item-style").each((function(t,a){var i=e(a),s=i.find(" > .menu-item-wrap");if(i.hasClass("menu-item-style-5")||i.hasClass("menu-item-style-3")||s.hasClass("menu-item-col-4")||s.hasClass("menu-item-col-5")){var l=i.offset().left;if(s.css({left:-l,width:r}),!c&&i.hasClass("menu-item-style-5")&&0===i.find(".dropdown-menu-tab").length){var d=i.find(" > .dropdown-menu"),u="",p="";d.find(" > ul > li").each((function(t){var a=e(this),i=a.find("> .dropdown-menu").html()||"",n=a.find("> a").html();u+='<div class="menu-tab-item '.concat(0===t?"active":"",'">').concat(n,"</div>"),p+='<ul class="dropdown-menu '.concat(0===t?"active":"",'">').concat(i,"</ul>")}));var h='<div class="dropdown-menu-tab">'.concat(u,'</div><div class="dropdown-menu-wrap">').concat(p,"</div>");d.html(h),i.find(".menu-tab-item").on("mouseenter",(function(){var t=e(this).index();e(this).addClass("active").siblings().removeClass("active"),i.find(".dropdown-menu-wrap > .dropdown-menu").removeClass("active").eq(t).addClass("active")}))}else if(c&&i.hasClass("menu-item-style-5")&&i.find(" > div.dropdown-menu").length){var f=i.find(" > div.dropdown-menu"),m=f.html();f.replaceWith(m)}}else{var v=i.position().left,g=s.outerWidth(),w=n.offset().left-(r-o)/2,y="";v+g>o-w&&(y=-(i.offset().left+g-o-(r-o)/2)),y&&s.css("--dropdown-menu-left",y+"px")}})),setTimeout((function(){return n.addClass("menu-ready")}),100)}))}function B(){e(".j-map").each((function(t,a){var i=e(a).find("script").html(),n=JSON.parse(i);void 0===window.wpcom_maps&&(window.wpcom_maps=[]),window.wpcom_maps.push(n)})),void 0!==window.wpcom_maps&&window.wpcom_maps.length&&function(){var t="\u6682\u672a\u8bbe\u7f6e\u5730\u56fe\u63a5\u53e3\uff0c\u5982\u679c\u60a8\u662f\u7f51\u7ad9\u7ba1\u7406\u5458\uff0c\u8bf7\u524d\u5f80\u3010\u4e3b\u9898\u8bbe\u7f6e>\u5e38\u89c4\u8bbe\u7f6e>\u5730\u56fe\u63a5\u53e3\u3011\u8fdb\u884c\u8bbe\u7f6e",a=[],i=[];for(var n in window.wpcom_maps)1==window.wpcom_maps[n].type?i.push(window.wpcom_maps[n]):a.push(window.wpcom_maps[n]);a.length&&"1"===_wpcom_js.is_admin&&wpcom_alert("\u6e29\u99a8\u63d0\u793a\uff1a\u7531\u4e8e\u767e\u5ea6\u5730\u56fe\u5546\u7528\u9700\u8981\u6388\u6743\uff0c\u7ecf\u8003\u8651\u76ee\u524d\u5df2\u5c06\u767e\u5ea6\u5730\u56fe\u529f\u80fd\u4e0b\u7ebf\uff0c\u5efa\u8bae\u60a8\u5c06\u9875\u9762\u6d89\u53ca\u5230\u767e\u5ea6\u5730\u56fe\u5c55\u793a\u76f8\u5173\u7684\u6a21\u5757\u8fdb\u884c\u5220\u9664\u6216\u8005\u8c03\u6574\u3002\uff08\u4ec5\u7ba1\u7406\u5458\u53ef\u89c1\uff09");if(i.length&&!i[0].key)wpcom_alert(t);else if(i.length){var r="//maps.googleapis.com/maps/api/js?key="+i[0].key;e.ajax({url:r,dataType:"script",cache:!0,success:function(){for(var t=[],a=[],n=[],r=0;r<i.length;r++)!function(r){var o=i[r],s={zoom:15,center:{lat:Number(e.trim(o.pos[0])),lng:Number(e.trim(o.pos[1]))},scrollwheel:!!o.scrollWheelZoom,styles:[{elementType:"geometry",stylers:[{lightness:45},{saturation:-25}]},{featureType:"poi",stylers:[{visibility:"off"}]},{featureType:"transit",stylers:[{visibility:"off"}]}],disableDefaultUI:!0};t[r]=new google.maps.Map(document.getElementById(o.id),s);var l={position:s.center,map:t[r]};o.icon&&(l.icon={url:o.icon,size:new google.maps.Size(27,27),scaledSize:new google.maps.Size(27,27)}),a[r]=new google.maps.Marker(l),o.html&&(n[r]=new google.maps.InfoWindow({content:o.html,maxWidth:500}),n[r].open(t[r],a[r]),a[r].addListener("click",(function(){n[r].open(t[r],a[r])})))}(r)}})}}()}function z(){n&&void 0!==_wpcom_js.lightbox&&1==_wpcom_js.lightbox&&e(".entry-content img").each((function(t,a){var n=e(a);if(!n.hasClass("no-lightbox")){var o=n.parent(),s=n.data("original");if((s=s||n.attr("src"))&&s.match(/^\/\//)&&(s=window.location.protocol+s),"a"===o.prop("tagName").toLowerCase()){var l=o.attr("href");!o.hasClass("j-wpcom-lightbox")&&(l==s||l&&l.match(/.*(\.png|\.jpg|\.jpeg|\.gif|\.webp|\.bmp)$/i))&&(o.addClass("j-wpcom-lightbox"),o.attr("data-group","0"),(i.match(/MicroMessenger/i)||i.match(/baiduboxapp/i))&&r.push(s))}else n.hasClass("wp-smiley")||n.closest("a").length||(n.replaceWith('<a class="j-wpcom-lightbox" href="'+s+'" data-group="0">'+a.outerHTML+"</a>"),(i.match(/MicroMessenger/i)||i.match(/baiduboxapp/i))&&r.push(s))}}))}if(A.length&&t.on("scroll",(function(){t.scrollTop()>100?(A.addClass("active"),L.removeClass("hide-gotop")):(A.removeClass("active"),L.addClass("hide-gotop"))})),L.length&&setTimeout((function(){L.find(".action-item").each((function(t,a){var i=e(a).find(".action-item-inner");i.length&&i.css("margin-top",-i.outerHeight()/2)}))}),200),L.on("mouseenter",".action-item",(function(){var t=e(this).find(".action-item-inner");t.length&&t.css("margin-top",-t.outerHeight()/2)})),t.on("resize",(function(){M&&clearTimeout(M),M=setTimeout((function(){D(),e(".navbar-collapse").removeAttr("style")}),50)})),setTimeout((function(){b.init()}),50),D(),e(".wpcom-adv-menu").find("img").on("load",(function(){O&&clearTimeout(O),O=setTimeout((function(){return D()}),100)})),function(){if(i.match(/MicroMessenger/i)){var t,a=location.href.split("#")[0],n=document.querySelector("body").classList,s=0;if(n.contains("page"))for(var l=0;l<n.length;l++)(t=n[l].match(/page-id-(\d+)$/))&&(s=t[1]);else if(n.contains("single"))for(l=0;l<n.length;l++)(t=n[l].match(/postid-(\d+)$/))&&(s=t[1]);e(".q-content.q-single").length&&(s=e(".q-content.q-single").data("id")),e.ajax({url:_wpcom_js.ajaxurl,type:"POST",data:{action:"wpcom_wx_config",url:encodeURIComponent(a),ID:s},dataType:"json",success:function(t){if(t.signature){var i=t.thumb;i.match(/^\/\//)&&(i=window.location.protocol+i);var n=document.title,s=e("meta[name=description]").attr("content");s=s||t.desc;var l=document.createElement("script");l.src="//res.wx.qq.com/open/js/jweixin-1.6.0.js",l.onload=function(){window.wx.config({debug:!1,appId:t.appId,timestamp:t.timestamp,nonceStr:t.noncestr,signature:t.signature,jsApiList:["updateAppMessageShareData","updateTimelineShareData","onMenuShareWeibo","previewImage"]}),window.wx.ready((function(){e(document).trigger("wx.ready");var t={imgUrl:i,link:a,desc:s,title:n},l={imgUrl:i,link:a,title:n};wx.updateAppMessageShareData(t),wx.updateTimelineShareData(l),wx.onMenuShareWeibo(t),e(".entry-content,.modules-gutenberg").find("a.j-wpcom-lightbox").each((function(t,a){var i=e(a),n=i.find("> img"),r=i.attr("href");n.attr("data-img",r).addClass("j-previewImage"),i.replaceWith(i.html())})),e(".entry-content .j-previewImage.j-lazy").lazyload({webp:o}),e(document).on("click","img.j-previewImage",(function(t){t.preventDefault(),wx.previewImage({current:e(this).data("img"),urls:r})}))})),wx.error((function(e){console.log(e,"error")}))},document.body.appendChild(l)}}})}}(),void 0!==_wpcom_js.share&&"1"==_wpcom_js.share){var G=void 0!==_wpcom_js.share_items&&_wpcom_js.share_items;setup_share(G)}})),window.wpcom_alert=function(e,t){t=t||"\u63d0\u793a\u4fe1\u606f";var a=jQuery("#wpcom-alert");if(a.length)a.find(".modal-title").html(t),a.find(".modal-body").html(e),a._modal("show");else{var i='<div class="wpcom-modal fade modal-alert" id="wpcom-alert" data-backdrop="static">\n            <div class="modal-dialog modal-sm">\n                <div class="modal-content">                   <div class="modal-header"><div class="wpcom-close" data-wpcom-dismiss="modal" aria-label="Close"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-close"></use></svg></i></div><h4 class="modal-title">'+t+'</h4></div>\n                   <div class="modal-body">'+e+'</div>\n                   <div class="modal-footer"><button type="button" class="wpcom-btn btn-primary" data-wpcom-dismiss="modal" aria-label="Close">'+_wpcom_js.js_lang.confirm+"</button></div>                </div>\n            </div>\n        </div>";jQuery("body").append(i)}jQuery("#wpcom-alert")._modal("show")},window.wpcom_notice=function(e){if(!arguments.length||1===arguments.length&&"object"===m(arguments[0])||(e={},void 0!==arguments[0]&&(e.message=arguments[0]),void 0!==arguments[1]&&(e.type=arguments[1]),void 0!==arguments[2]&&"loading"!==e.type&&(e.show=arguments[2]),void 0!==arguments[2]&&"loading"===e.type&&(e.callback=arguments[2])),e&&e.message){e.type=e.type?e.type:"success";var t='<div class="notice-message"><div class="notice-message-content notice-message-'+e.type+'">';"success"===e.type?t+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-success"></use></svg></i>':"warning"===e.type||"error"===e.type?t+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-warning"></use></svg></i>':"loading"===e.type&&(t+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'),t+=e.message+"</div></div>";var a=jQuery(t),i=jQuery(".notice-message-wrapper");return 0===i.length&&(jQuery(document.body).append('<div class="notice-message-wrapper"></div>'),i=jQuery(".notice-message-wrapper")),i.append(a),a.one("hide.notice",(function(){var e=jQuery(this);e.removeClass("notice-message-active").addClass("notice-message-up"),setTimeout((function(){e.remove(),0===i.find(".notice-message").length&&i.remove()}),320)})),setTimeout((function(){a.addClass("notice-message-active"),"loading"===e.type&&void 0!==e.callback?e.callback(a):setTimeout((function(){a.trigger("hide.notice")}),e.show?e.show:3e3)}),50),a}},window.setup_share=function(e){if(e&&Object.keys(e).length){var t='<div class="action-item-inner share-more-wrap"><h4 class="share-more-title">'+_wpcom_js.js_lang.share_to+"</h4>";for(var a in e)t+='<a class="action-share-item" data-share="'+a+'" target="_blank" rel="noopener"><i class="wpcom-icon wi wi-'+a+'"><svg aria-hidden="true"><use xlink:href="#wi-'+e[a].icon+'"></use></svg></i>'+e[a].title+"</a>";t+="</div>",jQuery(".action .action-item.j-share").append(t)}else{var i=[{name:"weibo",label:"\u5fae\u535a"},{name:"wechat",label:"\u5fae\u4fe1"},{name:"qq",label:"QQ\u597d\u53cb"},{name:"qzone",label:"QQ\u7a7a\u95f4"},{name:"douban",label:"\u8c46\u74e3"},{name:"linkedin",label:"LinkedIn"},{name:"facebook",label:"Facebook"},{name:"x",label:"X",icon:"twitter-x"}],n="";for(var r in i)n+='<a class="action-share-item" data-share="'+i[r].name+'" target="_blank" rel="noopener noreferrer"><i class="wpcom-icon wi wi-'+i[r].name+'"><svg aria-hidden="true"><use xlink:href="#wi-'+(i[r].icon?i[r].icon:i[r].name)+'"></use></svg></i>'+i[r].label+"</a>";jQuery(".action .action-item.j-share").append('<div class="action-item-inner share-more-wrap"><h4 class="share-more-title">'+_wpcom_js.js_lang.share_to+"</h4>"+n+"</div>")}};var bt={init:function(){var e=this;this.checker=null,this.loader='<i class="wpcom-icon wi wpcom-icon-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>',this.error='<i class="wpcom-icon wi wpcom-icon-error"><svg aria-hidden="true"><use xlink:href="#wi-warning"></use></svg></i>',jQuery(document).on("click",".j-message",(function(t){e.load_box(t)})).on("click",".j-message-send",(function(t){e.send(t)})).on("input propertychange change",".j-message-text",(function(){var e=jQuery(this);jQuery.trim(e.val()).length?e.parent().find(".j-message-send").removeClass("disabled"):e.parent().find(".j-message-send").addClass("disabled")})).on("keydown",".j-message-text",(function(e){13!==e.keyCode||e.shiftKey||(e.preventDefault(),e.returnValue=!1,jQuery(e.target).closest(".modal-content").find(".j-message-send").trigger("click"))}))},load_box:function(e){if(!1===window.is_login)return jQuery("#login-modal")._modal(),!1;var t=this,a=jQuery(e.target).closest(".j-message");if(a.hasClass("loading"))return!1;var i=a.data("user");i&&(a.loading(1),jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_message_box",user:i},dataType:"json",success:function(e,n,r){if(a.loading(0),a.find(".wi").show(),0==e.result){if(!jQuery("#message-modal").length){jQuery("body").append('<div class="wpcom-modal modal-message fade" id="message-modal" data-backdrop="static">\n            <div class="modal-dialog">\n                <div class="modal-content"><div class="modal-header">\n                <div class="wpcom-close" data-wpcom-dismiss="modal" aria-label="Close"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-close"></use></svg></i></div>\n                <h4 class="modal-title"></h4>\n            </div>\n                    <div class="modal-body"><div class="modal-message-list"></div><div class="modal-message-editor modal-editor-withbar"><div class="modal-message-smile j-smilies" data-target=".j-message-text"><i class="wpcom-icon wi smile-icon"><svg aria-hidden="true"><use xlink:href="#wi-emotion"></use></svg></i></div><textarea class="modal-message-text j-message-text"></textarea><div class="modal-message-send">\u6309 Enter \u952e\u53d1\u9001<button type="button" class="wpcom-btn btn-primary btn-message disabled j-message-send">\u53d1\u9001</button></div></div></div>\n                </div>\n            </div>\n        </div>')}var o=jQuery("#message-modal"),s='<div class="modal-message-more">'+t.loader+"</div>",l=o.find(".modal-message-list"),c=e.to_uname?e.to_uname:" ";e.to_url&&(c='<a href="'+e.to_url+'" target="_blank">'+c+"</a>"),o.find(".modal-title").html(c).data("user",e.to_uid?e.to_uid:0),l.html(s+(e.messages?e.messages:"")),o.find(".j-message-send").data("avatar",e.avatar),o._modal("show").find(".j-message-text").val(""),setTimeout((function(){o.find(".j-message-text").focus()}),500),"0"===r.getResponseHeader("Next-page")&&o.find(".modal-message-more").remove(),setTimeout((function(){var e=o.find(".modal-message-item:last-child")[0];e&&e.scrollIntoView(),t.load_more(o,i)}),200),t.set_read(i,a),t.checker&&clearInterval(t.checker),t.checker=setInterval((function(){t.check_messages(o,i)}),1e4),o.on("hide.wpcom.modal",(function(){clearInterval(t.checker)}))}else-1==e.result?(jQuery(document).trigger("wpcom_not_login"),jQuery("#login-modal")._modal()):-3==e.result&&e.msg&&wpcom_notice({message:e.msg,type:"warning",show:1500})},error:function(){a.loading(0),a.find(".wi").show()}}))},send:function(e){var t=jQuery(e.target).closest(".j-message-send");if(!t.hasClass("disabled")){var a=t.closest(".modal-content"),i=a.find(".modal-message-list"),n=a.find(".j-message-text").val().trim(),r=a.find(".modal-title").data("user"),o=this;if(n){o.checker&&clearInterval(o.checker),o.checker=setInterval((function(){o.check_messages(a,r)}),1e4);var s=jQuery('<div class="modal-message-item message-sender"><div class="modal-message-time"></div><div class="modal-message-inner"><div class="modal-message-status">'+o.loader+'</div><div class="modal-message-content"><div class="message-text"></div></div><div class="modal-message-avatar"><img src="'+t.data("avatar")+'"></div></div></div>');s.find(".message-text").text(n);var l=i.find(".modal-message-item:last-child"),c=l.length?l.data("id"):0;i.append(s),a.find(".j-message-text").val("").trigger("input"),setTimeout((function(){i.animate({scrollTop:i.prop("scrollHeight")},150)}),100),jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_send_message",to:r,content:n,last:c},dataType:"json",success:function(e){try{0===e.result?e.messages?(s.replaceWith(e.messages),i.animate({scrollTop:i.prop("scrollHeight")},150)):(s.data("id",e.message_id).find(".modal-message-status").html(""),s.find(".modal-message-time").html(e.message_time),e.message_content&&s.find(".modal-message-content .message-text").html(e.message_content)):-1===e.result?(jQuery(document).trigger("wpcom_not_login"),a.closest(".wpcom-modal")._modal("hide"),setTimeout((function(){jQuery("#login-modal")._modal("show")}),100)):-3===e.result?(e.msg&&wpcom_notice({message:e.msg,type:"warning",show:1500}),s.remove()):s.find(".modal-message-status").html(o.error)}catch(e){s.find(".modal-message-status").html(o.error)}},error:function(){s.find(".modal-message-status").html(o.error)}})}else wpcom_alert("\u79c1\u4fe1\u5185\u5bb9\u4e0d\u80fd\u4e3a\u7a7a")}},load_more:function(e,t){var a=0,i=e.find(".modal-message-list"),n=e.find(".modal-message-more");i.off("scroll.message").on("scroll.message",(function(r){if(r.target.scrollTop<=20&&r.target.scrollTop<a&&(n=e.find(".modal-message-more")).length&&!n.hasClass("active")){n.addClass("active");var o=e.find(".modal-message-item").first(),s=o.length?o.data("id"):0;jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_load_messages",user:t,last:s},dataType:"html",success:function(e,t,a){if(e){var r=o.offset().top-i.scrollTop();n.after(e),i.scrollTop(o.offset().top-r)}n.removeClass("active"),"0"===a.getResponseHeader("Next-page")&&n.remove()},error:function(){n.removeClass("active")}})}a=r.target.scrollTop}))},set_read:function(e,t){jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_read_messages",user:e},dataType:"html",success:function(e){e>0&&t&&t.find(".messages-item-unread").length&&t.find(".messages-item-unread").remove()}})},check_messages:function(e,t){var a=this,i=e.find(".modal-message-list"),n=i.find(".modal-message-item:last-child"),r=n.length?n.data("id"):0;jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_check_messages",user:t,last:r},dataType:"json",success:function(e){0===e.result&&e.messages&&(i.append(e.messages),a.set_read(t),i.animate({scrollTop:i.prop("scrollHeight")},150))}})}},xt={init:function(){var e=this;jQuery(document).on("click",".j-notification .notify-item-title a",(function(t){var a=jQuery(this).closest(".j-notification");if(!a.hasClass("status-1")){var i=a.data("id");e.set_read(a,i)}}))},set_read:function(e,t){jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_read_notification",id:t},dataType:"html",success:function(t){t&&e.removeClass("status-0").addClass("status-1")}})}},Et={init:function(){var e=this;jQuery(document).on("click",".j-follow",(function(t){e.follow(t)})).on("check_follow wpcom_login",(function(){e.check_follow()})).on("click",".profile-tab .profile-tab-item",(function(){var e=jQuery(this),t=e.closest(".wpcom-profile-main"),a=e.index();t.find(".profile-tab-item, .profile-tab-content").removeClass("active"),e.addClass("active"),t.find(".profile-tab-content").eq(a).addClass("active").trigger("profile_tab_show")})).on("profile_tab_show",".profile-tab-content",(function(){var e=jQuery(this);e.closest(".profile-follows").length&&e.find(".follow-items-loading").length&&e.find(".j-user-followers").trigger("click")}))},follow:function(e){if(!1===window.is_login)return jQuery("#login-modal")._modal(),!1;var t=jQuery(e.target).closest(".j-follow");if(t.hasClass("loading"))return!1;var a=t.hasClass("followed"),i=t.data("user");i&&(t.loading(1),jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_follow",follow:i},dataType:"json",success:function(e){0==e.result?t.html(_wpcom_js.followed_btn).addClass("followed").removeClass("btn-primary"):1==e.result?t.html(_wpcom_js.follow_btn).removeClass("followed").addClass("btn-primary"):-1==e.result?(jQuery(document).trigger("wpcom_not_login"),jQuery("#login-modal")._modal(),t.html(a?_wpcom_js.followed_btn:_wpcom_js.follow_btn)):(t.html(a?_wpcom_js.followed_btn:_wpcom_js.follow_btn),e.msg&&wpcom_notice({message:e.msg,type:"warning",show:1500})),t.loading(0)},error:function(){t.html(a?_wpcom_js.followed_btn:_wpcom_js.follow_btn).loading(0)}}))},check_follow:function(){var e=[];jQuery(".j-follow").each((function(t,a){var i=jQuery(a).data("user");i&&jQuery.inArray(i,e)<0&&e.push(i)})),e.length&&jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_check_follow",ids:e},dataType:"json",success:function(t){if(t&&"object"===m(t))for(var a in t)t[a]&&jQuery.inArray(a,e)&&jQuery(".j-follow[data-user="+a+"]").addClass("followed").removeClass("btn-primary").html(_wpcom_js.followed_btn)}})}},Tt={init:function(){if(!this.is_mobile()&&_wpcom_js.user_card){var e=this;jQuery(document).on("mouseenter",".j-user-card",(function(){e.timer&&clearTimeout(e.timer),e.timer2&&clearTimeout(e.timer2);var t=this;e.timer=setTimeout((function(){var a=jQuery(t),i=a.data("user");i&&(e.show_card(a),e.get_data(i,(function(t){setTimeout((function(){e.render_card(t,a)}),300)})))}),500)})).on("mouseleave",".j-user-card",(function(){e.timer&&clearTimeout(e.timer),e.timer2&&clearTimeout(e.timer2),e.hide_card()})).on("mouseenter","#j-user-card",(function(){e.timer&&clearTimeout(e.timer),e.timer2&&clearTimeout(e.timer2)})).on("mouseleave","#j-user-card",(function(){e.timer&&clearTimeout(e.timer),e.timer2&&clearTimeout(e.timer2),e.hide_card()}))}},get_data:function(e,t){jQuery.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_user_card",user:e},dataType:"json",success:function(e){t(e.html)}})},show_card:function(e){var t=jQuery("#j-user-card"),a=t.length?t:jQuery('<div id="j-user-card" class="user-card-wrap"><div class="user-card-loading"><i class="wpcom-icon wi wi-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i></div></div>');t.length||jQuery("body").append(a);var i=this.get_style(e,!a.find(".user-card-loading").length);a.css(i),t.length||a.fadeIn(200)},hide_card:function(){this.timer2=setTimeout((function(){jQuery("#j-user-card").fadeOut(200,(function(){jQuery("#j-user-card").remove()}))}),300)},render_card:function(e,t){var a=jQuery("#j-user-card");a.html(e);var i=this.get_style(t,1);a.css(i)},get_style:function(e,t){var a=e.offset(),i=jQuery(window),n=0;if(i.height()-(a.top-i.scrollTop()+e.outerHeight())<350){var r=t?_wpcom_js.user_card_height?_wpcom_js.user_card_height:346:180;n=a.top-r-5}else n=a.top+e.outerHeight()+5;return{left:a.left,top:n}},is_mobile:function(){return/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent)}},_t={init:function(){var e=this;this.loaded=0,this.builded=0,this.builded_id=0,this.post_id=0;var t=void 0!==_wpcom_js.framework_url?_wpcom_js.framework_url:_wpcom_js.theme_url+"/themer";jQuery(".j-mobile-share").length&&this.loadFont(),jQuery(document).on("click",".j-mobile-share",(function(){var a='<div class="mobile-share-bg"></div><div class="mobile-share-wrap"><div class="loading"><i class="wpcom-icon wi wpcom-icon-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'+_wpcom_js.poster.generating+"</div></div>";jQuery("body").append(a);var i=jQuery(this);if(e.post_id=i.data("id"),e.loaded)return e.getData(i),!1;var n="1.4.1";(navigator.userAgent.indexOf("Safari/")>-1||navigator.userAgent.indexOf("iPhone;")>-1)&&(n="1.0.0-rc.1"),jQuery.ajax({url:t+"/assets/js/html2canvas-"+n+".min.js",dataType:"script",cache:!0,success:function(){e.loaded=1,e.getData(i)}})})).on("click",".mobile-share-close,.mobile-share-bg,.mobile-share-wrap",(function(){jQuery(".mobile-share-bg,.mobile-share-wrap").remove()})).on("click",".mobile-share-down",(function(){var t=jQuery(".mobile-share-canvas img");if(t.length&&t.attr("src")){var a=t.attr("src"),i=document.createElement("a");i.href=a,i.download="poster_"+e.post_id+".png",i.click()}return!1})).on("click",".mobile-share-container",(function(e){e.stopPropagation()}))},getData:function(e){var t=e.data("id"),a=this;if(this.builded&&this.builded_id===t)return jQuery(".mobile-share-wrap").html('<div class="mobile-share-container"><div class="top_tips">'+_wpcom_js.poster.notice+'</div><div class="mobile-share-canvas"><img src="'+this.builded+'"></div></div><div class="mobile-share-action"><div class="mobile-share-down"><i class="wpcom-icon wi"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 19h18v2H3v-2zm10-5.828L19.071 7.1l1.414 1.415L12 17 3.515 8.515 4.929 7.1 11 13.172V2h2v11.172z"/></svg></i>\u4e0b\u8f7d</div><div class="mobile-share-close"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-close"></use></svg></i>\u5173\u95ed</div></div>').find(".top_tips").show(),!1;jQuery.ajax({url:_wpcom_js.ajaxurl,data:{id:t,action:"wpcom_mobile_share"},method:"POST",dataType:"html",timeout:1e4,success:function(i){var n=jQuery(".meta-item.wechat"),r="";if((n=n.length?n:e.closest(".kx-meta").find(".j-share-qrcode")).find("canvas")[0])r=n.find("canvas")[0].toDataURL();else{var o=jQuery('<div style="display: none;"></div>');jQuery("body").append(o);var s=e.data("qrcode")?e.data("qrcode"):location.href;o.qrcode({text:s}),r=o.find("canvas")[0].toDataURL(),o.remove()}if(i&&r){var l=jQuery(i);l.find(".mobile-share-qrcode").html('<img src="'+r+'">'),l&&(jQuery(".mobile-share-wrap").html(l),setTimeout((function(){html2canvas(document.querySelector(".mobile-share-inner"),{scale:2,useCORS:!0,scrollY:0,backgroundColor:null}).then((function(e){var i=e.toDataURL("image/png");jQuery(".mobile-share-canvas").html('<img src="'+i+'">'),jQuery(".mobile-share-wrap .top_tips").show(),jQuery(".mobile-share-inner").css("visibility","hidden"),a.builded=i,a.builded_id=t}))}),300))}else jQuery(".mobile-share-bg,.mobile-share-wrap").remove(),setTimeout((function(){wpcom_alert(_wpcom_js.poster.failed)}),50)},error:function(){jQuery(".mobile-share-bg,.mobile-share-wrap").remove(),setTimeout((function(){wpcom_alert(_wpcom_js.poster.failed)}),50)}})},loadFont:function(){if(!this.loaded_font){var e=document.getElementsByTagName("head")[0],t=document.createElement("link");t.href=_wpcom_js.font_url,t.rel="stylesheet",t.type="text/css",e.appendChild(t),this.loaded_font=1}}},St={init:function(){var e=this;jQuery(document.body).on("click",".j-smilies .smile-icon",(function(){return e.$el=jQuery(this).closest(".j-smilies"),e.target=e.$el.data("target"),e.open_smile_box(),void 0===window.smilies&&e.get_smilies(),!1})).on("smilies_loaded",(function(){e.render_smilies()})).on("click",".smilies-item",(function(){var t=jQuery(this).data("name");t&&e.add_smile(t)})).on("click",(function(t){if(e.target){var a=jQuery(t.target);0===a.closest(e.target).length&&0===a.closest(".j-smilies").length&&e.$el.find(".smilies-box").removeClass("active")}}))},get_smilies:function(){jQuery.ajax({type:"GET",url:_wpcom_js.ajaxurl,data:{action:"wpcom_get_smilies"},dataType:"json",success:function(e){e&&"object"===m(e)&&(window.smilies=e,jQuery(document.body).trigger("smilies_loaded"))}})},open_smile_box:function(){var e=this.$el.find(".smilies-box");e.length||(e=jQuery('<div class="smilies-box"><div class="smilies-box-loading"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i></div></div>'),this.$el.append(e)),this.render_smilies(),e.toggleClass("active")},render_smilies:function(){if(window.smilies&&!this.$el.find(".smilies-box .smilies-item").length){var e="";for(var t in window.smilies)e+='<div class="smilies-item" data-name="'+window.smilies[t].name+'" title="'+window.smilies[t].title+'"><img src="'+window.smilies[t].src+'" alt="'+window.smilies[t].name+'"></div>';this.$el.find(".smilies-box").html(e)}},add_smile:function(e){var t=this.target?jQuery(this.target):null;t&&this.ins2pos(e,t)},ins2pos:function(e,t){var a=t.val(),i=a.substring(0,t[0].selectionStart),n=a.substring(t[0].selectionEnd,a.length);t.val(i+e+n).trigger("change"),this.setCursor(t[0],i.length+e.length)},setCursor:function(e,t){if(e.setSelectionRange)e.focus(),e.setSelectionRange(t,t);else if(e.createTextRange){var a=e.createTextRange();a.collapse(!0),a.moveEnd("character",t),a.moveStart("character",t),a.select()}}},Ct={init:function(){var e=this;jQuery((function(t){e.$=t,e.load_content(),t(".hidden-content-wrap").off("click",".j-refresh-hidden-content").on("click",".j-refresh-hidden-content",(function(){var a=t(this);return a.addClass("loading"),e.load_content((function(){a.removeClass("loading")})),!1})).off("click",".hidden-content-btn-password").on("click",".hidden-content-btn-password",(function(){var a=t(this).parent(),i=a.find('input[name="password"]').val();if(i){var n=a.closest(".hidden-content-wrap").attr("id");e.load_content((function(e){void 0===e[n]&&wpcom_notice({message:"\u89e3\u9501\u5931\u8d25\uff0c\u8bf7\u68c0\u67e5\u53e3\u4ee4\u662f\u5426\u6b63\u786e",type:"warning",show:2e3})}),i,n)}else a.addClass("error"),wpcom_notice({message:"\u8bf7\u8f93\u5165\u53e3\u4ee4",type:"warning",show:2e3})})).off("input change",'.hidden-content-input input[name="password"]').on("input change",'.hidden-content-input input[name="password"]',(function(){t(this).parent().removeClass("error")}))}))},load_content:function(e,t,a){var i=this.$,n=i(".wp-block-wpcom-hidden-content .hidden-content-wrap");if(n.length){n.addClass("loading");var r="undefined"!=typeof _wpcom_js&&_wpcom_js.post_id?_wpcom_js:_wpmx_js;0===n.find(".hidden-content-loading").length&&n.append('<i class="wpcom-icon wi hidden-content-loading"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>');var o={post_id:r.post_id?r.post_id:"",action:"wpcom_get_hidden_content"};t&&(o.password=t,o.id=a),i.ajax({url:r.ajaxurl,data:o,method:"POST",dataType:"json",success:function(t){if(t)for(var a in t){var r=i("#"+a).parent();r.html(t[a]),r.find(".j-map").length&&i(document).trigger("wpcom.map"),r.find(".wp-block-wpcom-accordion").length&&r.find(".wp-block-wpcom-accordion").collapse("show"),i(document).trigger("wpcom.lightbox",[r]),/^post-hidden-content-[\d]+$/.test(a)&&i(".entry-content .wpcom-unlock-more").remove(),r.find(".wp-block-wpcom-hljs > pre").length&&r.find(".wp-block-wpcom-hljs > pre").trigger("hljs.wpcom");var o=r.find(".j-wpcom-video, .wp-block-video video, .wp-block-wpcom-video-code video, .wp-video video");if(o.length)for(var s=0;s<o.length;s++)i("body").trigger("player",[{el:o[s]}])}n.removeClass("loading"),e&&e(t)},error:function(){n.removeClass("loading"),e&&e()}})}}},kt={exports:{}};!function(e,t){e.exports=function(){function e(e,t){var a=void 0;return function(){a&&clearTimeout(a),a=setTimeout(e,t)}}function t(e,t){for(var a=e.length,i=a,n=[];a--;)n.push(t(e[i-a-1]));return n}function a(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(window.Promise)return x(e,t,a);e.recalculate(!0,!0)}function i(e){for(var t=e.options,a=e.responsiveOptions,i=e.keys,n=e.docWidth,r=void 0,o=0;o<i.length;o++){var s=parseInt(i[o],10);n>=s&&(r=t.breakAt[s],P(r,a))}return a}function n(e){for(var t=e.options,a=e.responsiveOptions,i=e.keys,n=e.docWidth,r=void 0,o=i.length-1;o>=0;o--){var s=parseInt(i[o],10);n<=s&&(r=t.breakAt[s],P(r,a))}return a}function r(e){var t=e.useContainerForBreakpoints?e.container.clientWidth:window.innerWidth,a={columns:e.columns};k(e.margin)?a.margin={x:e.margin.x,y:e.margin.y}:a.margin={x:e.margin,y:e.margin};var r=Object.keys(e.breakAt);return e.mobileFirst?i({options:e,responsiveOptions:a,keys:r,docWidth:t}):n({options:e,responsiveOptions:a,keys:r,docWidth:t})}function o(e){return r(e).columns}function s(e){return r(e).margin}function l(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=o(e),i=s(e).x,n=100/a;if(!t)return n;if(1===a)return"100%";var r="px";if("string"==typeof i){var l=parseFloat(i);r=i.replace(l,""),i=l}return i=(a-1)*i/a,"%"===r?n-i+"%":"calc("+n+"% - "+i+r+")"}function c(e,t){var a=o(e.options),i=0,n=void 0,r=void 0;if(1==++t)return 0;var c="px";if("string"==typeof(r=s(e.options).x)){var d=parseFloat(r,10);c=r.replace(d,""),r=d}return n=(r-(a-1)*r/a)*(t-1),i+=l(e.options,!1)*(t-1),"%"===c?i+n+"%":"calc("+i+"% + "+n+c+")"}function d(e){var t=0,a=e.container,i=e.rows;f(i,(function(e){t=e>t?e:t})),a.style.height=t+"px"}function u(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],n=o(e.options),r=s(e.options).y;I(e,n,a),f(t,(function(t){var a=0,n=parseInt(t.offsetHeight,10);isNaN(n)||(e.rows.forEach((function(t,i){t<e.rows[a]&&(a=i)})),t.style.position="absolute",t.style.top=e.rows[a]+"px",t.style.left=""+e.cols[a],e.rows[a]+=isNaN(n)?0:n+r,i&&(t.dataset.macyComplete=1))})),i&&(e.tmpRows=null),d(e)}function p(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],n=o(e.options),r=s(e.options).y;I(e,n,a),f(t,(function(t){e.lastcol===n&&(e.lastcol=0);var a=j(t,"height");a=parseInt(t.offsetHeight,10),isNaN(a)||(t.style.position="absolute",t.style.top=e.rows[e.lastcol]+"px",t.style.left=""+e.cols[e.lastcol],e.rows[e.lastcol]+=isNaN(a)?0:a+r,e.lastcol+=1,i&&(t.dataset.macyComplete=1))})),i&&(e.tmpRows=null),d(e)}var h=function e(t,a){if(!(this instanceof e))return new e(t,a);if(t&&t.nodeName)return t;if(t=t.replace(/^\s*/,"").replace(/\s*$/,""),a)return this.byCss(t,a);for(var i in this.selectors)if(a=i.split("/"),new RegExp(a[1],a[2]).test(t))return this.selectors[i](t);return this.byCss(t)};h.prototype.byCss=function(e,t){return(t||document).querySelectorAll(e)},h.prototype.selectors={},h.prototype.selectors[/^\.[\w\-]+$/]=function(e){return document.getElementsByClassName(e.substring(1))},h.prototype.selectors[/^\w+$/]=function(e){return document.getElementsByTagName(e)},h.prototype.selectors[/^\#[\w\-]+$/]=function(e){return document.getElementById(e.substring(1))};var f=function(e,t){for(var a=e.length,i=a;a--;)t(e[i-a-1])},m=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.running=!1,this.events=[],this.add(e)};m.prototype.run=function(){if(!this.running&&this.events.length>0){var e=this.events.shift();this.running=!0,e(),this.running=!1,this.run()}},m.prototype.add=function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return!!t&&(Array.isArray(t)?f(t,(function(t){return e.add(t)})):(this.events.push(t),void this.run()))},m.prototype.clear=function(){this.events=[]};var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.instance=e,this.data=t,this},g=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.events={},this.instance=e};g.prototype.on=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!(!e||!t)&&(Array.isArray(this.events[e])||(this.events[e]=[]),this.events[e].push(t))},g.prototype.emit=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e||!Array.isArray(this.events[e]))return!1;var a=new v(this.instance,t);f(this.events[e],(function(e){return e(a)}))};var w=function(e){return!("naturalHeight"in e&&e.naturalHeight+e.naturalWidth===0)||e.width+e.height!==0},y=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return new Promise((function(e,a){if(t.complete)return w(t)?e(t):a(t);t.addEventListener("load",(function(){return w(t)?e(t):a(t)})),t.addEventListener("error",(function(){return a(t)}))})).then((function(t){a&&e.emit(e.constants.EVENT_IMAGE_LOAD,{img:t})})).catch((function(t){return e.emit(e.constants.EVENT_IMAGE_ERROR,{img:t})}))},b=function(e,a){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t(a,(function(t){return y(e,t,i)}))},x=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return Promise.all(b(e,t,a)).then((function(){e.emit(e.constants.EVENT_IMAGE_COMPLETE)}))},E=function(t){return e((function(){t.emit(t.constants.EVENT_RESIZE),t.queue.add((function(){return t.recalculate(!0,!0)}))}),100)},T=function(e){if(e.container=h(e.options.container),e.container instanceof h||!e.container)return!!e.options.debug&&console.error("Error: Container not found");e.container.length&&(e.container=e.container[0]),e.options.container=e.container,e.container.style.position="relative"},_=function(e){e.queue=new m,e.events=new g(e),e.rows=[],e.resizer=E(e)},S=function(e){var t=h("img",e.container);window.addEventListener("resize",e.resizer),e.on(e.constants.EVENT_IMAGE_LOAD,(function(){return e.recalculate(!1,!1)})),e.on(e.constants.EVENT_IMAGE_COMPLETE,(function(){return e.recalculate(!0,!0)})),e.options.useOwnImageLoader||a(e,t,!e.options.waitForImages),e.emit(e.constants.EVENT_INITIALIZED)},C=function(e){T(e),_(e),S(e)},k=function(e){return e===Object(e)&&"[object Array]"!==Object.prototype.toString.call(e)},P=function(e,t){k(e)||(t.columns=e),k(e)&&e.columns&&(t.columns=e.columns),k(e)&&e.margin&&!k(e.margin)&&(t.margin={x:e.margin,y:e.margin}),k(e)&&e.margin&&k(e.margin)&&e.margin.x&&(t.margin.x=e.margin.x),k(e)&&e.margin&&k(e.margin)&&e.margin.y&&(t.margin.y=e.margin.y)},j=function(e,t){return window.getComputedStyle(e,null).getPropertyValue(t)},I=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e.lastcol||(e.lastcol=0),e.rows.length<1&&(a=!0),a){e.rows=[],e.cols=[],e.lastcol=0;for(var i=t-1;i>=0;i--)e.rows[i]=0,e.cols[i]=c(e,i)}else if(e.tmpRows)for(e.rows=[],i=t-1;i>=0;i--)e.rows[i]=e.tmpRows[i];else for(e.tmpRows=[],i=t-1;i>=0;i--)e.tmpRows[i]=e.rows[i]},M=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=t?e.container.children:h(':scope > *:not([data-macy-complete="1"])',e.container);i=Array.from(i).filter((function(e){return null!==e.offsetParent}));var n=l(e.options);return f(i,(function(e){t&&(e.dataset.macyComplete=0),e.style.width=n})),e.options.trueOrder?(p(e,i,t,a),e.emit(e.constants.EVENT_RECALCULATED)):(u(e,i,t,a),e.emit(e.constants.EVENT_RECALCULATED))},O=function(){return!!window.Promise},A=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e};Array.from||(Array.from=function(e){for(var t=0,a=[];t<e.length;)a.push(e[t++]);return a});var L={columns:4,margin:2,trueOrder:!1,waitForImages:!1,useImageLoader:!0,breakAt:{},useOwnImageLoader:!1,onInit:!1,cancelLegacy:!1,useContainerForBreakpoints:!1};!function(){try{document.createElement("a").querySelector(":scope *")}catch(e){!function(){function e(e){return function(a){if(a&&t.test(a)){var i=this.getAttribute("id");i||(this.id="q"+Math.floor(9e6*Math.random())+1e6),arguments[0]=a.replace(t,"#"+this.id);var n=e.apply(this,arguments);return null===i?this.removeAttribute("id"):i||(this.id=i),n}return e.apply(this,arguments)}}var t=/:scope\b/gi,a=e(Element.prototype.querySelector);Element.prototype.querySelector=function(e){return a.apply(this,arguments)};var i=e(Element.prototype.querySelectorAll);Element.prototype.querySelectorAll=function(e){return i.apply(this,arguments)}}()}}();var N=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:L;if(!(this instanceof e))return new e(t);this.options={},A(this.options,L,t),this.options.cancelLegacy&&!O()||C(this)};return N.init=function(e){return console.warn("Depreciated: Macy.init will be removed in v3.0.0 opt to use Macy directly like so Macy({ /*options here*/ }) "),new N(e)},N.prototype.recalculateOnImageLoad=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return a(this,h("img",this.container),!e)},N.prototype.runOnImageLoad=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=h("img",this.container);return this.on(this.constants.EVENT_IMAGE_COMPLETE,e),t&&this.on(this.constants.EVENT_IMAGE_LOAD,e),a(this,i,t)},N.prototype.recalculate=function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return a&&this.queue.clear(),this.queue.add((function(){return M(e,t,a)}))},N.prototype.remove=function(){window.removeEventListener("resize",this.resizer),f(this.container.children,(function(e){e.removeAttribute("data-macy-complete"),e.removeAttribute("style")})),this.container.removeAttribute("style")},N.prototype.reInit=function(){this.recalculate(!0,!0),this.emit(this.constants.EVENT_INITIALIZED),window.addEventListener("resize",this.resizer),this.container.style.position="relative"},N.prototype.on=function(e,t){this.events.on(e,t)},N.prototype.emit=function(e,t){this.events.emit(e,t)},N.constants={EVENT_INITIALIZED:"macy.initialized",EVENT_RECALCULATED:"macy.recalculated",EVENT_IMAGE_LOAD:"macy.image.load",EVENT_IMAGE_ERROR:"macy.image.error",EVENT_IMAGE_COMPLETE:"macy.images.complete",EVENT_RESIZE:"macy.resize"},N.prototype.constants=N.constants,N}()}(kt);var Pt=i(kt.exports);jQuery((function(e){var t=e(window),a=e("body"),i=t.height(),n=void 0!==_wpcom_js.webp&&_wpcom_js.webp?_wpcom_js.webp:null,r=e(".navbar-toggle").is(":hidden"),o=e("header.header"),s={},l=0,c=function(e){return{pagination:{el:e.querySelector(".swiper-pagination"),clickable:!0}}};bt.init(),xt.init(),Et.init(),Tt.init(),_t.init(),St.init(),Ct.init();var d=e(".dark-style-toggle");if(0===d.length&&_wpcom_js.dark_style&&"1"==_wpcom_js.dark_style||_wpcom_js.dark_style&&"2"==_wpcom_js.dark_style&&window.matchMedia("(prefers-color-scheme: dark)").matches)u(1,1);else if(d.length){u(d.hasClass("active"),1)}function u(e,t){setTimeout((function(){"undefined"!=typeof tinymce&&tinymce.editors&&tinymce.editors.map((function(t){try{t.contentWindow.document.body.style.color=e?"#fff":"#232339",e?t.contentWindow.document.body.classList.add("style-for-dark"):t.contentWindow.document.body.classList.remove("style-for-dark")}catch(e){}})),t&&setTimeout((function(){"undefined"!=typeof tinymce&&tinymce.editors&&tinymce.editors.map((function(t){t.contentWindow.document.body.style.color=e?"#fff":"#232339",e?t.contentWindow.document.body.classList.add("style-for-dark"):t.contentWindow.document.body.classList.remove("style-for-dark")}))}),2e3)}),500)}function p(){if(!r)for(var t=e("header li.dropdown"),a=0;a<t.length;a++){var i=e(t[a]);0==i.find(".m-dropdown").length&&i.append('<div class="m-dropdown"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-arrow-down-3"></use></svg></i></div>')}}window.kx_share=function(t){var a=e(t).closest(".kx-item");if(a.length&&a.hasClass("entry-footer"))return{title:(a=e(".entry")).find(".entry-title").text().trim(),description:a.find(".entry-content").text().trim().replace("[\u539f\u6587\u94fe\u63a5]",""),url:window.location.href,image:a.find(".entry-content img").attr("src")};if(a.length){var i=(a.find(".kx-title").length?a.find(".kx-title").text():a.find(".kx-content h2").text()).match(/^\s*([^\s]+)\s*$/);return{title:i&&i[1]?i[1]:"",description:a.find(".kx-content p").text().trim().replace("[\u539f\u6587\u94fe\u63a5]",""),url:a.find(".kx-meta").data("url"),image:a.find(".kx-content img").length?a.find(".kx-content img").attr("src"):""}}},window.zt_share=function(t){var a=e(t).closest(".special-item");if(a.length)return{title:a.find(".special-item-title h2").text().trim(),description:a.find(".special-item-desc").text().trim(),url:a.find(".special-item-more").attr("href"),image:a.find(".special-item-thumb img").attr("src")}},p(),t.on("resize",(function(){r=e(".navbar-toggle").is(":hidden"),i=t.height(),p(),x()})),e(".swiper-container").not(".main-slider,.entry-content-slider,.modules-default-slider > .swiper-container").each((function(t,i){var n=!!e(i).closest(".widget_post_slider, .widget_image_slider").length;a.trigger("swiper",{el:i,args:Object.assign(c(i),{autoHeight:n})})})),e(".main-slider").each((function(e,t){a.trigger("swiper",{el:t,args:Object.assign(c(t),{autoHeight:!0,breakpoints:{768:{autoHeight:!1}}})})}));var h,f=e("#wrap"),m=e("footer.footer");f.find(".post-loop-masonry").length&&f.find(".post-loop-masonry").each((function(t,a){!function(t){var a=e(t),i="id-"+l;a.data("macy_id",i),l+=1;var n,r=a.attr("class").match(/cols-([\d]+)/i),o=Pt({container:t,columns:r&&r[1]?r[1]:3,breakAt:{1024:3,767:2}});s[i]=o;var c=new MutationObserver((function(t){t.forEach((function(t){if("attributes"===t.type&&"IMG"===t.target.nodeName)n&&clearTimeout(n),n=setTimeout((function(){return o.recalculate(!0)}),1);else if("childList"===t.type){n&&clearTimeout(n),n=setTimeout((function(){return o.recalculate(!0)}),1);var a=Array.prototype.slice.call(t.addedNodes);a&&a.length&&e(a).find("img").on("load",(function(){e(this).attr("img-loaded",!0)}))}}))}));c.observe(t,{childList:!0,subtree:!0,attributes:!0,characterData:!1})}(a)}));var v=new MutationObserver((function(e){e.forEach((function(e){h&&clearTimeout(h),h=setTimeout((function(){x(),f.find("img").eq(0).attr("top-news-change",!0)}),50)}))})),g=document.querySelector(".top-news");g&&v.observe(g,{childList:!0,subtree:!0,attributes:!0,characterData:!1});var w=new MutationObserver((function(e){e.forEach((function(e){"childList"===e.type&&e.addedNodes.length&&p()}))})),y=e(".navbar-action");function x(){if(f.length){var e=parseInt(m.css("marginBottom"));e=e||0;var t=i-f.offset().top-m.outerHeight()-e;f.css("min-height",t)}}y.length&&w.observe(y[0],{childList:!0,subtree:!0,attributes:!1,characterData:!1}),a.on("click",".kx-new",(function(){window.location.href=window.location.href})).on("click",".widget-kx-list .kx-title",(function(){var t=e(this);t.parent().find(".kx-content").slideToggle("fast"),t.closest(".kx-item").toggleClass("active")})).on("click",".j-post-tab",(function(){var t=e(this),a=t.closest(".widget"),i=t.index(),n=a.find(".j-post-tab-wrap");a.find(".j-post-tab").removeClass("active"),t.addClass("active"),n.removeClass("active").eq(i).addClass("active")})).on("click",".ez-toc-widget-sticky-container li > a, .ez-toc-widget-container li > a, .ez-toc-counter li > a",(function(){M.find(".entry-readmore-btn").trigger("click")})),x();var E=e(".sidebar");if(_wpcom_js.fixed_sidebar&&"1"==_wpcom_js.fixed_sidebar&&E.length&&E.find(".widget").length&&t.width()>991)for(var T=0;T<E.length;T++)B(e(E[T]));var _=e(".kx-list");if(_.length&&!_.closest(".tab-wrap").length){var S;window.kxDate=_.find(".kx-date"),S=o.outerHeight()+o.position().top;var C=kxDate.first().offset().top,k={$el:null},P=e(".kx-new"),j=kxDate.first().outerHeight();t.on("scroll",(function(){var a=t.scrollTop(),i=kxDate.length-1;e.each(kxDate,(function(t,n){var r=e(n),o=r.offset().top-a-S;return o>0&&k.$el&&k.top<0?(kxDate.removeClass("fixed").css({width:"auto",top:"auto"}),k.$el.addClass("fixed").css("top",S).css("width",_.outerWidth()),P.addClass("fixed").css({top:S+51,width:_.outerWidth()}),void _.css("padding-top",j)):0===t&&o<=0?(C-S>=a?(kxDate.removeClass("fixed").css({width:"auto",top:"auto"}),P.removeClass("fixed").css("width","auto"),_.css("padding-top","")):(kxDate.removeClass("fixed").css({width:"auto",top:"auto"}),r.addClass("fixed").css("top",S).css("width",_.outerWidth()),P.addClass("fixed").css({top:S+51,width:_.outerWidth()}),_.css("padding-top",j)),k.$el=r,void(k.top=o)):(t===i&&o<=0?(kxDate.removeClass("fixed").css({width:"auto",top:"auto"}),r.addClass("fixed").css("top",S).css("width",_.outerWidth()),P.addClass("fixed").css({top:S+51,width:_.outerWidth()}),_.css("padding-top",j)):0===t&&o>0&&kxDate.hasClass("fixed")&&(kxDate.removeClass("fixed").css({width:"auto",top:"auto"}),P.removeClass("fixed").css("width","auto"),_.css("padding-top","")),k.$el=r,void(k.top=o))}))})),setInterval((function(){var t=e(".kx-item").first().data("id");e.ajax({url:_wpcom_js.ajaxurl,data:{id:t,action:"wpcom_new_kuaixun"},method:"POST",dataType:"text",success:function(t){t&&e(".kx-new").html(t).show()}})}),1e4)}e(".kx-list,.widget-kx-list,.entry-footer,.tab-wrap").on("click",".share-icon",(function(){var t=e(this),a=kx_share(this);if(a&&t.hasClass("copy")){var i=a.title+"\r\n"+a.description+"\r\n"+decodeURIComponent(a.url);e(document).trigger("copy_text",[i,function(){wpcom_notice({message:_wpcom_js.js_lang.copy_done,show:1500})},function(){wpcom_alert(_wpcom_js.js_lang.copy_fail)}])}})),e(".navbar-search").on("keydown",".navbar-search-input",(function(){e(this).closest(".navbar-search").removeClass("warning")})).on("submit",(function(){var t=e(this);if(""===t.find(".navbar-search-input").val().trim())return t.addClass("warning"),t.find(".navbar-search-input").trigger("focus"),!1})),e(document).on("click",(function(t){var a=e(t.target);r&&0===a.closest(".navbar-search").length&&0===a.closest(".j-navbar-search").length&&o.find(".navbar-search").fadeOut(300,(function(){o.find(".primary-menu").fadeIn(300),o.find(".j-navbar-search").fadeIn(300).css("display","inline-block"),o.removeClass("is-search")}))})).on("click",".j-navbar-search",(function(){o.find(".j-navbar-search").fadeOut(300),o.find(".primary-menu").fadeOut(300,(function(){o.find(".navbar-search").removeClass("warning").fadeIn(300,(function(){e(".navbar-search-input").trigger("focus")})),o.addClass("is-search")}))})).on("click",".navbar-search-close",(function(){o.find(".navbar-search").fadeOut(300,(function(){o.find(".primary-menu").fadeIn(300),o.find(".j-navbar-search").fadeIn(300).css("display","inline-block"),o.removeClass("is-search")}))})).on("click","#j-reading-back",(function(){a.removeClass("reading"),e(this).remove()})).on("click","#j-reading",(function(){a.addClass("reading").append('<div class="reading-back" id="j-reading-back"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-back"></use></svg></i></div>')})).on("click",".dark-style-toggle",(function(){var t=e(this),i=t.hasClass("active");t.addClass("loading"),window.localStorage&&localStorage.setItem("darkStyle",i?0:1),u(i?0:1),i?(a.removeClass("style-for-dark"),setTimeout((function(){t.find("use")[0].setAttributeNS("http://www.w3.org/1999/xlink","xlink:href","#wi-sun-fill"),t.removeClass("active"),t.removeClass("loading")}),580)):(a.addClass("style-for-dark"),setTimeout((function(){t.find("use")[0].setAttributeNS("http://www.w3.org/1999/xlink","xlink:href","#wi-moon-fill"),t.addClass("active"),t.removeClass("loading")}),580))})),e(".entry").on("click",".btn-zan",(function(){var t=e(this);if(!t.hasClass("liked")){var a=t.data("id");e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_like_it",id:a},dataType:"json",success:function(e){0==e.result?t.addClass("liked").find("span").html("("+e.likes+")"):-2==e.result&&t.addClass("liked")}})}})).on("click",".j-heart",(function(){var t=e(this),a=t.data("id");e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_heart_it",id:a},dataType:"json",success:function(a){0==a.result?(t.addClass("hearted").find("span").html(a.favorites),t.find(".wi").html('<svg aria-hidden="true"><use xlink:href="#wi-star-fill"></use></svg>')):1==a.result?(t.removeClass("hearted").find("span").html(a.favorites),t.find(".wi").html('<svg aria-hidden="true"><use xlink:href="#wi-star"></use></svg>')):-1==a.result&&e("#login-modal")._modal()}})})),e("#commentform").on("submit",(function(){var t=e(".comment-form-comment textarea"),a=0,i=0,n=e(this).find("input.required");if(""===t.val().trim()&&(t.addClass("error").trigger("focus"),i=1,a=1),n.each((function(t,n){var r=e(n);""===r.val().trim()&&(r.addClass("error"),0==i&&(r.trigger("focus"),i=1),a=1)})),a)return!1})).on("keydown",".required",(function(){e(this).removeClass("error")})),e("#comments, #reviews").on("click",".comment-must-login,#must-submit,.comment-reply-login",(function(){return e("#login-modal")._modal(),!1}));var I=e(".entry-bar");I.length&&t.width()>767&&(D(),t.on("scroll",(function(){D()})));var M=e(".entry-readmore");if(M.length){var O=e(".entry-content"),A=O.outerHeight();if(O.find(".entry-copyright").length&&(A-=O.find(".entry-copyright").outerHeight()),A>t.height()+150){var L=(A-t.height())/A,N=_wpcom_js.js_lang.expand_more.replace("%s",parseInt(100*L)+"%");M.find(".entry-readmore-btn").html(N+'<i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-expand"></use></svg></i>'),O.css({height:t.height(),overflow:"hidden"}),M.on("click",".entry-readmore-btn",(function(){O.css({height:"",overflow:""}),M.hide()})),M.show()}}function D(){I.offset().top+I.outerHeight()>t.scrollTop()+i?(I.addClass("fixed"),I.find(".entry-bar-inner").css("width",e(".main").width())):I.removeClass("fixed")}function B(a){var n=a.closest(".container").find(".main"),r=n.offset().top,o=0,s=0,l=0;if(n.length){var c,d=new MutationObserver((function(t){t.forEach((function(t){if("attributes"!==t.type||"IMG"!==t.target.nodeName&&!/(post-tabs-list|tab-wrap|profile-tab-item|entry-readmore|swiper-wrapper|panel-collapse|wp-block-wpcom-tab)/i.test(t.target.className)){if("childList"===t.type){c&&clearTimeout(c),c=setTimeout((function(){return u()}),5);var a=Array.prototype.slice.call(t.addedNodes);a&&a.length&&e(a).find("img").on("load",(function(){e(this).attr("img-loaded",!0)}))}}else c&&clearTimeout(c),c=setTimeout((function(){return u()}),5)}))}));function u(){o=a.outerHeight(),l=n.outerHeight(),r=n.offset().top,s=r+l,t.trigger("scroll")}f.length&&d.observe(f[0],{childList:!0,subtree:!0,attributes:!0,characterData:!1}),t.on("scroll",(function(){if(l<=o)a.removeClass("fixed").removeClass("abs");else{var e=t.scrollTop();i-r>o?e+o+r>s?a.removeClass("fixed").addClass("abs").css({bottom:0,top:"auto"}):a.removeClass("abs").addClass("fixed").css({bottom:"auto",top:r}):e+i>s?a.addClass("abs").removeClass("fixed").css({bottom:0,top:"auto"}):e+i>r+o?a.addClass("fixed").removeClass("abs").css({bottom:0,top:"auto"}):a.removeClass("fixed").removeClass("abs")}})),c=setTimeout((function(){return u()}),10)}}f.find("img").on("load",(function(){e(this).attr("img-loaded",!0)}));var z=null;e("#wrap").on("click",".j-newslist .tab",(function(){var t=e(this),i=t.parent(),n=t.closest(".main-list").find(".tab-wrap"),r=i.find(".tab.active").index(),o=t.index();i.find(".tab").removeClass("active"),t.addClass("active");var l=t.find("a").data("id");if(l&&1!=t.data("loaded")&&0!==t.index()){n.eq(r).append('<i class="wpcom-icon wi wi-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'),n.eq(r).addClass("loading");var d=i.data("type"),u=i.data("per_page");e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_load_posts",id:l,type:d||"default",per_page:u},dataType:"html",success:function(i){var s="0"==i?'<li class="item"><p style="text-align: center;color:#999;margin:10px 0;">'+_wpcom_js.js_lang.no_content+"</p></li>":i,l=e(s);n.eq(o).find(".post-loop").html(l),n.eq(r).find(".wi-loader").remove(),n.eq(r).removeClass("loading"),setTimeout((function(){n.removeClass("active"),n.eq(o).addClass("active");var t=l.parent().find(".load-more-wrap");if(t.length){var i=t.prop("outerHTML");n.eq(o).append(i),t.remove()}n.eq(o).find(".post-loop-masonry").length&&n.eq(o).find(".post-loop-masonry").find("img").on("load",(function(){e(this).attr("img-loaded",!0)})),q(l.find(".j-lazy")),l.find(".swiper-container").length&&l.find(".swiper-container").each((function(e,t){a.trigger("swiper",{el:t,args:c(t)})}))}),0),t.data("loaded",1)},error:function(){n.eq(r).find(".wi-loader").remove(),n.eq(r).removeClass("loading"),n.removeClass("active"),n.eq(o).addClass("active"),n.eq(o).find(".post-loop").html('<li class="item"><p style="text-align: center;color:#999;margin:10px 0;">'+_wpcom_js.js_lang.load_failed+"</p></li>")}})}else{n.removeClass("active"),n.eq(o).addClass("active");var p=n.eq(o).find(".post-loop-masonry");if(p.length&&p.data("macy_id")){var h=p.data("macy_id");s&&s[h]&&s[h].recalculate(!0)}}})).on("click",".j-mix-tabs .tab",(function(){var t=e(this),a=t.parent(),i=t.closest(".mix-tabs").find(".tab-wrap");if(a.find(".tab").removeClass("active"),t.addClass("active"),i.removeClass("active"),i.eq(t.index()).addClass("active"),1!=t.data("loaded")&&0!==t.index()){i.eq(t.index()).addClass("loading");var n=a.closest(".wpcom-modules").find("script").html(),r=(n=JSON.parse(n))[t.index()];r&&e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:Object.assign(r,{action:"wpcom_load_mix_tabs"}),dataType:"html",success:function(a){i.eq(t.index()).removeClass("loading");var n=e(a);i.eq(t.index()).html(n),i.eq(t.index()).find(".post-loop-masonry").length&&i.eq(t.index()).find(".post-loop-masonry").find("img").on("load",(function(){e(this).attr("img-loaded",!0)})),q(n.find(".j-lazy")),2==r.type&&b.init(),t.data("loaded",1)},error:function(){i.eq(t.index()).html('<li class="item"><p style="text-align: center;color:#999;margin:10px 0;">'+_wpcom_js.js_lang.load_failed+"</p></li>"),i.eq(t.index()).removeClass("loading")}})}else{var o=i.eq(t.index()).find(".post-loop-masonry");if(o.length&&o.data("macy_id")){var l=o.data("macy_id");s&&s[l]&&s[l].recalculate(!0)}}})).on("mouseenter",".j-newslist > li, .j-mix-tabs > li",(function(){clearTimeout(z);var t=e(this),a=t.closest("ul"),i=a.find(".tab-underscore"),n=a.find(">li").first().position().left,r=t.position().left-n;i.css({transform:"translateX("+r+"px)",width:t.width()})})).on("mouseleave",".j-newslist > li, .j-mix-tabs > li",(function(){var t=this;clearTimeout(z),z=setTimeout((function(){var a=e(t).closest("ul"),i=a.find(".active"),n=a.find(".tab-underscore"),r=a.find(">li").first().position().left,o=i.position().left-r;n.css({transform:"translateX("+o+"px)",width:i.width()})}),300)})).on("click",".j-load-more, .j-load-kx",(function(){var t=e(this);if(!t.hasClass("disabled")&&!t.hasClass("loading")){var i=null,n=t.data("page");if(n=void 0!==n?n+1:2,t.hasClass("j-load-kx"))i={action:"wpcom_load_kuaixun",page:n};else{var r=t.data("id"),o=t.data("exclude"),s=t.closest(".main-list").find(".j-newslist"),l=s.data("type"),d=s.data("per_page");l=l||t.closest(".main-list").data("type"),i={action:"wpcom_load_posts",id:r,page:n,type:l||"default",per_page:d,exclude:o}}return t.loading(1),e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:i,dataType:"html",success:function(i,r,o){if("0"==i)t.addClass("disabled").text(_wpcom_js.js_lang.page_loaded);else{var s=e(i);t.hasClass("j-load-more")?t.closest(".tab-wrap").find(".post-loop").append(s):t.hasClass("j-load-kx")&&(e(s[0]).text()==e(".kx-list .kx-date:last").text()&&s.first().hide(),t.parent().before(s),t.parent().parent().find(".kx-date:hidden").remove(),window.kxDate=e(".kx-list .kx-date"),b.init()),q(s.find(".j-lazy")),s.find(".swiper-container").length&&s.find(".swiper-container").each((function(e,t){a.trigger("swiper",{el:t,args:c(t)})})),t.data("page",n)}t.loading(0)},error:function(){t.loading(0)}}),!1}})).on("click",".j-mix-tabs-more",(function(){var t=e(this),a=t.closest(".tab-wrap"),i=t.closest(".wpcom-modules");if(!t.hasClass("disabled")&&!t.hasClass("loading")){var n=i.find("script").html();n=JSON.parse(n);var r=t.data("page");r=r||2;var o=a.data("index");if(n&&n[o]){t.loading(1);var s=n[o];s.page=r,e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:Object.assign(s,{action:"wpcom_load_mix_tabs"}),dataType:"html",success:function(i){var n=e(e(i).html());n&&n.length?(0==s.type?a.find(".post-loop").append(n):1==s.type?a.find(".topic-list").append(n):3==s.type?a.find(".q-content").append(n):(a.find(".kx-list").append(n),b.init()),q(n.find(".j-lazy")),t.data("page",r+1)):(t.addClass("disabled"),t.text(_wpcom_js.js_lang.page_loaded)),t.loading(0)},error:function(){t.loading(0)}})}}})).on("click",".j-load-archive",(function(){var t=e(this);if(!t.hasClass("disabled")&&!t.hasClass("loading")){var a=t.data("page");a=void 0!==a?a+1:2,t.loading(1);var i=t.closest(".sec-panel-body").find(" > .post-loop"),n=i.attr("class").match(/post-loop-([a-z0-9_-]+)/i),r=$("attr"),o=$("order");return H({$dom:i,data:{action:"wpcom_load_posts",page:a,taxonomy:t.data("tax"),id:t.data("id"),type:n&&n[1]?n[1]:"default",attr:r||"",order:o||""},callback:function(){t.data("page",a),t.loading(0)},error:function(){t.loading(0)}}),!1}})),e(".special-wrap").on("click",".load-more",(function(){var t=e(this);if(!t.hasClass("disabled")&&!t.hasClass("loading")){var a=t.data("page");a=a?a+1:2,t.loading(1),e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:{action:"wpcom_load_special",page:a},dataType:"html",success:function(i){if("0"==i)t.addClass("disabled").text(_wpcom_js.js_lang.page_loaded);else{var n=e(i);t.closest(".special-wrap").find(".special-list").append(n),q(n.find(".j-lazy")),t.data("page",a),b.init()}t.loading(0)},error:function(){t.loading(0)}})}}));var G=e(".load-more-wrap > .scroll-loader");if(G.length){var R=G.parent();t.on("scroll",(function(){if(t.scrollTop()+i>R.offset().top-50){if(G.hasClass("disabled")||G.hasClass("loading"))return;var e=G.data("page");e=void 0!==e?e+1:2,G.addClass("loading");var a=G.closest(".sec-panel-body").find(" > .post-loop"),n=a.attr("class").match(/post-loop-([a-z0-9_-]+)/i),r=$("attr"),o=$("order");H({$dom:a,data:{action:"wpcom_load_posts",page:e,taxonomy:G.data("tax"),id:G.data("id"),type:n&&n[1]?n[1]:"default",attr:r||"",order:o||""},callback:function(t){G.data("page",e),"0"==t?G.removeClass("loading").addClass("disabled").text(_wpcom_js.js_lang.page_loaded):G.removeClass("loading")},error:function(){G.removeClass("loading").addClass("disabled")}})}}))}function q(e){e.length&&e.lazyload({webp:n})}function H(t){e.ajax({type:"POST",url:_wpcom_js.ajaxurl,data:t.data,dataType:"html",success:function(i){if("0"==i)t.$dom.parent().find(".load-more").addClass("disabled").text(_wpcom_js.js_lang.page_loaded);else{var n=e(i);t.$dom.append(n),q(n.find(".j-lazy")),n.find(".swiper-container").length&&n.find(".swiper-container").each((function(e,t){a.trigger("swiper",{el:t,args:c(t)})}))}t.callback&&t.callback(i)},error:function(){t.error&&t.error()}})}function $(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),a=window.location.search.substr(1).match(t);return null!=a?decodeURIComponent(a[2]):null}}))}));
