<?php if (!defined("__TYPECHO_ROOT_DIR__")) {
  exit();
}

error_reporting(E_ERROR);

require_once "core/index.php";
require_once __DIR__ . '/core/statistics.php';

/**
 * 初始化主题
 * @param $archive
 * @return void
 */
function themeInit($archive)
{
    // 添加文章浏览量字段
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();
    
    // 检查是否已经存在views字段
    try {
        $row = $db->fetchRow($db->select()->from('table.contents')->page(1, 1));
        if (!array_key_exists('views', $row)) {
            // 在文章表中添加views字段
            $db->query('ALTER TABLE `' . $prefix . 'contents` ADD `views` INT(10) DEFAULT 0;');
        }

        // 创建下载统计表（如果不存在）
        $db->query("CREATE TABLE IF NOT EXISTS `{$prefix}downloads` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `url` varchar(255) NOT NULL,
            `count` int(11) DEFAULT 0,
            `last_download` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `url` (`url`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
    } catch (Exception $e) {
        // 如果发生错误，记录到日志中
        error_log($e->getMessage());
    }
    
    // 设置首页和分类页面每页显示数量统一为10篇
    if ($archive->is('index') || $archive->is('category')) {
        $archive->parameter->pageSize = 10;
    }
    
    //评论回复楼层最高999层.这个正常设置最高只有7层
    Helper::options()->commentsMaxNestingLevels = 999;
    //将最新的评论展示在前
    Helper::options()->commentsOrder = "DESC";
    //设置评论发布间隔时间为 10 秒
    Helper::options()->commentsPostIntervalEnable = true;
    Helper::options()->commentsPostInterval = 10;

    // 注册 Ajax 用户处理器
    Helper::addRoute('ajax-user', '/action/ajax-user', 'Ajax_User', 'action');
}

/**
 * 文章与独立页自定义字段
 */
function themeFields($layout)
{
    $banner = new Typecho_Widget_Helper_Form_Element_Text(
        "thumbnail",
        null,
        null,
        _t("缩略图"),
        _t("输入一个图片 url，作为缩略图显示在文章列表，没有则不显示")
    );
    $layout->addItem($banner);
    
    $keyword = new Typecho_Widget_Helper_Form_Element_Textarea(
        "keyword",
        null,
        null,
        _t("SEO 关键词"),
        _t("多个关键词用英文下逗号隔开")
    );
    $layout->addItem($keyword);
    
    $description = new Typecho_Widget_Helper_Form_Element_Textarea(
        "description",
        null,
        null,
        _t("SEO 描述"),
        _t("简单一句话描述")
    );
    $layout->addItem($description);

    // 只在独立页面添加资源导航字段
    if ($layout instanceof Widget_Contents_Page_Edit) {
        $resourceItems = new Typecho_Widget_Helper_Form_Element_Textarea(
            'resourceItems',
            NULL,
            NULL,
            _t('资源项目'),
            _t('每行一个资源，格式：分类ID|名称|描述|图标URL|链接')
        );
        $layout->addItem($resourceItems);
    }
}

/**
 * 获取优化后的缩略图URL
 * @param string $url 原始URL
 * @return string 优化后的URL
 */
function getOptimizedThumbnail($url) {
    if (strpos($url, $_SERVER['HTTP_HOST']) !== false) {
        return $url;
    }
    return $url;
}

/**
 * 获取随机缩略图
 * @return string 图片URL
 */
function getRandomThumbnail() {
    static $thumbnails = null;
    static $defaultImage = '/images/thumbnails/default.jpg';
    
    if ($thumbnails === null) {
        $thumbnailDir = __DIR__ . '/images/thumbnails/';
        if (!is_dir($thumbnailDir)) {
            return Helper::options()->themeUrl . $defaultImage;
        }
        $thumbnails = glob($thumbnailDir . 'thumb[0-9][0-9].{jpg,png,gif,webp}', GLOB_BRACE);
    }
    
    if (empty($thumbnails)) {
        return Helper::options()->themeUrl . $defaultImage;
    }
    
    $randomIndex = array_rand($thumbnails);
    $filename = basename($thumbnails[$randomIndex]);
    
    return Helper::options()->themeUrl . '/images/thumbnails/' . $filename;
}

/**
 * 截取字符串
 * @param string $str 需要截取的字符串
 * @param int $length 截取长度
 * @param string $suffix 后缀
 * @return string
 */
function subString($str, $length = 25, $suffix = '...') {
    if(mb_strlen($str, 'UTF-8') > $length) {
        return mb_substr($str, 0, $length, 'UTF-8') . $suffix;
    }
    return $str;
}

/**
 * 获取随机名言
 * @return string
 */
function getRandomQuote() {
    $quotes = Helper::options()->quotes ? explode("\n", Helper::options()->quotes) : [
        "生活不止眼前的苟且，还有诗和远方。",
        "人生就像一场旅行，不必在乎目的地，在乎的是沿途的风景。",
        "不要等待机会，而要创造机会。",
        "生活中不是缺少美，而是缺少发现美的眼睛。"
    ];
    return $quotes[array_rand($quotes)];
}

/**
 * 获取最新评论
 * @param int $limit 获取数量
 * @return array 评论数组
 */
function getRecentComments($limit = 5) {
    $db = Typecho_Db::get();
    
    $select = $db->select()->from('table.comments')
        ->where('status = ?', 'approved')
        ->where('type = ?', 'comment')
        ->order('created', Typecho_Db::SORT_DESC)
        ->limit($limit);

    try {
        $comments = $db->fetchAll($select);
        return array_map(function ($comment) {
            return [
                'author' => mb_substr($comment['author'], 0, 10, 'UTF-8'),
                'text' => strip_tags($comment['text'])
            ];
        }, $comments);
    } catch (Exception $e) {
        return array();
    }
}

// 添加路由删除钩子
function themeDeactivate() {
    Helper::removeRoute('ajax-user');
}

$custom_functions = __DIR__ . "/custom/functions.php";
if (file_exists($custom_functions)) {
    include_once $custom_functions;
}

// 处理下载统计更新的action
if (isset($_GET['action']) && $_GET['action'] === 'update_download_count' && isset($_POST['url'])) {
    require_once 'includes/download-btn.php';
    updateDownloadCount($_POST['url']);
    exit;
}
