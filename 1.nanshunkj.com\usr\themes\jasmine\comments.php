<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php
// 调试信息
$isLoggedIn = $this->user->hasLogin();
$userId = $this->user->uid;
$userName = $this->user->screenName;
$userMail = $this->user->mail;
?>
<script>
console.log('PHP Login Status:', {
    isLoggedIn: <?php echo $isLoggedIn ? 'true' : 'false' ?>,
    userId: <?php echo $userId ? $userId : 'null' ?>,
    userName: '<?php echo $userName ?>',
    userMail: '<?php echo $userMail ?>'
});
</script>

<div id="comments" class="mt-12" data-no-instant>
    <?php $this->comments()->to($comments); ?>
    <?php if ($this->allow('comment')): ?>
        <div id="<?php $this->respondId(); ?>" class="respond">
            <?php if ($this->user->hasLogin()): ?>
                <!-- 已登录用户的评论表单 -->
                <form method="post" action="<?php $this->commentUrl() ?>" id="comment-form" role="form" class="flex flex-col gap-y-4 bg-white dark:bg-[#1a1a1a] rounded-lg p-4 border border-gray-100 dark:border-gray-800" data-no-instant>
                    <div class="flex items-center gap-x-3 mb-2">
                        <img class="rounded-lg w-[40px] h-[40px] object-cover" src="<?php echo Typecho_Common::gravatarUrl($this->user->mail, 40, 'X', 'mm', $this->request->isSecure()) ?>" loading="lazy" alt="<?php $this->user->screenName(); ?>">
                        <div class="flex flex-col">
                            <span class="text-gray-900 dark:text-white font-medium text-sm"><?php $this->user->screenName(); ?></span>
                            <a class="text-gray-500 hover:text-black dark:text-gray-400 dark:hover:text-white text-xs transition-colors" href="<?php $this->options->logoutUrl(); ?>" title="注销">注销</a>
                        </div>
                    </div>
                    
                    <div class="basis-full">
                        <textarea rows="4" name="text" id="textarea-user" class="w-full border border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3 dark:bg-[#1a1a1a] dark:text-gray-300 focus:border-black dark:focus:border-gray-500 transition-colors" required placeholder="写下你的想法..."><?php $this->remember('text'); ?></textarea>
                    </div>
                    
                    <div class="flex justify-end items-center gap-x-3">
                        <button type="button" id="emoji-btn" class="px-4 py-1.5 text-sm rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 transition-all">表情</button>
                        <?php $comments->cancelReply(); ?>
                        <button type="submit" class="px-4 py-1.5 text-sm rounded-lg bg-black text-white hover:bg-gray-800 transition-all">提交评论</button>
                    </div>
                    
                    <?php $security = $this->widget('Widget_Security'); ?>
                    <input type="hidden" name="_" value="<?php echo $security->getToken($this->request->getReferer()); ?>"/>
                </form>
            <?php else: ?>
                <!-- 未登录用户的评论提示 -->
                <div class="flex flex-col items-center justify-center py-8 px-4 bg-gray-50 dark:bg-[#1a1a1a] rounded-lg border border-gray-100 dark:border-gray-800">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">登录后参与讨论</div>
                    <div class="flex gap-3">
                        <button type="button" class="quick-login-btn px-5 py-2 bg-black text-white rounded-lg text-sm hover:shadow-md transition-all">快速登录</button>
                        <button type="button" class="quick-register-btn px-5 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm hover:shadow-md dark:bg-gray-800 dark:text-gray-300 transition-all">注册账号</button>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if ($comments->have()): ?>
        <div class="comment-list mt-8">
            <?php $comments->listComments(); ?>
        </div>
        <div class="mt-8">
            <?php $comments->pageNav('上一页', '下页', 0, '..'); ?>
        </div>
    <?php endif; ?>
</div>

<div id="emoji-panel" class="emoji-panel">
    <div class="emoji-tabs">
        <div class="emoji-tab active" data-category="faces">表情</div>
        <div class="emoji-tab" data-category="hearts">爱心</div>
        <div class="emoji-tab" data-category="gestures">手势</div>
        <div class="emoji-tab" data-category="animals">动物</div>
        <div class="emoji-tab" data-category="foods">美食</div>
        <div class="emoji-tab" data-category="weather">天气</div>
    </div>
    <div class="emoji-content">
        <div id="faces" class="emoji-category active"></div>
        <div id="hearts" class="emoji-category"></div>
        <div id="gestures" class="emoji-category"></div>
        <div id="animals" class="emoji-category"></div>
        <div id="foods" class="emoji-category"></div>
        <div id="weather" class="emoji-category"></div>
    </div>
</div>

<link rel="stylesheet" href="<?php $this->options->themeUrl('assets/css/comments.css'); ?>">
<script src="<?php $this->options->themeUrl('assets/js/auth.js'); ?>"></script>
<script src="<?php $this->options->themeUrl('assets/js/emoji.js'); ?>"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化认证对话框
    const auth = new AuthDialog({
        onLoginSuccess: function() {
            // 登录成功后刷新评论区
            window.location.reload();
        }
    });
    
    // 初始化表情面板
    const emojiPanel = new EmojiPanel();
    
    // 获取DOM元素
    const userTextarea = document.getElementById('textarea-user');
    const emojiBtn = document.getElementById('emoji-btn');
    
    // 检查是否已登录
    const isLoggedIn = <?php echo $this->user->hasLogin() ? 'true' : 'false'; ?>;
    console.log('Login status:', isLoggedIn);
    
    // 如果未登录，绑定登录和注册按钮事件
    if (!isLoggedIn) {
        const loginBtn = document.querySelector('.quick-login-btn');
        const registerBtn = document.querySelector('.quick-register-btn');
        
        if (loginBtn) {
            loginBtn.addEventListener('click', function() {
                auth.showLoginForm();
            });
        }
        
        if (registerBtn) {
            registerBtn.addEventListener('click', function() {
                auth.showRegisterForm();
            });
        }
    }
    
    // 如果已登录，初始化表情面板
    if (isLoggedIn && emojiBtn && userTextarea) {
        emojiBtn.addEventListener('click', function() {
            emojiPanel.toggle();
        });
        
        // 监听表情选择
        document.addEventListener('emoji:select', function(e) {
            const emoji = e.detail.emoji;
            const startPos = userTextarea.selectionStart;
            const endPos = userTextarea.selectionEnd;
            const text = userTextarea.value;
            
            userTextarea.value = text.substring(0, startPos) + emoji + text.substring(endPos);
            userTextarea.focus();
            userTextarea.selectionStart = userTextarea.selectionEnd = startPos + emoji.length;
        });
    }
});
</script>

<?php function threadedComments($comments, $options) {
    $commentClass = '';
    if ($comments->authorId) {
        if ($comments->authorId == $comments->ownerId) {
            $commentClass .= ' comment-by-author';
        } else {
            $commentClass .= ' comment-by-user';
        }
    }
?>
    <li id="<?php $comments->theId(); ?>" class="comment-body flex flex-col gap-y-4 py-6 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-[#1a1a1a] transition-all<?php
        if ($comments->levels > 0) {
            echo ' comment-child ml-8';
            $comments->levelsAlt(' comment-level-odd', ' comment-level-even');
        } else {
            echo ' comment-parent';
        }
        $comments->alt(' comment-odd', ' comment-even');
        echo $commentClass;
    ?>">
        <div class="flex w-full gap-x-3 grow">
            <img class="rounded-lg w-[45px] h-[45px] object-cover" width="45" height="45" src="<?php echo Typecho_Common::gravatarUrl($comments->mail, 45, 'X', 'mm', $this->request->isSecure()); ?>" loading="lazy" alt="<?php $comments->author; ?>">
            <div class="flex flex-col w-full">
                <div class="flex justify-between items-center">
                    <div class="flex items-center gap-x-2">
                        <span class="author-name text-sm"><?php echo $comments->author; ?></span>
                        <?php if ($comments->authorId == $comments->ownerId): ?>
                            <span class="text-xs px-1.5 py-0.5 bg-black text-white rounded">作者</span>
                        <?php endif; ?>
                        <span class="text-xs text-gray-400"><?php echo getHumanizedDate($comments->created); ?></span>
                        <?php if ($comments->status == 'waiting'): ?>
                            <span class="text-xs text-yellow-500">- 审核中</span>
                        <?php endif; ?>
                    </div>
                    <div class="comments-reply bg-black/5 dark:bg-white/5 hover:bg-black/10 dark:hover:bg-white/10 text-gray-600 dark:text-gray-400 rounded-lg px-3 py-1 text-sm transition-all" data-no-instant>
                        <?php $comments->reply('回复'); ?>
                    </div>
                </div>
                <div class="comment-content text-gray-700 dark:text-gray-300 mt-2 text-sm">
                    <?php echo getCommentAt($comments->coid); ?> <?php $comments->content(); ?>
                </div>
            </div>
        </div>
        <?php if ($comments->children): ?>
            <div class="comment-children mt-4">
                <?php $comments->threadedComments($options); ?>
            </div>
        <?php endif; ?>
    </li>
<?php } ?>
