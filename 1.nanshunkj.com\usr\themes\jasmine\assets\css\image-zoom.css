/* 遮罩层样式优化 */
.medium-zoom-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0;
    transition: opacity .3s ease-in-out;
    will-change: opacity;
    background: rgba(0, 0, 0, 0.9) !important;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    z-index: 9999;
}

.medium-zoom--opened .medium-zoom-overlay {
    opacity: 1;
}

/* 图片样式优化 */
.markdown-body img {
    cursor: zoom-in !important;
    transition: all 0.3s ease-in-out !important;
    border-radius: 8px;
}

.markdown-body img:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 放大后的图片样式 */
.medium-zoom-image {
    cursor: zoom-in !important;
    transition: all 0.3s cubic-bezier(0.2, 0, 0.2, 1) !important;
    border-radius: 8px;
}

.medium-zoom-image--hidden {
    visibility: hidden;
}

.medium-zoom-image--opened {
    position: relative;
    cursor: zoom-out !important;
    will-change: transform;
    border-radius: 8px;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.5) !important;
    z-index: 10000;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .medium-zoom-overlay {
        background: rgba(0, 0, 0, 0.95) !important;
    }
    
    .markdown-body img:hover {
        box-shadow: 0 8px 24px rgba(255, 255, 255, 0.08);
    }
    
    .medium-zoom-image--opened {
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.8) !important;
    }
} 