<?php

namespace Typecho\Plugin;

if (!defined('__TYPECHO_ROOT_DIR__')) {
    exit;
}

use Typecho\Exception as TypechoException;

/**
 * Typecho Blog Platform
 *
 * @copyright  Copyright (c) 2008 Typecho team (http://www.typecho.org)
 * @license    GNU General Public License 2.0
 * @version    $Id: WidgetException.php 46 2008-03-10 13:59:36Z magike.net $
 */

/**
 * 插件异常
 *
 * @package Plugin
 */
class Exception extends TypechoException
{
}
