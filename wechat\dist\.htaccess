<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /toolbox/wechat/
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /toolbox/wechat/index.html [L]
</IfModule>

# 启用 CORS
<IfModule mod_headers.c>
  Header set Access-Control-Allow-Origin "*"
  Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
  Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
</IfModule>

# 缓存控制
<IfModule mod_expires.c>
  ExpiresActive On

  # 图片缓存 1 周
  ExpiresByType image/jpg "access plus 1 week"
  ExpiresByType image/jpeg "access plus 1 week"
  ExpiresByType image/gif "access plus 1 week"
  ExpiresByType image/png "access plus 1 week"
  ExpiresByType image/webp "access plus 1 week"
  ExpiresByType image/svg+xml "access plus 1 week"
  ExpiresByType image/x-icon "access plus 1 week"

  # CSS 和 JavaScript 缓存 1 天
  ExpiresByType text/css "access plus 1 day"
  ExpiresByType application/javascript "access plus 1 day"
  ExpiresByType application/x-javascript "access plus 1 day"

  # HTML 文档不缓存
  ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# 启用 Gzip 压缩
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/x-javascript
  AddOutputFilterByType DEFLATE application/json
</IfModule>
