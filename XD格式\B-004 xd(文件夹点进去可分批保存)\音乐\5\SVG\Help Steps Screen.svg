<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 375 812">
  <defs>
    <style>
      .cls-1 {
        clip-path: url(#clip-Help_Steps_Screen);
      }

      .cls-2, .cls-9 {
        fill: #fff;
      }

      .cls-3, .cls-7 {
        fill: #222;
      }

      .cls-3 {
        font-size: 30px;
      }

      .cls-11, .cls-3, .cls-9 {
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
      }

      .cls-4 {
        font-size: 24px;
        font-family: Montserrat-Light, Montserrat;
        font-weight: 300;
      }

      .cls-5 {
        fill: url(#linear-gradient);
      }

      .cls-6 {
        fill: #e5e5e5;
        opacity: 0.3;
      }

      .cls-7 {
        font-size: 14px;
        font-family: Montserrat-Regular, Montserrat;
        opacity: 0.7;
      }

      .cls-8 {
        fill: url(#linear-gradient-2);
      }

      .cls-9 {
        font-size: 11px;
        letter-spacing: 0.2em;
      }

      .cls-10 {
        fill: url(#linear-gradient-3);
      }

      .cls-11, .cls-12 {
        fill: #522b83;
      }

      .cls-11 {
        font-size: 18px;
      }

      .cls-13 {
        filter: url(#Rectangle_1);
      }
    </style>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#7b52ab"/>
      <stop offset="1" stop-color="#522b83"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0" x2="1" xlink:href="#linear-gradient"/>
    <filter id="Rectangle_1" x="88" y="559" width="198" height="68" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#522b83" flood-opacity="0.349"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f1e7fc"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <clipPath id="clip-Help_Steps_Screen">
      <rect width="375" height="812"/>
    </clipPath>
  </defs>
  <g id="Help_Steps_Screen" data-name="Help Steps Screen" class="cls-1">
    <rect class="cls-2" width="375" height="812"/>
    <rect id="Screen_Background" data-name="Screen Background" class="cls-2" width="375" height="812"/>
    <g id="Logo" transform="translate(-69 110)">
      <text id="Playband_UI_Kit" data-name="Playband
UI Kit" class="cls-3" transform="translate(156 189)"><tspan x="27.705" y="29">Playband</tspan><tspan class="cls-4"><tspan x="68.136" y="63">UI Kit</tspan></tspan></text>
      <path id="Symbol" class="cls-5" d="M-10,30a40.279,40.279,0,0,1-8.228-.847V20.876H-8.64A26.932,26.932,0,0,0,2.342,18.8a16.1,16.1,0,0,0,7.174-5.983,16.6,16.6,0,0,0,2.516-9.146,16.555,16.555,0,0,0-2.516-9.18,16.4,16.4,0,0,0-7.174-5.984A26.57,26.57,0,0,0-8.64-13.6h-20.6V25.075a40.2,40.2,0,0,1-15.017-14.42A39.933,39.933,0,0,1-50-10a39.751,39.751,0,0,1,3.143-15.57,39.869,39.869,0,0,1,8.572-12.715A39.869,39.869,0,0,1-25.57-46.857,39.751,39.751,0,0,1-10-50,39.748,39.748,0,0,1,5.57-46.857a39.868,39.868,0,0,1,12.714,8.572A39.868,39.868,0,0,1,26.856-25.57,39.751,39.751,0,0,1,30-10,39.748,39.748,0,0,1,26.856,5.57a39.867,39.867,0,0,1-8.572,12.714A39.867,39.867,0,0,1,5.57,26.856,39.748,39.748,0,0,1-10,30Zm.748-18.1h-8.976V-4.624h8.976c3.295,0,5.834.721,7.548,2.142A7.57,7.57,0,0,1,.879,3.672,7.491,7.491,0,0,1-1.7,9.758C-3.418,11.179-5.958,11.9-9.252,11.9Z" transform="translate(267 138.844)"/>
    </g>
    <g id="Note" transform="translate(-1 -77)">
      <path id="Union_1" data-name="Union 1" class="cls-6" d="M-395,91.7a10,10,0,0,1-10-10v-62a10,10,0,0,1,10-10h17.481V0l9.7,9.7H-120a10,10,0,0,1,10,10v62a10,10,0,0,1-10,10Z" transform="translate(446 500.303)"/>
      <text id="Check_your_mail_and_confirm_your_registration." data-name="Check your mail and confirm your registration." class="cls-7" transform="translate(77 528)"><tspan x="8.51" y="14">Check your mail and confirm </tspan><tspan x="50.965" y="40">your registration.</tspan></text>
    </g>
    <g id="Create_Account_Button" data-name="Create Account Button" transform="translate(-67 -167)">
      <g class="cls-13" transform="matrix(1, 0, 0, 1, 67, 167)">
        <rect id="Rectangle_1-2" data-name="Rectangle 1" class="cls-8" width="180" height="50" rx="25" transform="translate(97 565)"/>
      </g>
      <text id="LOGIN" class="cls-9" transform="translate(254 762)"><tspan x="-22.709" y="0">LOGIN</tspan></text>
    </g>
    <g id="Header">
      <path id="Background_Color" data-name="Background Color" class="cls-10" d="M0,0H375V70H0Z"/>
      <text id="Playband" class="cls-11" transform="translate(40 24)"><tspan x="0" y="17">Playband</tspan></text>
      <path id="Menu" class="cls-12" d="M-808.5,17a1.5,1.5,0,0,1-1.5-1.5,1.5,1.5,0,0,1,1.5-1.5h19a1.5,1.5,0,0,1,1.5,1.5,1.5,1.5,0,0,1-1.5,1.5Zm0-7A1.5,1.5,0,0,1-810,8.5,1.5,1.5,0,0,1-808.5,7h19A1.5,1.5,0,0,1-788,8.5a1.5,1.5,0,0,1-1.5,1.5Zm0-7A1.5,1.5,0,0,1-810,1.5,1.5,1.5,0,0,1-808.5,0h19A1.5,1.5,0,0,1-788,1.5,1.5,1.5,0,0,1-789.5,3Z" transform="translate(1123 26)"/>
    </g>
  </g>
</svg>
