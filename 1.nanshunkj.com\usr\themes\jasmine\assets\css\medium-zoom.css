.medium-zoom-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0;
    transition: opacity 300ms;
    will-change: opacity;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.8) !important;
}

.medium-zoom--opened .medium-zoom-overlay {
    opacity: 1;
}

.markdown-body img {
    cursor: zoom-in !important;
}

.medium-zoom-image {
    cursor: zoom-in !important;
    transition: transform 300ms cubic-bezier(0.2, 0, 0.2, 1) !important;
}

.medium-zoom-image--hidden {
    visibility: hidden;
}

.medium-zoom-image--opened {
    position: relative;
    cursor: zoom-out !important;
    will-change: transform;
    z-index: 10000;
} 