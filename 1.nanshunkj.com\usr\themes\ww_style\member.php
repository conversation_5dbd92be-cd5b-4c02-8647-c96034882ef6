<?php
/**
 * 会员中心页面
 *
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 检查用户是否登录，未登录则跳转到登录页面
if (!$this->user->hasLogin()) {
    $this->response->redirect($this->options->loginUrl);
}

$this->need('header.php');
?>

<div class="main-container">
    <!-- 会员中心侧边栏 -->
    <div class="member-sidebar">
        <div class="member-avatar">
            <img class="avatar-img" src="<?php echo getUserAvatar($this->user->mail, 80); ?>" alt="<?php $this->user->screenName(); ?>">
            <div class="member-name"><?php $this->user->screenName(); ?></div>
            <?php if (getUserMemberStatus($this->user->uid)): ?>
            <div class="member-type"><?php echo getMemberLevel($this->user->uid); ?></div>
            <?php else: ?>
            <div class="member-type" style="background:#aaa;">普通用户</div>
            <?php endif; ?>
            <div class="member-stats">
                <div class="stat-item">
                    <div class="stat-value"><?php echo getUserPostCount($this->user->uid); ?></div>
                    <div class="stat-label">发布</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo getUserViews($this->user->uid); ?></div>
                    <div class="stat-label">访问</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo number_format($this->db->fetchObject($this->db->select(array('COUNT(coid)' => 'num'))->from('table.comments')->where('authorId = ?', $this->user->uid))->num); ?></div>
                    <div class="stat-label">评论</div>
                </div>
            </div>
        </div>

        <!-- 会员导航菜单 -->
        <div class="member-menu">
            <a href="<?php $this->options->siteUrl(); ?>member.html" class="menu-item active">
                <i class="ri-user-line"></i> 我的资料
            </a>
            <a href="<?php $this->options->siteUrl(); ?>member/posts.html" class="menu-item">
                <i class="ri-file-list-line"></i> 我的文章
            </a>
            <a href="<?php $this->options->siteUrl(); ?>member/comments.html" class="menu-item">
                <i class="ri-message-2-line"></i> 我的评论
            </a>
            <a href="<?php $this->options->siteUrl(); ?>member/purchases.html" class="menu-item">
                <i class="ri-shopping-cart-line"></i> 购买记录
            </a>
            <?php if (getUserMemberStatus($this->user->uid)): ?>
            <a href="<?php $this->options->siteUrl(); ?>member/favorites.html" class="menu-item">
                <i class="ri-heart-line"></i> 我的收藏
            </a>
            <?php endif; ?>
            <a href="<?php $this->options->siteUrl(); ?>member/settings.html" class="menu-item">
                <i class="ri-settings-line"></i> 个人设置
            </a>
        </div>

        <!-- 会员VIP按钮 -->
        <?php if (!getUserMemberStatus($this->user->uid)): ?>
        <div class="upgrade-vip">
            <a href="<?php $this->options->siteUrl(); ?>payment.html" class="upgrade-btn">
                <i class="ri-vip-crown-line"></i> 升级VIP会员
            </a>
        </div>
        <?php elseif (getMemberExpires($this->user->uid) != '永久'): ?>
        <div class="upgrade-vip">
            <a href="<?php $this->options->siteUrl(); ?>payment.html" class="upgrade-btn">
                <i class="ri-vip-crown-line"></i> 续费会员
            </a>
        </div>
        <?php endif; ?>
    </div>

    <!-- 会员主内容区 -->
    <div class="member-main-content">
        <?php $section = isset($_GET['section']) ? $_GET['section'] : 'profile'; ?>

        <!-- 个人资料部分 -->
        <?php if ($section == 'profile'): ?>
        <div class="content-header">
            <h2 class="content-title"><i class="ri-user-line"></i> 个人资料</h2>
        </div>

        <div class="profile-card">
            <div class="profile-section">
                <h3 class="section-title">基本信息</h3>
                <div class="profile-form">
                    <div class="form-group">
                        <label for="nickname">显示名称</label>
                        <input type="text" id="nickname" name="nickname" value="<?php $this->user->screenName(); ?>" placeholder="请输入您的显示名称">
                    </div>
                    <div class="form-group">
                        <label for="email">电子邮箱</label>
                        <input type="email" id="email" name="email" value="<?php $this->user->mail(); ?>" placeholder="请输入您的电子邮箱" readonly>
                        <small class="form-tip">邮箱地址不可修改</small>
                    </div>
                    <div class="form-group">
                        <label for="bio">个人简介</label>
                        <textarea id="bio" name="bio" rows="4" placeholder="介绍一下自己吧..."><?php echo getUserBio($this->user->uid); ?></textarea>
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn-primary btn-save">保存修改</button>
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <h3 class="section-title">账号安全</h3>
                <div class="profile-form">
                    <div class="form-group">
                        <label for="old_password">旧密码</label>
                        <input type="password" id="old_password" name="old_password" placeholder="请输入旧密码">
                    </div>
                    <div class="form-group">
                        <label for="new_password">新密码</label>
                        <input type="password" id="new_password" name="new_password" placeholder="请输入新密码">
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">确认密码</label>
                        <input type="password" id="confirm_password" name="confirm_password" placeholder="请再次输入新密码">
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn-primary btn-save">修改密码</button>
                    </div>
                </div>
            </div>

            <?php if (getUserMemberStatus($this->user->uid)): ?>
            <div class="profile-section">
                <h3 class="section-title">会员信息</h3>
                <div class="vip-info">
                    <div class="vip-item">
                        <div class="vip-label">会员类型</div>
                        <div class="vip-value"><?php echo getMemberLevel($this->user->uid); ?></div>
                    </div>
                    <div class="vip-item">
                        <div class="vip-label">会员状态</div>
                        <div class="vip-value"><span class="status-active">有效</span></div>
                    </div>
                    <div class="vip-item">
                        <div class="vip-label">到期时间</div>
                        <div class="vip-value"><?php echo getMemberExpires($this->user->uid); ?></div>
                    </div>
                    <div class="vip-actions">
                        <?php if (getMemberExpires($this->user->uid) != '永久'): ?>
                        <a href="<?php $this->options->siteUrl(); ?>payment.html" class="btn-primary">续费会员</a>
                        <?php else: ?>
                        <a href="<?php $this->options->siteUrl(); ?>payment.html" class="btn-secondary disabled">永久会员</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- 我的文章部分 -->
        <?php elseif ($section == 'posts'): ?>
        <div class="content-header">
            <h2 class="content-title"><i class="ri-file-list-line"></i> 我的文章</h2>
        </div>

        <div class="profile-card">
            <div class="post-list">
                <?php
                $uid = $this->user->uid;
                $db = Typecho_Db::get();
                $posts = $db->fetchAll($db->select()
                    ->from('table.contents')
                    ->where('authorId = ? AND type = ? AND status = ?', $uid, 'post', 'publish')
                    ->order('created', Typecho_Db::SORT_DESC));

                if (count($posts) > 0):
                    foreach ($posts as $post):
                        $post = Typecho_Widget::widget('Widget_Abstract_Contents')->push($post);
                ?>
                <div class="post-item">
                    <div class="post-info">
                        <div class="post-title">
                            <a href="<?php echo $post['permalink']; ?>"><?php echo $post['title']; ?></a>
                        </div>
                        <div class="post-meta">
                            <span class="meta-item"><i class="ri-time-line"></i> <?php echo date('Y-m-d', $post['created']); ?></span>
                            <span class="meta-item"><i class="ri-eye-line"></i> <?php echo getPostViews($post['cid']); ?>次浏览</span>
                            <span class="meta-item"><i class="ri-message-2-line"></i> <?php echo $post['commentsNum']; ?>条评论</span>
                        </div>
                    </div>
                    <div class="post-actions">
                        <a href="<?php $this->options->adminUrl(); ?>write-post.php?cid=<?php echo $post['cid']; ?>" class="btn-action edit-action">
                            <i class="ri-edit-line"></i> 编辑
                        </a>
                    </div>
                </div>
                <?php
                    endforeach;
                else:
                ?>
                <div class="empty-message">
                    <div class="empty-icon"><i class="ri-file-list-3-line"></i></div>
                    <div class="empty-text">您还没有发布过文章</div>
                    <a href="<?php $this->options->adminUrl(); ?>write-post.php" class="btn-primary">写文章</a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 我的评论部分 -->
        <?php elseif ($section == 'comments'): ?>
        <div class="content-header">
            <h2 class="content-title"><i class="ri-message-2-line"></i> 我的评论</h2>
        </div>

        <div class="profile-card">
            <div class="comment-list">
                <?php
                $uid = $this->user->uid;
                $db = Typecho_Db::get();
                $comments = $db->fetchAll($db->select()
                    ->from('table.comments')
                    ->where('authorId = ? AND status = ?', $uid, 'approved')
                    ->order('created', Typecho_Db::SORT_DESC));

                if (count($comments) > 0):
                    foreach ($comments as $comment):
                        $post = $db->fetchRow($db->select('title')->from('table.contents')->where('cid = ?', $comment['cid']));
                ?>
                <div class="comment-item">
                    <div class="comment-content">
                        <div class="comment-text"><?php echo $comment['text']; ?></div>
                        <div class="comment-meta">
                            <span class="meta-item"><i class="ri-time-line"></i> <?php echo date('Y-m-d H:i', $comment['created']); ?></span>
                            <span class="meta-item"><i class="ri-article-line"></i> <?php echo $post['title']; ?></span>
                        </div>
                    </div>
                    <div class="comment-actions">
                        <a href="<?php echo $this->options->siteUrl . 'index.php/archives/' . $comment['cid'] . '#comment-' . $comment['coid']; ?>" class="btn-action view-action">
                            <i class="ri-eye-line"></i> 查看
                        </a>
                    </div>
                </div>
                <?php
                    endforeach;
                else:
                ?>
                <div class="empty-message">
                    <div class="empty-icon"><i class="ri-message-2-line"></i></div>
                    <div class="empty-text">您还没有发表过评论</div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 购买记录部分 -->
        <?php elseif ($section == 'purchases'): ?>
        <div class="content-header">
            <h2 class="content-title"><i class="ri-shopping-cart-line"></i> 购买记录</h2>
        </div>

        <div class="profile-card">
            <div class="purchase-list">
                <?php
                $uid = $this->user->uid;
                $db = Typecho_Db::get();

                // 查找所有以"user_purchased_"开头的字段，表示用户购买记录
                $purchases = $db->fetchAll($db->select('cid, name, value')
                    ->from('table.fields')
                    ->where('name = ?', 'user_purchased_' . $uid)
                    ->where('value = ?', 'yes'));

                if (count($purchases) > 0):
                ?>
                <div class="purchase-header">
                    <div class="purchase-col col-title">商品名称</div>
                    <div class="purchase-col col-price">价格</div>
                    <div class="purchase-col col-date">购买日期</div>
                    <div class="purchase-col col-actions">操作</div>
                </div>
                <?php
                    foreach ($purchases as $purchase):
                        // 获取文章信息
                        $post = $db->fetchRow($db->select('title')->from('table.contents')->where('cid = ?', $purchase['cid']));

                        // 查找购买价格和日期（假设在另一个字段中存储）
                        $purchaseInfo = $db->fetchRow($db->select('value')->from('table.fields')
                            ->where('cid = ? AND name = ?', $purchase['cid'], 'purchase_info_' . $uid));

                        $purchaseData = $purchaseInfo ? json_decode($purchaseInfo['value'], true) : array('price' => '未知', 'date' => time());
                ?>
                <div class="purchase-item">
                    <div class="purchase-col col-title"><?php echo $post['title']; ?></div>
                    <div class="purchase-col col-price">¥<?php echo isset($purchaseData['price']) ? $purchaseData['price'] : '未知'; ?></div>
                    <div class="purchase-col col-date"><?php echo isset($purchaseData['date']) ? date('Y-m-d H:i', $purchaseData['date']) : '未知'; ?></div>
                    <div class="purchase-col col-actions">
                        <a href="<?php echo $this->options->siteUrl . 'index.php/archives/' . $purchase['cid']; ?>" class="btn-action view-action">
                            <i class="ri-eye-line"></i> 查看
                        </a>
                    </div>
                </div>
                <?php
                    endforeach;
                else:
                ?>
                <div class="empty-message">
                    <div class="empty-icon"><i class="ri-shopping-cart-line"></i></div>
                    <div class="empty-text">您还没有购买过任何内容</div>
                    <a href="<?php $this->options->siteUrl(); ?>" class="btn-primary">浏览内容</a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 我的收藏部分 -->
        <?php elseif ($section == 'favorites' && getUserMemberStatus($this->user->uid)): ?>
        <div class="content-header">
            <h2 class="content-title"><i class="ri-heart-line"></i> 我的收藏</h2>
        </div>

        <div class="profile-card">
            <div class="favorites-list">
                <?php
                $uid = $this->user->uid;
                $db = Typecho_Db::get();

                // 查找所有以"user_favorite_"开头的字段，表示用户收藏
                $favorites = $db->fetchAll($db->select('cid, name, value')
                    ->from('table.fields')
                    ->where('name = ?', 'user_favorite_' . $uid)
                    ->where('value = ?', 'yes'));

                if (count($favorites) > 0):
                    foreach ($favorites as $favorite):
                        // 获取文章信息
                        $post = $db->fetchRow($db->select('title, created')->from('table.contents')->where('cid = ?', $favorite['cid']));
                ?>
                <div class="favorite-item">
                    <div class="favorite-info">
                        <div class="favorite-title">
                            <a href="<?php echo $this->options->siteUrl . 'index.php/archives/' . $favorite['cid']; ?>"><?php echo $post['title']; ?></a>
                        </div>
                        <div class="favorite-meta">
                            <span class="meta-item"><i class="ri-time-line"></i> 收藏于 <?php echo date('Y-m-d', isset($favorite['created']) ? $favorite['created'] : time()); ?></span>
                        </div>
                    </div>
                    <div class="favorite-actions">
                        <a href="<?php echo $this->options->siteUrl . 'index.php/archives/' . $favorite['cid']; ?>" class="btn-action view-action">
                            <i class="ri-eye-line"></i> 查看
                        </a>
                        <a href="javascript:;" class="btn-action remove-action" data-cid="<?php echo $favorite['cid']; ?>">
                            <i class="ri-delete-bin-line"></i> 取消收藏
                        </a>
                    </div>
                </div>
                <?php
                    endforeach;
                else:
                ?>
                <div class="empty-message">
                    <div class="empty-icon"><i class="ri-heart-line"></i></div>
                    <div class="empty-text">您还没有收藏任何内容</div>
                    <a href="<?php $this->options->siteUrl(); ?>" class="btn-primary">浏览内容</a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 个人设置部分 -->
        <?php elseif ($section == 'settings'): ?>
        <div class="content-header">
            <h2 class="content-title"><i class="ri-settings-line"></i> 个人设置</h2>
        </div>

        <div class="profile-card">
            <div class="profile-section">
                <h3 class="section-title">通知设置</h3>
                <div class="profile-form">
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="notify_comment" checked>
                            <span class="checkbox-text">评论回复通知</span>
                        </label>
                    </div>
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="notify_system" checked>
                            <span class="checkbox-text">系统通知</span>
                        </label>
                    </div>
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="notify_update">
                            <span class="checkbox-text">会员到期提醒</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <h3 class="section-title">隐私设置</h3>
                <div class="profile-form">
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="show_profile" checked>
                            <span class="checkbox-text">在个人页面显示我的资料</span>
                        </label>
                    </div>
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="show_email">
                            <span class="checkbox-text">公开我的邮箱地址</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <h3 class="section-title">其他设置</h3>
                <div class="form-group">
                    <button type="button" class="btn-primary btn-save">保存设置</button>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- 页面特定的CSS样式 -->
<style>
    /* 会员中心样式 */
    .member-menu {
        padding: 0 15px;
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #ccc;
        border-radius: 8px;
        margin-bottom: 8px;
        transition: all 0.3s;
    }

    .menu-item i {
        margin-right: 10px;
        font-size: 18px;
    }

    .menu-item:hover {
        background-color: rgba(255, 255, 255, 0.05);
        color: #fff;
    }

    .menu-item.active {
        background-color: rgba(254, 44, 85, 0.1);
        color: #FE2C55;
    }

    .upgrade-vip {
        padding: 15px;
        margin-top: 20px;
        text-align: center;
    }

    .upgrade-btn {
        display: inline-block;
        background: linear-gradient(to right, #FE2C55, #8134AF);
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(254, 44, 85, 0.3);
        transition: all 0.3s;
    }

    .upgrade-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(254, 44, 85, 0.4);
    }

    .upgrade-btn i {
        margin-right: 5px;
    }

    .member-main-content {
        flex: 1;
        min-width: 0;
    }

    .content-header {
        margin-bottom: 20px;
    }

    .content-title {
        font-size: 22px;
        display: flex;
        align-items: center;
    }

    .content-title i {
        color: #FE2C55;
        margin-right: 10px;
    }

    .profile-card {
        background-color: #191919;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    .profile-section {
        padding: 25px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .profile-section:last-child {
        border-bottom: none;
    }

    .section-title {
        font-size: 18px;
        margin-bottom: 20px;
        color: #fff;
    }

    .profile-form {
        max-width: 600px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #ccc;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        color: #fff;
        font-size: 14px;
        transition: all 0.3s;
    }

    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #FE2C55;
        box-shadow: 0 0 0 2px rgba(254, 44, 85, 0.2);
    }

    .form-tip {
        display: block;
        margin-top: 5px;
        color: #aaa;
        font-size: 12px;
    }

    .btn-save {
        min-width: 120px;
    }

    .vip-info {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 20px;
    }

    .vip-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .vip-item:last-child {
        border-bottom: none;
    }

    .vip-label {
        color: #ccc;
    }

    .vip-value {
        font-weight: 600;
    }

    .status-active {
        color: #52c41a;
    }

    .vip-actions {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }

    .btn-secondary.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    @media (max-width: 768px) {
        .main-container {
            flex-direction: column;
        }

        .member-sidebar {
            width: 100%;
            margin-bottom: 20px;
        }
    }

    /* 添加额外的样式 */
    .post-list, .comment-list, .purchase-list, .favorites-list {
        margin-top: 10px;
    }

    .post-item, .comment-item, .favorite-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        margin-bottom: 10px;
        background-color: #222;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .post-item:hover, .comment-item:hover, .favorite-item:hover {
        background-color: #2a2a2a;
    }

    .post-title, .favorite-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .post-meta, .comment-meta, .favorite-meta {
        font-size: 12px;
        color: #999;
    }

    .meta-item {
        margin-right: 15px;
    }

    .meta-item i {
        margin-right: 4px;
    }

    .post-actions, .comment-actions, .favorite-actions {
        display: flex;
        gap: 10px;
    }

    .btn-action {
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 13px;
        display: flex;
        align-items: center;
        transition: all 0.3s;
    }

    .btn-action i {
        margin-right: 4px;
    }

    .edit-action {
        background-color: #5755d9;
        color: white;
    }

    .view-action {
        background-color: #3498db;
        color: white;
    }

    .remove-action {
        background-color: #e74c3c;
        color: white;
    }

    .edit-action:hover {
        background-color: #4240c7;
    }

    .view-action:hover {
        background-color: #2980b9;
    }

    .remove-action:hover {
        background-color: #c0392b;
    }

    .empty-message {
        padding: 40px 20px;
        text-align: center;
    }

    .empty-icon {
        font-size: 48px;
        color: #444;
        margin-bottom: 15px;
    }

    .empty-text {
        font-size: 16px;
        color: #888;
        margin-bottom: 20px;
    }

    .purchase-header {
        display: flex;
        background-color: #222;
        padding: 12px 15px;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        color: #ddd;
        margin-bottom: 5px;
    }

    .purchase-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        background-color: #222;
        margin-bottom: 5px;
        border-radius: 4px;
        transition: all 0.3s;
    }

    .purchase-item:hover {
        background-color: #2a2a2a;
    }

    .purchase-col {
        padding: 0 10px;
    }

    .col-title {
        flex: 2;
    }

    .col-price, .col-date {
        flex: 1;
        white-space: nowrap;
    }

    .col-actions {
        flex: 0.5;
        text-align: right;
    }

    .checkbox-group {
        margin-bottom: 15px;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .checkbox-label input {
        margin-right: 10px;
    }

    .status-active {
        color: #2ecc71;
        font-weight: 600;
    }

    .status-expired {
        color: #e74c3c;
        font-weight: 600;
    }

    @media (max-width: 768px) {
        .purchase-col.col-date, .post-meta .meta-item:nth-child(2) {
            display: none;
        }

        .btn-action {
            padding: 5px;
        }

        .btn-action span {
            display: none;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 取消收藏操作
    const removeFavoriteButtons = document.querySelectorAll('.remove-action');
    removeFavoriteButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const cid = this.getAttribute('data-cid');
            if (confirm('确定要取消收藏这篇文章吗？')) {
                // 发送AJAX请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '<?php echo $this->options->siteUrl; ?>index.php/action/favorite?cid=' + cid + '&unfavorite=1', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.success) {
                                // 移除该收藏项
                                const favoriteItem = button.closest('.favorite-item');
                                favoriteItem.style.height = '0';
                                favoriteItem.style.opacity = '0';
                                favoriteItem.style.marginBottom = '0';
                                setTimeout(function() {
                                    favoriteItem.remove();
                                    // 检查是否没有收藏了
                                    const favoritesList = document.querySelector('.favorites-list');
                                    if (!favoritesList.querySelector('.favorite-item')) {
                                        // 显示空收藏提示
                                        const emptyMessage = `
                                            <div class="empty-message">
                                                <div class="empty-icon"><i class="ri-heart-line"></i></div>
                                                <div class="empty-text">您还没有收藏任何内容</div>
                                                <a href="<?php $this->options->siteUrl(); ?>" class="btn-primary">浏览内容</a>
                                            </div>
                                        `;
                                        favoritesList.innerHTML = emptyMessage;
                                    }
                                }, 300);
                            } else {
                                alert(response.message || '操作失败，请稍后再试');
                            }
                        } catch (e) {
                            alert('系统错误，请稍后再试');
                        }
                    }
                };
                xhr.send();
            }
        });
    });

    // 保存个人资料
    const saveProfileButton = document.querySelector('.profile-form .btn-save');
    if (saveProfileButton) {
        saveProfileButton.addEventListener('click', function() {
            const nickname = document.getElementById('nickname').value;
            const bio = document.getElementById('bio').value;

            // 简单验证
            if (!nickname.trim()) {
                alert('显示名称不能为空');
                return;
            }

            // 发送AJAX请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo $this->options->siteUrl; ?>index.php/action/profile-edit', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            alert('个人资料已更新');
                        } else {
                            alert(response.message || '更新失败，请稍后再试');
                        }
                    } catch (e) {
                        alert('系统错误，请稍后再试');
                    }
                }
            };
            xhr.send('nickname=' + encodeURIComponent(nickname) + '&bio=' + encodeURIComponent(bio));
        });
    }
});
</script>

<?php $this->need('footer.php'); ?>