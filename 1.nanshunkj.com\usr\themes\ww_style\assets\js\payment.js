/**
 * WW Style 主题支付相关JavaScript
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

(function() {
    "use strict";
    
    // DOM完全加载后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 会员套餐选择
        initMembershipPlanSelection();
        
        // 支付方式选择
        initPaymentMethodSelection();
        
        // 支付表单提交
        initPaymentFormSubmission();
        
        // 支付状态检查
        initPaymentStatusCheck();
    });
    
    /**
     * 初始化会员套餐选择功能
     */
    function initMembershipPlanSelection() {
        const membershipPlans = document.querySelectorAll('.membership-plan');
        
        membershipPlans.forEach(function(plan) {
            plan.addEventListener('click', function() {
                // 移除所有选中状态
                membershipPlans.forEach(function(p) {
                    p.classList.remove('active');
                });
                
                // 添加当前选中状态
                this.classList.add('active');
                
                // 更新选择的套餐ID和价格
                const planId = this.getAttribute('data-plan');
                const planPrice = this.getAttribute('data-price');
                const planName = this.getAttribute('data-name');
                
                const selectedPlanInput = document.querySelector('#selected_plan');
                const selectedPriceInput = document.querySelector('#selected_price');
                const selectedPriceDisplay = document.querySelector('#price_display');
                const selectedPlanNameDisplay = document.querySelector('#plan_name_display');
                
                if (selectedPlanInput) {
                    selectedPlanInput.value = planId;
                }
                
                if (selectedPriceInput) {
                    selectedPriceInput.value = planPrice;
                }
                
                if (selectedPriceDisplay) {
                    selectedPriceDisplay.textContent = '¥' + planPrice;
                }
                
                if (selectedPlanNameDisplay) {
                    selectedPlanNameDisplay.textContent = planName;
                }
                
                // 更新支付按钮
                updatePayButton();
            });
        });
    }
    
    /**
     * 初始化支付方式选择功能
     */
    function initPaymentMethodSelection() {
        const paymentMethods = document.querySelectorAll('.payment-method');
        
        paymentMethods.forEach(function(method) {
            method.addEventListener('click', function() {
                // 移除所有选中状态
                paymentMethods.forEach(function(m) {
                    m.classList.remove('active');
                });
                
                // 添加当前选中状态
                this.classList.add('active');
                
                // 更新选择的支付方式
                const payMethod = this.getAttribute('data-method');
                const selectedMethodInput = document.querySelector('#selected_method');
                
                if (selectedMethodInput) {
                    selectedMethodInput.value = payMethod;
                }
                
                // 显示对应的支付二维码
                const qrcodes = document.querySelectorAll('.payment-qrcode');
                qrcodes.forEach(function(qrcode) {
                    qrcode.classList.remove('active');
                });
                
                const selectedQrcode = document.querySelector('.payment-qrcode[data-method="' + payMethod + '"]');
                if (selectedQrcode) {
                    selectedQrcode.classList.add('active');
                }
                
                // 更新支付按钮
                updatePayButton();
            });
        });
    }
    
    /**
     * 初始化支付表单提交功能
     */
    function initPaymentFormSubmission() {
        const paymentForm = document.getElementById('payment-form');
        
        if (paymentForm) {
            paymentForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const selectedPlan = document.querySelector('#selected_plan').value;
                const selectedMethod = document.querySelector('#selected_method').value;
                
                // 验证选择
                if (!selectedPlan) {
                    showMessage('请选择会员套餐', 'error');
                    return;
                }
                
                if (!selectedMethod) {
                    showMessage('请选择支付方式', 'error');
                    return;
                }
                
                // 显示加载状态
                const submitBtn = document.querySelector('#submit-payment');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="ri-loader-2-line spin"></i> 处理中...';
                }
                
                // 提交表单
                const formData = new FormData(paymentForm);
                const xhr = new XMLHttpRequest();
                xhr.open('POST', paymentForm.getAttribute('action'), true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.success) {
                                    // 跳转到支付二维码页面
                                    if (response.redirect) {
                                        window.location.href = response.redirect;
                                    } else {
                                        // 显示支付信息
                                        document.querySelector('.payment-selection').style.display = 'none';
                                        document.querySelector('.payment-qrcodes').style.display = 'block';
                                        
                                        // 开始轮询支付状态
                                        if (response.order_id) {
                                            localStorage.setItem('payment_order_id', response.order_id);
                                            checkPaymentStatus(response.order_id);
                                        }
                                    }
                                } else {
                                    showMessage(response.message || '支付处理失败，请稍后再试', 'error');
                                    if (submitBtn) {
                                        submitBtn.disabled = false;
                                        submitBtn.innerHTML = '确认支付';
                                    }
                                }
                            } catch (e) {
                                showMessage('系统错误，请稍后再试', 'error');
                                if (submitBtn) {
                                    submitBtn.disabled = false;
                                    submitBtn.innerHTML = '确认支付';
                                }
                            }
                        } else {
                            showMessage('网络错误，请稍后再试', 'error');
                            if (submitBtn) {
                                submitBtn.disabled = false;
                                submitBtn.innerHTML = '确认支付';
                            }
                        }
                    }
                };
                xhr.send(formData);
            });
        }
    }
    
    /**
     * 初始化支付状态检查功能
     */
    function initPaymentStatusCheck() {
        // 检查是否存在订单ID
        const orderId = localStorage.getItem('payment_order_id');
        if (orderId && document.querySelector('.payment-qrcodes')) {
            // 如果在支付二维码页面并且有订单ID，开始检查支付状态
            checkPaymentStatus(orderId);
        }
    }
    
    /**
     * 检查支付状态
     * @param {string} orderId 订单ID
     */
    function checkPaymentStatus(orderId) {
        let checkCount = 0;
        const maxChecks = 60; // 最多检查60次，即10分钟
        
        const statusCheck = setInterval(function() {
            checkCount++;
            
            if (checkCount > maxChecks) {
                clearInterval(statusCheck);
                showMessage('支付超时，请重新发起支付', 'error');
                return;
            }
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', typechoConfig.siteUrl + 'index.php/action/payment-check?order_id=' + orderId, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            if (response.status === 'paid') {
                                // 支付成功
                                clearInterval(statusCheck);
                                localStorage.removeItem('payment_order_id');
                                
                                // 显示成功信息
                                document.querySelector('.payment-qrcodes').style.display = 'none';
                                document.querySelector('.payment-success').style.display = 'block';
                                
                                // 倒计时跳转
                                let countdown = 5;
                                document.querySelector('#redirect-countdown').textContent = countdown;
                                
                                const redirectTimer = setInterval(function() {
                                    countdown--;
                                    document.querySelector('#redirect-countdown').textContent = countdown;
                                    
                                    if (countdown <= 0) {
                                        clearInterval(redirectTimer);
                                        window.location.href = response.redirect || typechoConfig.siteUrl + 'member.html';
                                    }
                                }, 1000);
                            }
                        }
                    } catch (e) {
                        console.error('支付状态检查失败', e);
                    }
                }
            };
            xhr.send();
        }, 10000); // 每10秒检查一次
    }
    
    /**
     * 更新支付按钮状态
     */
    function updatePayButton() {
        const selectedPlan = document.querySelector('#selected_plan').value;
        const selectedMethod = document.querySelector('#selected_method').value;
        const submitBtn = document.querySelector('#submit-payment');
        
        if (submitBtn) {
            if (selectedPlan && selectedMethod) {
                submitBtn.disabled = false;
            } else {
                submitBtn.disabled = true;
            }
        }
    }
    
    /**
     * 显示消息提示
     * @param {string} message 消息内容
     * @param {string} type 消息类型（success/error）
     */
    function showMessage(message, type = 'success') {
        const messageContainer = document.querySelector('.message-container');
        
        if (!messageContainer) {
            return;
        }
        
        const messageElement = document.createElement('div');
        messageElement.className = 'message ' + type;
        messageElement.innerHTML = '<i class="ri-' + (type === 'success' ? 'check-line' : 'close-circle-line') + '"></i> ' + message;
        
        messageContainer.appendChild(messageElement);
        
        // 显示消息
        setTimeout(function() {
            messageElement.classList.add('show');
        }, 10);
        
        // 自动移除
        setTimeout(function() {
            messageElement.classList.remove('show');
            setTimeout(function() {
                messageContainer.removeChild(messageElement);
            }, 300);
        }, 3000);
    }
})(); 