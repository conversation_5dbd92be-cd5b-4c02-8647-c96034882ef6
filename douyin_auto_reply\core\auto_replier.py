#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动回复模块
根据消息内容生成智能回复
"""

import random
import time
from datetime import datetime, timedelta

class AutoReplier:
    """自动回复类"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.reply_history = []
        self.last_reply_time = None
        self.reply_count_today = 0
        
        # 默认回复模板
        self.default_replies = {
            "keywords": {
                "你好": [
                    "你好！很高兴收到您的消息😊",
                    "嗨！有什么可以帮助您的吗？",
                    "您好！我在的，请问有什么需要咨询的？"
                ],
                "咨询": [
                    "感谢您的咨询，请详细说明您的需求",
                    "我来为您详细解答，请问想了解什么？",
                    "好的，我很乐意为您提供帮助"
                ],
                "价格": [
                    "关于价格问题，请稍等，我马上为您查询",
                    "价格会根据具体需求有所不同，请告诉我详细要求",
                    "我们有不同的价格方案，请说明您的具体需求"
                ],
                "购买": [
                    "感谢您的购买意向，请告诉我具体需求",
                    "好的，我来为您安排，请问需要什么产品？",
                    "非常感谢！请详细说明您想购买的商品"
                ],
                "产品": [
                    "我们有多种产品可选，请问您对哪类产品感兴趣？",
                    "产品信息我可以详细介绍，请问想了解哪方面？",
                    "好的，我来为您介绍我们的产品"
                ],
                "在吗": [
                    "在的！有什么可以帮您？",
                    "我在的，请说",
                    "嗯嗯，我在线的，有什么需要吗？"
                ],
                "客服": [
                    "我就是客服，很高兴为您服务",
                    "您好，我是客服，请问需要什么帮助？",
                    "客服在线，请问有什么问题？"
                ],
                "再见": [
                    "再见！期待下次为您服务😊",
                    "拜拜！有问题随时联系我",
                    "好的，再见！祝您生活愉快"
                ]
            },
            "default_replies": [
                "收到您的消息了，稍后回复您",
                "您好，我已经看到您的消息了",
                "感谢您的留言，正在处理中",
                "好的，我了解了您的需求",
                "谢谢您的信息，我会尽快回复"
            ],
            "blacklist_keywords": [
                "广告", "推广", "加群", "刷单", "兼职", "投资", "理财"
            ]
        }
    
    def load_replies_config(self):
        """加载回复配置"""
        if self.config_manager and hasattr(self.config_manager, 'replies'):
            return self.config_manager.replies
        return self.default_replies
    
    def is_blacklisted(self, message):
        """检查消息是否在黑名单中"""
        replies_config = self.load_replies_config()
        blacklist = replies_config.get('blacklist_keywords', [])
        
        for keyword in blacklist:
            if keyword in message:
                print(f"🚫 检测到黑名单关键词: {keyword}")
                return True
        return False
    
    def find_keyword_match(self, message):
        """查找匹配的关键词"""
        replies_config = self.load_replies_config()
        keywords = replies_config.get('keywords', {})
        
        # 按关键词长度排序，优先匹配长关键词
        sorted_keywords = sorted(keywords.keys(), key=len, reverse=True)
        
        for keyword in sorted_keywords:
            if keyword in message:
                return keyword
        return None
    
    def generate_reply(self, message):
        """生成回复内容"""
        message = message.strip()
        
        # 检查黑名单
        if self.is_blacklisted(message):
            return None
        
        replies_config = self.load_replies_config()
        
        # 查找关键词匹配
        matched_keyword = self.find_keyword_match(message)
        
        if matched_keyword:
            # 使用关键词对应的回复
            replies = replies_config['keywords'].get(matched_keyword, [])
            if replies:
                reply = random.choice(replies)
                print(f"🎯 关键词匹配: '{matched_keyword}' -> '{reply}'")
                return reply
        
        # 使用默认回复
        default_replies = replies_config.get('default_replies', [])
        if default_replies:
            reply = random.choice(default_replies)
            print(f"💬 默认回复: '{reply}'")
            return reply
        
        return "收到您的消息了"
    
    def should_reply(self):
        """判断是否应该回复 (频率控制)"""
        now = datetime.now()
        
        # 检查今日回复次数限制
        max_replies_per_hour = 50  # 每小时最大回复数
        if self.reply_count_today >= max_replies_per_hour:
            print(f"⏰ 今日回复次数已达上限 ({max_replies_per_hour})")
            return False
        
        # 检查回复间隔
        min_interval = 1  # 最小间隔1秒
        if self.last_reply_time:
            time_diff = (now - self.last_reply_time).total_seconds()
            if time_diff < min_interval:
                print(f"⏰ 回复间隔过短，等待 {min_interval - time_diff:.1f} 秒")
                return False
        
        return True
    
    def send_reply(self, message, window_manager):
        """发送回复"""
        if not self.should_reply():
            return False
        
        # 生成回复内容
        reply = self.generate_reply(message)
        if not reply:
            print("🚫 消息被黑名单过滤，不回复")
            return False
        
        # 模拟思考时间
        think_time = random.uniform(1, 3)
        print(f"🤔 思考中... ({think_time:.1f}秒)")
        time.sleep(think_time)
        
        # 发送回复
        success = window_manager.send_text(reply)
        
        if success:
            # 记录回复历史
            self.reply_history.append({
                'time': datetime.now(),
                'message': message,
                'reply': reply
            })
            self.last_reply_time = datetime.now()
            self.reply_count_today += 1
            
            print(f"✅ 回复发送成功: '{reply}'")
            return True
        else:
            print("❌ 回复发送失败")
            return False
    
    def get_statistics(self):
        """获取回复统计信息"""
        return {
            'total_replies': len(self.reply_history),
            'replies_today': self.reply_count_today,
            'last_reply_time': self.last_reply_time,
            'recent_replies': self.reply_history[-10:] if self.reply_history else []
        }
    
    def reset_daily_count(self):
        """重置日计数器"""
        self.reply_count_today = 0
        print("🔄 已重置日回复计数器") 