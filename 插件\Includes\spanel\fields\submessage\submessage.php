<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.
/**
 *
 * Field: submessage
 *
 * <AUTHOR> Studio
 * @Uri https://shilin.studio
 *
 */
if ( ! class_exists( 'Shilin_Field_submessage' ) ) {
  class Shilin_Field_submessage extends Shilin_Fields {

    public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
      parent::__construct( $field, $value, $unique, $where, $parent );
    }

    public function render() {

      $style = ( ! empty( $this->field['style'] ) ) ? $this->field['style'] : 'normal';

      echo '<div class="shilin-submessage shilin-submessage-'. esc_attr( $style ) .'">'. $this->field['content'] .'</div>';

    }

  }
}
