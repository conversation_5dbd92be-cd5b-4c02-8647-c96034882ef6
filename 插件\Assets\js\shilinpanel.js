eval(function(p,a,c,k,e,r){e=String;if('0'.replace(0,e)==0){while(c--)r[e(c)]=k[c];k=[function(e){return r[e]||e}];e=function(){return'[0-9]'};c=1};while(c--)if(k[c])p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c]);return p}('jQuery(document).ready(1($){var w=$(window).width();$(\'.0-3-4\').5(\'6\',1(){$(\'.0-3-4 i\').2(\'0-7 0-8\')});$(\'.0-9 .0-menu\').5(\'6\',1(){$(\'.0-nav-options\').2(\'show\');$(\'.0-9 a i\').2(\'0-7 0-8\')})});',[],10,'shilin|function|toggleClass|expand|all|on|click|zhankai2|shousuo2|mNav'.split('|'),0,{}))