<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.
/**
 *
 * Field: backup
 *
 * <AUTHOR> Studio
 * @Uri https://shilin.studio
 *
 */
if ( ! class_exists( 'Shilin_Field_backup' ) ) {
  class Shilin_Field_backup extends Shilin_Fields {

    public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
      parent::__construct( $field, $value, $unique, $where, $parent );
    }

    public function render() {

      $unique = $this->unique;
      $nonce  = wp_create_nonce( 'shilin_backup_nonce' );
      $export = add_query_arg( array( 'action' => 'shilin-export', 'unique' => $unique, 'nonce' => $nonce ), admin_url( 'admin-ajax.php' ) );

      echo $this->field_before();

      echo '<textarea name="shilin_import_data" class="shilin-import-data"></textarea>';
      echo '<button type="submit" class="button button-primary shilin-confirm shilin-import" data-unique="'. esc_attr( $unique ) .'" data-nonce="'. esc_attr( $nonce ) .'">'. esc_html__( 'Import', 'shilin' ) .'</button>';
      echo '<hr />';
      echo '<textarea readonly="readonly" class="shilin-export-data">'. esc_attr( json_encode( get_option( $unique ) ) ) .'</textarea>';
      echo '<a href="'. esc_url( $export ) .'" class="button button-primary shilin-export" target="_blank">'. esc_html__( 'Export & Download', 'shilin' ) .'</a>';
      echo '<hr />';
      echo '<button type="submit" name="shilin_transient[reset]" value="reset" class="button shilin-warning-primary shilin-confirm shilin-reset" data-unique="'. esc_attr( $unique ) .'" data-nonce="'. esc_attr( $nonce ) .'">'. esc_html__( 'Reset', 'shilin' ) .'</button>';

      echo $this->field_after();

    }

  }
}
