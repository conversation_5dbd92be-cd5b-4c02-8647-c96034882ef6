!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";function e(s){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(s)}jQuery((function(e){e.fn.loading||e.fn.extend({loading:function(s){var a=e(this);s?a.addClass("loading").prepend('<i class="wpcom-icon wi wi-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'):a.removeClass("loading").find(".wi-loader").remove()}});var s=null;function a(e,a,n,o){var t=o||jQuery(".mce-tinymce");clearTimeout(s),jQuery("#notice").remove(),t.append('<div id="notice"><div class="notice-bg"></div><div class="notice-wrap"><div class="notice-inner notice-'+e+'">'+a+"</div></div></div>"),n&&(s=setTimeout((function(){jQuery("#notice").remove()}),n))}var n=0,o=e(".q-single");o.length&&e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_views",id:o.data("id")},type:"POST",success:function(e){}}),e("#answer").on("click",".j-reply",(function(){var s=e(this).closest(".as-item");if(s.find(".as-comments-box").length)e(".as-comments-box").remove(),e(".as-comments").remove();else{var a=e(e("#as-comments-box").html());a.find("input[name=id]").val(s.data("aid")),a.find("input[name=comment]").attr("placeholder",QAPress_js.lang.addcomment),a.find(".as-comments-submit").val(QAPress_js.lang.submit),e(".as-comments-box").remove(),e(".as-comments").remove(),s.find(".as-main").append(a),s.find(".as-comments-input").focus()}})).on("click",".j-reply-comment",(function(){var s=e(this).closest(".as-comments-item");if(s.find(".as-comments-box").length)e(".as-comments-box").remove();else{var a=e(e("#as-comments-box").html());a.find("input[name=id]").val(s.data("id")),a.find("input[name=comment]").attr("placeholder",QAPress_js.lang.addcomment),a.find(".as-comments-submit").val(QAPress_js.lang.submit),e(".as-comments-box").remove(),s.append(a),s.find(".as-comments-input").focus()}})).on("click",".btn-vote",(function(){var s=e(this),a=s.hasClass("btn-vote-up")?1:-1,n=s.closest(".as-item"),o=n.find(".btn-vote.active"),t=n.find(".btn-vote-up"),r=Number(t.data("vote"));if(s.hasClass("active")){if(s.removeClass("active"),1===a){var i=r-1;i=i<1?"":" "+i,t.html(t.find("svg").prop("outerHTML")+t.attr("aria-label")+i)}}else{if(1===a){var c=r+1;c=c<1?"":" "+c,t.html(t.find("svg").prop("outerHTML")+t.attr("aria-label")+c)}else if(o.length){var l=r-1;l=l<1?"":" "+l,t.html(t.find("svg").prop("outerHTML")+t.attr("aria-label")+l)}o.removeClass("active"),s.addClass("active")}e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_vote",id:n.data("aid"),type:a,nonce:e("#comments_list_nonce").val()},type:"POST",dataType:"json",success:function(e){if("0"==e.result){t.data("vote",e.upvote);var s=e.upvote<1?"":" "+e.upvote;t.html(t.find("svg").prop("outerHTML")+t.attr("aria-label")+s),n.find(".btn-vote").removeClass("active"),e.voted&&"upvote"===e.voted?n.find(".btn-vote-up").addClass("active"):e.voted&&"downvote"===e.voted&&n.find(".btn-vote-down").addClass("active")}else if(e.msg){n.find(".btn-vote.active").removeClass("active");var a=r<1?"":" "+r;t.html(t.find("svg").prop("outerHTML")+t.attr("aria-label")+a),wpcom_notice({message:e.msg,show:2e3,type:"warning"})}}})})),e("#answer").on("click",".j-reply-list",(function(){var s=e(this).closest(".as-item"),a=s.find(".as-comments").length;s.find(".as-comments-box").remove(),s.find(".as-comments").remove(),a||(s.find(".as-main").append('<div class="as-comments"><ul class="as-comments-list"><li class="as-comments-none"><img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.loading+"</li></ul></div>"),e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_comments",aid:s.data("aid")},type:"POST",dataType:"json",success:function(e){if(0==e.result){var a='<li class="as-comments-none">'+QAPress_js.lang.nocomment2+"</li>";void 0!==e.comments&&""!==e.comments&&(a=e.comments),s.find(".as-main .as-comments-list").html(a)}else s.find(".as-main .as-comments-list").html('<li class="as-comments-none">'+QAPress_js.lang.error1+"</li>"),wpcom_notice({message:QAPress_js.lang.error1,show:2e3,type:"error"})},error:function(){s.find(".as-main .as-comments-list").html('<li class="as-comments-none">'+QAPress_js.lang.error2+"</li>"),wpcom_notice({message:QAPress_js.lang.error2,show:2e3,type:"error"})}}))})).on("click",".j-answer-del",(function(){if(confirm(QAPress_js.lang.confirm)){var s=e(this).closest(".as-item");a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.deleting,0,s);var n=s.data("aid");e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_delete_answer",id:n},type:"POST",dataType:"json",success:function(a){e("#notice").remove(),0==a.result?(wpcom_notice({message:QAPress_js.lang.success,show:2e3}),setTimeout((function(){s.fadeOut("fast",(function(){var e=s.closest(".as-list");s.remove(),0==e.find(".as-item").length&&e.append('<li class="as-item-none" style="text-align: center;color: #999;padding-top: 10px;">'+QAPress_js.lang.nocomment+"</li>")}))}),300)):2==a.result?wpcom_notice({message:QAPress_js.lang.denied,show:2e3,type:"error"}):wpcom_notice({message:QAPress_js.lang.error3,show:2e3,type:"error"})},error:function(s){e("#notice").remove(),wpcom_notice({message:QAPress_js.lang.error3,show:2e3,type:"error"})}})}})).on("click",".j-del-comment",(function(){if(confirm(QAPress_js.lang.confirm2)){var s=e(this).closest(".as-comments-item");a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.deleting,0,s);var n=s.data("id");e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_delete_comment",id:n},type:"POST",dataType:"json",success:function(a){e("#notice").remove(),0==a.result?(wpcom_notice({message:QAPress_js.lang.success,show:2e3}),setTimeout((function(){s.fadeOut("fast",(function(){var e=s.closest(".as-comments-list");s.remove(),0==e.find(".as-comments-item").length&&e.append('<li class="as-comments-none">'+QAPress_js.lang.nocomment2+"</li>")}))}),300)):2==a.result?wpcom_notice({message:QAPress_js.lang.denied,show:2e3,type:"error"}):wpcom_notice({message:QAPress_js.lang.error3,show:2e3,type:"error"})},error:function(s){e("#notice").remove(),wpcom_notice({message:QAPress_js.lang.error3,show:2e3,type:"error"})}})}})),e("#as-form").submit((function(){if(n)return!1;n=1,tinyMCE.triggerSave();var s=e("#editor-answer").val();return e.trim(s)?(a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.submitting,0),e.ajax({url:QAPress_js.ajaxurl,data:e(this).serialize()+"&action=QAPress_add_answer",type:"POST",dataType:"json",success:function(s){if(e("#notice").remove(),n=0,0==s.result){wpcom_notice({message:QAPress_js.lang.success2,show:2e3});var a='<li id="as-'+s.answer.ID+'" class="as-item" data-aid="'+s.answer.ID+'" style="display: none;"><div class="as-head">                            <div class="as-avatar">'+s.user.avatar+'</div>                            <div class="as-user">'+s.user.nickname+'</div>                            </div>                        <div class="as-main">                        <div class="as-content entry-content">'+s.answer.content+'</div>                        <div class="as-action">                        <span class="as-time">'+s.answer.date+'</span>                        <span class="as-reply-count"><a class="j-reply-list" href="javascript:;">'+QAPress_js.lang.ncomment+"</a></span>                        </div>                        </div>                        </li>";e(".as-list").append(a),e("#as-"+s.answer.ID).fadeIn(),e(".as-item-none").remove(),tinyMCE.activeEditor.setContent("")}else 101==s.result?wpcom_notice({message:QAPress_js.lang.empty,show:2e3,type:"warning"}):2==s.result?wpcom_notice({message:QAPress_js.lang.login,show:2e3,type:"warning"}):s.msg?wpcom_notice({message:s.msg,show:2e3,type:"warning"}):wpcom_notice({message:QAPress_js.lang.error4,show:2e3,type:"error"})},error:function(s){e("#notice").remove(),n=0,wpcom_notice({message:QAPress_js.lang.error4,show:2e3,type:"error"})}}),!1):(wpcom_notice({message:QAPress_js.lang.empty,show:2e3,type:"warning"}),n=0,!1)})),e("#answer").on("submit",".as-comments-form",(function(){if(n)return!1;n=1;var s=e(this),o=s.find(".as-comments-input").val(),t=s.closest(".as-comments-item").length?s.closest(".as-comments-item"):s.closest(".as-item");return e.trim(o)?(a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.submitting,0,t),e.ajax({url:QAPress_js.ajaxurl,data:s.serialize()+"&action=QAPress_add_comment",type:"POST",dataType:"json",success:function(o){if(n=0,jQuery("#notice").remove(),0==o.result){if(wpcom_notice({message:QAPress_js.lang.success2,show:2e3}),t.find(".as-comments-input").val(""),s.closest(".as-comments-item").length){var r=s.closest(".as-item");a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.submitting,0,t),e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_comments",aid:r.data("aid")},type:"POST",dataType:"json",success:function(e){if(jQuery("#notice").remove(),0==e.result){var s='<li class="as-comments-none">'+QAPress_js.lang.nocomment2+"</li>";void 0!==e.comments&&""!==e.comments&&(s=e.comments),r.find(".as-main .as-comments-list").html(s)}else r.find(".as-main .as-comments-list").html('<li class="as-comments-none">'+QAPress_js.lang.error1+"</li>"),wpcom_notice({message:QAPress_js.lang.error1,show:2e3,type:"error"})},error:function(){jQuery("#notice").remove(),r.find(".as-main .as-comments-list").html('<li class="as-comments-none">'+QAPress_js.lang.error2+"</li>"),wpcom_notice({message:QAPress_js.lang.error2,show:2e3,type:"error"})}})}}else 101==o.result?wpcom_notice({message:QAPress_js.lang.empty,show:2e3,type:"warning"}):2==o.result?wpcom_notice({message:QAPress_js.lang.login,show:2e3,type:"warning"}):o.msg?wpcom_notice({message:o.msg,show:2e3,type:"warning"}):wpcom_notice({message:QAPress_js.lang.error4,show:2e3,type:"error"})},error:function(e){jQuery("#notice").remove(),n=0,wpcom_notice({message:QAPress_js.lang.error4,show:2e3,type:"error"})}}),!1):(wpcom_notice({message:QAPress_js.lang.empty,show:2e3,type:"warning"}),n=0,!1)})),e("#question-form").submit((function(){if(n)return!1;n=1,tinyMCE.triggerSave();var s=e("input[name=title]").val(),o=e("select[name=category]").val(),t=e("#editor-question").val();return s?o?e.trim(t)?(a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.submitting,0),e.ajax({url:QAPress_js.ajaxurl,data:e(this).serialize()+"&action=QAPress_add_question",type:"POST",dataType:"json",success:function(s){jQuery("#notice").remove(),n=0,0==s.result?(e("input[name=id]").val()?wpcom_notice({message:void 0===s.msg?QAPress_js.lang.success3:s.msg,show:2e3}):(n=1,wpcom_notice({message:void 0===s.msg?QAPress_js.lang.success4:s.msg,show:2e3})),void 0!==s.location&&setTimeout((function(){window.location.href=s.location,n=0}),1500)):101==s.result?wpcom_notice({message:QAPress_js.lang.need_all,show:2e3,type:"warning"}):102==s.result?wpcom_notice({message:QAPress_js.lang.length,show:2e3,type:"warning"}):s.msg?wpcom_notice({message:s.msg,show:2e3,type:"warning"}):wpcom_notice({message:QAPress_js.lang.error4,show:2e3,type:"error"})},error:function(e){jQuery("#notice").remove(),n=0,wpcom_notice({message:QAPress_js.lang.error4,show:2e3,type:"error"})}}),!1):(wpcom_notice({message:QAPress_js.lang.need_content,show:2e3,type:"warning"}),n=0,!1):(wpcom_notice({message:QAPress_js.lang.need_cat,show:2e3,type:"warning"}),n=0,!1):(wpcom_notice({message:QAPress_js.lang.need_title,show:2e3,type:"warning"}),n=0,!1)})),e("#answer").on("click",".q-load-more",(function(){var s=e(this);if(!s.hasClass("disabled")){s.addClass("disabled").text(QAPress_js.lang.loading);var a=e(".q-single").data("id"),n=s.data("page");n=n||2,e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_answers_pagination",question:a,page:n},type:"POST",dataType:"json",success:function(a){if(0==a.result){var o="";if(a.answers.length){for(var t=0;t<a.answers.length;t++)o+=a.answers[t];e(".as-list").append(o)}else e(".q-load-more").css("visibility","hidden"),wpcom_notice({message:QAPress_js.lang.load_done,show:2e3})}else wpcom_notice({message:QAPress_js.lang.load_fail,show:2e3,type:"warning"});s.removeClass("disabled").text(QAPress_js.lang.load_more),s.data("page",n+1)},error:function(e){s.removeClass("disabled").text(QAPress_js.lang.load_more),wpcom_notice({message:QAPress_js.lang.load_fail,show:2e3,type:"warning"})}})}})).on("click",".j-adopt-answer",(function(){var s=e(this);if(s.hasClass("loading"))return!1;var a=s.closest(".as-item");s.loading(1);var n=e(".q-single").data("id");e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_adopt_answer",id:n,aid:a.data("aid")},type:"POST",dataType:"json",success:function(a){s.loading(0),0==a.result?(wpcom_notice({message:QAPress_js.lang.success,show:2e3}),s.parent().remove(),e(".is-adopted").remove()):2==a.result?wpcom_notice({message:QAPress_js.lang.denied,show:2e3,type:"error"}):wpcom_notice({message:a.msg?a.msg:QAPress_js.lang.error2,show:2e3,type:"error"})},error:function(e){s.loading(0),wpcom_notice({message:QAPress_js.lang.error2,show:2e3,type:"error"})}})})),e(".topic-header").on("click",".j-del",(function(){if(confirm(QAPress_js.lang.confirm3)){a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.deleting,0,e(".q-entry"));var s=e(".q-single").data("id");e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_delete_question",id:s},type:"POST",dataType:"json",success:function(s){e("#notice").remove(),0==s.result?(wpcom_notice({message:QAPress_js.lang.success,show:2e3}),s.location&&setTimeout((function(){window.location.href=s.location,n=0}),1800)):2==s.result?wpcom_notice({message:QAPress_js.lang.denied,show:2e3,type:"error"}):wpcom_notice({message:QAPress_js.lang.error3,show:2e3,type:"error"})},error:function(s){e("#notice").remove(),wpcom_notice({message:QAPress_js.lang.error3,show:2e3,type:"error"})}})}})).on("click",".j-approve",(function(){if(confirm(QAPress_js.lang.approve)){a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.loading,0,e(".q-entry"));var s=e(".q-single").data("id");e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_approve_question",id:s},type:"POST",dataType:"json",success:function(s){e("#notice").remove(),0==s.result?(wpcom_notice({message:QAPress_js.lang.success,show:2e3}),e(".j-approve").remove()):2==s.result?wpcom_notice({message:QAPress_js.lang.denied,show:2e3,type:"error"}):wpcom_notice({message:QAPress_js.lang.error2,show:2e3,type:"error"})},error:function(s){e("#notice").remove(),wpcom_notice({message:QAPress_js.lang.error2,show:2e3,type:"error"})}})}})).on("click",".j-set-top",(function(){a(1,'<img class="notice-loading" src="'+QAPress_js.ajaxloading+'"> '+QAPress_js.lang.loading,0,e(".q-entry"));var s=e(".q-single").data("id");e.ajax({url:QAPress_js.ajaxurl,data:{action:"QAPress_set_top",id:s},type:"POST",dataType:"json",success:function(s){e("#notice").remove(),0==s.result?(wpcom_notice({message:QAPress_js.lang.success,show:2e3}),setTimeout((function(){window.location.reload()}),500)):2==s.result?wpcom_notice({message:QAPress_js.lang.denied,show:2e3,type:"error"}):wpcom_notice({message:QAPress_js.lang.error2,show:2e3,type:"error"})},error:function(s){e("#notice").remove(),wpcom_notice({message:QAPress_js.lang.error2,show:2e3,type:"error"})}})})),e(".wpcom-profile").on("click",".j-user-questions,.j-user-answers",(function(){var s=e(this);if(s.hasClass("loading")||s.hasClass("disabled"))return!1;var a=null,n=s.data("page");n=n?n+1:2;var o=e(".profile-tab").data("user");s.hasClass("j-user-questions")?a={action:"QAPress_user_questions",user:o||0,page:n}:s.hasClass("j-user-answers")&&(a={action:"QAPress_user_answers",user:o||0,page:n}),s.loading(1),e.ajax({type:"POST",url:_wpmx_js?_wpmx_js.ajaxurl:_wpcom_js.ajaxurl,data:a,dataType:"html",success:function(a){if("0"==a)s.addClass("disabled").text(QAPress_js.lang.end);else{var o=e(a);s.parent().prev().append(o),e.fn.lazyload&&o.find(".j-lazy").lazyload({webp:void 0!==_wpcom_js.webp&&_wpcom_js.webp?_wpcom_js.webp:null}),s.data("page",n)}s.loading(0)},error:function(){s.loading(0)}})}))})),window.wpcom_notice||(window.wpcom_notice=function(s){if(!arguments.length||1===arguments.length&&"object"===e(arguments[0])||(s={},void 0!==arguments[0]&&(s.message=arguments[0]),void 0!==arguments[1]&&(s.type=arguments[1]),void 0!==arguments[2]&&"loading"!==s.type&&(s.show=arguments[2]),void 0!==arguments[2]&&"loading"===s.type&&(s.callback=arguments[2])),s&&s.message){s.type=s.type?s.type:"success";var a='<div class="notice-message"><div class="notice-message-content notice-message-'+s.type+'">';"success"===s.type?a+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-success"></use></svg></i>':"warning"===s.type||"error"===s.type?a+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-warning"></use></svg></i>':"loading"===s.type&&(a+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'),a+=s.message+"</div></div>";var n=jQuery(a),o=jQuery(".notice-message-wrapper");return 0===o.length&&(jQuery(document.body).append('<div class="notice-message-wrapper"></div>'),o=jQuery(".notice-message-wrapper")),o.append(n),n.one("hide.notice",(function(){var e=jQuery(this);e.removeClass("notice-message-active").addClass("notice-message-up"),setTimeout((function(){e.remove(),0===o.find(".notice-message").length&&o.remove()}),320)})),setTimeout((function(){n.addClass("notice-message-active"),"loading"===s.type&&void 0!==s.callback?s.callback(n):setTimeout((function(){n.trigger("hide.notice")}),s.show?s.show:3e3)}),50),n}})}));
