<?php if ( ! defined( 'ABSPATH' )  ) { die; } // Cannot access directly.

//
// Create a widget 1
//
SHILIN::createWidget( 'shilin_widget_example_1', array(
  'title'       => '诗林小工具示例1',
  'classname'   => 'shilin-widget-classname',
  'description' => '小工具的描述',
  'fields'      => array(

    array(
      'id'      => 'title',
      'type'    => 'text',
      'title'   => '标题',
    ),

    array(
      'id'      => 'opt-text',
      'type'    => 'text',
      'title'   => '标题',
      'default' => '默认标题'
    ),

    array(
      'id'      => 'opt-color',
      'type'    => 'color',
      'title'   => '颜色',
    ),

    array(
      'id'      => 'opt-upload',
      'type'    => 'upload',
      'title'   => '上传',
    ),

    array(
      'id'      => 'opt-textarea',
      'type'    => 'textarea',
      'title'   => '文本域',
      'help'    => '辅助提示帮助描述。',
    ),

  )
) );

//
// Front-end display of widget example 1
// Attention: This function named considering above widget base id.
//
if ( ! function_exists( 'shilin_widget_example_1' ) ) {
  function shilin_widget_example_1( $args, $instance ) {

    echo $args['before_widget'];

    // if ( ! empty( $instance['title'] ) ) {
    //   echo $args['before_title'] . apply_filters( 'widget_title', $instance['title'] ) . $args['after_title'];
    // }

    echo '<div style="padding: 20px; background-color: #f7f7f7;">';
    echo '<h3>诗林小工具示例1</h3>';
    echo '<p><strong>标题：</strong> '. $instance['title'] .'</p>';
    echo '<p><strong>文本：</strong> '. $instance['opt-text'] .'</p>';
    echo '<p><strong>颜色：</strong> '. $instance['opt-color'] .'</p>';
    echo '<p><strong>上传：</strong> '. $instance['opt-upload'] .'</p>';
    echo '<p><strong>文本域：</strong> '. $instance['opt-textarea'] .'</p>';
    echo '</div>';

    echo $args['after_widget'];

  }
}

//
// Create a widget 2
//
SHILIN::createWidget( 'shilin_widget_example_2', array(
  'title'       => '诗林小工具示例2',
  'classname'   => 'shilin-widget-classname',
  'description' => 'A description for widget example 2',
  'fields'      => array(

    array(
      'id'      => 'title',
      'type'    => 'text',
      'title'   => 'Title',
    ),

    array(
      'id'      => 'opt-text',
      'type'    => 'text',
      'title'   => 'Text',
      'default' => 'Default text value'
    ),

    array(
      'id'      => 'opt-color',
      'type'    => 'color',
      'title'   => 'Color',
    ),

    array(
      'id'      => 'opt-switcher',
      'type'    => 'switcher',
      'title'   => 'Switcher',
      'label'   => 'The label text of the switcher.',
    ),

    array(
      'id'      => 'opt-checkbox',
      'type'    => 'checkbox',
      'title'   => 'Checkbox',
      'label'   => 'The label text of the checkbox.',
    ),

    array(
      'id'          => 'opt-select',
      'type'        => 'select',
      'title'       => 'Select',
      'placeholder' => 'Select an option',
      'options'     => array(
        'opt-1'     => 'Option 1',
        'opt-2'     => 'Option 2',
        'opt-3'     => 'Option 3',
      ),
    ),

    array(
      'id'      => 'opt-radio',
      'type'    => 'radio',
      'title'   => 'Radio',
      'options' => array(
        'yes'   => 'Yes, Please.',
        'no'    => 'No, Thank you.',
      ),
      'default' => 'yes',
    ),
    array(
      'type'    => 'notice',
      'style'   => 'success',
      'content' => 'A <strong>notice</strong> field with <strong>success</strong> style.',
    ),

    array(
      'id'      => 'opt-textarea',
      'type'    => 'textarea',
      'title'   => 'Textarea',
      'help'    => 'The help text of the field.',
    ),

  )
) );

//
// Front-end display of widget example 2
// Attention: This function named considering above widget base id.
//
if ( ! function_exists( 'shilin_widget_example_2' ) ) {
  function shilin_widget_example_2( $args, $instance ) {

    echo $args['before_widget'];

    // if ( ! empty( $instance['title'] ) ) {
    //   echo $args['before_title'] . apply_filters( 'widget_title', $instance['title'] ) . $args['after_title'];
    // }

    echo '<div style="padding: 20px; background-color: #f7f7f7;">';
    echo '<h3>Shilin Widget Example 2</h3>';
    echo '<p><strong>Title:</strong> '. $instance['title'] .'</p>';
    echo '<p><strong>Text:</strong> '. $instance['opt-text'] .'</p>';
    echo '<p><strong>Color:</strong> '. $instance['opt-color'] .'</p>';
    echo '<p><strong>Switcher:</strong> '. $instance['opt-switcher'] .'</p>';
    echo '<p><strong>Checkbox:</strong> '. $instance['opt-checkbox'] .'</p>';
    echo '<p><strong>Select:</strong> '. $instance['opt-select'] .'</p>';
    echo '<p><strong>Radio:</strong> '. $instance['opt-radio'] .'</p>';
    echo '<p><strong>Textarea:</strong> '. $instance['opt-textarea'] .'</p>';
    echo '</div>';

    echo $args['after_widget'];

  }
}
