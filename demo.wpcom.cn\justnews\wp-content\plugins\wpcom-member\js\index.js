!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}var t;(t=jQuery).fn.emulateTransitionEnd=function(e){var i=!1,o=this;return t(this).one("wpcomTransitionEnd",(function(){i=!0})),setTimeout((function(){i||t(o).trigger(t.__transition.end)}),e),this},t((function(){t.__transition=function(){var e=document.createElement("wpcom"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in t)if(void 0!==e.style[i])return{end:t[i]};return!1}(),t.__transition&&(t.event.special.wpcomTransitionEnd={bindType:t.__transition.end,delegateType:t.__transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})),function(t){var i=function(e,i){this.options=i,this.$body=t(document.body),this.$element=t(e),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.fixedContent=".navbar-fixed-top, .navbar-fixed-bottom",this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy((function(){this.$element.trigger("loaded.wpcom.modal")}),this))};function o(o,s){return this.each((function(){var a=t(this),n=a.data("wpcom.modal"),r=t.extend({},i.DEFAULTS,a.data(),"object"==e(o)&&o);n||a.data("wpcom.modal",n=new i(this,r)),"string"==typeof o?n[o](s):r.show&&n.show(s)}))}if(i.TRANSITION_DURATION=250,i.BACKDROP_TRANSITION_DURATION=120,i.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},i.prototype.toggle=function(e){return this.isShown?this.hide():this.show(e)},i.prototype.show=function(e){var o=this,s=t.Event("show.wpcom.modal",{relatedTarget:e});this.$element.trigger(s),this.isShown||s.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.wpcom.modal",'[data-wpcom-dismiss="modal"], [data-dismiss="modal"]',t.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.wpcom.modal",(function(){o.$element.one("mouseup.dismiss.wpcom.modal",(function(e){t(e.target).is(o.$element)&&(o.ignoreBackdropClick=!0)}))})),this.backdrop((function(){var s=t.__transition&&o.$element.hasClass("fade");o.$element.parent().length||o.$element.appendTo(o.$body),o.$element.show().scrollTop(0),o.adjustDialog(),s&&o.$element[0].offsetWidth,o.$element.addClass("in"),o.enforceFocus();var a=t.Event("shown.wpcom.modal",{relatedTarget:e});s?o.$dialog.one("wpcomTransitionEnd",(function(){o.$element.trigger("focus").trigger(a)})).emulateTransitionEnd(i.TRANSITION_DURATION):o.$element.trigger("focus").trigger(a)})))},i.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.wpcom.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),t(document).off("focusin.wpcom.modal"),this.$element.removeClass("in").off("click.dismiss.wpcom.modal").off("mouseup.dismiss.wpcom.modal"),this.$dialog.off("mousedown.dismiss.wpcom.modal"),t.__transition&&this.$element.hasClass("fade")?this.$element.one("wpcomTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(i.TRANSITION_DURATION):this.hideModal())},i.prototype.enforceFocus=function(){t(document).off("focusin.wpcom.modal").on("focusin.wpcom.modal",t.proxy((function(e){document===e.target||this.$element[0]===e.target||this.$element.has(e.target).length||this.$element.trigger("focus")}),this))},i.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.wpcom.modal",t.proxy((function(e){27==e.which&&this.hide()}),this)):this.isShown||this.$element.off("keydown.dismiss.wpcom.modal")},i.prototype.resize=function(){this.isShown?t(window).on("resize.wpcom.modal",t.proxy(this.handleUpdate,this)):t(window).off("resize.wpcom.modal")},i.prototype.hideModal=function(){var e=this;this.$element.hide(),this.backdrop((function(){e.$body.removeClass("modal-open"),e.resetAdjustments(),e.resetScrollbar(),e.$element.trigger("hidden.wpcom.modal")}))},i.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},i.prototype.backdrop=function(e){var o=this,s=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var a=t.__transition&&s;if(this.$backdrop=t(document.createElement("div")).addClass("wpcom-modal-backdrop "+s).appendTo(this.$body),this.$element.on("click.dismiss.wpcom.modal",t.proxy((function(e){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide())}),this)),a&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;a?this.$backdrop.one("wpcomTransitionEnd",e).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var n=function(){o.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("wpcomTransitionEnd",n).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):n()}else e&&e()},i.prototype.handleUpdate=function(){this.adjustDialog()},i.prototype.adjustDialog=function(){var e=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&e?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!e?this.scrollbarWidth:""})},i.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},i.prototype.checkScrollbar=function(){var e=window.innerWidth;if(!e){var t=document.documentElement.getBoundingClientRect();e=t.right-Math.abs(t.left)}this.bodyIsOverflowing=document.body.clientWidth<e,this.scrollbarWidth=this.measureScrollbar()},i.prototype.setScrollbar=function(){var e=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||0;var i=this.scrollbarWidth;this.bodyIsOverflowing&&(this.$body.css("--scrollbar-width",e+i+"px"),t(this.fixedContent).each((function(e,o){var s=o.style.paddingRight,a=t(o).css("padding-right");t(o).data("padding-right",s).css("padding-right",parseFloat(a)+i+"px")})))},i.prototype.resetScrollbar=function(){this.$body.css("--scrollbar-width",this.originalBodyPad+"px"),t(this.fixedContent).each((function(e,i){var o=t(i).data("padding-right");t(i).removeData("padding-right"),i.style.paddingRight=o||""}))},i.prototype.measureScrollbar=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",this.$body.append(e);var t=e.offsetWidth-e.clientWidth;return this.$body[0].removeChild(e),t},void 0===t.fn._modal){var s=t.fn._modal;t.fn._modal=o,t.fn._modal.Constructor=i,t.fn._modal.noConflict=function(){return t.fn._modal=s,this},t(document).on("click.wpcom.modal.data-api",'[data-toggle="modal"]',(function(e){var i=t(this),s=i.attr("href"),a=i.attr("data-target")||s&&s.replace(/.*(?=#[^\s]+$)/,""),n=t(document).find(a),r=n.data("wpcom.modal")?"toggle":t.extend({remote:!/#/.test(s)&&s},n.data(),i.data());i.is("a")&&e.preventDefault(),n.one("show.wpcom.modal",(function(e){e.isDefaultPrevented()||n.one("hidden.wpcom.modal",(function(){i.is(":visible")&&i.trigger("focus")}))})),o.call(n,r,this)}))}}(jQuery),function(e){var t='[data-wpcom-dismiss="alert"], [data-dismiss="alert"]',i=function(i){e(i).on("click",t,this.close)};i.TRANSITION_DURATION=150,i.prototype.close=function(t){var o=e(this),s=o.attr("data-target");s||(s=(s=o.attr("href"))&&s.replace(/.*(?=#[^\s]*$)/,"")),s="#"===s?[]:s;var a=e(document).find(s);function n(){a.detach().trigger("closed.wpcom.alert").remove()}t&&t.preventDefault(),a.length||(a=o.closest(".wpcom-alert")),a.trigger(t=e.Event("close.wpcom.alert")),t.isDefaultPrevented()||(a.removeClass("in"),e.__transition&&a.hasClass("fade")?a.one("wpcomTransitionEnd",n).emulateTransitionEnd(i.TRANSITION_DURATION):n())};var o=e.fn._alert;e.fn._alert=function(t){return this.each((function(){var o=e(this),s=o.data("wpcom.alert");s||o.data("wpcom.alert",s=new i(this)),"string"==typeof t&&s[t].call(o)}))},e.fn._alert.Constructor=i,e.fn._alert.noConflict=function(){return e.fn._alert=o,this},e(document).on("click.wpcom.alert.data-api",t,i.prototype.close)}(jQuery),jQuery((function(e){function t(e,t){var i=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),o=(e&&e.split("?")[1]?e.split("?")[1]:"").match(i);return null!=o?unescape(o[2]):null}var i,o,s=0;e(document).on("click",".edit-avatar, .edit-cover",(function(t){t.preventDefault(),s=e(this).hasClass("edit-cover")?1:0,o=e(this).data("user");var a=cropperModal({lg:s,title:_wpmx_js.cropper.title,desc:s?_wpmx_js.cropper.desc_1:_wpmx_js.cropper.desc_0,btn:_wpmx_js.cropper.btn,loading:_wpmx_js.cropper.loading,apply:_wpmx_js.cropper.apply,cancel:_wpmx_js.cropper.cancel});e("#crop-modal").length?e("#crop-modal").replaceWith(a):e("body").append(a),i&&(i.destroy(),i=null,e(".crop-img-wrap").hide(),e(".crop-img-btn").show(),e("#crop-img").remove()),e("#crop-modal")._modal("show")})).on("change","#img-file",(function(t){if(!this.files.length)return!1;var o;if(this.files[0].size/1024>5120)return wpcom_alert(_wpmx_js.cropper.alert_size),!1;this.files[0].type.match(/image.*/)?(o=window.URL.createObjectURL(this.files[0]),e(".crop-img-wrap").append('<img id="crop-img" src="'+o+'">').show(),e(".crop-img-btn").hide(),i=new Cropper(document.getElementById("crop-img"),{aspectRatio:s?2.7:1,minContainerHeight:300,viewMode:s?3:1,ready:function(){var e={width:300,height:300};s&&(e={width:810,height:300,left:44}),i.setCropBoxData(e)}}),e(this).val("")):wpcom_alert(_wpmx_js.cropper.alert_filetype)})).on("click",".j-crop-close",(function(){i&&i.destroy(),i=null,e(".crop-img-wrap").hide(),e(".crop-img-btn").show(),e("#crop-img").remove()})).on("click",".j-crop-apply",(function(){var t=e(this);if(t.loading(1),i)if(i.crop().cropped){var a={minWidth:200,minHeight:200,maxWidth:600,maxHeight:600,fillColor:"#fff",imageSmoothingQuality:"high"};s&&(a={minWidth:810,minHeight:300,maxWidth:1620,maxHeight:600,fillColor:"#fff",imageSmoothingQuality:"high"});var n=e.extend(i.getCropBoxData(),a),r=i.getCroppedCanvas(n).toDataURL("image/jpeg",.95);if(r){var l=new FormData;l.append("action","wpcom_cropped_upload"),l.append("nonce",e("#wpcom_cropper_nonce").val()),l.append("image",r),l.append("type",s),o&&l.append("user",o),e.ajax(_wpmx_js.ajaxurl,{type:"POST",data:l,dataType:"json",processData:!1,contentType:!1,success:function(i){"1"==i.result?(s?e(".wpcom-profile-head .wpcom-ph-bg img").attr("src",i.url):e(".member-account-avatar img.avatar,.wpcom-ph-avatar img.avatar,#j-user-wrap img.avatar").replaceWith('<img class="avatar photo" src="'+i.url+"?t="+Date.parse(new Date)/1e3+'">'),e("#crop-modal")._modal("hide")):"-1"==i.result?wpcom_notice({message:_wpmx_js.cropper.err_nonce,show:2e3,type:"warning"}):"-2"==i.result?wpcom_notice({message:_wpmx_js.cropper.err_fail,show:2e3,type:"error"}):"-3"==i.result&&wpcom_notice({message:_wpmx_js.cropper.err_login,show:2e3,type:"warning"}),t.loading(0)},error:function(){wpcom_notice({message:_wpmx_js.cropper.ajaxerr,show:2e3,type:"error"}),t.loading(0)}})}else t.loading(0)}else t.loading(0);else wpcom_notice({message:_wpmx_js.cropper.err_empty,show:2e3,type:"warning"}),t.loading(0)})).on("click",".j-social-unbind",(function(){var t=e(this);if(t.hasClass("disabled"))return!1;var i=t.data("name");t.addClass("disabled").text("\u5904\u7406\u4e2d..."),confirm("\u662f\u5426\u786e\u5b9a\u89e3\u9664\u7ed1\u5b9a\uff1f")?e.ajax({type:"POST",url:_wpmx_js.ajaxurl,data:{action:"wpcom_social_unbind",name:i},dataType:"json",success:function(e){t.removeClass("disabled").text("\u89e3\u9664\u7ed1\u5b9a"),1==e.result?(wpcom_alert("\u89e3\u7ed1\u6210\u529f\uff01"),t.parent().html(e.error)):e.error&&wpcom_alert(e.error)},error:function(){t.removeClass("disabled").text("\u89e3\u9664\u7ed1\u5b9a")}}):t.removeClass("disabled").text("\u89e3\u9664\u7ed1\u5b9a")})).on("click","a",(function(i){var o=e(this).attr("href"),s=o?o.match(/(\?|&)modal-type=(login|register)/i):null;if(s&&s[2]){if(e("body.navbar-on").length)return;i.preventDefault(),i.stopPropagation();var a=e("#login-form-modal");0===a.length&&(jQuery(document.body).append('<div class="wpcom-modal modal-login fade" id="login-form-modal" data-backdrop="static">\n            <div class="modal-dialog">\n                <div class="modal-content"><div class="wpcom-close" data-wpcom-dismiss="modal" aria-label="Close"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-close"></use></svg></i></div>\n                    <div class="modal-body"></div>\n                </div>\n            </div>\n        </div>'),a=jQuery("#login-form-modal")),e("#login-modal").length&&e("#login-modal")._modal("hide");var n=a.find(".modal-body");n.html('<i class="wpcom-icon wi wpcom-icon-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'),e("body").hasClass("modal-open")?setTimeout((function(){a._modal("show")}),200):a._modal("show");var r=t(o,"approve"),l={action:"wpcom_login_modal",type:s[2]};return r&&(l.approve=r,l.login=t(o,"login")),e.ajax({type:"POST",url:_wpmx_js.ajaxurl,data:l,dataType:"html",success:function(t){"undefined"==typeof is_load_login?e.getScript(_wpmx_js.plugin_url+"js/login.js",(function(){n.html(t),e(document).trigger("init_captcha")})):(n.html(t),e(document).trigger("init_captcha")),setTimeout((function(){e.fn.tooltip&&n.find('[data-toggle="tooltip"]').tooltip()}),1e3)},error:function(){}}),!1}})).on("click",".member-form-tab a",(function(t){t.preventDefault();var i=e(this);if(i.closest("li").hasClass("active"))return!1;var o=i.closest("ul"),s=o.closest(".member-form-inner"),a=i.data("type"),n=e("#j-tpl-login"+("2"==a?"2":"")).html();n&&(s.find(".member-form-items").html(n),o.find("li").removeClass("active"),i.closest("li").addClass("active"),a=a?0:1,e(document).trigger("init_captcha"))})).on("click",".show-password",(function(){var t=e(this);t.hasClass("active")?(t.html('<i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-eye-off-fill"></use></svg></i>').removeClass("active"),t.parent().find("input").attr("type","password")):(t.html('<i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-eye-fill"></use></svg></i>').addClass("active"),t.parent().find("input").attr("type","text"))})).on("wpcom_not_login",(function(){!function(){if(0===jQuery("#login-modal").length){var e=_wpmx_js.login_url,t=_wpmx_js.register_url,i='<div class="wpcom-modal fade" id="login-modal">\n    <div class="modal-dialog modal-sm">\n        <div class="modal-content">\n            <div class="modal-header">\n                <div class="wpcom-close" data-wpcom-dismiss="modal" aria-label="Close"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-close"></use></svg></i></div>\n                <h4 class="modal-title">'+_wpmx_js.js_lang.login_title+'</h4>\n            </div>\n            <div class="modal-body">\n                <p>'+_wpmx_js.js_lang.login_desc+'</p>\n            </div>\n           <div class="modal-footer">\n                    <a class="wpcom-btn btn-primary" href="'+e+'">'+_wpmx_js.js_lang.login_btn+'</a>\n                    <a class="wpcom-btn" href="'+t+'">'+_wpmx_js.js_lang.reg_btn+"</a>\n                </div>\n        </div>\n    </div>\n</div>";jQuery("body").append(i)}}()}));var a=e("#j-user-wrap");a.length&&e.ajax({type:"POST",url:_wpmx_js.ajaxurl,data:{action:"wpcom_is_login"},dataType:"json",success:function(t){if(0==t.result){var i=0;t.messages&&(i=Number(t.messages)),t.notifications&&(i+=Number(t.notifications));var o='<ul class="profile"><li class="menu-item dropdown"><a class="menu-item-user" href="'+(t.account?t.account:t.url)+'"><span class="menu-item-avatar">'+t.avatar+(i?'<span class="menu-item-unread">'+i+"</span>":"")+'</span><span class="menu-item-name">'+t.display_name+"</span></a>",s=e(".header .navbar-toggle");if(i&&s.length&&s.append('<span class="navbar-unread">'+i+"</span>"),t.menus&&t.menus.length){o+='<ul class="dropdown-menu">';for(var n=0;n<t.menus.length;n++)o+='<li><a href="'+t.menus[n].url+'">'+t.menus[n].title+"</a></li>";o+="</ul>"}o+="</li></ul>",a.html(o),window.is_login=!0,e(document).trigger("wpcom_login",t)}else a.find(".login").addClass("cur"),window.is_login=!1,e(document).trigger("wpcom_not_login");t.wc&&(t.wc.fragments&&t.wc.fragments["a.cart-contents"]&&e("header .shopping-cart").html(t.wc.fragments["a.cart-contents"]),setTimeout((function(){t.wc.fragments&&t.wc.fragments["div.widget_shopping_cart_content"]&&e("header .shopping-cart").append(t.wc.fragments["div.widget_shopping_cart_content"])}),100)),e(document).trigger("wpcom_login_checked")}}),e(".social-login-wrap").on("submit","#sl-form-create",(function(){var t=e(this);if(t.find(".sl-input-submit.disabled").length)return!1;t.find(".sl-input-submit").addClass("disabled");for(var i=0,o=t.find(".sl-input input.require"),s=0;s<o.length;s++){""==(e(o[s]).length?e(o[s]).val():"").trim()&&(e(o[s]).addClass("error"),i=1)}return i?t.find(".sl-input-submit").removeClass("disabled"):e.ajax({url:_wpmx_js.ajaxurl,data:e(this).serialize()+"&action=wpcom_sl_login",type:"POST",dataType:"json",success:function(e){t.find(".sl-input-submit").removeClass("disabled"),"-1"==e?t.find(".sl-result").text("\u8bf7\u6c42\u51fa\u9519\uff0c\u8bf7\u91cd\u8bd5\uff01").addClass("error"):"1"==e.result?t.find(".sl-result").text("\u7528\u6237\u540d\u6216\u5bc6\u7801\u4e0d\u80fd\u4e3a\u7a7a").addClass("error"):"2"==e.result?t.find(".sl-result").text("\u7528\u6237\u540d\u6216\u5bc6\u7801\u9519\u8bef").addClass("error"):"3"==e.result?t.find(".sl-result").text("\u7b2c\u4e09\u65b9\u5e94\u7528\u6388\u6743\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5").addClass("error"):"4"==e.result?t.find(".sl-result").text("\u7b2c\u4e09\u65b9\u5e10\u53f7\u5df2\u4e0e\u672c\u7ad9\u5176\u4ed6\u5e10\u53f7\u7ed1\u5b9a").addClass("error"):"0"==e.result&&(t.find(".sl-result").text("\u7ed1\u5b9a\u6210\u529f\uff01").removeClass("error"),setTimeout((function(){window.location.href=e.redirect}),100))},error:function(e){t.find(".sl-result").text("\u8bf7\u6c42\u51fa\u9519\uff0c\u8bf7\u91cd\u8bd5\uff01").addClass("error"),t.find(".sl-input-submit").removeClass("disabled")}}),!1})).on("submit","#sl-form-bind",(function(){var t=e(this);if(t.find(".sl-input-submit.disabled").length)return!1;t.find(".sl-input-submit").addClass("disabled");for(var i=0,o=t.find(".sl-input input.require"),s=0;s<o.length;s++){""==(e(o[s]).length?e(o[s]).val():"").trim()&&(e(o[s]).addClass("error"),i=1)}return i?t.find(".sl-input-submit").removeClass("disabled"):e.ajax({url:_wpmx_js.ajaxurl,data:e(this).serialize()+"&action=wpcom_sl_create",type:"POST",dataType:"json",success:function(e){"-1"==e?t.find(".sl-result").text("\u8bf7\u6c42\u51fa\u9519\uff0c\u8bf7\u91cd\u8bd5\uff01").addClass("error"):"1"==e.result?t.find(".sl-result").text("\u8bf7\u8f93\u5165\u7535\u5b50\u90ae\u7bb1").addClass("error"):"2"==e.result?t.find(".sl-result").text("\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u7535\u5b50\u90ae\u7bb1").addClass("error"):"3"==e.result?t.find(".sl-result").text("\u7b2c\u4e09\u65b9\u5e94\u7528\u6388\u6743\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5").addClass("error"):"4"==e.result?t.find(".sl-result").text("\u7b2c\u4e09\u65b9\u5e10\u53f7\u5df2\u4e0e\u672c\u7ad9\u5176\u4ed6\u5e10\u53f7\u7ed1\u5b9a").addClass("error"):"5"==e.result?t.find(".sl-result").text("\u8be5\u90ae\u7bb1\u5df2\u88ab\u6ce8\u518c").addClass("error"):"0"==e.result?(t.find(".sl-result").text("\u6ce8\u518c\u6210\u529f\uff01").removeClass("error"),setTimeout((function(){window.location.href=e.redirect}),100)):e.result&&e.msg&&t.find(".sl-result").text(e.msg).addClass("error"),t.find(".sl-input-submit").removeClass("disabled")},error:function(e){t.find(".sl-result").text("\u8bf7\u6c42\u51fa\u9519\uff0c\u8bf7\u91cd\u8bd5\uff01").addClass("error"),t.find(".sl-input-submit").removeClass("disabled")}}),!1})).on("input change",".sl-input input",(function(){var t=e(this);t.removeClass("error"),t.closest(".sl-info-form").find(".sl-result").text("")})).on("click",".sl-form-title",(function(){var t=e(this).closest(".sl-form-item");e(".sl-form-item").removeClass("active"),t.addClass("active")}))})),jQuery((function(t){t.fn.loading||t.fn.extend({loading:function(e){var i=t(this);e?i.addClass("loading").prepend('<i class="wpcom-icon wi wi-loader"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'):i.removeClass("loading").find(".wi-loader").remove()}}),window.wpcom_alert||(window.wpcom_alert=function(e,i){i=i||"\u63d0\u793a\u4fe1\u606f";var o=t("#wpcom-alert");if(o.length)o.find(".modal-title").html(i),o.find(".modal-body").html(e),o._modal("show");else{var s='<div class="wpcom-modal fade modal-alert" id="wpcom-alert" data-backdrop="static">\n            <div class="modal-dialog modal-sm">\n                <div class="modal-content">                   <div class="modal-header"><div class="wpcom-close" data-wpcom-dismiss="modal" aria-label="Close"><i class="wpcom-icon wi"><svg aria-hidden="true"><use xlink:href="#wi-close"></use></svg></i></div><h4 class="modal-title">'+i+'</h4></div>\n                   <div class="modal-body">'+e+'</div>\n                   <div class="modal-footer"><button type="button" class="wpcom-btn btn-primary" data-wpcom-dismiss="modal" aria-label="Close">\u786e\u5b9a</button></div>                </div>\n            </div>\n        </div>';t("body").append(s)}t("#wpcom-alert")._modal("show")}),window.wpcom_notice||(window.wpcom_notice=function(t){if(!arguments.length||1===arguments.length&&"object"===e(arguments[0])||(t={},void 0!==arguments[0]&&(t.message=arguments[0]),void 0!==arguments[1]&&(t.type=arguments[1]),void 0!==arguments[2]&&"loading"!==t.type&&(t.show=arguments[2]),void 0!==arguments[2]&&"loading"===t.type&&(t.callback=arguments[2])),t&&t.message){t.type=t.type?t.type:"success";var i='<div class="notice-message"><div class="notice-message-content notice-message-'+t.type+'">';"success"===t.type?i+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-success"></use></svg></i>':"warning"===t.type||"error"===t.type?i+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-warning"></use></svg></i>':"loading"===t.type&&(i+='<i class="wpcom-icon wi notice-message-icon"><svg aria-hidden="true"><use xlink:href="#wi-loader"></use></svg></i>'),i+=t.message+"</div></div>";var o=jQuery(i),s=jQuery(".notice-message-wrapper");return 0===s.length&&(jQuery(document.body).append('<div class="notice-message-wrapper"></div>'),s=jQuery(".notice-message-wrapper")),s.append(o),o.one("hide.notice",(function(){var e=jQuery(this);e.removeClass("notice-message-active").addClass("notice-message-up"),setTimeout((function(){e.remove(),0===s.find(".notice-message").length&&s.remove()}),320)})),setTimeout((function(){o.addClass("notice-message-active"),"loading"===t.type&&void 0!==t.callback?t.callback(o):setTimeout((function(){o.trigger("hide.notice")}),t.show?t.show:3e3)}),50),o}}),t(document.body).on("click",".profile-tab .profile-tab-item",(function(){var e=t(this),i=e.closest(".wpcom-profile-main"),o=e.index();i.find(".profile-tab-item, .profile-tab-content").removeClass("active"),e.addClass("active"),i.find(".profile-tab-content").eq(o).addClass("active").trigger("profile_tab_show")})).on("click",".j-user-posts, .j-user-comments, .j-user-favorites, .j-user-follows, .j-user-followers",(function(){var e=t(this);if(!e.hasClass("disabled")&&!e.hasClass("loading")){var i=null,o=e.data("page");if(o=void 0!==o?o+1:2,e.hasClass("j-user-posts")){var s=t(".profile-posts-list").data("user");i={action:"wpcom_user_posts",user:s||0,page:o}}else if(e.hasClass("j-user-comments")){var a=t(".profile-comments-list").data("user");i={action:"wpcom_user_comments",user:a||0,page:o}}else if(e.hasClass("j-user-favorites")){var n=t(".profile-favorites-list").data("user");i={action:"wpcom_user_favorites",user:n||0,page:o}}else if(e.hasClass("j-user-follows")){var r=t(".profile-tab").data("user");i={action:"wpcom_user_follows",user:r||0,page:o}}else if(e.hasClass("j-user-followers")){var l=t(".profile-tab").data("user");i={action:"wpcom_user_followers",user:l||0,page:o}}return e.loading(1),t.ajax({type:"POST",url:_wpmx_js.ajaxurl,data:i,dataType:"html",success:function(i,s,a){if("0"==i){if(e.addClass("disabled").text(_wpmx_js.js_lang.page_loaded),e.hasClass("j-user-followers")){var n=e.closest(".profile-tab-content");n.find(".follow-items-loading").length&&(n.find(".follow-items-loading").remove(),n.find(".profile-no-content").show())}}else{var r=t(i);if(e.parent().prev().append(r),e.hasClass("j-user-follows"))t(document).trigger("check_follow");else if(e.hasClass("j-user-followers")){var l=e.closest(".profile-tab-content");l.find(".follow-items-loading").remove(),l.find(".follow-items").show(),"0"!==a.getResponseHeader("Next-page")&&l.find(".load-more-wrap").show(),t(document).trigger("check_follow")}"undefined"!=typeof _wpcom_js&&t.fn.lazyload&&function(e){if(e.length){var t=void 0!==_wpcom_js.webp&&_wpcom_js.webp?_wpcom_js.webp:null;if(e.eq(0).is("img"))e.eq(0).one("load",(function(){e.lazyload({webp:t})}));else{var i=document.createElement("img"),o=window.getComputedStyle(e[0]).getPropertyValue("background-image");o?(i.src=o.slice(4,-1).replace(/['"]/g,""),i.onload=function(){e.lazyload({webp:t})}):setTimeout((function(){i.onload=function(){e.lazyload({webp:t})}}),300)}}}(r.find(".j-lazy")),e.data("page",o),t(window).trigger("scroll")}e.loading(0)},error:function(){e.loading(0)}}),!1}}))}))}));
