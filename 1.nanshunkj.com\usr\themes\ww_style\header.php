<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="<?php $this->options->charset(); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#0e0e0e">
    <title><?php $this->archiveTitle(array(
        'category'  =>  _t('分类 %s 下的文章'),
        'search'    =>  _t('包含关键字 %s 的文章'),
        'tag'       =>  _t('标签 %s 下的文章'),
        'author'    =>  _t('%s 发布的文章')
    ), '', ' - '); ?><?php $this->options->title(); ?></title>

    <!-- 引入样式表和字体图标 -->
    <link rel="stylesheet" href="https://cdn.staticfile.org/remixicon/3.5.0/remixicon.min.css">
    <link rel="stylesheet" href="<?php $this->options->themeUrl('assets/css/style.css'); ?>">



    <!-- 全局配置变量 -->
    <script>
    var typechoConfig = {
        siteUrl: '<?php $this->options->siteUrl(); ?>',
        themeUrl: '<?php $this->options->themeUrl(); ?>',
        userId: <?php echo $this->user->hasLogin() ? $this->user->uid : 'null'; ?>,
        isLoggedIn: <?php echo $this->user->hasLogin() ? 'true' : 'false'; ?>
    };
    </script>

    <!-- 引入主题JavaScript -->
    <script src="<?php $this->options->themeUrl('assets/js/main.js'); ?>"></script>

    <!-- 通过自有函数输出HTML头部信息 -->
    <?php $this->header(); ?>
</head>
<body<?php if($this->user->hasLogin()): ?> class="logged-in"<?php endif; ?>>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo">
            <?php if ($this->options->logoUrl): ?>
                <a href="<?php $this->options->siteUrl(); ?>">
                    <img src="<?php $this->options->logoUrl(); ?>" alt="<?php $this->options->title(); ?>">
                </a>
            <?php else: ?>
                <a href="<?php $this->options->siteUrl(); ?>">WW Style</a>
            <?php endif; ?>
        </div>

        <!-- 移动端菜单按钮 -->
        <button id="mobile-menu-toggle" class="mobile-menu-toggle" aria-label="菜单" aria-expanded="false">
            <span class="menu-icon"></span>
        </button>

        <nav class="nav-links">
            <a href="<?php $this->options->siteUrl(); ?>" <?php if($this->is('index')): ?>class="active"<?php endif; ?>>首页</a>
            <a href="<?php $this->options->siteUrl(); ?>index.php/archive.html">分类</a>
            <a href="<?php $this->options->siteUrl(); ?>index.php/links.html">好物推荐</a>
            <a href="<?php $this->options->siteUrl(); ?>index.php/about.html">关于</a>
        </nav>

        <div class="user-area">
            <div class="search-btn">
                <i class="ri-search-line"></i>
            </div>
            <?php if ($this->user->hasLogin()): ?>
                <div class="user-dropdown">
                    <div class="user-avatar">
                        <img src="<?php echo getUserAvatar($this->user->mail, 32); ?>" alt="<?php $this->user->screenName(); ?>">
                    </div>
                    <div class="dropdown-menu">
                        <a href="<?php $this->options->siteUrl(); ?>index.php/member.html" class="dropdown-item">
                            <i class="ri-user-line"></i> 会员中心
                        </a>
                        <?php if ($this->user->group == 'administrator'): ?>
                        <a href="<?php $this->options->adminUrl(); ?>" class="dropdown-item">
                            <i class="ri-dashboard-line"></i> 管理后台
                        </a>
                        <?php endif; ?>
                        <a href="<?php $this->options->logoutUrl(); ?>" class="dropdown-item">
                            <i class="ri-logout-box-line"></i> 退出登录
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <a href="<?php $this->options->adminUrl('login.php'); ?>" class="login-btn">立即登录</a>
            <?php endif; ?>
        </div>
    </header>

    <!-- 搜索框 -->
    <div class="search-overlay" id="search-overlay">
        <div class="search-container">
            <div class="search-box">
                <form id="search" method="post" action="<?php $this->options->siteUrl(); ?>" role="search">
                    <input type="text" name="s" class="search-input" placeholder="输入关键词搜索..." required>
                    <button type="submit" class="search-submit"><i class="ri-search-line"></i></button>
                </form>
                <div class="search-close" id="search-close"><i class="ri-close-line"></i></div>
            </div>
        </div>
    </div>