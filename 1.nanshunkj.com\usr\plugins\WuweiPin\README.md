# Wuwei_Pin 文章置顶插件

一个功能强大的 Typecho 文章置顶插件，支持多种置顶样式和灵活的配置选项。专为提升博客文章管理效率设计。

## 功能特点

### 1. 多文章置顶
- 支持同时置顶多篇文章
- 可设置置顶文章的显示顺序
- 支持在首页和分类页面分别控制置顶显示

### 2. 样式定制
- Jasmine主题风格：精美的卡片式设计
- 简约风格：轻量简洁的图标标记
- 自定义样式：完全自由的HTML/CSS定制
- 支持主题配色跟随
- 自适应移动端显示

### 3. 便捷操作
- 可视化文章选择
- 快速置顶按钮
- 实时预览效果
- 拖拽排序支持

### 4. 其他特性
- 分类页面独立控制
- 兼容主流主题
- 支持触屏设备
- 完善的错误处理

## 安装要求

- PHP 5.4.0 或更高版本
- Typecho 1.1 或更高版本
- JSON PHP 扩展
- 现代浏览器支持

## 安装方法

1. 下载插件压缩包
2. 解压后将 `Wuwei_Pin` 文件夹上传到 Typecho 的 `usr/plugins` 目录
3. 进入 Typecho 后台 -> 控制台 -> 插件
4. 找到 "Wuwei_Pin" 插件，点击"启用"

## 详细配置说明

### 1. 文章置顶设置

#### 选择置顶文章
- 在插件设置页面可以看到所有已发布文章的列表
- 通过勾选框可以选择需要置顶的文章
- 支持批量选择和取消
- 文章列表按发布时间排序，方便查找

#### 置顶排序
可以选择以下排序方式：
- 按发布时间倒序（最新的置顶文章显示在前）
- 按发布时间正序（最早的置顶文章显示在前）
- 自定义顺序（按选择顺序排列）

### 2. 样式设置

#### Jasmine主题风格
- 带有背景色的标签式设计
- 自动适配主题配色
- 包含图标和文字
- 响应式设计

示例效果：