/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

!function(I,_,b,y){"use strict";var T=T||{};T.funcs={},T.vars={onloaded:!1,$body:I("body"),$window:I(_),$document:I(b),$form_warning:null,is_confirm:!1,form_modified:!1,code_themes:[],is_rtl:I("body").hasClass("rtl")},T.helper={uid:function(e){return(e||"")+Math.random().toString(36).substr(2,9)},preg_quote:function(e){return(e+"").replace(/(\[|\])/g,"\\$1")},name_nested_replace:function(e,t){var n=new RegExp(T.helper.preg_quote(t+"[\\d+]"),"g");e.find(":radio").each(function(){(this.checked||this.orginal_checked)&&(this.orginal_checked=!0)}),e.each(function(e){I(this).find(":input").each(function(){this.name=this.name.replace(n,t+"["+e+"]"),this.orginal_checked&&(this.checked=!0)})})},debounce:function(i,a,s){var c;return function(){var e=this,t=arguments,n=s&&!c;clearTimeout(c),c=setTimeout(function(){c=null,s||i.apply(e,t)},a),n&&i.apply(e,t)}}},I.fn.shilin_clone=function(){for(var e=I.fn.clone.apply(this,arguments),t=this.find("select").add(this.filter("select")),n=e.find("select").add(e.filter("select")),i=0;i<t.length;++i)for(var a=0;a<t[i].options.length;++a)!0===t[i].options[a].selected&&(n[i].options[a].selected=!0);return this.find(":radio").each(function(){this.orginal_checked=this.checked}),e},I.fn.shilin_expand_all=function(){return this.each(function(){I(this).on("click",function(e){e.preventDefault(),I(".shilin-wrapper").toggleClass("shilin-show-all"),I(".shilin-section").shilin_reload_script(),I(this).find(".fa").toggleClass("fa-indent").toggleClass("fa-outdent")})})},I.fn.shilin_nav_options=function(){return this.each(function(){var a,e=I(this),t=I(_),s=I("#wpwrap"),c=e.find("a");t.on("hashchange shilin.hashchange",function(){var e=_.location.hash.replace("#tab=",""),t=e||c.first().attr("href").replace("#tab=",""),n=I('[data-tab-id="'+t+'"]');if(n.length){n.closest(".shilin-tab-item").addClass("shilin-tab-expanded").siblings().removeClass("shilin-tab-expanded"),n.next().is("ul")&&(t=(n=n.next().find("li").first().find("a")).data("tab-id")),c.removeClass("shilin-active"),n.addClass("shilin-active"),a&&a.addClass("hidden");var i=I('[data-section-id="'+t+'"]');i.removeClass("hidden"),i.shilin_reload_script(),I(".shilin-section-id").val(i.index()+1),a=i,s.hasClass("wp-responsive-open")&&(I("html, body").animate({scrollTop:i.offset().top-50},200),s.removeClass("wp-responsive-open"))}}).trigger("shilin.hashchange")})},I.fn.shilin_nav_metabox=function(){return this.each(function(){var a,e=I(this),s=e.find("a"),c=e.parent().find(".shilin-section");s.each(function(i){I(this).on("click",function(e){e.preventDefault();var t=I(this);s.removeClass("shilin-active"),t.addClass("shilin-active"),a!==y&&a.addClass("hidden");var n=c.eq(i);n.removeClass("hidden"),n.shilin_reload_script(),a=n})}),s.first().trigger("click")})},I.fn.shilin_page_templates=function(){this.length&&I(b).on("change",".editor-page-attributes__template select, #page_template",function(){var e=I(this).val()||"default";I(".shilin-page-templates").removeClass("shilin-metabox-show").addClass("shilin-metabox-hide"),I(".shilin-page-"+e.toLowerCase().replace(/[^a-zA-Z0-9]+/g,"-")).removeClass("shilin-metabox-hide").addClass("shilin-metabox-show")})},I.fn.shilin_post_formats=function(){this.length&&I(b).on("change",'.editor-post-format select, #formatdiv input[name="post_format"]',function(){var e=I(this).val()||"default";e="0"===e?"default":e,I(".shilin-post-formats").removeClass("shilin-metabox-show").addClass("shilin-metabox-hide"),I(".shilin-post-format-"+e).removeClass("shilin-metabox-hide").addClass("shilin-metabox-show")})},I.fn.shilin_search=function(){return this.each(function(){I(this).find("input").on("change keyup",function(){var n=I(this).val(),e=I(".shilin-wrapper"),t=e.find(".shilin-section").find("> .shilin-field:not(.shilin-depend-on)"),i=t.find("> .shilin-title, .shilin-search-tags");3<n.length?(t.addClass("shilin-metabox-hide"),e.addClass("shilin-search-all"),i.each(function(){var e=I(this);if(e.text().match(new RegExp(".*?"+n+".*?","i"))){var t=e.closest(".shilin-field");t.removeClass("shilin-metabox-hide"),t.parent().shilin_reload_script()}})):(t.removeClass("shilin-metabox-hide"),e.removeClass("shilin-search-all"))})})},I.fn.shilin_sticky=function(){return this.each(function(){var i=I(this),a=I(_),s=i.find(".shilin-header-inner"),c=parseInt(s.css("padding-left"))+parseInt(s.css("padding-right")),r=0,o=!1,e=function(){o||requestAnimationFrame(function(){var e,t,n;e=i.offset().top,t=Math.max(32,e-r),n=a.innerWidth(),t<=32&&782<n?(s.css({width:i.outerWidth()-c}),i.css({height:i.outerHeight()}).addClass("shilin-sticky")):(s.removeAttr("style"),i.removeAttr("style").removeClass("shilin-sticky")),o=!1}),o=!0},t=function(){r=a.scrollTop(),e()};a.on("scroll resize",t),t()})},I.fn.shilin_dependency=function(){return this.each(function(){var e=I(this),t=e.children("[data-controller]");if(t.length){var n=I.shilin_deps.createRuleset(),i=I.shilin_deps.createRuleset(),f=[],l=[];t.each(function(){var a=I(this),e=a.data("controller").split("|"),s=a.data("condition").split("|"),c=a.data("value").toString().split("|"),r=!!a.data("depend-global"),o=r?i:n;I.each(e,function(e,t){var n=c[e]||"",i=s[e]||s[0];(o=o.createRule('[data-depend-id="'+t+'"]',i,n)).include(a),r?l.push(t):f.push(t)})}),f.length&&I.shilin_deps.enable(e,n,f),l.length&&I.shilin_deps.enable(T.vars.$body,i,l)}})},I.fn.shilin_field_accordion=function(){return this.each(function(){I(this).find(".shilin-accordion-title").on("click",function(){var e=I(this),t=e.find(".shilin-accordion-icon"),n=e.next();t.hasClass("fa-angle-right")?t.removeClass("fa-angle-right").addClass("fa-angle-down"):t.removeClass("fa-angle-down").addClass("fa-angle-right"),n.data("opened")||(n.shilin_reload_script(),n.data("opened",!0)),n.toggleClass("shilin-accordion-open")})})},I.fn.shilin_field_backup=function(){return this.each(function(){if(_.wp.customize!==y){var t=this,n=I(this),i=(I("body"),n.find(".shilin-import")),a=n.find(".shilin-reset");t.notificationOverlay=function(){wp.customize.notifications&&wp.customize.OverlayNotification&&(wp.customize.state("saved").get()||(wp.customize.state("changesetStatus").set("trash"),wp.customize.each(function(e){e._dirty=!1}),wp.customize.state("saved").set(!0)),wp.customize.notifications.add(new wp.customize.OverlayNotification("shilin_field_backup_notification",{type:"default",message:"&nbsp;",loading:!0})))},a.on("click",function(e){e.preventDefault(),T.vars.is_confirm&&(t.notificationOverlay(),_.wp.ajax.post("shilin-reset",{unique:a.data("unique"),nonce:a.data("nonce")}).done(function(e){_.location.reload(!0)}).fail(function(e){alert(e.error),wp.customize.notifications.remove("shilin_field_backup_notification")}))}),i.on("click",function(e){e.preventDefault(),T.vars.is_confirm&&(t.notificationOverlay(),_.wp.ajax.post("shilin-import",{unique:i.data("unique"),nonce:i.data("nonce"),data:n.find(".shilin-import-data").val()}).done(function(e){_.location.reload(!0)}).fail(function(e){alert(e.error),wp.customize.notifications.remove("shilin_field_backup_notification")}))})}})},I.fn.shilin_field_background=function(){return this.each(function(){I(this).find(".shilin--background-image").shilin_reload_script()})},I.fn.shilin_field_code_editor=function(){return this.each(function(){if("function"==typeof CodeMirror){var t=I(this),i=t.find("textarea"),e=t.find(".CodeMirror"),a=i.data("editor");e.length&&e.remove();var s=setInterval(function(){if(t.is(":visible")){var n=CodeMirror.fromTextArea(i[0],a);if("default"!==a.theme&&-1===T.vars.code_themes.indexOf(a.theme)){var e=I("<link>");I("#shilin-codemirror-css").after(e),e.attr({rel:"stylesheet",id:"shilin-codemirror-"+a.theme+"-css",href:a.cdnURL+"/theme/"+a.theme+".min.css",type:"text/css",media:"all"}),T.vars.code_themes.push(a.theme)}CodeMirror.modeURL=a.cdnURL+"/mode/%N/%N.min.js",CodeMirror.autoLoadMode(n,a.mode),n.on("change",function(e,t){i.val(n.getValue()).trigger("change")}),clearInterval(s)}})}})},I.fn.shilin_field_date=function(){return this.each(function(){var e=I(this),i=e.find("input"),a=e.find(".shilin-date-settings").data("settings"),t={showAnim:"",beforeShow:function(e,t){I(t.dpDiv).addClass("shilin-datepicker-wrapper")},onClose:function(e,t){I(t.dpDiv).removeClass("shilin-datepicker-wrapper")}};a=I.extend({},a,t),2===i.length&&(a=I.extend({},a,{onSelect:function(e){I(this),i.first();var t=i.first().attr("id")===I(this).attr("id")?"minDate":"maxDate",n=I.datepicker.parseDate(a.dateFormat,e);i.not(this).datepicker("option",t,n)}})),i.each(function(){var e=I(this);e.hasClass("hasDatepicker")&&e.removeAttr("id").removeClass("hasDatepicker"),e.datepicker(a)})})},I.fn.shilin_field_datetime=function(){return this.each(function(){var e=I(this),i=e.find("input"),t=e.find(".shilin-datetime-settings").data("settings");t=I.extend({},t,{onReady:function(e,t,n){I(n.calendarContainer).addClass("shilin-flatpickr")}}),2===i.length&&(t=I.extend({},t,{onChange:function(e,t,n){"from"===I(n.element).data("type")?i.last().get(0)._flatpickr.set("minDate",e[0]):i.first().get(0)._flatpickr.set("maxDate",e[0])}})),i.each(function(){I(this).flatpickr(t)})})},I.fn.shilin_field_fieldset=function(){return this.each(function(){I(this).find(".shilin-fieldset-content").shilin_reload_script()})},I.fn.shilin_field_gallery=function(){return this.each(function(){var s,e=I(this),c=e.find(".shilin-edit-gallery"),r=e.find(".shilin-clear-gallery"),o=e.find("ul"),f=e.find("input");e.find("img");e.on("click",".shilin-button, .shilin-edit-gallery",function(e){var t=I(this),n=f.val(),i=t.hasClass("shilin-edit-gallery")?"edit":"add",a="add"!==i||n.length?"gallery-edit":"gallery";e.preventDefault(),void 0!==_.wp&&_.wp.media&&_.wp.media.gallery&&("gallery"===a?(s=_.wp.media({library:{type:"image"},frame:"post",state:"gallery",multiple:!0})).open():(s=_.wp.media.gallery.edit('[gallery ids="'+n+'"]'),"add"===i&&s.setState("gallery-library")),s.on("update",function(e){o.empty();var t=e.models.map(function(e){var t=e.toJSON(),n=t.sizes&&t.sizes.thumbnail&&t.sizes.thumbnail.url?t.sizes.thumbnail.url:t.url;return o.append('<li><img src="'+n+'"></li>'),t.id});f.val(t.join(",")).trigger("change"),r.removeClass("hidden"),c.removeClass("hidden")}))}),r.on("click",function(e){e.preventDefault(),o.empty(),f.val("").trigger("change"),r.addClass("hidden"),c.addClass("hidden")})})},I.fn.shilin_field_group=function(){return this.each(function(){var e=I(this),t=e.children(".shilin-fieldset"),n=t.length?t:e,r=n.children(".shilin-cloneable-wrapper"),i=n.children(".shilin-cloneable-hidden"),o=n.children(".shilin-cloneable-max"),f=n.children(".shilin-cloneable-min"),c=r.data("title-by"),l=r.data("title-by-prefix"),d=r.data("field-id"),h=Boolean(Number(r.data("title-number"))),p=parseInt(r.data("max")),a=parseInt(r.data("min"));r.hasClass("ui-accordion")&&r.find(".ui-accordion-header-icon").remove();var u=function(e){e.find(".shilin-cloneable-title-number").each(function(e){I(this).html(I(this).closest(".shilin-cloneable-item").index()+1+".")})};r.accordion({header:"> .shilin-cloneable-item > .shilin-cloneable-title",collapsible:!0,active:!1,animate:!1,heightStyle:"content",icons:{header:"shilin-cloneable-header-icon fas fa-angle-right",activeHeader:"shilin-cloneable-header-icon fas fa-angle-down"},activate:function(e,t){var n=t.newPanel,i=t.newHeader;if(n.length&&!n.data("opened")){var a=i.find(".shilin-cloneable-value"),s=[];I.each(c,function(e,t){s.push(n.find('[data-depend-id="'+t+'"]'))}),I.each(s,function(e,t){t.on("change keyup shilin.keyup",function(){var i=[];I.each(s,function(e,t){var n=t.val();n&&i.push(n)}),i.length&&a.text(i.join(l))}).trigger("shilin.keyup")}),n.shilin_reload_script(),n.data("opened",!0),n.data("retry",!1)}else n.data("retry")&&(n.shilin_reload_script_retry(),n.data("retry",!1))}}),r.sortable({axis:"y",handle:".shilin-cloneable-title,.shilin-cloneable-sort",helper:"original",cursor:"move",placeholder:"widget-placeholder",start:function(e,t){r.accordion({active:!1}),r.sortable("refreshPositions"),t.item.children(".shilin-cloneable-content").data("retry",!0)},update:function(e,t){T.helper.name_nested_replace(r.children(".shilin-cloneable-item"),d),r.shilin_customizer_refresh(),h&&u(r)}}),n.children(".shilin-cloneable-add").on("click",function(e){e.preventDefault();var t=r.children(".shilin-cloneable-item").length;if(f.hide(),p&&p<t+1)o.show();else{var n=i.shilin_clone(!0);n.removeClass("shilin-cloneable-hidden"),n.find(':input[name!="_pseudo"]').each(function(){this.name=this.name.replace("___","").replace(d+"[0]",d+"["+t+"]")}),r.append(n),r.accordion("refresh"),r.accordion({active:t}),r.shilin_customizer_refresh(),r.shilin_customizer_listen({closest:!0}),h&&u(r)}});var s=function(e){e.preventDefault();var t=r.children(".shilin-cloneable-item").length;if(f.hide(),p&&p<t+1)o.show();else{var n=I(this).parent().parent(),i=n.children(".shilin-cloneable-helper").shilin_clone(!0),a=n.children(".shilin-cloneable-title").shilin_clone(),s=n.children(".shilin-cloneable-content").shilin_clone(),c=I('<div class="shilin-cloneable-item" />');c.append(i),c.append(a),c.append(s),r.children().eq(n.index()).after(c),T.helper.name_nested_replace(r.children(".shilin-cloneable-item"),d),r.accordion("refresh"),r.shilin_customizer_refresh(),r.shilin_customizer_listen({closest:!0}),h&&u(r)}};r.children(".shilin-cloneable-item").children(".shilin-cloneable-helper").on("click",".shilin-cloneable-clone",s),n.children(".shilin-cloneable-hidden").children(".shilin-cloneable-helper").on("click",".shilin-cloneable-clone",s);var v=function(e){e.preventDefault();var t=r.children(".shilin-cloneable-item").length;o.hide(),f.hide(),a&&t-1<a?f.show():(I(this).closest(".shilin-cloneable-item").remove(),T.helper.name_nested_replace(r.children(".shilin-cloneable-item"),d),r.shilin_customizer_refresh(),h&&u(r))};r.children(".shilin-cloneable-item").children(".shilin-cloneable-helper").on("click",".shilin-cloneable-remove",v),n.children(".shilin-cloneable-hidden").children(".shilin-cloneable-helper").on("click",".shilin-cloneable-remove",v)})},I.fn.shilin_field_icon=function(){return this.each(function(){var n=I(this);n.on("click",".shilin-icon-add",function(e){e.preventDefault();var t=I(this),i=I("#shilin-modal-icon");i.removeClass("hidden"),T.vars.$icon_target=n,T.vars.icon_modal_loaded||(i.find(".shilin-modal-loading").show(),_.wp.ajax.post("shilin-get-icons",{nonce:t.data("nonce")}).done(function(e){i.find(".shilin-modal-loading").hide(),T.vars.icon_modal_loaded=!0;var n=i.find(".shilin-modal-load").html(e.content);n.on("click","i",function(e){e.preventDefault();var t=I(this).attr("title");T.vars.$icon_target.find(".shilin-icon-preview i").removeAttr("class").addClass(t),T.vars.$icon_target.find(".shilin-icon-preview").removeClass("hidden"),T.vars.$icon_target.find(".shilin-icon-remove").removeClass("hidden"),T.vars.$icon_target.find("input").val(t).trigger("change"),i.addClass("hidden")}),i.on("change keyup",".shilin-icon-search",function(){var t=I(this).val();n.find("i").each(function(){var e=I(this);e.attr("title").search(new RegExp(t,"i"))<0?e.hide():e.show()})}),i.on("click",".shilin-modal-close, .shilin-modal-overlay",function(){i.addClass("hidden")})}).fail(function(e){i.find(".shilin-modal-loading").hide(),i.find(".shilin-modal-load").html(e.error),i.on("click",function(){i.addClass("hidden")})}))}),n.on("click",".shilin-icon-remove",function(e){e.preventDefault(),n.find(".shilin-icon-preview").addClass("hidden"),n.find("input").val("").trigger("change"),I(this).addClass("hidden")})})},I.fn.shilin_field_map=function(){return this.each(function(){if("undefined"!=typeof L){var e=I(this),t=e.find(".shilin--map-osm"),n=e.find(".shilin--map-search input"),i=e.find(".shilin--latitude"),a=e.find(".shilin--longitude"),s=e.find(".shilin--zoom"),c=t.data("map"),r=L.map(t.get(0),c);L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}).addTo(r);var o=L.marker(c.center,{draggable:!0}).addTo(r),f=function(e){i.val(e.lat),a.val(e.lng),s.val(r.getZoom())};r.on("click",function(e){o.setLatLng(e.latlng),f(e.latlng)}),r.on("zoom",function(){f(o.getLatLng())}),o.on("drag",function(){f(o.getLatLng())}),n.length||(n=I('[data-depend-id="'+e.find(".shilin--address-field").data("address-field")+'"]'));var l={};n.autocomplete({source:function(e,n){var i=e.term;i in l?n(l[i]):I.get("https://nominatim.openstreetmap.org/search",{format:"json",q:i},function(e){var t;t=e.length?e.map(function(e){return{value:e.display_name,label:e.display_name,lat:e.lat,lon:e.lon}},"json"):[{value:"no-data",label:"No Results."}],l[i]=t,n(t)})},select:function(e,t){if("no-data"===t.item.value)return!1;var n=L.latLng(t.item.lat,t.item.lon);r.panTo(n),o.setLatLng(n),f(n)},create:function(e,t){I(this).autocomplete("widget").addClass("shilin-map-ui-autocomplate")}});var d=function(){var e=L.latLng(i.val(),a.val());r.panTo(e),o.setLatLng(e)};i.on("change",d),a.on("change",d)}})},I.fn.shilin_field_link=function(){return this.each(function(){var a=I(this),e=a.find(".shilin--link"),s=a.find(".shilin--add"),c=a.find(".shilin--edit"),r=a.find(".shilin--remove"),o=a.find(".shilin--result"),t=T.helper.uid("shilin-wplink-textarea-");s.on("click",function(e){e.preventDefault(),_.wpLink.open(t)}),c.on("click",function(e){e.preventDefault(),s.trigger("click"),I("#wp-link-url").val(a.find(".shilin--url").val()),I("#wp-link-text").val(a.find(".shilin--text").val()),I("#wp-link-target").prop("checked","_blank"===a.find(".shilin--target").val())}),r.on("click",function(e){e.preventDefault(),a.find(".shilin--url").val("").trigger("change"),a.find(".shilin--text").val(""),a.find(".shilin--target").val(""),s.removeClass("hidden"),c.addClass("hidden"),r.addClass("hidden"),o.parent().addClass("hidden")}),e.attr("id",t).on("change",function(){var e=_.wpLink.getAttrs(),t=e.href,n=I("#wp-link-text").val(),i=e.target?e.target:"";a.find(".shilin--url").val(t).trigger("change"),a.find(".shilin--text").val(n),a.find(".shilin--target").val(i),o.html('{url:"'+t+'", text:"'+n+'", target:"'+i+'"}'),s.addClass("hidden"),c.removeClass("hidden"),r.removeClass("hidden"),o.parent().removeClass("hidden")})})},I.fn.shilin_field_media=function(){return this.each(function(){var i,a=I(this),s=a.find(".shilin--button"),c=a.find(".shilin--remove"),r=s.data("library")&&s.data("library").split(",")||"",o=!!a.hasClass("shilin-assign-field-background")&&a.closest(".shilin-field-background").find(".shilin--auto-attributes");s.on("click",function(e){e.preventDefault(),void 0!==_.wp&&_.wp.media&&_.wp.media.gallery&&(i||(i=_.wp.media({library:{type:r}})).on("select",function(){var e,t=i.state().get("selection").first().attributes,n=s.data("preview-size")||"thumbnail";r.length&&-1===r.indexOf(t.subtype)&&-1===r.indexOf(t.type)||(a.find(".shilin--id").val(t.id),a.find(".shilin--width").val(t.width),a.find(".shilin--height").val(t.height),a.find(".shilin--alt").val(t.alt),a.find(".shilin--title").val(t.title),a.find(".shilin--description").val(t.description),e=void 0!==t.sizes&&void 0!==t.sizes.thumbnail&&"thumbnail"===n?t.sizes.thumbnail.url:void 0!==t.sizes&&void 0!==t.sizes.full?t.sizes.full.url:"image"===t.type?t.url:t.icon,o&&o.removeClass("shilin--attributes-hidden"),c.removeClass("hidden"),a.find(".shilin--preview").removeClass("hidden"),a.find(".shilin--src").attr("src",e),a.find(".shilin--thumbnail").val(e),a.find(".shilin--url").val(t.url).trigger("change"))}),i.open())}),c.on("click",function(e){e.preventDefault(),o&&o.addClass("shilin--attributes-hidden"),c.addClass("hidden"),a.find("input").val(""),a.find(".shilin--preview").addClass("hidden"),a.find(".shilin--url").trigger("change")})})},I.fn.shilin_field_repeater=function(){return this.each(function(){var e=I(this),t=e.children(".shilin-fieldset"),n=t.length?t:e,c=n.children(".shilin-repeater-wrapper"),i=n.children(".shilin-repeater-hidden"),r=n.children(".shilin-repeater-max"),o=n.children(".shilin-repeater-min"),f=c.data("field-id"),l=parseInt(c.data("max")),a=parseInt(c.data("min"));c.children(".shilin-repeater-item").children(".shilin-repeater-content").shilin_reload_script(),c.sortable({axis:"y",handle:".shilin-repeater-sort",helper:"original",cursor:"move",placeholder:"widget-placeholder",update:function(e,t){T.helper.name_nested_replace(c.children(".shilin-repeater-item"),f),c.shilin_customizer_refresh(),t.item.shilin_reload_script_retry()}}),n.children(".shilin-repeater-add").on("click",function(e){e.preventDefault();var t=c.children(".shilin-repeater-item").length;if(o.hide(),l&&l<t+1)r.show();else{var n=i.shilin_clone(!0);n.removeClass("shilin-repeater-hidden"),n.find(':input[name!="_pseudo"]').each(function(){this.name=this.name.replace("___","").replace(f+"[0]",f+"["+t+"]")}),c.append(n),n.children(".shilin-repeater-content").shilin_reload_script(),c.shilin_customizer_refresh(),c.shilin_customizer_listen({closest:!0})}});var s=function(e){e.preventDefault();var t=c.children(".shilin-repeater-item").length;if(o.hide(),l&&l<t+1)r.show();else{var n=I(this).parent().parent().parent(),i=n.children(".shilin-repeater-content").shilin_clone(),a=n.children(".shilin-repeater-helper").shilin_clone(!0),s=I('<div class="shilin-repeater-item" />');s.append(i),s.append(a),c.children().eq(n.index()).after(s),s.children(".shilin-repeater-content").shilin_reload_script(),T.helper.name_nested_replace(c.children(".shilin-repeater-item"),f),c.shilin_customizer_refresh(),c.shilin_customizer_listen({closest:!0})}};c.children(".shilin-repeater-item").children(".shilin-repeater-helper").on("click",".shilin-repeater-clone",s),n.children(".shilin-repeater-hidden").children(".shilin-repeater-helper").on("click",".shilin-repeater-clone",s);var d=function(e){e.preventDefault();var t=c.children(".shilin-repeater-item").length;r.hide(),o.hide(),a&&t-1<a?o.show():(I(this).closest(".shilin-repeater-item").remove(),T.helper.name_nested_replace(c.children(".shilin-repeater-item"),f),c.shilin_customizer_refresh())};c.children(".shilin-repeater-item").children(".shilin-repeater-helper").on("click",".shilin-repeater-remove",d),n.children(".shilin-repeater-hidden").children(".shilin-repeater-helper").on("click",".shilin-repeater-remove",d)})},I.fn.shilin_field_slider=function(){return this.each(function(){var e=I(this),n=e.find("input"),t=e.find(".shilin-slider-ui"),i=n.data(),a=n.val()||0;t.hasClass("ui-slider")&&t.empty(),t.slider({range:"min",value:a,min:i.min||0,max:i.max||100,step:i.step||1,slide:function(e,t){n.val(t.value).trigger("change")}}),n.on("keyup",function(){t.slider("value",n.val())})})},I.fn.shilin_field_sortable=function(){return this.each(function(){var n=I(this).find(".shilin-sortable");n.sortable({axis:"y",helper:"original",cursor:"move",placeholder:"widget-placeholder",update:function(e,t){n.shilin_customizer_refresh()}}),n.find(".shilin-sortable-content").shilin_reload_script()})},I.fn.shilin_field_sorter=function(){return this.each(function(){var i=I(this),e=i.find(".shilin-enabled"),t=i.find(".shilin-disabled"),n=!!t.length&&t;e.sortable({connectWith:n,placeholder:"ui-sortable-placeholder",update:function(e,t){var n=t.item.find("input");t.item.parent().hasClass("shilin-enabled")?n.attr("name",n.attr("name").replace("disabled","enabled")):n.attr("name",n.attr("name").replace("enabled","disabled")),i.shilin_customizer_refresh()}}),n&&n.sortable({connectWith:e,placeholder:"ui-sortable-placeholder",update:function(e,t){i.shilin_customizer_refresh()}})})},I.fn.shilin_field_spinner=function(){return this.each(function(){var e=I(this),n=e.find("input"),t=e.find(".ui-button"),i=n.data();t.length&&t.remove(),n.spinner({min:i.min||0,max:i.max||100,step:i.step||1,create:function(e,t){i.unit&&n.after('<span class="ui-button shilin--unit">'+i.unit+"</span>")},spin:function(e,t){n.val(t.value).trigger("change")}})})},I.fn.shilin_field_switcher=function(){return this.each(function(){var n=I(this).find(".shilin--switcher");n.on("click",function(){var e=0,t=n.find("input");n.hasClass("shilin--active")?n.removeClass("shilin--active"):(e=1,n.addClass("shilin--active")),t.val(e).trigger("change")})})},I.fn.shilin_field_tabbed=function(){return this.each(function(){var e=I(this),t=e.find(".shilin-tabbed-nav a"),a=e.find(".shilin-tabbed-content");a.eq(0).shilin_reload_script(),t.on("click",function(e){e.preventDefault();var t=I(this),n=t.index(),i=a.eq(n);t.addClass("shilin-tabbed-active").siblings().removeClass("shilin-tabbed-active"),i.shilin_reload_script(),i.removeClass("hidden").siblings().addClass("hidden")})})},I.fn.shilin_field_typography=function(){return this.each(function(){var j=this,L=I(this),i=[],A=shilin_typography_json.webfonts,t=shilin_typography_json.googlestyles,q=shilin_typography_json.defaultstyles;j.sanitize_subset=function(e){return e=(e=e.replace("-ext"," Extended")).charAt(0).toUpperCase()+e.slice(1)},j.sanitize_style=function(e){return t[e]?t[e]:e},j.load_google_font=function(e,t,n){e&&"object"==typeof WebFont&&(t=t?t.replace("normal",""):"",n=n?n.replace("normal",""):"",(t||n)&&(e=e+":"+t+n),-1===i.indexOf(e)&&WebFont.load({google:{families:[e]}}),i.push(e))},j.append_select_options=function(e,t,a,s,c){e.find("option").not(":first").remove();var r="";I.each(t,function(e,t){var n,i=t;n=c?a&&-1!==a.indexOf(t)?" selected":"":a&&a===t?" selected":"","subset"===s?i=j.sanitize_subset(t):"style"===s&&(i=j.sanitize_style(t)),r+='<option value="'+t+'"'+n+">"+i+"</option>"}),e.append(r).trigger("shilin.change").trigger("chosen:updated")},j.init=function(){var l=[],e=L.find(".shilin--typography"),d=L.find(".shilin--type"),h=L.find(".shilin--block-font-style"),v=e.data("unit"),g=e.data("line-height-unit"),t=e.data("exclude")?e.data("exclude").split(","):[];L.find(".shilin--chosen").length&&L.find("select").each(function(){var e=I(this),t=e.parent().find(".chosen-container");t.length&&t.remove(),e.chosen({allow_single_deselect:!0,disable_search_threshold:15,width:"100%"})});var m=L.find(".shilin--font-family"),i=m.val();m.find("option").not(":first-child").remove();var a="";I.each(A,function(n,e){t&&-1!==t.indexOf(n)||(a+='<optgroup label="'+e.label+'">',I.each(e.fonts,function(e,t){a+='<option value="'+(t="object"==typeof t?e:t)+'" data-type="'+n+'"'+(t===i?" selected":"")+">"+t+"</option>"}),a+="</optgroup>")}),m.append(a).trigger("chosen:updated");var p=L.find(".shilin--block-font-style");if(p.length){var u=L.find(".shilin--font-style-select"),_=u.val()?u.val().replace(/normal/g,""):"";u.on("change shilin.change",function(e){var t=u.val();!t&&l&&-1===l.indexOf("normal")&&(t=l[0]);var n=t&&"italic"!==t&&"normal"===t?"normal":"",i=t&&"italic"!==t&&"normal"!==t?t.replace("italic",""):n,a=t&&"italic"===t.substr(-6)?"italic":"";L.find(".shilin--font-weight").val(i),L.find(".shilin--font-style").val(a)});var b=L.find(".shilin--block-extra-styles");if(b.length)var y=L.find(".shilin--extra-styles"),w=y.val()}var C=L.find(".shilin--block-subset");if(C.length)var k=L.find(".shilin--subset"),x=k.val(),z=k.data("multiple")||!1;var D=L.find(".shilin--block-backup-font-family");m.on("change shilin.change",function(e){C.length&&C.addClass("hidden"),b.length&&b.addClass("hidden"),D.length&&D.addClass("hidden");var t=m.find(":selected"),n=t.val(),i=t.data("type");if(i&&n){if("google"!==i&&"custom"!==i||!D.length||D.removeClass("hidden"),p.length){var a=q;"google"===i&&A[i].fonts[n][0]?a=A[i].fonts[n][0]:"custom"===i&&A[i].fonts[n]&&(a=A[i].fonts[n]);var s=-1!==(l=a).indexOf("normal")?"normal":a[0],c=_&&-1!==a.indexOf(_)?_:s;j.append_select_options(u,a,c,"style"),_=!1,p.removeClass("hidden"),"google"===i&&b.length&&1<a.length&&(j.append_select_options(y,a,w,"style",!0),w=!1,b.removeClass("hidden"))}if("google"===i&&C.length&&A[i].fonts[n][1]){var r=A[i].fonts[n][1],o=r.length<2&&"latin"!==r[0]?r[0]:"",f=x&&-1!==r.indexOf(x)?x:o;f=z&&x?x:f,j.append_select_options(k,r,f,"subset",z),x=!1,C.removeClass("hidden")}}else h.find(":input").val(""),C.length&&(k.find("option").not(":first-child").remove(),k.trigger("chosen:updated")),p.length&&(u.find("option").not(":first-child").remove(),u.trigger("chosen:updated"));d.val(i)}).trigger("shilin.change");var O=L.find(".shilin--block-preview");if(O.length){var S=L.find(".shilin--preview");L.on("change",T.helper.debounce(function(e){O.removeClass("hidden");var t=m.val(),n=L.find(".shilin--font-weight").val(),i=L.find(".shilin--font-style").val(),a=L.find(".shilin--font-size").val(),s=L.find(".shilin--font-variant").val(),c=L.find(".shilin--line-height").val(),r=L.find(".shilin--text-align").val(),o=L.find(".shilin--text-transform").val(),f=L.find(".shilin--text-decoration").val(),l=L.find(".shilin--color").val(),d=L.find(".shilin--word-spacing").val(),h=L.find(".shilin--letter-spacing").val(),p=L.find(".shilin--custom-style").val();"google"===L.find(".shilin--type").val()&&j.load_google_font(t,n,i);var u={};t&&(u.fontFamily=t),n&&(u.fontWeight=n),i&&(u.fontStyle=i),s&&(u.fontVariant=s),a&&(u.fontSize=a+v),c&&(u.lineHeight=c+g),h&&(u.letterSpacing=h+v),d&&(u.wordSpacing=d+v),r&&(u.textAlign=r),o&&(u.textTransform=o),f&&(u.textDecoration=f),l&&(u.color=l),S.removeAttr("style"),p&&S.attr("style",p),S.css(u)},100)),O.on("click",function(){S.toggleClass("shilin--black-background");var e=O.find(".shilin--toggle");e.hasClass("fa-toggle-off")?e.removeClass("fa-toggle-off").addClass("fa-toggle-on"):e.removeClass("fa-toggle-on").addClass("fa-toggle-off")}),O.hasClass("hidden")||L.trigger("change")}},j.init()})},I.fn.shilin_field_upload=function(){return this.each(function(){var t,e=I(this),n=e.find("input"),i=e.find(".shilin--button"),a=e.find(".shilin--remove"),s=e.find(".shilin--preview"),c=e.find(".shilin--src"),r=i.data("library")&&i.data("library").split(",")||"";i.on("click",function(e){e.preventDefault(),void 0!==_.wp&&_.wp.media&&_.wp.media.gallery&&(t||(t=_.wp.media({library:{type:r}})).on("select",function(){var e=t.state().get("selection").first().attributes;r.length&&-1===r.indexOf(e.subtype)&&-1===r.indexOf(e.type)||n.val(e.url).trigger("change")}),t.open())}),a.on("click",function(e){e.preventDefault(),n.val("").trigger("change")}),n.on("change",function(e){var t=n.val();t?a.removeClass("hidden"):a.addClass("hidden"),s.length&&(-1!==I.inArray(t.split(".").pop().toLowerCase(),["jpg","jpeg","gif","png","svg","webp"])?(s.removeClass("hidden"),c.attr("src",t)):s.addClass("hidden"))})})},I.fn.shilin_field_wp_editor=function(){return this.each(function(){if(void 0!==_.wp.editor&&void 0!==_.tinyMCEPreInit&&void 0!==_.tinyMCEPreInit.mceInit.shilin_wp_editor){var e=I(this),t=e.find(".shilin-wp-editor"),n=e.find("textarea");(e.find(".wp-editor-wrap").length||e.find(".mce-container").length)&&(t.empty(),t.append(n),n.css("display",""));var i=T.helper.uid("shilin-editor-");n.attr("id",i);var a={tinymce:_.tinyMCEPreInit.mceInit.shilin_wp_editor,quicktags:_.tinyMCEPreInit.qtInit.shilin_wp_editor},s=t.data("editor-settings"),c=wp.oldEditor?wp.oldEditor:wp.editor;c&&c.hasOwnProperty("autop")&&(wp.editor.autop=c.autop,wp.editor.removep=c.removep,wp.editor.initialize=c.initialize);a.tinymce=I.extend({},a.tinymce,{selector:"#"+i,setup:function(t){t.on("change keyup",function(){var e=s.wpautop?t.getContent():wp.editor.removep(t.getContent());n.val(e).trigger("change")})}}),!1===s.tinymce&&(a.tinymce=!1,t.addClass("shilin-no-tinymce")),!1===s.quicktags&&(a.quicktags=!1,t.addClass("shilin-no-quicktags"));var r=setInterval(function(){e.is(":visible")&&(_.wp.editor.initialize(i,a),clearInterval(r))});if(s.media_buttons&&_.shilin_media_buttons){var o=t.find(".wp-media-buttons");if(o.length)o.find(".shilin-shortcode-button").data("editor-id",i);else{var f=I(_.shilin_media_buttons);f.find(".shilin-shortcode-button").data("editor-id",i),t.prepend(f)}}}})},I.fn.shilin_confirm=function(){return this.each(function(){I(this).on("click",function(e){var t=I(this).data("confirm")||_.shilin_vars.i18n.confirm;if(!confirm(t))return e.preventDefault(),!1;T.vars.is_confirm=!0,T.vars.form_modified=!1})})},I.fn.serializeObject=function(){var a={};return I.each(this.serializeArray(),function(e,t){var n=t.name,i=t.value;a[n]=a[n]===y?i:I.isArray(a[n])?a[n].concat(i):[a[n],i]}),a},I.fn.shilin_save=function(){return this.each(function(){var i,a=I(this),c=I(".shilin-save"),r=I(".shilin-options"),o=!1;a.on("click",function(e){if(!o){var t=a.data("save"),n=a.val();c.attr("value",t),a.hasClass("shilin-save-ajax")?(e.preventDefault(),r.addClass("shilin-saving"),c.prop("disabled",!0),_.wp.ajax.post("shilin_"+r.data("unique")+"_ajax_save",{data:I("#shilin-form").serializeJSONShilin()}).done(function(e){if(I(".shilin-error").remove(),Object.keys(e.errors).length){var s='<i class="shilin-label-error shilin-error">!</i>';I.each(e.errors,function(e,t){var n=I('[data-depend-id="'+e+'"]'),i=I('a[href="#tab='+n.closest(".shilin-section").data("section-id")+'"]'),a=i.closest(".shilin-tab-item");n.closest(".shilin-fieldset").append('<p class="shilin-error shilin-error-text">'+t+"</p>"),i.find(".shilin-error").length||i.append(s),a.find(".shilin-arrow .shilin-error").length||a.find(".shilin-arrow").append(s)})}r.removeClass("shilin-saving"),c.prop("disabled",!1).attr("value",n),o=!1,T.vars.form_modified=!1,T.vars.$form_warning.hide(),clearTimeout(i);var t=I(".shilin-form-success");t.empty().append(e.notice).fadeIn("fast",function(){i=setTimeout(function(){t.fadeOut("fast")},1e3)})}).fail(function(e){alert(e.error)})):T.vars.form_modified=!1}o=!0})})},I.fn.shilin_options=function(){return this.each(function(){var e=I(this),t=e.find(".shilin-content"),n=e.find(".shilin-form-success"),i=e.find(".shilin-form-warning"),a=e.find(".shilin-header .shilin-save");(T.vars.$form_warning=i).length&&(_.onbeforeunload=function(){return!!T.vars.form_modified||y},t.on("change keypress",":input",function(){T.vars.form_modified||(n.hide(),i.fadeIn("fast"),T.vars.form_modified=!0)})),n.hasClass("shilin-form-show")&&setTimeout(function(){n.fadeOut("fast")},1e3),I(b).keydown(function(e){if((e.ctrlKey||e.metaKey)&&83===e.which)return a.trigger("click"),e.preventDefault(),!1})})},I.fn.shilin_taxonomy=function(){return this.each(function(){var e=I(this),t=e.parents("form");if("addtag"===t.attr("id")){var n=t.find("#submit"),i=e.find(".shilin-field").shilin_clone();n.on("click",function(){t.find(".form-required").hasClass("form-invalid")||(e.data("inited",!1),e.empty(),e.html(i),i=i.shilin_clone(),e.shilin_reload_script())})}})},I.fn.shilin_shortcode=function(){var m=this;return m.shortcode_parse=function(e,n){var i="";return I.each(e,function(e,t){i+="["+(n=n||e),I.each(t,function(e,t){"content"===e?(i+="]",i+=t,i+="[/"+n):i+=m.shortcode_tags(e,t)}),i+="]"}),i},m.shortcode_tags=function(e,t){var n="";return""!==t&&("object"!=typeof t||I.isArray(t)?n+=" "+e+'="'+t.toString()+'"':I.each(t,function(e,t){switch(e){case"background-image":t=t.url?t.url:""}""!==t&&(n+=" "+e+'="'+t.toString()+'"')})),n},m.insertAtChars=function(e,t){var n=void 0!==e[0].name?e[0]:e;return n.value.length&&void 0!==n.selectionStart?(n.focus(),n.value.substring(0,n.selectionStart)+t+n.value.substring(n.selectionEnd,n.value.length)):(n.focus(),t)},m.send_to_editor=function(e,t){var n;if("undefined"!=typeof tinymce&&(n=tinymce.get(t)),n&&!n.isHidden())n.execCommand("mceInsertContent",!1,e);else{var i=I("#"+t);i.val(m.insertAtChars(i,e)).trigger("change")}},this.each(function(){var c,r,o,n,f,l,d,a,h,p=I(this),i=p.find(".shilin-modal-load"),u=(p.find(".shilin-modal-content"),p.find(".shilin-modal-insert")),s=p.find(".shilin-modal-loading"),t=p.find("select"),v=p.data("modal-id"),g=p.data("nonce");I(b).on("click",'.shilin-shortcode-button[data-modal-id="'+v+'"]',function(e){e.preventDefault(),h=I(this),c=h.data("editor-id")||!1,r=h.data("target-id")||!1,o=h.data("gutenberg-id")||!1,p.removeClass("hidden"),p.hasClass("shilin-shortcode-single")&&f===y&&t.trigger("change")}),t.on("change",function(){var e=I(this),t=e.find(":selected");n=e.val(),f=t.data("shortcode"),l=t.data("view")||"normal",d=t.data("group")||f,i.empty(),n?(s.show(),_.wp.ajax.post("shilin-get-shortcode-"+v,{shortcode_key:n,nonce:g}).done(function(e){s.hide();var t=I(e.content).appendTo(i);u.parent().removeClass("hidden"),a=t.find(".shilin--repeat-shortcode").shilin_clone(),t.shilin_reload_script(),t.find(".shilin-fields").shilin_reload_script()})):u.parent().addClass("hidden")}),u.on("click",function(e){if(e.preventDefault(),!u.prop("disabled")&&!u.attr("disabled")){var i="",t=p.find(".shilin-field:not(.shilin-depend-on)").find(":input:not(.ignore)").serializeObjectShilin();switch(l){case"contents":var n=f?t[f]:t;I.each(n,function(e,t){var n=f||e;i+="["+n+"]"+t+"[/"+n+"]"});break;case"group":i+="["+f,I.each(t[f],function(e,t){i+=m.shortcode_tags(e,t)}),i+="]",i+=m.shortcode_parse(t[d],d),i+="[/"+f+"]";break;case"repeater":i+=m.shortcode_parse(t[d],d);break;default:i+=m.shortcode_parse(t)}if(i=""===i?"["+f+"]":i,o){var a=_.shilin_gutenberg_props.attributes.hasOwnProperty("shortcode")?_.shilin_gutenberg_props.attributes.shortcode:"";_.shilin_gutenberg_props.setAttributes({shortcode:a+i})}else if(c)m.send_to_editor(i,c);else{var s=r?I(r):h.parent().find("textarea");s.val(m.insertAtChars(s,i)).trigger("change")}p.addClass("hidden")}}),p.on("click",".shilin--repeat-button",function(e){e.preventDefault();var t=p.find(".shilin--repeatable"),n=a.shilin_clone(),i=n.find(".shilin-repeat-remove");n.appendTo(t);n.find(".shilin-fields").shilin_reload_script(),T.helper.name_nested_replace(p.find(".shilin--repeat-shortcode"),d),i.on("click",function(){n.remove(),T.helper.name_nested_replace(p.find(".shilin--repeat-shortcode"),d)})}),p.on("click",".shilin-modal-close, .shilin-modal-overlay",function(){p.addClass("hidden")})})},"function"==typeof Color&&(Color.prototype.toString=function(){if(this._alpha<1)return this.toCSS("rgba",this._alpha).replace(/\s+/g,"");var e=parseInt(this._color,10).toString(16);if(this.error)return"";if(e.length<6)for(var t=6-e.length-1;0<=t;t--)e="0"+e;return"#"+e}),T.funcs.parse_color=function(e){var t=e.replace(/\s+/g,""),n=-1!==t.indexOf("rgba")?parseFloat(100*t.replace(/^.*,(.+)\)/,"$1")):100;return{value:t,transparent:n,rgba:n<100}},I.fn.shilin_color=function(){return this.each(function(){var c,r=I(this),o=T.funcs.parse_color(r.val()),e=!_.shilin_vars.color_palette.length||_.shilin_vars.color_palette;r.hasClass("wp-color-picker")&&r.closest(".wp-picker-container").after(r).remove(),r.wpColorPicker({palettes:e,change:function(e,t){var n=t.color.toString();c.removeClass("shilin--transparent-active"),c.find(".shilin--transparent-offset").css("background-color",n),r.val(n).trigger("change")},create:function(){c=r.closest(".wp-picker-container");var i=r.data("a8cIris"),e=I('<div class="shilin--transparent-wrap"><div class="shilin--transparent-slider"></div><div class="shilin--transparent-offset"></div><div class="shilin--transparent-text"></div><div class="shilin--transparent-button">transparent <i class="fas fa-toggle-off"></i></div></div>').appendTo(c.find(".wp-picker-holder")),a=e.find(".shilin--transparent-slider"),s=e.find(".shilin--transparent-text"),n=e.find(".shilin--transparent-offset"),t=e.find(".shilin--transparent-button");"transparent"===r.val()&&c.addClass("shilin--transparent-active"),t.on("click",function(){"transparent"!==r.val()?(r.val("transparent").trigger("change").removeClass("iris-error"),c.addClass("shilin--transparent-active")):(r.val(i._color.toString()).trigger("change"),c.removeClass("shilin--transparent-active"))}),a.slider({value:o.transparent,step:1,min:0,max:100,slide:function(e,t){var n=parseFloat(t.value/100);i._color._alpha=n,r.wpColorPicker("color",i._color.toString()),s.text(1===n||0===n?"":n)},create:function(){var e=parseFloat(o.transparent/100),t=e<1?e:"";s.text(t),n.css("background-color",o.value),c.on("click",".wp-picker-clear",function(){i._color._alpha=1,s.text(""),a.slider("option","value",100),c.removeClass("shilin--transparent-active"),r.trigger("change")}),c.on("click",".wp-picker-default",function(){var e=T.funcs.parse_color(r.data("default-color")),t=parseFloat(e.transparent/100),n=t<1?t:"";i._color._alpha=t,s.text(n),a.slider("option","value",e.transparent),"transparent"===e.value&&(r.removeClass("iris-error"),c.addClass("shilin--transparent-active"))})}})}})})},I.fn.shilin_chosen=function(){return this.each(function(){var s=I(this),e=s.parent().find(".chosen-container"),t=s.hasClass("shilin-chosen-sortable")||!1,n=s.hasClass("shilin-chosen-ajax")||!1,i=s.attr("multiple")||!1,a=i?"100%":"auto",c=I.extend({allow_single_deselect:!0,disable_search_threshold:10,width:a,no_results_text:_.shilin_vars.i18n.no_results_text},s.data("chosen-settings"));if(e.length&&e.remove(),n){var r=I.extend({data:{type:"post",nonce:""},allow_single_deselect:!0,disable_search_threshold:-1,width:"100%",min_length:3,type_delay:500,typing_text:_.shilin_vars.i18n.typing_text,searching_text:_.shilin_vars.i18n.searching_text,no_results_text:_.shilin_vars.i18n.no_results_text},s.data("chosen-settings"));s.ShilinAjaxChosen(r)}else s.chosen(c);if(i){var o=s.parent().find(".shilin-hide-select"),f=o.val()||[];s.on("change",function(e,t){t&&t.selected?o.append('<option value="'+t.selected+'" selected="selected">'+t.selected+"</option>"):t&&t.deselected&&o.find('option[value="'+t.deselected+'"]').remove(),_.wp.customize!==y&&0===o.children().length&&o.data("customize-setting-link")&&_.wp.customize.control(o.data("customize-setting-link")).setting.set(""),o.trigger("change")}),s.ShilinChosenOrder(f,!0)}if(t){var l=s.parent().find(".chosen-container").find(".chosen-choices");l.bind("mousedown",function(e){I(e.target).is("span")&&e.stopPropagation()}),l.sortable({items:"li:not(.search-field)",helper:"orginal",cursor:"move",placeholder:"search-choice-placeholder",start:function(e,t){t.placeholder.width(t.item.innerWidth()),t.placeholder.height(t.item.innerHeight())},update:function(e,t){var i="",a=s.data("chosen"),n=s.parent().find(".shilin-hide-select");l.find(".search-choice-close").each(function(){var n=I(this).data("option-array-index");I.each(a.results_data,function(e,t){t.array_index===n&&(i+='<option value="'+t.value+'" selected>'+t.value+"</option>")})}),n.children().remove(),n.append(i),n.trigger("change")}})}})},I.fn.shilin_checkbox=function(){return this.each(function(){var e=I(this),t=e.find(".shilin--input"),n=e.find(".shilin--checkbox");n.on("click",function(){t.val(Number(n.prop("checked"))).trigger("change")})})},I.fn.shilin_siblings=function(){return this.each(function(){var t=I(this),e=t.find(".shilin--sibling"),n=t.data("multiple")||!1;e.on("click",function(){var e=I(this);n?e.hasClass("shilin--active")?(e.removeClass("shilin--active"),e.find("input").prop("checked",!1).trigger("change")):(e.addClass("shilin--active"),e.find("input").prop("checked",!0).trigger("change")):(t.find("input").prop("checked",!1),e.find("input").prop("checked",!0).trigger("change"),e.addClass("shilin--active").siblings().removeClass("shilin--active"))})})},I.fn.shilin_help=function(){return this.each(function(){var e,t,n=I(this);n.on({mouseenter:function(){e=I('<div class="shilin-tooltip"></div>').html(n.find(".shilin-help-text").html()).appendTo("body"),t=T.vars.is_rtl?n.offset().left+24:n.offset().left-e.outerWidth(),e.css({top:n.offset().top-(e.outerHeight()/2-14),left:t})},mouseleave:function(){e!==y&&e.remove()}})})},I.fn.shilin_customizer_refresh=function(){return this.each(function(){var e=I(this),t=e.closest(".shilin-customize-complex");if(t.length){var n=t.data("unique-id");if(n===y)return;var i=t.find(":input"),a=t.data("option-id"),s=i.serializeObjectShilin(),c=!I.isEmptyObject(s)&&s[n]&&s[n][a]?s[n][a]:"",r=_.wp.customize.control(n+"["+a+"]");r.setting._value=null,r.setting.set(c)}else e.find(":input").first().trigger("change");I(b).trigger("shilin-customizer-refresh",e)})},I.fn.shilin_customizer_listen=function(e){var t=I.extend({closest:!1},e);return this.each(function(){if(_.wp.customize!==y){var n=t.closest?I(this).closest(".shilin-customize-complex"):I(this),e=n.find(":input"),i=n.data("unique-id"),a=n.data("option-id");i!==y&&e.on("change keyup shilin.change",function(){var e=n.find(":input").serializeObjectShilin(),t=!I.isEmptyObject(e)&&e[i]&&e[i][a]?e[i][a]:"";_.wp.customize.control(i+"["+a+"]").setting.set(t)})}})},I(b).on("expanded",".control-section",function(){var e=I(this);if(e.hasClass("open")&&!e.data("inited")){var t=e.find(".shilin-customize-field"),n=e.find(".shilin-customize-complex");t.length&&(e.shilin_dependency(),t.shilin_reload_script({dependency:!1}),n.shilin_customizer_listen()),e.data("inited",!0)}}),T.vars.$window.on("resize shilin.resize",T.helper.debounce(function(e){(-1<navigator.userAgent.indexOf("AppleWebKit/")?T.vars.$window.width():_.innerWidth)<=782&&!T.vars.onloaded&&(I(".shilin-section").shilin_reload_script(),T.vars.onloaded=!0)},200)).trigger("shilin.resize"),I.fn.shilin_widgets=function(){return this.each(function(){I(b).on("widget-added widget-updated",function(e,t){var n=t.find(".shilin-fields");n.length&&n.shilin_reload_script()}),I(b).on("click",".widget-top",function(e){var t=I(this).parent().find(".shilin-fields");t.length&&t.shilin_reload_script()}),I(".widgets-sortables, .control-section-sidebar").on("sortstop",function(e,t){t.item.find(".shilin-fields").shilin_reload_script_retry()})})},I.fn.shilin_nav_menu=function(){return this.each(function(){var e=I(this);e.on("click","a.item-edit",function(){I(this).closest("li.menu-item").find(".shilin-fields").shilin_reload_script()}),e.on("sortstop",function(e,t){t.item.find(".shilin-fields").shilin_reload_script_retry()})})},I.fn.shilin_reload_script_retry=function(){return this.each(function(){var e=I(this);e.data("inited")&&e.children(".shilin-field-wp_editor").shilin_field_wp_editor()})},I.fn.shilin_reload_script=function(e){var t=I.extend({dependency:!0},e);return this.each(function(){var e=I(this);e.data("inited")||(e.children(".shilin-field-accordion").shilin_field_accordion(),e.children(".shilin-field-backup").shilin_field_backup(),e.children(".shilin-field-background").shilin_field_background(),e.children(".shilin-field-code_editor").shilin_field_code_editor(),e.children(".shilin-field-date").shilin_field_date(),e.children(".shilin-field-datetime").shilin_field_datetime(),e.children(".shilin-field-fieldset").shilin_field_fieldset(),e.children(".shilin-field-gallery").shilin_field_gallery(),e.children(".shilin-field-group").shilin_field_group(),e.children(".shilin-field-icon").shilin_field_icon(),e.children(".shilin-field-link").shilin_field_link(),e.children(".shilin-field-media").shilin_field_media(),e.children(".shilin-field-map").shilin_field_map(),e.children(".shilin-field-repeater").shilin_field_repeater(),e.children(".shilin-field-slider").shilin_field_slider(),e.children(".shilin-field-sortable").shilin_field_sortable(),e.children(".shilin-field-sorter").shilin_field_sorter(),e.children(".shilin-field-spinner").shilin_field_spinner(),e.children(".shilin-field-switcher").shilin_field_switcher(),e.children(".shilin-field-tabbed").shilin_field_tabbed(),e.children(".shilin-field-typography").shilin_field_typography(),e.children(".shilin-field-upload").shilin_field_upload(),e.children(".shilin-field-wp_editor").shilin_field_wp_editor(),e.children(".shilin-field-border").find(".shilin-color").shilin_color(),e.children(".shilin-field-background").find(".shilin-color").shilin_color(),e.children(".shilin-field-color").find(".shilin-color").shilin_color(),e.children(".shilin-field-color_group").find(".shilin-color").shilin_color(),e.children(".shilin-field-link_color").find(".shilin-color").shilin_color(),e.children(".shilin-field-typography").find(".shilin-color").shilin_color(),e.children(".shilin-field-select").find(".shilin-chosen").shilin_chosen(),e.children(".shilin-field-checkbox").find(".shilin-checkbox").shilin_checkbox(),e.children(".shilin-field-button_set").find(".shilin-siblings").shilin_siblings(),e.children(".shilin-field-image_select").find(".shilin-siblings").shilin_siblings(),e.children(".shilin-field-palette").find(".shilin-siblings").shilin_siblings(),e.children(".shilin-field").find(".shilin-help").shilin_help(),t.dependency&&e.shilin_dependency(),e.data("inited",!0),I(b).trigger("shilin-reload-script",e))})},I(b).ready(function(){I(".shilin-save").shilin_save(),I(".shilin-options").shilin_options(),I(".shilin-sticky-header").shilin_sticky(),I(".shilin-nav-options").shilin_nav_options(),I(".shilin-nav-metabox").shilin_nav_metabox(),I(".shilin-taxonomy").shilin_taxonomy(),I(".shilin-page-templates").shilin_page_templates(),I(".shilin-post-formats").shilin_post_formats(),I(".shilin-shortcode").shilin_shortcode(),I(".shilin-search").shilin_search(),I(".shilin-confirm").shilin_confirm(),I(".shilin-expand-all").shilin_expand_all(),I(".shilin-onload").shilin_reload_script(),I("#widgets-editor").shilin_widgets(),I("#widgets-right").shilin_widgets(),I("#menu-to-edit").shilin_nav_menu()})}(jQuery,window,document);