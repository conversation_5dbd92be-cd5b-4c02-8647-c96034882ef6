<?php
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

function convertDownloadLinks($content) {
    // 移除外层的样式包装
    $content = preg_replace('/<p[^>]*style="[^"]*background-color:\s*#[^"]*"[^>]*>(.*?)<\/p>/is', '$1', $content);
    
    // 避免重复处理
    if (strpos($content, 'class="download-btn"') !== false) {
        return $content;
    }

    // 定义网盘类型映射
    $driveTypes = [
        // 国内网盘
        'lanzou[a-z]*\.com' => ['蓝奏云盘', '#06b6d4'],
        'pan\.baidu\.com' => ['百度网盘', '#3b82f6'],
        '123pan\.com' => ['123网盘', '#8b5cf6'],
        'aliyundrive\.com|alipan\.com' => ['阿里云盘', '#f59e0b'],
        'cloud\.189\.cn' => ['天翼云盘', '#0ea5e9'],
        'quark\.cn' => ['夸克网盘', '#10b981'],
        'weiyun\.com' => ['微云', '#14b8a6'],
        '115\.com' => ['115网盘', '#f43f5e'],
        'ctfile\.com' => ['城通网盘', '#ec4899'],
        'cowtransfer\.com|qingfuwu\.cn' => ['奶牛快传', '#0284c7'],
        'jianguoyun\.com' => ['坚果云', '#2563eb'],
        'yunpan\.360\.cn' => ['360云盘', '#22c55e'],
        'pan\.xunlei\.com' => ['迅雷云盘', '#0ea5e9'],
        'pan\.xiaomi\.com' => ['小米云盘', '#f97316'],
        'cloud\.139\.com' => ['和彩云', '#22c55e'],
        'pan\.uc\.cn' => ['UC网盘', '#0284c7'],
        'pan\.sina\.com\.cn' => ['新浪微盘', '#f43f5e'],
        'tmp\.link' => ['TMP.link', '#8b5cf6'],
        'pan\.bitqiu\.com' => ['比特球云盘', '#f59e0b'],
        '123wzwp\.com' => ['123网盘外链', '#dc2626'],
        'pan\.bilnn\.com' => ['比邻云盘', '#0ea5e9'],
        'feijix\.com' => ['飞机盘', '#6366f1'],
        
        // 国外网盘
        'mediafire\.com' => ['MediaFire', '#ef4444'],
        'mega\.nz' => ['MEGA', '#eab308'],
        'dropbox\.com' => ['Dropbox', '#0284c7'],
        'drive\.google\.com' => ['Google Drive', '#16a34a'],
        'onedrive\.live\.com|1drv\.ms' => ['OneDrive', '#0ea5e9'],
        'cloud\.mail\.ru' => ['Mail.ru', '#0ea5e9'],
        'yandex\.disk' => ['Yandex.Disk', '#eab308'],
        'pcloud\.com' => ['pCloud', '#0284c7'],
        'box\.com' => ['Box', '#2563eb'],
        
        // 代码仓库
        'github\.com' => ['GitHub', '#24292e', 'github'],
        'gitee\.com' => ['Gitee', '#c71d23', 'gitee']
    ];

    // 获取网盘类型信息
    function getDriveInfo($url, $driveTypes) {
        foreach ($driveTypes as $pattern => $info) {
            if (preg_match("/{$pattern}/i", $url)) {
                $icon = isset($info[2]) ? getGitIcon($info[2]) : getDownloadIcon();
                return [
                    'type' => $info[0],
                    'color' => $info[1],
                    'icon' => $icon
                ];
            }
        }
        return null;
    }

    // 获取下载图标
    function getDownloadIcon() {
        return '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>';
    }

    // 获取Git平台图标
    function getGitIcon($platform) {
        return '<path fill-rule="evenodd" clip-rule="evenodd" d="M12 2C6.477 2 2 6.477 2 12c0 4.42 2.87 8.17 6.84 ********.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 **********-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 ********** 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 **********.69.92.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0012 2z"/>';
    }

    // 获取下载次数
    function getDownloadCount($url) {
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();
        try {
            $row = $db->fetchRow(
                $db->select('count')
                   ->from('table.downloads')
                   ->where('url = ?', $url)
            );
            return $row ? $row['count'] : 0;
        } catch (Exception $e) {
            error_log($e->getMessage());
            return 0;
        }
    }

    // 更新下载次数
    function updateDownloadCount($url) {
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();
        try {
            $exists = $db->fetchRow(
                $db->select('id')
                   ->from('table.downloads')
                   ->where('url = ?', $url)
            );
            
            if ($exists) {
                $db->query($db->update('table.downloads')
                             ->rows(['count' => new Typecho_Db_Expression('count + 1'), 
                                    'last_download' => date('Y-m-d H:i:s')])
                             ->where('url = ?', $url));
            } else {
                $db->query($db->insert('table.downloads')
                             ->rows(['url' => $url, 
                                    'count' => 1, 
                                    'last_download' => date('Y-m-d H:i:s')]));
            }
        } catch (Exception $e) {
            error_log($e->getMessage());
        }
    }

    // 生成下载按钮HTML
    function generateDownloadButton($url, $info) {
        $downloadCount = getDownloadCount($url);
        $countDisplay = $downloadCount > 0 ? "<span class='download-count'>({$downloadCount})</span>" : '';
        
        return sprintf(
            '<a href="%s" class="download-btn" style="background-color: %s;" target="_blank" rel="nofollow" onclick="updateDownloadCount(\'%s\')">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    %s
                </svg>
                %s%s
            </a>
            <script>
            function updateDownloadCount(url) {
                fetch("?action=update_download_count", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                    body: "url=" + encodeURIComponent(url)
                });
            }
            </script>',
            $url ? htmlspecialchars($url) : 'javascript:;',
            $info['color'],
            htmlspecialchars($url),
            $info['icon'],
            $info['type'],
            $countDisplay
        );
    }

    // 处理特殊格式的链接
    $pattern = '/<a[^>]*?data-url="([^"]*)"[^>]*?data-cdk=""[^>]*?title="[^"]*"[^>]*?rel="nofollow">[^<]*<\/a>/i';
    $content = preg_replace_callback($pattern,
        function($matches) use ($driveTypes) {
            // 从data-url中提取实际URL
            if (preg_match('/https?:\/\/[^\s<>"\']+/i', $matches[1], $urlMatches)) {
                $url = $urlMatches[0];
                // 获取网盘信息
                foreach ($driveTypes as $pattern => $info) {
                    if (preg_match("/{$pattern}/i", $url)) {
                        return generateDownloadButton($url, [
                            'type' => $info[0],
                            'color' => $info[1],
                            'icon' => getDownloadIcon()
                        ]);
                    }
                }
            }
            return $matches[0];
        },
        $content
    );

    // 处理纯文本链接
    $content = preg_replace_callback('/(?<!href=[\'".])(https?:\/\/(?:[\w-]+\.)?(?:lanzou[a-z]*|pan\.baidu|123pan|aliyundrive|alipan|cloud\.189|quark|weiyun|115|ctfile|cowtransfer|qingfuwu|jianguoyun|yunpan\.360|pan\.xunlei|pan\.xiaomi|cloud\.139|pan\.uc|pan\.sina|wenshushu|tmp\.link|pan\.bitqiu|123wzwp|pan\.bilnn|feijix|mediafire|mega\.nz|dropbox|drive\.google|onedrive\.live|1drv\.ms|cloud\.mail\.ru|yandex\.disk|pcloud|box|github|gitee)\.(?:com|cn|nz|ms|ru)[^\s<>"\']+)/i',
        function($matches) use ($driveTypes) {
            $info = getDriveInfo($matches[1], $driveTypes);
            return $info ? generateDownloadButton($matches[1], $info) : $matches[0];
        },
        $content
    );

    // 处理已有的链接
    $content = preg_replace_callback('/<a[^>]*?href=[\'"](.*?)[\'"][^>]*?>(.*?)<\/a>/is',
        function($matches) use ($driveTypes) {
            if (strpos($matches[0], 'download-btn') !== false) {
                return $matches[0];
            }
            $info = getDriveInfo($matches[1], $driveTypes);
            return $info ? generateDownloadButton($matches[1], $info) : $matches[0];
        },
        $content
    );

    return $content;
}