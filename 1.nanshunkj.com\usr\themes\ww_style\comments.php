<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>

<?php if (!empty($this->options->siteUrl) && !$this->hidden): ?>
<div id="comments" class="comments-area">
    <h3 class="comments-title">
        <?php $this->commentsNum(
            '<i class="ri-message-2-line"></i> 暂无评论',
            '<i class="ri-message-2-line"></i> 1 条评论',
            '<i class="ri-message-2-line"></i> %d 条评论'
        ); ?>
    </h3>
    
    <?php $this->comments()->to($comments); ?>
    
    <?php if ($comments->have()): ?>
    
    <?php $comments->listComments(); ?>
    
    <div class="comments-pagination">
        <?php $comments->pageNav('&laquo;', '&raquo;', 1, '...', array('wrapTag' => 'ul', 'wrapClass' => 'page-navigator', 'itemTag' => 'li', 'textTag' => 'span', 'currentClass' => 'current', 'prevClass' => 'prev', 'nextClass' => 'next')); ?>
    </div>
    
    <?php endif; ?>
    
    <?php if($this->allow('comment')): ?>
    
    <div id="<?php $this->respondId(); ?>" class="comment-respond">
        <h3 class="comment-reply-title"><i class="ri-edit-line"></i> 发表评论</h3>
        
        <form method="post" action="<?php $this->commentUrl() ?>" id="comment-form" class="comment-form" role="form">
            <?php if($this->user->hasLogin()): ?>
            <div class="comment-logined">
                <img class="avatar" src="<?php echo getUserAvatar($this->user->mail, 40); ?>" alt="<?php $this->user->screenName(); ?>" />
                <span class="username"><?php $this->user->screenName(); ?></span>
                <a href="<?php $this->options->logoutUrl(); ?>" class="logout">[退出]</a>
            </div>
            <?php else: ?>
            <div class="comment-fields">
                <div class="row">
                    <div class="col comment-form-author">
                        <input type="text" name="author" id="author" class="form-input" placeholder="昵称 *" value="<?php $this->remember('author'); ?>" required />
                    </div>
                    <div class="col comment-form-email">
                        <input type="email" name="mail" id="mail" class="form-input" placeholder="邮箱 *" value="<?php $this->remember('mail'); ?>" <?php if ($this->options->commentsRequireMail): ?> required<?php endif; ?> />
                    </div>
                    <div class="col comment-form-url">
                        <input type="url" name="url" id="url" class="form-input" placeholder="网站" value="<?php $this->remember('url'); ?>" <?php if ($this->options->commentsRequireURL): ?> required<?php endif; ?> />
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="comment-form-textarea">
                <textarea name="text" id="textarea" class="form-input" placeholder="评论内容 *" required><?php $this->remember('text'); ?></textarea>
            </div>
            
            <div class="comment-form-submit">
                <button type="submit" class="btn-primary submit-btn"><i class="ri-send-plane-line"></i> 提交评论</button>
                <button type="button" class="btn-default cancel-comment-reply" id="cancel-comment-reply-link" style="display:none;">取消回复</button>
            </div>
            
            <?php $security = $this->widget('Widget_Security'); ?>
            <input type="hidden" name="_" value="<?php echo $security->getToken($this->request->getReferer()); ?>" />
        </form>
    </div>
    
    <?php else: ?>
    <div class="comment-closed">
        <i class="ri-lock-line"></i>
        <p>评论已关闭</p>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<style>
/* 评论列表样式 */
.comment-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.comment-body {
    position: relative;
    margin-bottom: 25px;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    transition: all 0.3s;
}

.comment-body:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.comment-author {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.comment-author .avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 10px;
}

.comment-author cite {
    font-style: normal;
    font-weight: 600;
    color: #fff;
    font-size: 16px;
}

.comment-author cite a {
    color: #fff;
    text-decoration: none;
}

.comment-meta {
    margin-left: 55px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #aaa;
}

.comment-meta a {
    color: #aaa;
}

.comment-content {
    margin-left: 55px;
    color: #eee;
    line-height: 1.5;
}

.comment-reply {
    margin-left: 55px;
    margin-top: 10px;
}

.comment-reply a {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    color: #ccc;
    font-size: 12px;
    transition: all 0.3s;
}

.comment-reply a:hover {
    background-color: #FE2C55;
    color: #fff;
}

.comment-children {
    margin-left: 55px;
    list-style: none;
}

.comment-child .comment-body {
    background-color: rgba(254, 44, 85, 0.05);
}

/* 评论分页 */
.comments-pagination {
    margin-top: 30px;
    margin-bottom: 30px;
}

/* 评论表单 */
.comment-respond {
    margin-top: 40px;
    padding: 25px;
    background-color: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
}

.comment-reply-title {
    margin-top: 0;
    font-size: 18px;
    color: #fff;
    margin-bottom: 20px;
}

.comment-form {
    position: relative;
}

.comment-fields .row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.comment-fields .col {
    flex: 1;
}

.comment-form-textarea {
    margin-bottom: 15px;
}

.comment-form-textarea textarea {
    min-height: 120px;
    resize: vertical;
}

.comment-form-submit {
    display: flex;
    justify-content: space-between;
}

.submit-btn {
    padding: 10px 25px;
}

.comment-logined {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.comment-logined .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.comment-logined .username {
    font-weight: 600;
    color: #fff;
}

.comment-logined .logout {
    margin-left: 10px;
    color: #aaa;
    font-size: 12px;
}

.comment-closed {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;
    color: #aaa;
}

.comment-closed i {
    font-size: 30px;
    margin-bottom: 10px;
}
</style> 