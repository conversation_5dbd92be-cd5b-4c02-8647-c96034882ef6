<?php ?><?php // /* *****  * @自助授权：https://sq.shilin.studio  * @自助下单：https://order.shilin.studio  * @Author: 诗林工作室  * @AuthorUri: https://shilin.studio  * @Date: 2025-03-30 20:49:54  * @LastEditTime: 2025-03-30 05:48:38  * Copyright (c) 2024 by Shilin Studio All Rights Reserved. */ - by 贝塔PHP加密|https://sg.bt58.vip ?><?php
if(!function_exists('sg_load')){$__v=phpversion();$__x=explode('.',$__v);$__v2=$__x[0].'.'.(int)$__x[1];$__u=strtolower(substr(php_uname(),0,3));$__ts=(@constant('PHP_ZTS') || @constant('ZEND_THREAD_SAFE')?'ts':'');$__f=$__f0='ixed.'.$__v2.$__ts.'.'.$__u;$__ff=$__ff0='ixed.'.$__v2.'.'.(int)$__x[2].$__ts.'.'.$__u;$__ed=@ini_get('extension_dir');$__e=$__e0=@realpath($__ed);$__dl=function_exists('dl') && function_exists('file_exists') && @ini_get('enable_dl') && !@ini_get('safe_mode');if($__dl && $__e && version_compare($__v,'5.2.5','<') && function_exists('getcwd') && function_exists('dirname')){$__d=$__d0=getcwd();if(@$__d[1]==':') {$__d=str_replace('\\','/',substr($__d,2));$__e=str_replace('\\','/',substr($__e,2));}$__e.=($__h=str_repeat('/..',substr_count($__e,'/')));$__f='/ixed/'.$__f0;$__ff='/ixed/'.$__ff0;while(!file_exists($__e.$__d.$__ff) && !file_exists($__e.$__d.$__f) && strlen($__d)>1){$__d=dirname($__d);}if(file_exists($__e.$__d.$__ff)) dl($__h.$__d.$__ff); else if(file_exists($__e.$__d.$__f)) dl($__h.$__d.$__f);}if(!function_exists('sg_load') && $__dl && $__e0){if(file_exists($__e0.'/'.$__ff0)) dl($__ff0); else if(file_exists($__e0.'/'.$__f0)) dl($__f0);}if(!function_exists('sg_load')){$__ixedurl='https://www.sourceguardian.com/loaders/download.php?php_v='.urlencode($__v).'&php_ts='.($__ts?'1':'0').'&php_is='.@constant('PHP_INT_SIZE').'&os_s='.urlencode(php_uname('s')).'&os_r='.urlencode(php_uname('r')).'&os_m='.urlencode(php_uname('m'));$__sapi=php_sapi_name();if(!$__e0) $__e0=$__ed;if(function_exists('php_ini_loaded_file')) $__ini=php_ini_loaded_file(); else $__ini='php.ini';if((substr($__sapi,0,3)=='cgi')||($__sapi=='cli')||($__sapi=='embed')){$__msg="\nPHP script '".__FILE__."' is protected by SourceGuardian and requires a SourceGuardian loader '".$__f0."' to be installed.\n\n1) Download the required loader '".$__f0."' from the SourceGuardian site: ".$__ixedurl."\n2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="\n3) Edit ".$__ini." and add 'extension=".$__f0."' directive";}}$__msg.="\n\n";}else{$__msg="<html><body>PHP script '".__FILE__."' is protected by <a href=\"https://www.sourceguardian.com/\">SourceGuardian</a> and requires a SourceGuardian loader '".$__f0."' to be installed.<br><br>1) <a href=\"".$__ixedurl."\" target=\"_blank\">Click here</a> to download the required '".$__f0."' loader from the SourceGuardian site<br>2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="<br>3) Edit ".$__ini." and add 'extension=".$__f0."' directive<br>4) Restart the web server";}}$__msg.="</body></html>";}die($__msg);exit();}}return sg_load('84188AC199EA7FA5AAQAAAAhAAAABKAAAACABAAAAAAAAAD/5npWO65fNHyqaUL47uj5JiJLCZBHizAVMDZQT1e2CfF8Z+yNkTI0n9pOI/5J3sy9hjeWQvQDuoHAHZ11HggkYoEC39qA1DOhF1+l9jOifmVrpiYsnYMmoiBnIVXwfPnQ5/RRexJ1JVwJOi3ixBJ9DxbOCXmh2Rj3sQxDLHBXOlM1LE7EGrPl0sMtQNcKx01+0u9qgxLlgSrthexQjOxBiUoAAABQCAAAWOFQ3cJjBqfhxwfB44Q11K17chBEJiflCYO1Aps/o/7NjpwBDesMGx7lKg96vlikzws52FDQ7KzZgyVSu6wR5BK4a+bK3tXbDzoJY/aTGk+vO3f2+bO737GtIX2cdJZAg6VS+LXOnajyjaut6yHo/SKgIYwi566k2WUb3pvyC+9TWuxEPYWrTIdcdnnzIuwWeAiL5J+3oQG62gMJDwi/mpaw6wEz8vdTS4AP+XELIzhORAeIw9imnb83W14y+eB10pEdZUz0f3rTu5v3AbIUNxt+oAmeXqNmqDoDDYm6IgIwLOS2ctzrORxYcPwCqIc5mqfF3eNm1jIdzWUqtnt58o0+g/1u9FFY/8ZmIQ7Fr1waZ4wM5U2uYwJJeomyk0Hvubyii9eR8T67OkEoh6ayWc/V3sLE/V2aDluWXBmqBPB7Sc6wCqsW/1Ou1oI0931sGbRCe1MBi3an91/jvMBpvKebzSykn8/vIkPRaQaOHVXxmq9zyYh+PlpxQtybljbuu/EBY/B2SfYsq+3UUqzfN8rnYIrRHogs0Y96BtxLk5hdoKty9IXnUwlCWMTn/hYYdLdmIIOIk/NvOKe9HDBeearY5svhAtxlo19is7bryBgCcRUn6EHitpdxAJkt+tzq0wNJv0H2LcHYX+qhiE6CtlAsMBcYjn4ZLYh65dXFXEU1FGDtH0Kp7be7R9Zpvtug97HTuhx19O8k2PnGzZshfZyKqb2Sf5SkolfGQY8NvYIC11OjZR4pj0pGW5X4iqdeCaoAQ4X22tj2Kx8lqeRfVysjx9+Kl6tF4n0cLJNsCoE7URD7RkFlAwD10740A3YZy9Tgx8Dt+Nb4fmGTeoEJemapbtBvRJhD43qhoWfatdOo69Q8WIT67CurcEB26Vns77nBbhFDoxrbglaXoVfvFgOtNP6IahIP9LH7HmvZf+oWMojsaPwyjl71DQP5PHfOorttH2CAuNYeSdwGykF3eCGq1BhutJZzAJFBk1bnPZ4GaJETLYVwpnjjR1wX68CZJ4oji5G8aQ75JsdWV3Uzh6Mkix7491yPWKr5Lv5LIa74FvpfDMoefKNS+8d+0WWkqTjPM8LkVZN030BYOhCGFJJ9qn0rcGWZiH6AygHXbJZxmtmdPdVc/2nBXjXjMgF52a1V89IXOklLq3I1pRwS2ok4ArTPzZyjdW8N57/ueawHofuPfzGz5ck5rCFKV67y8WnwYRitpghcODN7PmJi4iCu7aicnaEKByRjjXEHRf0OR4473MFamDCYUqIDcqFLsIqnwNten2OhF/IaNvih8y6QEsmSsQisuB7UU3IP3eYwMJUnVVrNO97m/TNaQa9tMTo+a3xpuW25sbY+O7mFDPHsXrg56SwPIBjEq0VgeK6wE4nBLCphpqeihmmlkAgg1RgTl6bowlvEGF3PUpUcaGRvh3KZfOMTpI2smSggvpC0a1uru9uSvqhKFo+lZ5kb6q9lYlGXzVoMEncxv/2BBdfSslqtlldcf4lRSR1BXKDI5ctOnGzK6/I0p+H2vQfLbMM39SkHIruSxzuY9jb2mZYZjqr+xalAXl2OmVgl9tTmcR4toicAHH2t1lpBSqUFY7p+p9nShSTAdEi/NdO9GNn8Bs6QCW7w/Cm/74QBRoKB0UGVXJIhVcTpcQ2qCwrM7W2j4lKf6jXYRCQC30J5tzyB3h+tsLrBw4Xe0fb4F8A5kLsIW7xZbBsoPC/Q86jAfizB4CEr9amFF4BgTq3cZUUfFGHGcDAyrOpQKUYbQRjyqI2TD5ovvIToHYIQc4NCxzmpuO46cVuhUrHBBQ+I6COXHI452sJbpkUw5CnFJoqNF1PJ9Sh16+GK9ClDn5ZiP7R/dKHfsua+yr8VfdfszTR+U+/lokNKVXNoA0EW69m+wejEJEBHNTE5wJOb1e7DvWJOlQU5TR9XO3FUqseEgajw/f5NNT9re49DO9pwgIvqb2r7sFEDZjZC4LgXXempltI0Es1VmDcOvvjFgjzwEfNqlfEBUufYnBZIUWXJ/KWEf5e97whuZWqeEVn6egSxNr0z8oxLGQkox0gfdl9KI9SnHGLB9+OoAfuIUzTafh9iZNKh8te/cR2p1HJ5M8zXSu8fGgW1tw1R22b8GT7KNx9f46HdWdvXpR+4VWfd8unjxgLfW2sfdvEJnAXKCzsJPm6B+wBSO5pEPetrH0OBrr6YNGTC7/e/A+xX2TnPhauvIvrA0DcPEejlANRBj/i4DZNzIz5DxlXJIrVkkdK1XX6Nm5L+O6sJk4ulobom1RMnROS2Fjh5GQxNmTUr7YombYjDelOGFP2dREKzTfHizFWQZkrmo0Y7yHBx6l8TukgwP1v3bMJ2+qrjVJ6hWtx5tVc9CjVM5dZuwFD5e3f8Ia6rejLFSBRYy0rNtkvmUJE+jVLdvib4FBb+LwD+c78dSYVGPauANGHDyjeKfOi83kmm20y95OIsBNYv71fqE2bYxvjJN/0lv5nLwbo8MTXtZBKmjsVbKlcTrre6a4+BIGlHynUZ9ZMqiizSD0Kn76jmE0c3gtEUi6p1np2pGEYG5mGd15qxcOkzSf4Isr6Bb3DWw9Skpx+97SzAQuFa7jtMprTj6Hp2gNfSFffP7rZ5xg/os2Llu/P9J01C8ADqm54unL8orFMFZejr1Wpz2EUYlcqrCoIDS796fTzVA25OeVxKGbImBBn7soUEm8Ke+pPYPOeWpr8kZnQoahMP/THS0Q/antHjKL9vhQX/7ItGklsJ0U1So57F2CkTldDXYhV4BvNUXBiPDlLEIAuauiBStwWDncvRY69wnKKohoeSRnols9Mo+zE56YT9ZT8WsQgAAACACAAAdAkILwNMuQ27UVMJR/du82GAD1mBJrrQgq3jpSuXu3L0WGZX2IsVcpod/4cRFkFxfL+1Ur3+M0uJcFXn8IToF6gjN76NUun2zK/gC2jC98gXEsoYAlArQaJaGFxRN5g2NRr/gzN05Se7MxkoqFo+mBMNwHd2Gl31POh5AqodCGRDcou77tSNb2KAbB5B/8qv9tgNm7lhYAf/c3mJ2LmJ+snFENhDgZ3A/26gh3ewnmbiDYq2SMH0vSCNYV6IB0M/fcGNfBCTu3Nd23WmEthMTR6KkHQ2yGErxFHlXpwIEiei/tNwlTAit/9/VT9aC+LVuq3CyMw+dapXe3JlCyX1wxQ9XK8FRFNMYRVYxfxazhbKhPwlkBhJZuR0UY0+vOy7fZJd9iI+mB1vVev5v79m649v7eC0ckG7hUPkrl3qyY2oJ3m299rNsrWC6Xxbv7qbq/2qg0hcu69TNMEXOoqfSoJ1w1DEzeblBkZOWXRP/TV6stvJT6BCS9wrW2QQrT3x8GJWLbZufFUwzkMZ2G2r0VQtXYp2SiJD3bA5359n8/IADXzUyZg8/by5KD07O9x8pA9wxKDv7xJ8F+v+FifvLNu3yqu15uwASK9A59d9ij9SRJztG5eoONzQTM2Q0z9Mtdpjml4/zow2Nw+N5olysLM92auM2IJLVOUTsymGNqkkUZRZt1yrA/0cL2VP0ziHck4mE36+eWNLJqre4vE/zqS7OFFEoR5egOBQOaFpd9RNDH+YHDXJvnTqeze5+lDsYIiM3Z5E7Myh4X+JAlVXZIFCq1KS9UW/E2gZaFlilzn22DlaFZbICKMYb4QszP5Fc1eFn9mlOJ0i7/cNgG1jF57AvoiEEkM0Y6Sf69gfsdH9w+QFiNKz44HYw1s7kC52W1hb8YJX5Dzn+G6TOSSmOzvVti3qRCLkAwKq57qMCyJK4ARDRqJpB50VBsj8szrQgLYgrSPjTUFEHZxqGlPQe5dtuCOHGnN/jN7y+ZGyl+7RDGfj04v1vsCK77sOmmVpkE5PWGiWQvNGQFjgH1jLeoukEWHYRgVEcvwDM7qjy3O3pGini7OKHvE/Jy/471CJxOnsAA60k1IC4azqq9QSK3lWc9OsM8WwSDzBsJzoHLFZX+wDoXUyMwD+NYe2jtT/KgTWDc86DnqklfsM5nLDq7wDCYw1On0kbFoGDvfSuhaHZYcqAVlNIpN7pkdlrIETu/6jvLBiQ/jXTIQaJJDWJ2sPl/wpoh1NV4SDgOYe+Q8V+dGGruYQjbE67K5yWSF1+PPEcloE8IENcwzK9B0CSHV7H08WlC+AR04abh9gMSV0GaaTfnIIdbhhRWiyHjOC69lonN21oPs1nXBA5qCreSGgm6sBBmotBzKIYW5wS6cUoGvnJc9LzeLb03JvpuO7RhG2OQYjdQ+APMYrmeLRfdFqmWD3SaUwyf1RpKexj1n6NXTJma0iy0Q2xorfIciY49nnQDxsV9apXslO9l6o933TaDq7CEp/nIGX3b01TY2jWqxSgPQqev9mWtKIxZE/aOGyOvX7wroUkI4wADNpF1E7OD6RcsR1pXARKBNTAqeylh/a2YcESQMkEYhjsbwCQ/Hu4qZYjespZXfvNty17Cl+KAzrJj+bJS9mVWvN43rUEIY/I0hWNNp+toet+rDmjjhGI3ksq/vU0SUliqIutbHtks5ViKo74xj9WFjanyYA6nhfkLqwvOgA2u9bZChvNVR/O9QZfQzDhF8hP0gqOlYekKPo0ZSc/v6ClfuzQ91IQ4GJ+0p8zLVmWLBeBFQnIKmlwbms5J6mhi4zuFVeLlBuPAM9hhDWWMtTY+ud6WlMQ5hKVWYt7q2pRjl32At6fZxG3h7wB68iuciTSx4TOQLrYy8MOiqARZQHISK+2fsEOCzY9huE5tPB9/Q7O1H57+OQLtYXZUoCVO2JrYakTmk1Q/zgv2tDcF94OGSyrQfDEVz836AQ5AW78yOudQkB6MW8N8Q2768eVHK6MVgD0CpLtMo1Ha/+9w+LFlPZm33eqt4tplIFPksm0EetgEqf6KPlyXetq57LOChqSp+uJwBWuCyydYdRtMK2dnGOt7ft1Ybc/xcTwNOtkwZJ29v4ec1/Mth9etL/EXLK6B+QRpemlBrbr/FYM/1wAs1AZX/bZav+I5wxymCx4If65B5StMN9BfbhFlskCidxidXnkeiFT8vK6Nefns0u75+rrvc2tAUZA7kEF5/8P6akJFgYtYhn0a2heyCJB36GpgqCA5CLoacZvK3vkx589HOM6TELPXsp3soj4+VlhKrm07BgrpAYZAwDwTzTKXfE3wapw7ld4wVCr9/8W1CPw4hAP0r3Yjg8uGbBWOkq0DyxnVJerF3XBgFYMOMqrhaMQApwbByCrCzSrD8B4srvouOtW1hQf7R5e2+u1CLvl5iaLXD3PplLmViWbBQhXTu4oFS2MUn/Qy2TRQgt0QZAZiQFvNreLINJS1N2Yo84raNX9dPqdOekle3PPkEW2QMnJ/q1wEUiS/SlhspRgEm04lsqE8CfHKvLxHTlRd3IigLpjIpCWUQHl7gDHmsU6bNveNDzxcxAxhX0gl5qx9+vdaC6PsDXA1Ih514iUiUHvoSQzwnXRK87PAXT/KVXQ6VEQGoX2TV2/B566Qv5SJixHsSc0drGav6U4S1i2KIP2hpN6YNTXtq2NjtcTMwni6yXCiN6Mh/3jIfoV6+fp2POkQNoGA/+WXkF327BFy8s3c2tldhfm5FV8nfsWDcrUyC85QPSho0FfFpWFDhr73kGVRWPHGQbyzrFYBDLLZQyLOKUUNbuB6d8dnGKWrlLczFSdftdSnGN4eSX3v8zXeJBfVu/0vHdNiTetlPJkvZUcFSS4UUD5UcLiYln7uOcdtI3/5ZD+1EAAACACAAAdQfF0E7q3YgaE25q7YHVyJAxXl9ZA/C4I2MdCTWJ1zeDkBdL/CYFxMe+XkHx1ucB5ra0yeK7AcNqIacmnPaEQ0RL+aqUqCl08xR9UH5nHA4wVdq9cNrieyQ+4Qsm4IIhn4wZFRYI3OMCZmGnMHjdLIgeK3CLmiqFCGfOM8BS4V7Ok4qGvWWQDbjRfNFHASueH+PCISblWT+RK+pxNdJ9eE9j/XmElRa/XFIAqu2MLOoGH+elFUY7Vlw9LDLfcdBKTbPEsKtJ66FVoNE6YeaYPeV8HiNdW68E23VtPNM8875PPoeRisye7U8yqOacfsPkmidKj63kKWe4tgzfLyP99CDI4BYhctv85DtpiJn4L/BuxxvEtow6cA25pYRvhXoo2VffjA2H4gq53ruC84ITRxxGFckfMJUIBNu/3K7A0r0Dkk6iKncMRdJ51c4HnCWszxZ1n7TbWP/AMLVKdCu3/x0vyoswAb7TZXyHrLyN+PeIxnVQW8PuZlOo6WOIdGB5//aeuywppoHrCAy24nLTi3n2sm5t+WLJQ7ETu0LVIgOIA/dMNapnGfset8SfdXn3tSZHMDegyT9cfnluQNW8PEWQUXdF9C2yj5GmMWrv9DzUbvXGZQT1hcS53Z2U2gzGTRFSScXY7ts1IdeMwN6RtyitzqvQauSN57bvpre0BrsPT9mSMjGVu0Jj4enm65wUcG7MQlm/CFUW2q9ykxHUBAdLpZCYJo0ma0sB1dMDhTzBGRfgPbQ5L1rbKkCwg9dhCUMnQkbs6l/7FVdt96+Za7YWlhUuP6WWg4VxAjlK3W81GD1Ek1yfYmguw7tn7VcectNpN8jmjWjezrDPNKZMeAb5kHVpcH0FI9uA0mNc2B/LVw5i0vfgO/KmLSO5EsFe1QZTj9yVal3F2H3yMcLSJod2OY/FSTE34pbK6xrDwjXw3Eub30oR9Kt4lg1ZnIBeIJN/R3FGW2AuVffEJrlMkatkVabPHeGEN5DOhVR6FNf11P0tMWz6oSeEsRhyZKuSNeSBmYMNhcQldvvZp1E/kcOsSgWeprXqzmHoC392sCcxOgOKXtyKla4JxqaGypLeLznGTYpHyBPzkgHDhICK48T1Xc7nMt8IVfgN3TvoYEpMNp0tiD6XBooHx0/MeNtnd4qMBQOrBzMuzgp8NCP6wc/6XNnj4UxoBwNGHRsn6IsSrDM/Gzued7xbDsINufMVVspud/u+Dk4V5V9ULqRT9UDGL9Mdl5HlZGSkbfGXLRiVHTUJiFER8MTHPIjuM8MhuAadBMnMJ2zCrh72s4KcDvG9VuiICtULOj4/zjaavRw0/auV/xVzfTr/N++xu/7ZiT8NRFyNZA/qwt8x5T3NpN7tEKo9FxPdxuv/p+GuB1/jgUu7UQpVoUr99KCCubkS4ZYjgtgp3nKTWDDLHK0mY3fgh+W+p7COhiSOHxaDiMLol0jR5VNpm/LBQOdGEISA/8n3fJqS91Q6C7SKRAK/pvhP59Ff33kZ280F2tmBiZpJfw9HalFIF7iAEdBqw0xt2TjrTvwH6ezaTaUs/ktMXmeN2mxtpvke+G+Few+y8WW7+EjcTqGtdu5k2SvFuwlXNUe/a3JtS9EHcrFj1j0dZ4AGPKM+XZasQ6ZSIgaH8Eqm0op1yn3uuarhkHRxog7je82puBJRqwpYmCvhd7lP9RsoZTFUPz0fpVVTYsb3XfIXkpQDMkhon5G9We/xFef6VyMxNxplZy2foYkeT1gSFCPOS6wvuoKVXeHCJ9SrnrqrHbekxwobF/1Qv+yn98jDAulRACDCNiRgSwaDlKK5Drl9T4JVZifpaWtp8bLAWrMwlnY3iFV9Z2Pr4k7ioYdHlyD3SRdbg1HzGYzXgyXw0Kj+UqxN6jf+5KaBf1tDUhymKeO1S1dr9sL+lfSIXaRzjRuFqOA7tUAT1U0ZYR0I3zaMLvk1yHx+ORwWVsX6xGN1aqXKBJc5/fBccAYuRy5AZzAv+QCmHMJf7gR613IKnpENiHNDPCTVoRcaYqY8FwoKBElmzxQ+8DufHeVReF/kNZZ35EkRFa5vk3PPe+OF2e8X10kU919MemEL1YXfJ/gBiRjrMWy2FRAvRYHN6bQWXPDedb2xTXgckiM/U5SJj88YZi2JxG9TqxtoFgEe0zx4fM1sba5QIYy65JjsJ/g4UOmT+rQVT1o20WhDB3tVvap0jsJvkeKNZUsi50j/qmLarsSD0svhjuqty6iHcZJ+u1zwZ5yUAhO8m3bHPiRcyQGNQj+Q4RqMkWEWD7eh1N5LZKr+YBpVu4VmthNQ9h8fDms4qflloZPfFV8yywgq7Etp/Y1j4ATjlaTO73sn9dFRNOzYAEy5Dylo7edapqbakcn5dXMPai8stlhxVgskUoU4nDNXkHL9CUDLZH3y5t0b+HzNqFJrD3vPI5s2RW2/21c74fMZIxHLysxtz9O0SFDhYe2x/MJCtmjiQtPYPQ+d5+FYHLF4e65qGlmCgXWMEmhVWTzJjLOvvHrlud/J5F1Rn0eP+twXWmZuxZQ4hq+lBqeIVkX58mSaLM3mK5OpjoFzO5l7a6rEFDBTYnY2OKyLssyJFmBxun2CrOj56DpJInaPyKciVfa6uxwzj36mGdqy8T6+KkTQDukLM5aWswxgtJivrgoUwp37V8G40c/CHm7369ZP0wg2nwVUAnEDwvCvq+J/PfDocrNN6N7ObCrtYSV2CjYjmESlOmSfF5WyKLMX3bP+Jt9pbgnsmdnNEgKIog7xTBrHDUXwK07eDOTKzYBbjypdPz6QDckmkpr7SpNPVRkg8eeFJoTo9kvHMC16NOY5txCkEfyIZYg0apM146Es2+G1pyxVNJmoPeGLDruxoGx3X4bxqPE4L5JE1YXw2aG3lkiufNaPav6mE1IAAABoCAAAU4etxkD2yfpwJJjLBc9Fn132R8O0e5NdfRHGAEX/qvbs+ZLmc4B91z2Jj0VbJV5yEk1/JD5cgk+ygCuEU4b+3FFVQlixj9Eoe5slpZFZ18VQ5EQrDvuQtqShy3mSbx2JmP3FNgRHZ4R3fNIDsA3Pu9KYJg0iR4KIHsmVEnFjW/NLNrJEXfrmLxYwfC137vqokKjSJk6pDAtIo1/HfTgxxgzjFxII874t2eZx0QnkUnuP8kpt5OKmDrhJwbkv97ucA2GpSRDNB3WvNHVpWKbNyRc1k0vwY1SRyzO442EYLh3A2ZWiZC93w9AFJWd1UJXLCEZSFnecYVW3ydCPWx8dJKI6Lp2AcESXZlw/XqtQByrjv+4MKb1sRFwBCoxyrCtOBAeK37u1AA9ZSyjGRgwQ7j4DXfyThoLfa2Hk6H3Bb8w3h9bjYrENpPQ4dECFyxHXJcozzi7e3b05dQRxYWVsq73d6aG3fW0SExni3wLfR6fbGUoIFMuEU+OExPYzt/pJOsqkkSG0D0UguoQDHRa+gyeCu8kKT/GhxcZm0oc5A+0OvW8usSjciaaSqse3Z4IM2dW5zWjyvCTsDYRe+dw1WAYvz7HM8Up54xRfyLz7pGNG9KG9N0EVtznkR1wV8yBx5Pxt+33n1/+zsf4wHg4Po/mkydMCybBeCvhFydNR+r1WQD+/JCcWmNy5qAuEXbS6O8TgJsCq+r4iOF8JKemYO73hzO2Xa2mXTCOCbtdWyk8C2EUrfX+x+sWJwlgyj0FsSs5I3iXnDUAsW5Ftz1XN1eWZTb3Vzpg/TragoZiu5OGA+zbv1KVB1LSaUohFFn8VADLGmfUpPspZZZGIvwHZB06DO7DQyaCj/J9rZ5C3D2lPgPflI38Ha3I3NUinXIx8A8E348Mrq84ZVZrS2yRwBa55wfv0gRNQz93diEp9vNXMVkxM04j5XeS58t+OaDdCIisNJ2FzH2yLerW65Tf2tCbgltbaPwYM/jfERKBqaM6lfGwkWJgL7Ox9mTSe4wC/3klXb+JCv71qZyV1sXfvNVANXV03D8SZO9taHA0fHwBvpgkHh+Q/cOGcypWYlX7CloJvz1brtDlBdhrXPZNx3gqaYfDLnyDUW7IgIVFtFotM0AF4IxDSZMExvnlqwghsUqMAE4+ei1cd1FG71M/mkMFwY28K9ndra8t/7F6lKbbcf29aKyOebcoSwxi2gi22/xwfwnNDPMYYdE04euTcpkjj5naFMRC6mNHkUYAehTLhcTun19vTGook9se2mwbYH8l/3uNRNzyfj0C0h9ckDXSiw1L0kRpsxkVwiIuSA7oxyt91/u4XpzP/jkp9s6Jap+MPgdvZRj5vcjdGfLxX3hKb8xfc/LH4lN3WXCPp1f3blGtI2A7Lfvwlg/IXYYRzMHHObGPLFwjF0qg1ilKWGNjBDgXAM2Gvgwo1TitMTAQJDsst321bmZhYkEQGzSLLB7Dd09yGJh4HOZ60c0q3QuYtzQtdfH3IUDlP4ptpBoP1QvKYkBXv/kAKBGRyH1KaA4Q9Rq9Upx2oIPYgNzC6bRWtN/zVZEpGIkUr2Bd5s34rPKwzleL70xhcpWlz2b+cPj5djmcyYyPDIe/ehjse7DMxWAvfpFnaDJh43trqpNRa/Im6A+4R99kGm3dkKSK3IbPs0eBQJ4Z8iVH7iVzCTI7vIAyiBDr3elG+AKpqDi24kqd0OK3kE8sqkYrHJIbpfvq14IW8dAJMVZu8dsJ8x6kYnl0sowMUzXJNpPKwtltRw+/BeFWpfM+VhdzbiOPrNGPgC0aPcIuuVa1sgq1jVYUNDkEKBTqMRCxPggM/kTlShndR4qLX2xmXaTXbf2e06uiR/T8XbvDaJUoBHbrERDBwJitlM7sSQYS6FhoGaLIECVSQpUxcI2a7jNdwrNtx3OmFYPRrq4EFUttOcGdA/4JDe8c9hwcfyzBeYob1fUJz3h2KAKHCMl4TPsyrX3bRKHbD4W0bqqNblamvxFjq3GMHpEoZUIEWslSrilh+djCiJ3B+tLpx2AFdJ8xLDb3lKAgfVNTKBmxCJmxNNpVAIaDgX5hedi8c3lUQJOFAKC8RP7kwAXzKqODOUk5WmmKqgXcLYNen4d4kVdeoHAv7wZtBjpteNocBy1s965FHsGSvCvw3dxXiujUfZ9hpx4d446HMaD5u5wdwXbFUOHdPWSz/RPfs9DRjSFm/p7RRURhyO4Sdj994fKmt6fp1s0hXEx2eR0cv4EANhPTOwtqwF4gJEi02DyYnImuGmTX7yBzWG7MvBxK68XkzklnzoqSgflBS8jojlv31v4JfI8AKfMr6q9s3yvXnHDJi4DPyv392BaSQqXi+VVacsbY1aHeeC7lmaaLpvK1hwvQxNG/B0ZEVcoYQMgF+M0cTWIPx42it7SLvAbSikAkQMNAnIftg7U5FMNI4WdpYlM322ZeXdShiYuTOTwxE5dr9ogkRDPrr6oYjUA6/GHTktgQ1VOVCMAIKhRCU2jcDGrStEfcRtZ/8m5ElJLPCB7z86O1InEP3tcpml7qt6MsGFDOW0gSh1khiN0nFSPgOrKd7OvD2IRPUsnt1ik9aaYYn6v4XEBK1+wNFIFVx/TO7FhXoVlz+yK6g2v6TT3sxabJ5SHAd4pQ5StC+wcowiOGvSbBEK3WxaOOzxQZ3Ti4nYWGpD3iR8Ti0/yPSx3i5Li8RbisSLoHrPG/1wCZgkv9LaBPHnrt0u0+sk2471BSjbbrEbOET+xpEtS8KDrQ6Lnbh05dofwvUrZfPaLruWHuJ7KlkQsQY9zYdkixHi1Lb9Zh8JQXiDmiH9Wn3YS5tpReTuUwdn5Gy7xSt9Gle3uadViOSIeX78aHxRKxjQQAAAAA=');
