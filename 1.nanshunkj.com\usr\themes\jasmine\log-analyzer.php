<?php
if (!defined("__TYPECHO_ROOT_DIR__")) {
    // 仅允许管理员访问
    session_start();
    if (!isset($_SESSION['admin']) || $_SESSION['admin'] !== true) {
        exit('Access denied');
    }
}

class LogAnalyzer {
    private $logFile;
    private $errorSummary;
    private $warningPatterns;
    
    public function __construct() {
        $this->logFile = __DIR__ . '/theme-error.log';
        $this->errorSummary = array(
            'total_logs' => 0,
            'error_count' => 0,
            'warning_count' => 0,
            'info_count' => 0,
            'recent_errors' => array(),
            'recent_warnings' => array(),
            'error_types' => array(),
            'warning_types' => array(),
            'last_analyzed' => null
        );
        
        $this->warningPatterns = array(
            'undefined_array_key' => '/Undefined array key/',
            'undefined_variable' => '/Undefined variable/',
            'undefined_index' => '/Undefined index/',
            'category_error' => '/Category\/Rows\.php/',
            'widget_error' => '/Widget.*error/'
        );
    }
    
    public function analyze() {
        if (!file_exists($this->logFile)) {
            return "日志文件不存在: {$this->logFile}";
        }
        
        $logs = file($this->logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($logs as $log) {
            $this->errorSummary['total_logs']++;
            
            if (preg_match('/\[(.*?)\]\[(.*?)\](.*?)$/', $log, $matches)) {
                $timestamp = $matches[1];
                $type = $matches[2];
                $message = trim($matches[3]);
                
                switch (strtoupper($type)) {
                    case 'ERROR':
                        $this->handleError($timestamp, $message);
                        break;
                    case 'WARNING':
                        $this->handleWarning($timestamp, $message);
                        break;
                    case 'INFO':
                        $this->errorSummary['info_count']++;
                        break;
                }
            }
        }
        
        $this->errorSummary['last_analyzed'] = date('Y-m-d H:i:s');
        $this->saveAnalysis();
        
        return $this->generateReport();
    }
    
    private function handleError($timestamp, $message) {
        $this->errorSummary['error_count']++;
        if (count($this->errorSummary['recent_errors']) < 10) {
            $this->errorSummary['recent_errors'][] = array(
                'time' => $timestamp,
                'message' => $message
            );
        }
        
        if (strpos($message, 'Exception') !== false) {
            $errorType = explode(':', $message)[0];
            if (!isset($this->errorSummary['error_types'][$errorType])) {
                $this->errorSummary['error_types'][$errorType] = 0;
            }
            $this->errorSummary['error_types'][$errorType]++;
        }
    }
    
    private function handleWarning($timestamp, $message) {
        $this->errorSummary['warning_count']++;
        
        if (count($this->errorSummary['recent_warnings']) < 10) {
            $this->errorSummary['recent_warnings'][] = array(
                'time' => $timestamp,
                'message' => $message
            );
        }
        
        foreach ($this->warningPatterns as $type => $pattern) {
            if (preg_match($pattern, $message)) {
                if (!isset($this->errorSummary['warning_types'][$type])) {
                    $this->errorSummary['warning_types'][$type] = 0;
                }
                $this->errorSummary['warning_types'][$type]++;
                break;
            }
        }
    }
    
    private function generateReport() {
        $report = "<h2>日志分析报告</h2>";
        $report .= "<p>分析时间: {$this->errorSummary['last_analyzed']}</p>";
        
        $report .= $this->generateSummarySection();
        
        $report .= $this->generateRecentErrorsSection();
        
        $report .= $this->generateRecentWarningsSection();
        
        $report .= $this->generateErrorTypesSection();
        
        $report .= $this->generateWarningTypesSection();
        
        $report .= $this->generateSolutionsSection();
        
        return $report;
    }
    
    private function generateSummarySection() {
        return "<h3>统计概要</h3>
                <ul>
                    <li>总日志数: {$this->errorSummary['total_logs']}</li>
                    <li>错误数: {$this->errorSummary['error_count']}</li>
                    <li>警告数: {$this->errorSummary['warning_count']}</li>
                    <li>信息数: {$this->errorSummary['info_count']}</li>
                </ul>";
    }
    
    private function generateRecentErrorsSection() {
        $section = "";
        if (!empty($this->errorSummary['recent_errors'])) {
            $section .= "<h3>最近的错误信息</h3><ul>";
            foreach ($this->errorSummary['recent_errors'] as $error) {
                $section .= "<li>[{$error['time']}] {$error['message']}</li>";
            }
            $section .= "</ul>";
        }
        return $section;
    }
    
    private function generateRecentWarningsSection() {
        $section = "";
        if (!empty($this->errorSummary['recent_warnings'])) {
            $section .= "<h3>最近的警告信息</h3><ul>";
            foreach ($this->errorSummary['recent_warnings'] as $warning) {
                $section .= "<li>[{$warning['time']}] {$warning['message']}</li>";
            }
            $section .= "</ul>";
        }
        return $section;
    }
    
    private function generateErrorTypesSection() {
        $section = "";
        if (!empty($this->errorSummary['error_types'])) {
            $section .= "<h3>错误类型统计</h3><ul>";
            foreach ($this->errorSummary['error_types'] as $type => $count) {
                $section .= "<li>{$type}: {$count}次</li>";
            }
            $section .= "</ul>";
        }
        return $section;
    }
    
    private function generateWarningTypesSection() {
        $section = "";
        if (!empty($this->errorSummary['warning_types'])) {
            $section .= "<h3>警告类型统计</h3><ul>";
            foreach ($this->errorSummary['warning_types'] as $type => $count) {
                $section .= "<li>" . $this->getWarningTypeDescription($type) . ": {$count}次</li>";
            }
            $section .= "</ul>";
        }
        return $section;
    }
    
    private function generateSolutionsSection() {
        $solutions = "<h3>可能的解决方案</h3><ul>";
        
        if (isset($this->errorSummary['warning_types']['category_error'])) {
            $solutions .= "<li>分类相关警告解决方案：
                            <ul>
                                <li>检查数据库中的分类数据完整性</li>
                                <li>确保所有文章都有正确的分类关联</li>
                                <li>尝试重建分类缓存</li>
                            </ul>
                         </li>";
        }
        
        if (isset($this->errorSummary['warning_types']['undefined_array_key'])) {
            $solutions .= "<li>未定义数组键警告解决方案：
                            <ul>
                                <li>在访问数组前使用 isset() 检查键是否存在</li>
                                <li>使用 array_key_exists() 检查键</li>
                                <li>为数组设置默认值</li>
                            </ul>
                         </li>";
        }
        
        $solutions .= "</ul>";
        return $solutions;
    }
    
    private function getWarningTypeDescription($type) {
        $descriptions = array(
            'undefined_array_key' => '未定义的数组键',
            'undefined_variable' => '未定义的变量',
            'undefined_index' => '未定义的索引',
            'category_error' => '分类相关错误',
            'widget_error' => '组件相关错误'
        );
        return isset($descriptions[$type]) ? $descriptions[$type] : $type;
    }
    
    private function saveAnalysis() {
        $analysisFile = __DIR__ . '/log-analysis.json';
        file_put_contents($analysisFile, json_encode($this->errorSummary, JSON_PRETTY_PRINT));
    }
    
    public function clearLog() {
        if (file_exists($this->logFile)) {
            file_put_contents($this->logFile, '');
            return "日志已清空";
        }
        return "日志文件不存在";
    }
}

if (isset($_GET['action'])) {
    $analyzer = new LogAnalyzer();
    switch ($_GET['action']) {
        case 'analyze':
            echo $analyzer->analyze();
            break;
        case 'clear':
            echo $analyzer->clearLog();
            break;
        default:
            echo "未知操作";
    }
}
?>

<!-- 简单的操作界面 -->
<!DOCTYPE html>
<html>
<head>
    <title>日志分析器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .button { 
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .button.clear { background-color: #f44336; }
    </style>
</head>
<body>
    <h1>主题错误日志分析器</h1>
    <a href="?action=analyze" class="button">分析日志</a>
    <a href="?action=clear" class="button clear" onclick="return confirm('确定要清空日志吗？')">清空日志</a>
</body>
</html> 