#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息检测模块 (MVP简化版)
使用简单的图像变化检测新消息
"""

import time
import hashlib
from datetime import datetime

try:
    from PIL import Image
    import numpy as np
except ImportError:
    print("警告: PIL 或 numpy 未安装，将使用基础功能")
    Image = None
    np = None

class MessageDetector:
    """消息检测类"""
    
    def __init__(self, window_manager):
        self.window_manager = window_manager
        self.last_screenshot_hash = None
        self.message_count = 0
        self.last_check_time = None
        
        # 检测区域 (相对于窗口的百分比)
        self.detection_area = {
            'left_percent': 0.0,    # 左侧0%
            'top_percent': 0.1,     # 顶部10%
            'width_percent': 0.7,   # 宽度70%
            'height_percent': 0.8   # 高度80%
        }
    
    def get_detection_region(self):
        """获取检测区域坐标"""
        window_info = self.window_manager.get_window_info()
        if not window_info:
            return None
        
        # 计算检测区域
        detection_left = int(window_info['width'] * self.detection_area['left_percent'])
        detection_top = int(window_info['height'] * self.detection_area['top_percent'])
        detection_width = int(window_info['width'] * self.detection_area['width_percent'])
        detection_height = int(window_info['height'] * self.detection_area['height_percent'])
        
        return {
            'left': detection_left,
            'top': detection_top,
            'width': detection_width,
            'height': detection_height
        }
    
    def take_detection_screenshot(self):
        """截取检测区域的截图"""
        screenshot = self.window_manager.take_screenshot()
        if not screenshot or not Image:
            return None
        
        region = self.get_detection_region()
        if not region:
            return None
        
        try:
            # 裁剪到检测区域
            cropped = screenshot.crop((
                region['left'],
                region['top'],
                region['left'] + region['width'],
                region['top'] + region['height']
            ))
            
            return cropped
        except Exception as e:
            print(f"❌ 截图裁剪失败: {e}")
            return None
    
    def calculate_image_hash(self, image):
        """计算图像的哈希值"""
        if not image:
            return None
        
        try:
            # 将图像转换为灰度并调整大小
            image = image.convert('L').resize((64, 64))
            
            # 转换为字节数据
            image_bytes = image.tobytes()
            
            # 计算MD5哈希
            return hashlib.md5(image_bytes).hexdigest()
        except Exception as e:
            print(f"❌ 计算图像哈希失败: {e}")
            return None
    
    def has_new_message(self):
        """检测是否有新消息 (基于图像变化)"""
        if not self.window_manager.douyin_window:
            return False
        
        # 截取当前检测区域
        current_screenshot = self.take_detection_screenshot()
        if not current_screenshot:
            return False
        
        # 计算当前截图的哈希
        current_hash = self.calculate_image_hash(current_screenshot)
        if not current_hash:
            return False
        
        # 首次运行，保存当前哈希
        if self.last_screenshot_hash is None:
            self.last_screenshot_hash = current_hash
            self.last_check_time = datetime.now()
            print("🔄 初始化消息检测区域")
            return False
        
        # 比较哈希值
        if current_hash != self.last_screenshot_hash:
            print(f"📩 检测到界面变化 (可能是新消息)")
            self.last_screenshot_hash = current_hash
            self.last_check_time = datetime.now()
            self.message_count += 1
            return True
        
        self.last_check_time = datetime.now()
        return False
    
    def get_mock_message_content(self):
        """获取模拟的消息内容 (MVP版本)"""
        # 由于没有OCR，这里返回模拟的消息内容
        mock_messages = [
            "你好",
            "咨询",
            "价格多少",
            "有什么产品",
            "想了解一下",
            "在吗",
            "购买",
            "客服"
        ]
        
        import random
        return random.choice(mock_messages)
    
    def start_monitoring(self, callback=None, interval=2):
        """开始监控消息 (简化版本)"""
        print(f"🔄 开始监控消息，检查间隔: {interval}秒")
        
        try:
            while True:
                if self.has_new_message():
                    mock_content = self.get_mock_message_content()
                    print(f"📥 模拟新消息: {mock_content}")
                    
                    if callback:
                        callback(mock_content)
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n⏹️ 停止监控")
        except Exception as e:
            print(f"❌ 监控过程出错: {e}")
    
    def get_statistics(self):
        """获取检测统计信息"""
        return {
            'message_count': self.message_count,
            'last_check_time': self.last_check_time,
            'is_monitoring': self.last_screenshot_hash is not None
        } 