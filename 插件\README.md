# 吴畏支付 (WuweiPay) - Typecho博客支付插件

## 插件介绍

吴畏支付是一款专为Typecho博客系统开发的支付插件，支持通过吴畏支付接口实现支付宝、微信支付等多种支付方式。

## 功能特点

- 支持多种支付方式（支付宝、微信支付）
- 简洁美观的支付界面
- 支持PC端和移动端
- 支持扫码支付
- 实时订单状态查询
- 完善的订单管理系统

## 安装方法

1. 下载插件压缩包
2. 解压后将文件夹重命名为 `WuweiPay`
3. 上传至Typecho的 `/usr/plugins/` 目录
4. 登录Typecho后台，在"控制台"->"插件"中找到"吴畏支付"，点击"启用"
5. 点击"设置"，配置吴畏支付的相关参数

## 配置说明

- **吴畏支付AppID**：在吴畏支付平台申请的AppID
- **吴畏支付密钥**：在吴畏支付平台申请的密钥
- **吴畏支付网关**：吴畏支付接口网关地址，默认为 `https://8ww.fun/api/pay`
- **启用的支付方式**：选择要启用的支付方式（支付宝、微信支付）

## 使用方法

### 在文章或页面中添加支付按钮

```php
<?php WuweiPay_Plugin::payButton(10, '购买此文章', 'general'); ?>
```

参数说明：
- 第一个参数：支付金额（元）
- 第二个参数：支付标题
- 第三个参数：订单类型（general：普通订单，donation：打赏，vip：会员）

### 在主题模板中添加支付按钮

```php
<?php if (class_exists('WuweiPay_Plugin')): ?>
    <?php WuweiPay_Plugin::payButton(10, '购买此文章', 'general'); ?>
<?php endif; ?>
```

## 订单管理

启用插件后，在Typecho后台会新增"吴畏支付"菜单，点击可以进入订单管理页面，查看和管理所有支付订单。

## 常见问题

1. **问题**：支付后订单状态没有更新？  
   **解答**：请检查服务器是否能正常接收到支付平台的回调通知，可能需要配置服务器防火墙或安全组。

2. **问题**：支付按钮点击后没有反应？  
   **解答**：请检查浏览器控制台是否有JavaScript错误，确保网站能正常加载插件的JS和CSS文件。

## 联系与支持

- 作者：吴畏
- 博客：[https://8ww.fun](https://8ww.fun)
- 邮箱：<EMAIL>

## 版权信息

Copyright © 2023 吴畏 All Rights Reserved.
