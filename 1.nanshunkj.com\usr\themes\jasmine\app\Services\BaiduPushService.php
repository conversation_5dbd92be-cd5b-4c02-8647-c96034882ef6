<?php

class BaiduPushService
{
    protected $apiUrl = 'http://data.zz.baidu.com/urls';
    protected $site = 'https://8ww.fun';
    protected $token = 'GQ0D8gvfGerXKQfS';
    protected $quotaFile;  // 用于存储配额信息的文件

    public function __construct()
    {
        $this->quotaFile = __DIR__ . '/baidu_quota.json';
    }

    protected function checkQuota()
    {
        if (file_exists($this->quotaFile)) {
            $quota = json_decode(file_get_contents($this->quotaFile), true);
            if ($quota && isset($quota['date']) && isset($quota['remain'])) {
                // 如果是今天的配额记录且配额为0，则返回false
                if ($quota['date'] === date('Y-m-d') && $quota['remain'] <= 0) {
                    return false;
                }
            }
        }
        return true;
    }

    protected function updateQuota($result)
    {
        if (isset($result['remain'])) {
            $quota = [
                'date' => date('Y-m-d'),
                'remain' => $result['remain']
            ];
            file_put_contents($this->quotaFile, json_encode($quota));
        }
    }

    public function pushUrls(array $urls)
    {
        // 检查今日配额
        if (!$this->checkQuota()) {
            return array('error' => 400, 'message' => 'Daily quota exceeded. Try again tomorrow.');
        }

        foreach(array_chunk($urls, 10) as $urlBatch) {
            try {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, "{$this->apiUrl}?site={$this->site}&token={$this->token}");
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, implode("\n", $urlBatch));
                curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: text/plain'));
                curl_setopt($ch, CURLOPT_TIMEOUT, 5);

                $result = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                error_log("Baidu Push Result: " . $result);
                
                $resultArray = json_decode($result, true);
                if ($resultArray) {
                    // 更新配额信息
                    $this->updateQuota($resultArray);
                    return $resultArray;
                }

                sleep(1);
                
            } catch (Exception $e) {
                error_log("Baidu Push Error: " . $e->getMessage());
                return array('error' => 500, 'message' => $e->getMessage());
            }
        }
    }

    public function pushUrl($url)
    {
        return $this->pushUrls([$url]);
    }
} 