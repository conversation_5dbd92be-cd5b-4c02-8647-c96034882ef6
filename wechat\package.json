{"name": "vue3-wechat-tool", "version": "0.0.1", "description": "使用 Vite + Vue3 + AntDesignVue + Pinia 搭建的在线微信对话生成器", "author": "ele-cat", "license": "MIT", "homepage": "https://github.com/ele-cat/vue3-wechat-tool", "private": true, "type": "module", "scripts": {"serve": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^10.4.1", "ant-design-vue": "^4.0.2", "file-saver": "^2.0.5", "gif.js": "^0.2.0", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "mitt": "^3.0.1", "pinia": "^2.1.6", "pinia-plugin-persist": "^1.0.0", "vue": "^3.3.4", "vue-advanced-cropper": "^2.8.8", "vue3-perfect-scrollbar": "^1.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "less": "^4.2.0", "less-loader": "^11.1.3", "vite": "^4.4.5"}}