// 图片放大功能初始化
window.addEventListener('load', function() {
    // 为所有文章内的图片添加统一的提示文字
    document.querySelectorAll('.markdown-body img').forEach(img => {
        img.title = '点击图片即可放大查看';
    });

    if (typeof mediumZoom === 'function') {
        try {
            // 初始化 medium-zoom
            const zoom = mediumZoom('.markdown-body img', {
                margin: 48,
                background: 'rgba(0, 0, 0, 0.9)',
                scrollOffset: 0,
            });

            // 缩放参数
            const minScale = 0.5;
            const maxScale = 3;
            const scaleStep = 0.1;

            // 当图片打开时添加滚轮缩放功能
            zoom.on('opened', () => {
                const zoomedImage = document.querySelector('.medium-zoom-image--opened');
                if (!zoomedImage) return;

                function handleWheel(e) {
                    e.preventDefault();
                    
                    // 获取当前变换信息
                    const transform = zoomedImage.style.transform;
                    const currentScale = transform.match(/scale\((.*?)\)/)?.[1] || 1;
                    
                    // 计算新的缩放值
                    let newScale;
                    if (e.deltaY < 0) {
                        // 向上滚动，放大
                        newScale = Math.min(maxScale, parseFloat(currentScale) + scaleStep);
                    } else {
                        // 向下滚动，缩小
                        newScale = Math.max(minScale, parseFloat(currentScale) - scaleStep);
                    }

                    // 保持原有的translate3d值，只更新scale
                    const translateMatch = transform.match(/translate3d\((.*?)\)/);
                    const translate = translateMatch ? translateMatch[0] : 'translate3d(0px, 0px, 0px)';
                    
                    // 应用新的变换，保持原有的位置
                    zoomedImage.style.transform = `${translate} scale(${newScale})`;
                }

                // 添加滚轮事件监听
                zoomedImage.addEventListener('wheel', handleWheel, { passive: false });

                // 在关闭时移除事件监听
                zoom.on('closed', () => {
                    zoomedImage.removeEventListener('wheel', handleWheel);
                });
            });

            console.log('Medium Zoom initialized with wheel zoom support');
        } catch (error) {
            console.error('Error initializing medium-zoom:', error);
        }
    } else {
        console.error('mediumZoom is not loaded');
    }
}); 