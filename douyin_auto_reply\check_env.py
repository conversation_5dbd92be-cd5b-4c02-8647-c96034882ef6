#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本
验证所有依赖包是否正确安装
"""

import sys
import subprocess

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要3.7+")
        return False
    else:
        print("✅ Python版本满足要求")
        return True

def check_module(module_name, import_name=None, package_name=None):
    """检查模块是否可以导入"""
    if import_name is None:
        import_name = module_name
    if package_name is None:
        package_name = module_name
        
    try:
        __import__(import_name)
        print(f"✅ {module_name} - 已安装")
        return True
    except ImportError:
        print(f"❌ {module_name} - 未安装")
        print(f"   安装命令: pip install {package_name}")
        return False

def check_tesseract():
    """检查Tesseract OCR是否安装"""
    print("🔍 检查Tesseract OCR...")
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ Tesseract OCR - {version_line}")
            return True
        else:
            print("❌ Tesseract OCR - 未正确安装")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Tesseract OCR - 未安装或未添加到PATH")
        print("   下载地址: https://github.com/UB-Mannheim/tesseract/wiki")
        return False

def main():
    """主检查函数"""
    print("="*60)
    print("           环境依赖检查")
    print("="*60)
    
    all_passed = True
    
    # 检查Python版本
    if not check_python_version():
        all_passed = False
    
    print("\n🔍 检查Python依赖包...")
    
    # 必需的包列表
    required_packages = [
        ('pyautogui', 'pyautogui'),
        ('opencv-python', 'cv2'),
        ('pytesseract', 'pytesseract'),
        ('Pillow', 'PIL'),
        ('numpy', 'numpy'),
        ('pywin32', 'win32api'),
        ('loguru', 'loguru'),
        ('requests', 'requests'),
    ]
    
    for package_name, import_name in required_packages:
        if not check_module(package_name, import_name):
            all_passed = False
    
    print("\n🔍 检查外部工具...")
    # 检查Tesseract
    if not check_tesseract():
        all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 所有检查通过！环境配置完成！")
        print("📖 可以运行: python main.py")
    else:
        print("❌ 环境检查失败，请安装缺失的依赖")
        print("💡 运行: pip install -r requirements.txt")
        print("💡 并手动安装Tesseract OCR")
    print("="*60)
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ 用户中断检查")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程出错: {e}")
        sys.exit(1) 