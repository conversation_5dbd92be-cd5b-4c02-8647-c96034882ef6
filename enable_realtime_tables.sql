-- 启用所有销售系统相关表的实时功能
-- 在 Supabase SQL Editor 中执行此脚本

-- 1. 启用销售记录表的实时功能
ALTER PUBLICATION supabase_realtime ADD TABLE sales_records;

-- 2. 启用用户资料表的实时功能
ALTER PUBLICATION supabase_realtime ADD TABLE user_profiles;

-- 3. 启用系统设置表的实时功能
ALTER PUBLICATION supabase_realtime ADD TABLE system_settings;

-- 4. 启用销售人员表的实时功能
ALTER PUBLICATION supabase_realtime ADD TABLE salesperson;

-- 5. 启用销售汇总表的实时功能（如果需要）
ALTER PUBLICATION supabase_realtime ADD TABLE sales_summary;

-- 6. 启用销售团队表的实时功能（如果需要）
ALTER PUBLICATION supabase_realtime ADD TABLE sales_teams;

-- 7. 启用团队成员表的实时功能（如果需要）
ALTER PUBLICATION supabase_realtime ADD TABLE team_members;

-- 验证实时发布状态
SELECT 
    schemaname,
    tablename,
    pubname
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime'
ORDER BY schemaname, tablename; 