# WW Style 主题安装使用指南

## 安装步骤

1. 下载主题压缩包
2. 解压后将`ww_style`文件夹上传到Typecho的`usr/themes/`目录下
3. 登录Typecho后台，进入"外观"菜单，启用"WW Style"主题
4. 根据需要进行相关设置

## 主题功能说明

### 1. 首页轮播图

首页轮播图支持以下功能：
- 自动轮播（每5秒切换一次）
- 触摸滑动支持（移动端可左右滑动切换）
- 键盘导航（使用左右方向键切换）
- 图片懒加载（节省带宽，提升加载速度）

### 2. 会员系统

支持多种会员等级：
- 月度会员
- 年度会员
- 永久会员

会员特权：
- 查看会员专属内容
- 资源下载
- 优先服务
- 专属优惠

### 3. 文章发布

#### 设置文章缩略图
在文章编辑页面的自定义字段中添加`thumb`字段，值为缩略图的URL地址。

#### 设置会员专享内容
在文章编辑页面的自定义字段中添加`isPremium`字段，值为`1`表示该文章为会员专享。

#### 设置付费内容
在文章编辑页面的自定义字段中添加`price`字段，值为付费价格（数字），例如`9.9`。

### 4. 新增功能说明

#### 消息提示组件

消息提示组件可以通过JavaScript调用，例如：

```javascript
// 显示消息提示
function showToast(message, duration = 3000) {
    const toast = document.getElementById('toast-message');
    if (toast) {
        toast.textContent = message;
        toast.classList.add('active');
        
        setTimeout(function() {
            toast.classList.remove('active');
        }, duration);
    }
}

// 使用示例
showToast('操作成功！');
```

#### 移动端导航菜单

在移动端浏览时，顶部导航会自动转换为折叠式菜单，点击右上角的菜单图标即可展开/收起。

#### 动画效果

主题内置了多种动画效果，可以通过添加以下CSS类来使用：

- `animate-fade-in`: 淡入动画
- `animate-fade-right`: 从右侧滑入
- `float-animation`: 上下浮动动画
- `pulse-animation`: 脉冲动画

用法示例：

```html
<div class="your-element animate-fade-in">
    这个元素将会淡入显示
</div>

<button class="pulse-animation">
    这个按钮将会有脉冲效果
</button>
```

## 性能优化提示

### 图片优化

1. 压缩图片大小，推荐使用WebP格式
2. 为图片设置适当的尺寸，避免浏览器缩放
3. 使用懒加载属性：`data-src`替代`src`

### 缓存设置

建议在服务器上设置适当的缓存策略，例如为静态资源（CSS、JS、图片）设置较长的缓存时间。

### CDN加速

可以使用CDN加速静态资源加载，主题已内置国内CDN加速的图标字体。

## 常见问题排查

### 图标显示异常

如果图标显示为文字符号而非图标，可能是图标字体加载失败。解决方法：

1. 检查网络连接是否正常
2. 尝试使用其他CDN源
3. 下载Remixicon图标字体到本地使用

### 动画效果不显示

1. 确认您的浏览器是否支持相关CSS动画属性
2. 检查是否启用了"减少动画"的辅助功能选项
3. 在低端设备上可能会限制动画效果，以保证性能

### 移动端菜单无法展开

1. 检查JavaScript是否正常加载
2. 尝试清除浏览器缓存
3. 检查控制台是否有JavaScript错误

## 联系与支持

如有任何问题或建议，请通过以下方式联系我们：

- 邮箱：<EMAIL>
- GitHub: https://github.com/yourusername/ww-style 