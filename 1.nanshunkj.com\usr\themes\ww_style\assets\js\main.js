/**
 * WW Style 主题JavaScript文件
 *
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.1
 */

// 全局配置变量 - 在header.php中定义
// var typechoConfig = {
//     siteUrl: '网站URL',
//     themeUrl: '主题URL',
//     userId: 用户ID或null,
//     isLoggedIn: true或false
// };

(function() {
    "use strict";

    // DOM完全加载后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 检测图标字体是否加载成功
        setTimeout(function() {
            // 检查图标是否正常显示
            var iconElement = document.querySelector('.ri-search-line');
            if (iconElement) {
                var style = window.getComputedStyle(iconElement, ':before');
                if (!style || !style.content || style.content === 'none' || style.content === '') {
                    // 图标未正常加载，应用备用方案
                    document.body.classList.add('icon-fallback');
                }
            }

            // 移动端菜单点击展开
            var menuToggle = document.getElementById('mobile-menu-toggle');
            var navLinks = document.querySelector('.nav-links');

            if (menuToggle && navLinks) {
                menuToggle.addEventListener('click', function() {
                    navLinks.classList.toggle('active');
                    // 更新aria-expanded状态
                    var isExpanded = navLinks.classList.contains('active');
                    menuToggle.setAttribute('aria-expanded', isExpanded);
                });

                // 点击导航链接后自动关闭菜单
                var navItems = navLinks.querySelectorAll('a');
                navItems.forEach(function(item) {
                    item.addEventListener('click', function() {
                        if (window.innerWidth <= 768) {
                            navLinks.classList.remove('active');
                            menuToggle.setAttribute('aria-expanded', 'false');
                        }
                    });
                });
            }
        }, 1000);
        // 导航栏滚动效果
        const header = document.querySelector('.header');
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });



        // 懒加载图片
        const lazyImages = document.querySelectorAll('img[data-src]');

        if (lazyImages.length > 0) {
            // 创建交叉观察器
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const lazyImage = entry.target;
                            lazyImage.src = lazyImage.dataset.src;
                            lazyImage.removeAttribute('data-src');
                            imageObserver.unobserve(lazyImage);
                        }
                    });
                });

                lazyImages.forEach(img => {
                    imageObserver.observe(img);
                });
            } else {
                // 兼容不支持IntersectionObserver的浏览器
                function lazyLoad() {
                    const lazyImages = document.querySelectorAll('img[data-src]');
                    lazyImages.forEach(img => {
                        if (isInViewport(img)) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                    });
                }

                // 初始加载
                lazyLoad();

                // 滚动时加载
                let lazyLoadThrottleTimeout;
                window.addEventListener('scroll', function() {
                    if (lazyLoadThrottleTimeout) {
                        clearTimeout(lazyLoadThrottleTimeout);
                    }

                    lazyLoadThrottleTimeout = setTimeout(function() {
                        lazyLoad();
                    }, 200);
                });
            }
        }

        // 搜索功能
        const searchBtn = document.querySelector('.search-btn');
        const searchContainer = document.querySelector('.search-container');
        const searchClose = document.querySelector('.search-close');
        const searchInput = document.querySelector('.search-input');

        if (searchBtn && searchContainer) {
            searchBtn.addEventListener('click', function() {
                searchContainer.classList.add('active');
                if (searchInput) {
                    setTimeout(function() {
                        searchInput.focus();
                    }, 100);
                }
            });
        }

        if (searchClose && searchContainer) {
            searchClose.addEventListener('click', function() {
                searchContainer.classList.remove('active');
            });

            // 点击ESC键也可以关闭搜索
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && searchContainer.classList.contains('active')) {
                    searchContainer.classList.remove('active');
                }
            });
        }

        // 优化的动画滚动显示
        const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-fade-right');

        if (animatedElements.length > 0 && 'IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                        entry.target.classList.add('animated');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -10% 0px'
            });

            animatedElements.forEach(element => {
                animationObserver.observe(element);
            });
        } else {
            // 回退方案
            // 检查元素是否在视口内
            function isInViewport(element) {
                const rect = element.getBoundingClientRect();
                return (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.9 &&
                    rect.bottom >= 0
                );
            }

            // 滚动时检查元素
            function checkElements() {
                animatedElements.forEach(element => {
                    if (isInViewport(element) && !element.classList.contains('animated')) {
                        // 标记为已动画
                        element.classList.add('animated');
                    }
                });
            }

            // 初始检查
            checkElements();

            // 滚动时检查，使用节流优化性能
            let animationThrottleTimeout;
            window.addEventListener('scroll', function() {
                if (animationThrottleTimeout) {
                    clearTimeout(animationThrottleTimeout);
                }

                animationThrottleTimeout = setTimeout(function() {
                    checkElements();
                }, 100);
            });
        }

        // 回到顶部按钮
        const backToTop = document.querySelector('.back-to-top');

        if (backToTop) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    backToTop.classList.add('active');
                } else {
                    backToTop.classList.remove('active');
                }
            });

            backToTop.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 会员内容解锁确认
        const memberContentBtn = document.querySelectorAll('.member-content-locked .btn-primary');

        memberContentBtn.forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                const isLoggedIn = document.body.classList.contains('logged-in');

                if (!isLoggedIn) {
                    e.preventDefault();
                    // 保存当前URL，以便登录后返回
                    localStorage.setItem('redirect_after_login', window.location.href);
                    // 重定向到登录页
                    window.location.href = typechoConfig.siteUrl + 'index.php/login';
                }
            });
        });

        // 付费内容购买确认
        const buyContentBtn = document.querySelectorAll('.buy-content');

        buyContentBtn.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const cid = this.getAttribute('data-cid');
                const price = this.getAttribute('data-price');

                if (confirm('确认购买此内容吗？价格：¥' + price)) {
                    // 跳转到支付页面
                    window.location.href = typechoConfig.siteUrl + 'index.php/payment-process?cid=' + cid + '&price=' + price;
                }
            });
        });

        // 会员套餐选择
        const membershipPlans = document.querySelectorAll('.membership-plan');

        membershipPlans.forEach(function(plan) {
            plan.addEventListener('click', function() {
                // 移除所有选中状态
                membershipPlans.forEach(function(p) {
                    p.classList.remove('active');
                });

                // 添加当前选中状态
                this.classList.add('active');

                // 更新选择的套餐ID和价格
                const planId = this.getAttribute('data-plan');
                const planPrice = this.getAttribute('data-price');

                const selectedPlanInput = document.querySelector('#selected_plan');
                const selectedPriceElem = document.querySelector('#selected_price');

                if (selectedPlanInput) {
                    selectedPlanInput.value = planId;
                }

                if (selectedPriceElem) {
                    selectedPriceElem.textContent = planPrice;
                }
            });
        });

        // 支付方式选择
        const paymentMethods = document.querySelectorAll('.payment-method');

        paymentMethods.forEach(function(method) {
            method.addEventListener('click', function() {
                // 移除所有选中状态
                paymentMethods.forEach(function(m) {
                    m.classList.remove('active');
                });

                // 添加当前选中状态
                this.classList.add('active');

                // 更新选择的支付方式
                const payMethod = this.getAttribute('data-method');
                const selectedMethodInput = document.querySelector('#selected_method');

                if (selectedMethodInput) {
                    selectedMethodInput.value = payMethod;
                }

                // 显示对应的支付二维码
                const qrcodes = document.querySelectorAll('.payment-qrcode');
                qrcodes.forEach(function(qrcode) {
                    qrcode.classList.remove('active');
                });

                const selectedQrcode = document.querySelector('.payment-qrcode[data-method="' + payMethod + '"]');
                if (selectedQrcode) {
                    selectedQrcode.classList.add('active');
                }
            });
        });

        // 文章点赞功能
        const likeButton = document.querySelector('.post-like-button');
        if (likeButton) {
            likeButton.addEventListener('click', function() {
                const cid = this.getAttribute('data-cid');
                const likeCount = document.querySelector('.like-count');
                const isLoggedIn = document.body.classList.contains('logged-in');
                const self = this;

                // 检查用户是否已登录
                if (!isLoggedIn) {
                    // 未登录用户提示登录
                    if (confirm('请先登录后再点赞')) {
                        localStorage.setItem('redirect_after_login', window.location.href);
                        window.location.href = typechoConfig.siteUrl + 'index.php/login';
                    }
                    return;
                }

                // 防止重复点击
                if (self.classList.contains('loading') || self.classList.contains('liked')) {
                    return;
                }

                // 添加加载状态
                self.classList.add('loading');

                // 发送AJAX请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', typechoConfig.siteUrl + 'index.php/action.php?action=like&cid=' + cid, true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        self.classList.remove('loading');

                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.success) {
                                    // 点赞成功
                                    self.classList.add('liked');
                                    if (likeCount) {
                                        likeCount.textContent = response.count;
                                    }

                                    // 显示成功消息
                                    showToast('点赞成功!');
                                } else {
                                    // 显示错误信息
                                    showToast(response.message || '操作失败，请稍后再试');
                                }
                            } catch (e) {
                                showToast('系统错误，请稍后再试');
                            }
                        } else {
                            showToast('网络错误，请稍后再试');
                        }
                    }
                };
                xhr.send();
            });
        }

        // 评论回复功能增强
        const replyLinks = document.querySelectorAll('.comment-reply-link');
        replyLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const commentId = this.getAttribute('data-commentid');
                const commentForm = document.getElementById('comment-form');
                const parentInput = document.getElementById('comment-parent');
                const cancelReply = document.getElementById('cancel-comment-reply');
                const commentReplyTo = document.getElementById('comment-reply-to');
                const replyToName = this.getAttribute('data-name');

                // 设置父评论ID
                if (parentInput) {
                    parentInput.value = commentId;
                }

                // 显示"回复给xxx"的提示
                if (commentReplyTo) {
                    commentReplyTo.textContent = '回复给 ' + replyToName;
                    commentReplyTo.style.display = 'block';
                }

                // 显示取消回复按钮
                if (cancelReply) {
                    cancelReply.style.display = 'inline-block';
                }

                // 将评论表单移动到该评论下方
                const commentItem = this.closest('.comment-item');
                if (commentItem && commentForm) {
                    commentItem.appendChild(commentForm);
                    // 聚焦到评论框
                    const textarea = commentForm.querySelector('textarea');
                    if (textarea) {
                        textarea.focus();
                    }
                }
            });
        });

        // 取消回复
        const cancelReply = document.getElementById('cancel-comment-reply');
        if (cancelReply) {
            cancelReply.addEventListener('click', function(e) {
                e.preventDefault();

                const commentForm = document.getElementById('comment-form');
                const parentInput = document.getElementById('comment-parent');
                const commentReplyTo = document.getElementById('comment-reply-to');
                const commentContainer = document.querySelector('.comment-form-container');

                // 重置父评论ID
                if (parentInput) {
                    parentInput.value = '0';
                }

                // 隐藏"回复给xxx"的提示
                if (commentReplyTo) {
                    commentReplyTo.style.display = 'none';
                }

                // 隐藏取消回复按钮
                this.style.display = 'none';

                // 将评论表单移回原位置
                if (commentForm && commentContainer) {
                    commentContainer.appendChild(commentForm);
                }
            });
        }

        // 显示消息提示
        function showToast(message, duration = 3000) {
            // 检查是否已存在toast元素
            let toast = document.getElementById('toast-message');

            if (!toast) {
                // 创建toast元素
                toast = document.createElement('div');
                toast.id = 'toast-message';
                document.body.appendChild(toast);
            }

            // 设置消息内容
            toast.textContent = message;
            toast.classList.add('active');

            // 自动关闭
            setTimeout(function() {
                toast.classList.remove('active');
            }, duration);
        }

        // 初始化图标失败检查
        setTimeout(function() {
            // 检查图标是否正常显示
            var iconElement = document.querySelector('.ri-search-line');
            if (iconElement) {
                var style = window.getComputedStyle(iconElement, ':before');
                if (!style || !style.content || style.content === 'none' || style.content === '' || style.content === 'normal') {
                    // 图标未正常加载，应用备用方案
                    document.body.classList.add('icon-fallback');
                    console.log('图标加载失败，已启用备用方案');
                }
            }
        }, 1000);

        // 文章分享功能
        const shareBtn = document.getElementById('share-btn');
        const shareModal = document.getElementById('share-modal');
        const shareClose = document.getElementById('share-close');
        const copyLink = document.getElementById('copy-link');

        if (shareBtn && shareModal) {
            shareBtn.addEventListener('click', function() {
                shareModal.classList.add('active');
            });
        }

        if (shareClose && shareModal) {
            shareClose.addEventListener('click', function() {
                shareModal.classList.remove('active');
            });

            // 点击模态框外部关闭
            shareModal.addEventListener('click', function(e) {
                if (e.target === shareModal) {
                    shareModal.classList.remove('active');
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && shareModal.classList.contains('active')) {
                    shareModal.classList.remove('active');
                }
            });
        }

        // 分享到社交媒体
        const shareItems = document.querySelectorAll('.share-item');
        shareItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                const url = encodeURIComponent(window.location.href);
                const title = encodeURIComponent(document.title);

                if (type === 'weibo') {
                    window.open('https://service.weibo.com/share/share.php?url=' + url + '&title=' + title);
                } else if (type === 'qq') {
                    window.open('https://connect.qq.com/widget/shareqq/index.html?url=' + url + '&title=' + title);
                } else if (type === 'twitter') {
                    window.open('https://twitter.com/intent/tweet?url=' + url + '&text=' + title);
                } else if (type === 'facebook') {
                    window.open('https://www.facebook.com/sharer/sharer.php?u=' + url);
                } else if (type === 'wechat') {
                    // 微信分享需要生成二维码，这里简化处理
                    alert('请打开微信，使用"扫一扫"，扫描当前页面网址');
                }

                // 关闭分享弹窗
                if (shareModal) {
                    shareModal.classList.remove('active');
                }
            });
        });

        // 复制链接功能
        if (copyLink) {
            copyLink.addEventListener('click', function() {
                // 使用现代Clipboard API
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(window.location.href)
                        .then(() => {
                            // 显示复制成功提示
                            showToast('链接已复制到剪贴板');

                            // 关闭分享弹窗
                            if (shareModal) {
                                shareModal.classList.remove('active');
                            }
                        })
                        .catch(err => {
                            console.error('复制失败:', err);
                            showToast('复制失败，请手动复制');
                        });
                } else {
                    // 兼容旧版浏览器
                    try {
                        const tempInput = document.createElement('input');
                        tempInput.value = window.location.href;
                        tempInput.style.position = 'absolute';
                        tempInput.style.left = '-9999px';
                        document.body.appendChild(tempInput);
                        tempInput.select();
                        document.execCommand('copy');
                        document.body.removeChild(tempInput);

                        // 显示复制成功提示
                        showToast('链接已复制到剪贴板');

                        // 关闭分享弹窗
                        if (shareModal) {
                            shareModal.classList.remove('active');
                        }
                    } catch (err) {
                        console.error('复制失败:', err);
                        showToast('复制失败，请手动复制');
                    }
                }
            });
        }

        // 链接预加载
        if ('IntersectionObserver' in window) {
            const linkObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const link = entry.target;
                        const href = link.getAttribute('href');

                        if (href && href.startsWith(typechoConfig.siteUrl) && !href.includes('#')) {
                            // 创建预加载链接
                            const preloadLink = document.createElement('link');
                            preloadLink.rel = 'prefetch';
                            preloadLink.href = href;
                            document.head.appendChild(preloadLink);

                            // 取消观察
                            linkObserver.unobserve(link);
                        }
                    }
                });
            }, {
                threshold: 0.1
            });

            // 观察首页卡片、导航和其他重要链接
            const importantLinks = document.querySelectorAll('.nav-links a, .recommend-card .card-title a, .hot-post-link');
            importantLinks.forEach(link => {
                linkObserver.observe(link);
            });
        }
        // 页面加载指示器
        var loadingIndicator = document.getElementById('page-loading-indicator');

        if (loadingIndicator) {
            // 页面加载完成后隐藏
            window.addEventListener('load', function() {
                setTimeout(function() {
                    loadingIndicator.classList.remove('active');
                }, 500);
            });

            // 页面链接点击时显示
            document.addEventListener('click', function(e) {
                var target = e.target;

                // 查找最近的a标签
                while (target && target.tagName !== 'A') {
                    target = target.parentNode;
                    if (!target || target === document) {
                        return;
                    }
                }

                // 检查是否是站内链接且不是锚点链接
                var href = target.getAttribute('href');
                if (href && href.indexOf(typechoConfig.siteUrl) === 0 && href.indexOf('#') === -1) {
                    loadingIndicator.classList.add('active');
                }
            });
        }

        // 显示Toast消息的函数
        window.showToast = function(message, duration) {
            duration = duration || 3000;

            var toast = document.getElementById('toast-message');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-message';
                document.body.appendChild(toast);
            }

            toast.textContent = message;
            toast.classList.add('active');

            setTimeout(function() {
                toast.classList.remove('active');
            }, duration);
        };

        // 平滑滚动到顶部
        var scrollTopBtn = document.getElementById('scroll-top');
        if (scrollTopBtn) {
            scrollTopBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // 滚动超过一定距离显示回到顶部按钮
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    scrollTopBtn.classList.add('active');
                } else {
                    scrollTopBtn.classList.remove('active');
                }
            });
        }
    });
})();