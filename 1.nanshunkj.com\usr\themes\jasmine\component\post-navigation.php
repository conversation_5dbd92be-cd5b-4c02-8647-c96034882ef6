<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<div class="post-navigation grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
    <?php $this->thePrev('
    <div class="nav-card bg-white dark:bg-[#161829] rounded-lg p-4 border border-stone-100 dark:border-neutral-600 transition duration-300">
        <div class="text-sm text-gray-500 dark:text-gray-400 mb-2 flex items-center">
            上一篇 <span class="emoji ml-1">⬅️</span>
        </div>
        <div class="nav-title text-base font-medium text-black dark:text-gray-300 line-clamp-1 hover:text-blue-500 dark:hover:text-blue-400">
            %s
        </div>
    </div>
    ', '
    <div class="nav-card bg-white dark:bg-[#161829] rounded-lg p-4 border border-stone-100 dark:border-neutral-600 opacity-50">
        <div class="text-sm text-gray-500 dark:text-gray-400 mb-2 flex items-center">
            上一篇 <span class="emoji ml-1">⬅️</span>
        </div>
        <div class="nav-title text-base font-medium text-gray-400 dark:text-gray-500">
            没有更多了
        </div>
    </div>
    '); ?>

    <?php $this->theNext('
    <div class="nav-card bg-white dark:bg-[#161829] rounded-lg p-4 border border-stone-100 dark:border-neutral-600 transition duration-300">
        <div class="text-sm text-gray-500 dark:text-gray-400 mb-2 flex items-center">
            <span class="emoji mr-1">➡️</span> 下一篇
        </div>
        <div class="nav-title text-base font-medium text-black dark:text-gray-300 line-clamp-1 hover:text-blue-500 dark:hover:text-blue-400">
            %s
        </div>
    </div>
    ', '
    <div class="nav-card bg-white dark:bg-[#161829] rounded-lg p-4 border border-stone-100 dark:border-neutral-600 opacity-50">
        <div class="text-sm text-gray-500 dark:text-gray-400 mb-2 flex items-center">
            <span class="emoji mr-1">➡️</span> 下一篇
        </div>
        <div class="nav-title text-base font-medium text-gray-400 dark:text-gray-500">
            没有更多了
        </div>
    </div>
    '); ?>
</div>