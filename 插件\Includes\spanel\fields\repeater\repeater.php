<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.
/**
 *
 * Field: repeater
 *
 * <AUTHOR> Studio
 * @Uri https://shilin.studio
 *
 */
if ( ! class_exists( 'Shilin_Field_repeater' ) ) {
  class Shilin_Field_repeater extends Shilin_Fields {

    public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
      parent::__construct( $field, $value, $unique, $where, $parent );
    }

    public function render() {

      $args = wp_parse_args( $this->field, array(
        'max'          => 0,
        'min'          => 0,
        'button_title' => '<i class="fas fa-plus-circle"></i>',
      ) );

      if ( preg_match( '/'. preg_quote( '['. $this->field['id'] .']' ) .'/', $this->unique ) ) {

        echo '<div class="shilin-notice shilin-notice-danger">'. esc_html__( 'Error: Field ID conflict.', 'shilin' ) .'</div>';

      } else {

        echo $this->field_before();

        echo '<div class="shilin-repeater-item shilin-repeater-hidden" data-depend-id="'. esc_attr( $this->field['id'] ) .'">';
        echo '<div class="shilin-repeater-content">';
        foreach ( $this->field['fields'] as $field ) {

          $field_default = ( isset( $field['default'] ) ) ? $field['default'] : '';
          $field_unique  = ( ! empty( $this->unique ) ) ? $this->unique .'['. $this->field['id'] .'][0]' : $this->field['id'] .'[0]';

          SHILIN::field( $field, $field_default, '___'. $field_unique, 'field/repeater' );

        }
        echo '</div>';
        echo '<div class="shilin-repeater-helper">';
        echo '<div class="shilin-repeater-helper-inner">';
        echo '<i class="shilin-repeater-sort fas fa-arrows-alt"></i>';
        echo '<i class="shilin-repeater-clone far fa-clone"></i>';
        echo '<i class="shilin-repeater-remove shilin-confirm fas fa-times" data-confirm="'. esc_html__( 'Are you sure to delete this item?', 'shilin' ) .'"></i>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '<div class="shilin-repeater-wrapper shilin-data-wrapper" data-field-id="['. esc_attr( $this->field['id'] ) .']" data-max="'. esc_attr( $args['max'] ) .'" data-min="'. esc_attr( $args['min'] ) .'">';

        if ( ! empty( $this->value ) && is_array( $this->value ) ) {

          $num = 0;

          foreach ( $this->value as $key => $value ) {

            echo '<div class="shilin-repeater-item">';
            echo '<div class="shilin-repeater-content">';
            foreach ( $this->field['fields'] as $field ) {

              $field_unique = ( ! empty( $this->unique ) ) ? $this->unique .'['. $this->field['id'] .']['. $num .']' : $this->field['id'] .'['. $num .']';
              $field_value  = ( isset( $field['id'] ) && isset( $this->value[$key][$field['id']] ) ) ? $this->value[$key][$field['id']] : '';

              SHILIN::field( $field, $field_value, $field_unique, 'field/repeater' );

            }
            echo '</div>';
            echo '<div class="shilin-repeater-helper">';
            echo '<div class="shilin-repeater-helper-inner">';
            echo '<i class="shilin-repeater-sort fas fa-arrows-alt"></i>';
            echo '<i class="shilin-repeater-clone far fa-clone"></i>';
            echo '<i class="shilin-repeater-remove shilin-confirm fas fa-times" data-confirm="'. esc_html__( 'Are you sure to delete this item?', 'shilin' ) .'"></i>';
            echo '</div>';
            echo '</div>';
            echo '</div>';

            $num++;

          }

        }

        echo '</div>';

        echo '<div class="shilin-repeater-alert shilin-repeater-max">'. esc_html__( 'You cannot add more.', 'shilin' ) .'</div>';
        echo '<div class="shilin-repeater-alert shilin-repeater-min">'. esc_html__( 'You cannot remove more.', 'shilin' ) .'</div>';
        echo '<a href="#" class="button button-primary shilin-repeater-add">'. $args['button_title'] .'</a>';

        echo $this->field_after();

      }

    }

    public function enqueue() {

      if ( ! wp_script_is( 'jquery-ui-sortable' ) ) {
        wp_enqueue_script( 'jquery-ui-sortable' );
      }

    }

  }
}
