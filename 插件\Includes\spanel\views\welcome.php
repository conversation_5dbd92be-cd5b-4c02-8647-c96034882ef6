<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.
/**
 *
 * Setup Framework Class
 *
 * <AUTHOR> Studio
 * @Uri https://shilin.studio
 *
 */
if ( ! class_exists( 'Shilin_Welcome' ) ) {
  class Shilin_Welcome{

    private static $instance = null;

    public function __construct() {

      if ( SHILIN::$premium && ( ! SHILIN::is_active_plugin( 'ShilinFramework/ShilinFramework.php' ) || apply_filters( 'shilin_welcome_page', true ) === false ) ) { return; }

      add_action( 'admin_menu', array( $this, 'add_about_menu' ), 0 );
      add_filter( 'plugin_action_links', array( $this, 'add_plugin_action_links' ), 10, 5 );
      add_filter( 'plugin_row_meta', array( $this, 'add_plugin_row_meta' ), 10, 2 );

      $this->set_demo_mode();

    }

    // instance
    public static function instance() {
      if ( is_null( self::$instance ) ) {
        self::$instance = new self();
      }
      return self::$instance;
    }

    public function add_about_menu() {
      add_management_page( 'Shilin Framework', 'Shilin Framework', 'manage_options', 'shilin-welcome', array( $this, 'add_page_welcome' ) );
    }

    public function add_page_welcome() {

      $section = ( ! empty( $_GET['section'] ) ) ? sanitize_text_field( wp_unslash( $_GET['section'] ) ) : '';

      SHILIN::include_plugin_file( 'views/header.php' );

      // safely include pages
      switch ( $section ) {

        case 'quickstart':
          SHILIN::include_plugin_file( 'views/quickstart.php' );
        break;

        case 'documentation':
          SHILIN::include_plugin_file( 'views/documentation.php' );
        break;

        case 'relnotes':
          SHILIN::include_plugin_file( 'views/relnotes.php' );
        break;

        case 'support':
          SHILIN::include_plugin_file( 'views/support.php' );
        break;

        case 'free-vs-premium':
          SHILIN::include_plugin_file( 'views/free-vs-premium.php' );
        break;

        default:
          SHILIN::include_plugin_file( 'views/about.php' );
        break;

      }

      SHILIN::include_plugin_file( 'views/footer.php' );

    }

    public static function add_plugin_action_links( $links, $plugin_file ) {

      if ( $plugin_file === 'ShilinFramework/ShilinFramework.php' && ! empty( $links ) ) {
        $links['shilin--welcome'] = '<a href="'. esc_url( admin_url( 'tools.php?page=shilin-welcome' ) ) .'">Settings</a>';
        if ( ! SHILIN::$premium ) {
          $links['shilin--upgrade'] = '<a href="https://shilin.studio/">Upgrade</a>';
        }
      }

      return $links;

    }

    public static function add_plugin_row_meta( $links, $plugin_file ) {

      if ( $plugin_file === 'ShilinFramework/ShilinFramework.php' && ! empty( $links ) ) {
        $links['shilin--docs'] = '<a href="http://shilin.studio/wpdoc/" target="_blank">文档</a>';
      }

      return $links;

    }

    public function set_demo_mode() {

      $demo_mode = get_option( 'shilin_demo_mode', false );

      $demo_activate = ( ! empty( $_GET[ 'shilin-demo' ] ) ) ? sanitize_text_field( wp_unslash( $_GET[ 'shilin-demo' ] ) ) : '';

      if ( ! empty( $demo_activate ) ) {

        $demo_mode = ( $demo_activate === 'activate' ) ? true : false;

        update_option( 'shilin_demo_mode', $demo_mode );

      }

      if ( ! empty( $demo_mode ) ) {

        SHILIN::include_plugin_file( 'samples/admin-options.php' );

        if ( SHILIN::$premium ) {

          SHILIN::include_plugin_file( 'samples/customize-options.php' );
          SHILIN::include_plugin_file( 'samples/metabox-options.php'   );
          SHILIN::include_plugin_file( 'samples/nav-menu-options.php'  );
          SHILIN::include_plugin_file( 'samples/profile-options.php'   );
          SHILIN::include_plugin_file( 'samples/shortcode-options.php' );
          SHILIN::include_plugin_file( 'samples/taxonomy-options.php'  );
          SHILIN::include_plugin_file( 'samples/widget-options.php'    );
          SHILIN::include_plugin_file( 'samples/comment-options.php'   );

        }

      }

    }

  }

  Shilin_Welcome::instance();
}
