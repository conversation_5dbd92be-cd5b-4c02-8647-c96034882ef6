<?php
/**
 * 订单管理系统
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

/**
 * 创建订单表（如果不存在）
 * 需要在主题激活时调用此函数
 */
function createOrdersTable() {
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();
    
    // 检查订单表是否已存在
    $tables = $db->fetchAll($db->query("SHOW TABLES LIKE '{$prefix}ww_orders'"));
    if (empty($tables)) {
        // 创建订单表
        $db->query("CREATE TABLE IF NOT EXISTS `{$prefix}ww_orders` (
            `id` varchar(32) NOT NULL COMMENT '订单ID',
            `uid` int(10) unsigned NOT NULL COMMENT '用户ID',
            `type` varchar(20) NOT NULL COMMENT '订单类型：member(会员购买)、content(内容购买)',
            `item_id` int(10) unsigned DEFAULT NULL COMMENT '商品ID（如内容ID）',
            `title` varchar(200) NOT NULL COMMENT '商品名称',
            `amount` decimal(10,2) NOT NULL COMMENT '金额',
            `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
            `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending(待支付)、paid(已支付)、failed(失败)、refunded(已退款)',
            `created` int(10) unsigned NOT NULL COMMENT '创建时间',
            `paid_time` int(10) unsigned DEFAULT NULL COMMENT '支付时间',
            `expire_time` int(10) unsigned DEFAULT NULL COMMENT '过期时间',
            `transaction_id` varchar(64) DEFAULT NULL COMMENT '支付交易号',
            `memo` text COMMENT '备注',
            PRIMARY KEY (`id`),
            KEY `uid` (`uid`),
            KEY `status` (`status`),
            KEY `type` (`type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';");
        
        return true;
    }
    
    return false;
}

/**
 * 创建订单
 * 
 * @param array $orderData 订单数据
 * @return string|bool 成功返回订单ID，失败返回false
 */
function createOrder($orderData) {
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();
    
    // 验证必须字段
    $requiredFields = ['uid', 'type', 'title', 'amount'];
    foreach ($requiredFields as $field) {
        if (!isset($orderData[$field]) || empty($orderData[$field])) {
            return false;
        }
    }
    
    // 生成订单ID
    $orderId = 'ORD' . date('YmdHis') . rand(1000, 9999);
    
    // 准备数据
    $orderData['id'] = $orderId;
    $orderData['created'] = time();
    $orderData['status'] = 'pending';
    
    // 插入订单记录
    try {
        $db->query($db->insert('table.ww_orders')->rows($orderData));
        return $orderId;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 获取订单信息
 * 
 * @param string $orderId 订单ID
 * @return array|false 成功返回订单数据，失败返回false
 */
function getOrder($orderId) {
    $db = Typecho_Db::get();
    
    try {
        $order = $db->fetchRow($db->select()->from('table.ww_orders')->where('id = ?', $orderId));
        return $order ? $order : false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 更新订单状态
 * 
 * @param string $orderId 订单ID
 * @param string $status 订单状态
 * @param array $extraData 额外数据
 * @return bool 成功返回true，失败返回false
 */
function updateOrderStatus($orderId, $status, $extraData = []) {
    $db = Typecho_Db::get();
    
    $data = ['status' => $status];
    
    // 如果状态是已支付，记录支付时间
    if ($status === 'paid') {
        $data['paid_time'] = time();
    }
    
    // 合并额外数据
    if (!empty($extraData)) {
        $data = array_merge($data, $extraData);
    }
    
    try {
        $db->query($db->update('table.ww_orders')->rows($data)->where('id = ?', $orderId));
        
        // 如果状态是已支付，处理会员或内容购买
        if ($status === 'paid') {
            handlePaidOrder($orderId);
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 处理已支付订单
 * 
 * @param string $orderId 订单ID
 * @return bool 成功返回true，失败返回false
 */
function handlePaidOrder($orderId) {
    $order = getOrder($orderId);
    if (!$order) {
        return false;
    }
    
    // 根据订单类型处理不同的购买
    if ($order['type'] === 'member') {
        // 处理会员购买
        $level = '';
        $duration = 0;
        
        if (strpos($order['title'], '月度会员') !== false) {
            $level = 'monthly';
            $duration = 30; // 30天
        } elseif (strpos($order['title'], '年度会员') !== false) {
            $level = 'yearly';
            $duration = 365; // 365天
        } elseif (strpos($order['title'], '永久会员') !== false) {
            $level = 'lifetime';
            $duration = 0; // 永久
        }
        
        if ($level) {
            return setUserMemberStatus($order['uid'], $level, $duration);
        }
    } elseif ($order['type'] === 'content') {
        // 处理内容购买
        if ($order['item_id']) {
            return addUserPurchaseRecord($order['item_id'], $order['uid'], $order['amount']);
        }
    }
    
    return false;
}

/**
 * 获取用户订单列表
 * 
 * @param int $uid 用户ID
 * @param string $status 订单状态，为空则获取所有状态
 * @param int $page 页码
 * @param int $pageSize 每页数量
 * @return array 订单列表和分页信息
 */
function getUserOrders($uid, $status = '', $page = 1, $pageSize = 10) {
    $db = Typecho_Db::get();
    
    // 构建查询
    $select = $db->select()->from('table.ww_orders')
        ->where('uid = ?', $uid)
        ->order('created', Typecho_Db::SORT_DESC);
    
    // 如果指定了状态，添加状态条件
    if ($status) {
        $select->where('status = ?', $status);
    }
    
    // 计算总数
    $totalCount = $db->fetchObject($db->select(array('COUNT(*)' => 'num'))->from('table.ww_orders')
        ->where('uid = ?', $uid)
        ->where($status ? 'status = ?' : '1=1', $status))->num;
    
    // 分页
    $offset = ($page - 1) * $pageSize;
    $select->limit($pageSize, $offset);
    
    // 查询订单
    $orders = $db->fetchAll($select);
    
    return [
        'orders' => $orders,
        'page' => $page,
        'pageSize' => $pageSize,
        'totalCount' => $totalCount,
        'totalPages' => ceil($totalCount / $pageSize)
    ];
}

/**
 * 获取所有订单（管理员使用）
 * 
 * @param string $type 订单类型，为空则获取所有类型
 * @param string $status 订单状态，为空则获取所有状态
 * @param int $page 页码
 * @param int $pageSize 每页数量
 * @param string $keyword 搜索关键词
 * @return array 订单列表和分页信息
 */
function getAllOrders($type = '', $status = '', $page = 1, $pageSize = 10, $keyword = '') {
    $db = Typecho_Db::get();
    
    // 构建查询
    $select = $db->select('o.*', 'u.name AS username', 'u.screenName')
        ->from('table.ww_orders AS o')
        ->join('table.users AS u', 'o.uid = u.uid', Typecho_Db::LEFT_JOIN)
        ->order('o.created', Typecho_Db::SORT_DESC);
    
    // 条件过滤
    $conditions = [];
    $params = [];
    
    if ($type) {
        $conditions[] = 'o.type = ?';
        $params[] = $type;
    }
    
    if ($status) {
        $conditions[] = 'o.status = ?';
        $params[] = $status;
    }
    
    if ($keyword) {
        $conditions[] = '(o.id LIKE ? OR u.name LIKE ? OR u.screenName LIKE ? OR o.title LIKE ?)';
        $params[] = "%{$keyword}%";
        $params[] = "%{$keyword}%";
        $params[] = "%{$keyword}%";
        $params[] = "%{$keyword}%";
    }
    
    // 应用条件
    if (!empty($conditions)) {
        $where = implode(' AND ', $conditions);
        $select->where($where, ...$params);
    }
    
    // 计算总数
    $countSelect = clone $select;
    $countSelect->clearFields()->clearOrder()->clearLimit();
    $countSelect->select(array('COUNT(DISTINCT o.id)' => 'num'));
    $totalCount = $db->fetchObject($countSelect)->num;
    
    // 分页
    $offset = ($page - 1) * $pageSize;
    $select->limit($pageSize, $offset);
    
    // 查询订单
    $orders = $db->fetchAll($select);
    
    return [
        'orders' => $orders,
        'page' => $page,
        'pageSize' => $pageSize,
        'totalCount' => $totalCount,
        'totalPages' => ceil($totalCount / $pageSize)
    ];
}

/**
 * 创建会员购买订单
 * 
 * @param int $uid 用户ID
 * @param string $level 会员等级
 * @param float $amount 金额
 * @return string|bool 成功返回订单ID，失败返回false
 */
function createMemberOrder($uid, $level, $amount) {
    $title = '';
    
    switch ($level) {
        case 'monthly':
            $title = '月度会员';
            break;
        case 'yearly':
            $title = '年度会员';
            break;
        case 'lifetime':
            $title = '永久会员';
            break;
        default:
            return false;
    }
    
    $orderData = [
        'uid' => $uid,
        'type' => 'member',
        'title' => $title,
        'amount' => $amount
    ];
    
    return createOrder($orderData);
}

/**
 * 创建内容购买订单
 * 
 * @param int $uid 用户ID
 * @param int $contentId 内容ID
 * @param string $title 内容标题
 * @param float $amount 金额
 * @return string|bool 成功返回订单ID，失败返回false
 */
function createContentOrder($uid, $contentId, $title, $amount) {
    $orderData = [
        'uid' => $uid,
        'type' => 'content',
        'item_id' => $contentId,
        'title' => $title,
        'amount' => $amount
    ];
    
    return createOrder($orderData);
}

/**
 * 检查订单是否已支付
 * 
 * @param string $orderId 订单ID
 * @return bool 是否已支付
 */
function isOrderPaid($orderId) {
    $order = getOrder($orderId);
    return $order && $order['status'] === 'paid';
}

/**
 * 模拟支付回调处理
 * 仅用于演示，实际支付回调需要进行签名验证等安全处理
 * 
 * @param string $orderId 订单ID
 * @param string $transactionId 支付交易号
 * @return bool 处理结果
 */
function handlePaymentCallback($orderId, $transactionId) {
    // 更新订单状态为已支付
    return updateOrderStatus($orderId, 'paid', [
        'transaction_id' => $transactionId
    ]);
}

/**
 * 获取订单统计信息
 * 
 * @return array 统计信息
 */
function getOrdersStats() {
    $db = Typecho_Db::get();
    
    // 获取总订单数
    $totalOrders = $db->fetchObject($db->select(array('COUNT(*)' => 'num'))->from('table.ww_orders'))->num;
    
    // 获取已支付订单数
    $paidOrders = $db->fetchObject($db->select(array('COUNT(*)' => 'num'))->from('table.ww_orders')->where('status = ?', 'paid'))->num;
    
    // 获取总收入
    $totalIncome = $db->fetchObject($db->select(array('SUM(amount)' => 'total'))->from('table.ww_orders')->where('status = ?', 'paid'))->total;
    
    // 获取会员订单数量
    $memberOrders = $db->fetchObject($db->select(array('COUNT(*)' => 'num'))->from('table.ww_orders')->where('type = ?', 'member')->where('status = ?', 'paid'))->num;
    
    // 获取内容购买订单数量
    $contentOrders = $db->fetchObject($db->select(array('COUNT(*)' => 'num'))->from('table.ww_orders')->where('type = ?', 'content')->where('status = ?', 'paid'))->num;
    
    // 获取当日收入
    $today = strtotime(date('Y-m-d'));
    $todayIncome = $db->fetchObject($db->select(array('SUM(amount)' => 'total'))->from('table.ww_orders')->where('status = ?', 'paid')->where('paid_time >= ?', $today))->total;
    
    return [
        'totalOrders' => $totalOrders,
        'paidOrders' => $paidOrders,
        'totalIncome' => $totalIncome ?: 0,
        'memberOrders' => $memberOrders,
        'contentOrders' => $contentOrders,
        'todayIncome' => $todayIncome ?: 0
    ];
} 