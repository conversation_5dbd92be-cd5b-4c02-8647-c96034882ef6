@echo off
chcp 65001 >nul
echo ===============================================
echo     抖音聊天自动回复工具 - 环境配置脚本
echo ===============================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Python环境！
    echo 📋 请先安装Python 3.7+版本
    echo 🔗 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python环境
python --version

echo.
echo 🔄 正在创建虚拟环境...
python -m venv venv
if %errorlevel% neq 0 (
    echo ❌ 虚拟环境创建失败！
    pause
    exit /b 1
)

echo ✅ 虚拟环境创建成功

echo.
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 🔄 升级pip...
python -m pip install --upgrade pip

echo.
echo 🔄 安装项目依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败！
    echo 💡 请检查网络连接或尝试使用国内镜像源
    echo 💡 命令：pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    pause
    exit /b 1
)

echo.
echo ✅ 所有依赖包安装完成！

echo.
echo 📋 接下来需要手动安装Tesseract OCR：
echo 1️⃣  下载Tesseract：https://github.com/UB-Mannheim/tesseract/wiki
echo 2️⃣  安装时选择中文语言包 (chi_sim)
echo 3️⃣  记住安装路径，稍后需要配置

echo.
echo 🎉 环境配置完成！
echo.
echo 📖 使用方法：
echo 1. 确保已安装Tesseract OCR
echo 2. 运行: python main.py
echo 3. 或者双击运行: run.bat
echo.
pause 