import{_ as De,o as sA,c as iA,a as J,t as vA,n as SA,b as qe,d as RA,p as jt,e as qt,u as JA,f as Ki,g as Si,h as $A,i as Di,j as en,k as xr,r as DA,F as Te,l as Ti,m as hA,w as dn,q as gs,s as ki,v as Oi,x as Mt,y as se,z as tn,A as Ri,B as lA,C as eA,D as aA,E as nt,G as le,H as st,I as rn,J as ws,K as Qs,L as ps,M as Cs,R as Mi,N as Gi,O as Ni,P as Vi}from"./index-50feddeb.js";import{e as gn}from"./emojiBase64-09fa9685.js";const Pi=r=>(jt("data-v-73391f5e"),r=r(),qt(),r),Xi={class:"phone-time"},Wi={key:1,class:"phone-no-wifi"},Ji={class:"battery-box"},<PERSON>=Pi(()=>J("i",null,null,-1)),zi={__name:"PhoneBar",props:{appearance:{type:Object,default:()=>{}}},setup(r){return(e,A)=>(sA(),iA("div",{class:SA(["phone-bar",{dark:r.appearance.darkMode}])},[J("div",Xi,vA(r.appearance.phoneTimeHour)+":"+vA(r.appearance.phoneTimeMinute),1),J("div",{class:SA(["phone-sigle",[`phone-sigle-v${r.appearance.phoneSignal}-${r.appearance.darkMode?"dark":"light"}`]])},"信号",2),r.appearance.networkType==="wifi"?(sA(),iA("div",{key:0,class:SA(["phone-wifi",[`phone-wifi-v${r.appearance.wifiSignal}-${r.appearance.darkMode?"dark":"light"}`]])},"wifi",2)):(sA(),iA("div",Wi,vA(r.appearance.networkType)+"G",1)),J("div",{class:SA(["phone-battery",{"phone-battery-charge":r.appearance.isCharging,dark:r.appearance.darkMode}])},[J("span",Ji,[J("span",{class:"battery-width",style:qe({width:r.appearance.phoneBattery+"%"})},"电量",4),RA(),Yi])],2)],2))}},Zi=De(zi,[["__scopeId","data-v-73391f5e"]]);const Us=r=>(jt("data-v-ca50082f"),r=r(),qt(),r),ji={class:"phone-nav-left"},qi=Us(()=>J("div",{class:"phone-nav-back"},"返回",-1)),$i={key:0},Aa={class:"phone-nav-center"},ea={class:"center-span"},ta={class:"user-name"},ra={key:0},na=Us(()=>J("div",{class:"phone-nav-right"},[J("div",{class:"phone-nav-more"},"更多")],-1)),sa={__name:"PhoneNav",props:{appearance:{type:Object,default:()=>{}}},setup(r){const{useSystemStore:e}=JA(),A=Ki(()=>Si(e.appearance.chatTitle,14));return(t,n)=>(sA(),iA("div",{class:SA(["phone-nav",{dark:r.appearance.darkMode}])},[J("div",ji,[qi,r.appearance.unreadMessages?(sA(),iA("span",$i,vA(r.appearance.unreadMessages>99?"99+":r.appearance.unreadMessages),1)):$A("",!0)]),J("div",Aa,[J("span",ea,[J("span",ta,vA(A.value),1),RA(),r.appearance.earphoneMode?(sA(),iA("i",ra)):$A("",!0)])]),na],2))}},ia=De(sa,[["__scopeId","data-v-ca50082f"]]);JA();function aa(r){Di(()=>{e(),en.on("sentChat",()=>{e()})});const e=()=>{!r||!r.value||xr(()=>{r.value.scrollTop=r.value.scrollHeight})};return{toBottom:e}}const YA=r=>(jt("data-v-bae0c80d"),r=r(),qt(),r),oa=["onContextmenu"],Ba={class:"wechat-content"},ca=["id","onContextmenu"],la={key:0,class:"wechat-item-avatar"},ua=["src"],fa={key:0,class:"wechat-item-name"},ha=["innerHTML"],da={key:2,class:"wechat-item-text wechat-item-image"},ga=["src"],wa={class:"wechat-item-trans-content"},Qa=YA(()=>J("i",null,null,-1)),pa={key:0,class:"font"},Ca={key:1,class:"font"},Ua={key:2,class:"font"},Fa=YA(()=>J("div",{class:"wechat-item-trans-bottom"},[J("span",null,"微信转账")],-1)),ma={class:"wechat-item-trans-content wechat-item-redp-content"},va=YA(()=>J("i",null,null,-1)),Ea={key:0,class:"font"},ya=YA(()=>J("div",{class:"wechat-item-trans-bottom"},[J("span",null,"微信红包")],-1)),Ha={key:5,class:"wechat-item-notice"},Ia=YA(()=>J("i",null,null,-1)),_a=YA(()=>J("em",null,"红包",-1)),ba={key:6,class:"wechat-item-text wechat-item-trans wechat-item-trans-received"},xa={class:"wechat-item-trans-content"},La=YA(()=>J("i",null,null,-1)),Ka=YA(()=>J("span",{class:"font"},"已收款",-1)),Sa=YA(()=>J("div",{class:"wechat-item-trans-bottom"},[J("span",null,"微信转账")],-1)),Da={key:7,class:"wechat-item-voice-wrapper"},Ta={class:"wechat-item-text wechat-item-voice"},ka=YA(()=>J("i",null,null,-1)),Oa={key:0},Ra={key:0,class:"wechat-item-text wechat-item-voice-text"},Ma={key:8,class:"wechat-item-text wechat-item-av"},Ga={key:0},Na={key:1},Va={key:9,class:"wechat-item-text wechat-item-businessCard"},Pa={class:"info"},Xa=["src"],Wa=YA(()=>J("span",null,"个人名片",-1)),Ja={key:0},Ya={key:1},za={__name:"PhoneBody",props:{appearance:{type:Object,default:()=>{}},emojiBase64:{type:Object,default:()=>{}}},setup(r){const{useSystemStore:e,useUserStore:A,useChatStore:t,useContextMenuStore:n}=JA(),s=c=>{c.preventDefault(),n.hideContextMenu(),n.activeChatId=""},i=(c,l)=>{c.preventDefault(),n.showContextMenu(c.clientX,c.clientY,l)},a=DA(null);aa(a);const o=c=>!["time","takeAPat","revoke","system"].includes(c.type)&&!(c.type==="receive"&&c.receivedChatType==="redEnvelope");return(c,l)=>(sA(),iA("div",{class:SA(["phone-body",{dark:r.appearance.darkMode}]),ref_key:"phoneBodyRef",ref:a,onContextmenu:dn(s,["stop"])},[J("div",Ba,[(sA(!0),iA(Te,null,Ti(hA(t).chatList,B=>(sA(),iA("div",{class:SA(["wechat-item",{"wechat-item-right":B.role==="own","wechat-item-rejected":B.role==="own"&&B.rejected,"wechat-item-notice-box":!o(B),active:hA(n).activeChatId===B.id}]),id:B.id,key:B.id,onContextmenu:dn(h=>i(h,B.id),["stop"])},[o(B)?(sA(),iA("div",la,[J("img",{src:B.user.avatar,alt:""},null,8,ua)])):$A("",!0),J("div",null,[hA(e).appearance.showChatName&&!["time","takeAPat","revoke","system"].includes(B.type)?(sA(),iA("div",fa,vA(B.user.nickname),1)):$A("",!0),B.type==="text"?(sA(),iA("div",{key:1,class:"wechat-item-text",innerHTML:hA(gs)(B.content,r.emojiBase64)},null,8,ha)):B.type==="image"?(sA(),iA("div",da,[J("img",{src:B.content,alt:""},null,8,ga)])):B.type==="transferAccounts"?(sA(),iA("div",{key:3,class:SA(["wechat-item-text wechat-item-trans",{"wechat-item-trans-received":B.received}])},[J("div",wa,[Qa,J("div",null,[J("span",null,"¥"+vA(B.money.toFixed(2)),1),B.received?(sA(),iA("span",pa,"已被领取")):B.content?(sA(),iA("span",Ua,vA(B.content),1)):(sA(),iA("span",Ca,"转账给"+vA(B.role==="own"?hA(A).activeUser.nickname:"你"),1))])]),Fa],2)):B.type==="redEnvelope"?(sA(),iA("div",{key:4,class:SA(["wechat-item-text wechat-item-trans",{"wechat-item-trans-received":B.received}])},[J("div",ma,[va,J("div",null,[J("span",null,vA(B.content||"恭喜发财，大吉大利"),1),B.received?(sA(),iA("span",Ea,"已领取")):$A("",!0)])]),ya],2)):B.type==="receive"&&B.receivedChatType==="redEnvelope"?(sA(),iA("div",Ha,[Ia,RA(vA(B.role==="own"?"你":hA(A).activeUser.nickname)+"领取了"+vA(B.role==="own"?hA(A).activeUser.nickname:"你")+"的",1),_a])):B.type==="receive"&&B.receivedChatType==="transferAccounts"?(sA(),iA("div",ba,[J("div",xa,[La,J("div",null,[J("span",null,"¥"+vA(B.money.toFixed(2)),1),Ka])]),Sa])):B.type==="voice"?(sA(),iA("div",Da,[J("div",Ta,[ka,J("span",null,vA(B.duration)+'"',1),J("div",{style:qe({width:B.duration*5+"px"})},null,4),B.received?$A("",!0):(sA(),iA("em",Oa))]),B.content?(sA(),iA("div",Ra,vA(B.content),1)):$A("",!0)])):B.type==="avInvite"?(sA(),iA("div",Ma,[J("i",{class:SA([B.invateType])},null,2),B.state==="success"?(sA(),iA("span",Ga,"通话时长 "+vA(B.duration),1)):(sA(),iA("span",Na,vA(B.role==="other"?"对方":"")+vA(hA(ki)(hA(Oi),B.state)),1))])):B.type==="businessCard"?(sA(),iA("div",Va,[J("div",Pa,[J("img",{src:B.image,alt:""},null,8,Xa),J("p",null,vA(B.content),1)]),Wa])):["time","takeAPat","revoke","system"].includes(B.type)?(sA(),iA("div",{key:10,class:SA(["wechat-item-notice bg",{bold:B.type==="takeAPat"&&B.patBold}])},[B.type!=="revoke"?(sA(),iA("span",Ja,vA(B.content),1)):(sA(),iA("span",Ya,vA(B.role==="own"?"你":hA(A).activeUser.nickname)+"撤回了一条消息",1))],2)):$A("",!0)])],42,ca))),128))])],42,oa))}},Za=De(za,[["__scopeId","data-v-bae0c80d"]]);const Fs=r=>(jt("data-v-7239f6c2"),r=r(),qt(),r),ja={class:"phone-bottom-chat"},qa=["innerHTML"],$a=Fs(()=>J("div",{class:"wechat-bottom-icon wechat-emoji-icon"},"表情",-1)),Ao=Fs(()=>J("div",{class:"phone-bottom-bar"},[J("i",null,"返回桌面")],-1)),eo={__name:"PhoneBottom",props:{appearance:{type:Object,default:()=>{}},emojiBase64:{type:Object,default:()=>{}}},setup(r){const e=r,{useChatStore:A,useUserStore:t}=JA(),n=DA("");return Mt(()=>[e.appearance.voiceMode,A.inputText,t.activeRole],()=>{e.appearance.voiceMode?n.value="按住 说话":t.activeRole==="other"?n.value="":n.value=gs(A.inputText,e.emojiBase64)},{immediate:!0}),(s,i)=>(sA(),iA("div",{class:SA(["phone-bottom",{dark:r.appearance.darkMode}])},[J("div",ja,[J("div",{class:SA(["wechat-bottom-icon",[r.appearance.voiceMode?"wechat-voice-say-icon":"wechat-voice-icon"]])}," 语音 ",2),J("div",{class:SA(["wechat-input",{"wechat-input-say":r.appearance.voiceMode,"wechat-input-sync":!r.appearance.voiceMode&&r.appearance.syncInputText}]),innerHTML:n.value},null,10,qa),$a,J("div",{class:SA(["wechat-bottom-icon",[!r.appearance.voiceMode&&r.appearance.syncInputText&&hA(A).inputText&&hA(t).activeRole==="own"?"wechat-bottom-sync":"wechat-more-icon"]])},"发送",2)]),Ao],2))}},to=De(eo,[["__scopeId","data-v-7239f6c2"]]);var ms={exports:{}};(function(r,e){(function(A,t){t()})(se,function(){function A(c,l){return typeof l>"u"?l={autoBom:!1}:typeof l!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),l={autoBom:!l}),l.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(c.type)?new Blob(["\uFEFF",c],{type:c.type}):c}function t(c,l,B){var h=new XMLHttpRequest;h.open("GET",c),h.responseType="blob",h.onload=function(){o(h.response,l,B)},h.onerror=function(){console.error("could not download file")},h.send()}function n(c){var l=new XMLHttpRequest;l.open("HEAD",c,!1);try{l.send()}catch{}return 200<=l.status&&299>=l.status}function s(c){try{c.dispatchEvent(new MouseEvent("click"))}catch{var l=document.createEvent("MouseEvents");l.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),c.dispatchEvent(l)}}var i=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof se=="object"&&se.global===se?se:void 0,a=i.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),o=i.saveAs||(typeof window!="object"||window!==i?function(){}:"download"in HTMLAnchorElement.prototype&&!a?function(c,l,B){var h=i.URL||i.webkitURL,u=document.createElement("a");l=l||c.name||"download",u.download=l,u.rel="noopener",typeof c=="string"?(u.href=c,u.origin===location.origin?s(u):n(u.href)?t(c,l,B):s(u,u.target="_blank")):(u.href=h.createObjectURL(c),setTimeout(function(){h.revokeObjectURL(u.href)},4e4),setTimeout(function(){s(u)},0))}:"msSaveOrOpenBlob"in navigator?function(c,l,B){if(l=l||c.name||"download",typeof c!="string")navigator.msSaveOrOpenBlob(A(c,B),l);else if(n(c))t(c,l,B);else{var h=document.createElement("a");h.href=c,h.target="_blank",setTimeout(function(){s(h)})}}:function(c,l,B,h){if(h=h||open("","_blank"),h&&(h.document.title=h.document.body.innerText="downloading..."),typeof c=="string")return t(c,l,B);var u=c.type==="application/octet-stream",p=/constructor/i.test(i.HTMLElement)||i.safari,f=/CriOS\/[\d]+/.test(navigator.userAgent);if((f||u&&p||a)&&typeof FileReader<"u"){var w=new FileReader;w.onloadend=function(){var F=w.result;F=f?F:F.replace(/^data:[^;]*;/,"data:attachment/file;"),h?h.location.href=F:location=F,h=null},w.readAsDataURL(c)}else{var g=i.URL||i.webkitURL,C=g.createObjectURL(c);h?h.location=C:location.href=C,h=null,setTimeout(function(){g.revokeObjectURL(C)},4e4)}});i.saveAs=o.saveAs=o,r.exports=o})})(ms);var ro=ms.exports;const no=tn(ro);/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Lr=function(r,e){return Lr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,t){A.__proto__=t}||function(A,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(A[n]=t[n])},Lr(r,e)};function zA(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");Lr(r,e);function A(){this.constructor=r}r.prototype=e===null?Object.create(e):(A.prototype=e.prototype,new A)}var Kr=function(){return Kr=Object.assign||function(e){for(var A,t=1,n=arguments.length;t<n;t++){A=arguments[t];for(var s in A)Object.prototype.hasOwnProperty.call(A,s)&&(e[s]=A[s])}return e},Kr.apply(this,arguments)};function TA(r,e,A,t){function n(s){return s instanceof A?s:new A(function(i){i(s)})}return new(A||(A=Promise))(function(s,i){function a(l){try{c(t.next(l))}catch(B){i(B)}}function o(l){try{c(t.throw(l))}catch(B){i(B)}}function c(l){l.done?s(l.value):n(l.value).then(a,o)}c((t=t.apply(r,e||[])).next())})}function LA(r,e){var A={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},t,n,s,i;return i={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function a(c){return function(l){return o([c,l])}}function o(c){if(t)throw new TypeError("Generator is already executing.");for(;A;)try{if(t=1,n&&(s=c[0]&2?n.return:c[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,c[1])).done)return s;switch(n=0,s&&(c=[c[0]&2,s.value]),c[0]){case 0:case 1:s=c;break;case 4:return A.label++,{value:c[1],done:!1};case 5:A.label++,n=c[1],c=[0];continue;case 7:c=A.ops.pop(),A.trys.pop();continue;default:if(s=A.trys,!(s=s.length>0&&s[s.length-1])&&(c[0]===6||c[0]===2)){A=0;continue}if(c[0]===3&&(!s||c[1]>s[0]&&c[1]<s[3])){A.label=c[1];break}if(c[0]===6&&A.label<s[1]){A.label=s[1],s=c;break}if(s&&A.label<s[2]){A.label=s[2],A.ops.push(c);break}s[2]&&A.ops.pop(),A.trys.pop();continue}c=e.call(r,A)}catch(l){c=[6,l],n=0}finally{t=s=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function ft(r,e,A){if(A||arguments.length===2)for(var t=0,n=e.length,s;t<n;t++)(s||!(t in e))&&(s||(s=Array.prototype.slice.call(e,0,t)),s[t]=e[t]);return r.concat(s||e)}var ae=function(){function r(e,A,t,n){this.left=e,this.top=A,this.width=t,this.height=n}return r.prototype.add=function(e,A,t,n){return new r(this.left+e,this.top+A,this.width+t,this.height+n)},r.fromClientRect=function(e,A){return new r(A.left+e.windowBounds.left,A.top+e.windowBounds.top,A.width,A.height)},r.fromDOMRectList=function(e,A){var t=Array.from(A).find(function(n){return n.width!==0});return t?new r(t.left+e.windowBounds.left,t.top+e.windowBounds.top,t.width,t.height):r.EMPTY},r.EMPTY=new r(0,0,0,0),r}(),$t=function(r,e){return ae.fromClientRect(r,e.getBoundingClientRect())},so=function(r){var e=r.body,A=r.documentElement;if(!e||!A)throw new Error("Unable to get document size");var t=Math.max(Math.max(e.scrollWidth,A.scrollWidth),Math.max(e.offsetWidth,A.offsetWidth),Math.max(e.clientWidth,A.clientWidth)),n=Math.max(Math.max(e.scrollHeight,A.scrollHeight),Math.max(e.offsetHeight,A.offsetHeight),Math.max(e.clientHeight,A.clientHeight));return new ae(0,0,t,n)},Ar=function(r){for(var e=[],A=0,t=r.length;A<t;){var n=r.charCodeAt(A++);if(n>=55296&&n<=56319&&A<t){var s=r.charCodeAt(A++);(s&64512)===56320?e.push(((n&1023)<<10)+(s&1023)+65536):(e.push(n),A--)}else e.push(n)}return e},EA=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,r);var A=r.length;if(!A)return"";for(var t=[],n=-1,s="";++n<A;){var i=r[n];i<=65535?t.push(i):(i-=65536,t.push((i>>10)+55296,i%1024+56320)),(n+1===A||t.length>16384)&&(s+=String.fromCharCode.apply(String,t),t.length=0)}return s},wn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",io=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var ht=0;ht<wn.length;ht++)io[wn.charCodeAt(ht)]=ht;var Qn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Xe=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var dt=0;dt<Qn.length;dt++)Xe[Qn.charCodeAt(dt)]=dt;var ao=function(r){var e=r.length*.75,A=r.length,t,n=0,s,i,a,o;r[r.length-1]==="="&&(e--,r[r.length-2]==="="&&e--);var c=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),l=Array.isArray(c)?c:new Uint8Array(c);for(t=0;t<A;t+=4)s=Xe[r.charCodeAt(t)],i=Xe[r.charCodeAt(t+1)],a=Xe[r.charCodeAt(t+2)],o=Xe[r.charCodeAt(t+3)],l[n++]=s<<2|i>>4,l[n++]=(i&15)<<4|a>>2,l[n++]=(a&3)<<6|o&63;return c},oo=function(r){for(var e=r.length,A=[],t=0;t<e;t+=2)A.push(r[t+1]<<8|r[t]);return A},Bo=function(r){for(var e=r.length,A=[],t=0;t<e;t+=4)A.push(r[t+3]<<24|r[t+2]<<16|r[t+1]<<8|r[t]);return A},Fe=5,nn=6+5,cr=2,co=nn-Fe,vs=65536>>Fe,lo=1<<Fe,lr=lo-1,uo=1024>>Fe,fo=vs+uo,ho=fo,go=32,wo=ho+go,Qo=65536>>nn,po=1<<co,Co=po-1,pn=function(r,e,A){return r.slice?r.slice(e,A):new Uint16Array(Array.prototype.slice.call(r,e,A))},Uo=function(r,e,A){return r.slice?r.slice(e,A):new Uint32Array(Array.prototype.slice.call(r,e,A))},Fo=function(r,e){var A=ao(r),t=Array.isArray(A)?Bo(A):new Uint32Array(A),n=Array.isArray(A)?oo(A):new Uint16Array(A),s=24,i=pn(n,s/2,t[4]/2),a=t[5]===2?pn(n,(s+t[4])/2):Uo(t,Math.ceil((s+t[4])/4));return new mo(t[0],t[1],t[2],t[3],i,a)},mo=function(){function r(e,A,t,n,s,i){this.initialValue=e,this.errorValue=A,this.highStart=t,this.highValueIndex=n,this.index=s,this.data=i}return r.prototype.get=function(e){var A;if(e>=0){if(e<55296||e>56319&&e<=65535)return A=this.index[e>>Fe],A=(A<<cr)+(e&lr),this.data[A];if(e<=65535)return A=this.index[vs+(e-55296>>Fe)],A=(A<<cr)+(e&lr),this.data[A];if(e<this.highStart)return A=wo-Qo+(e>>nn),A=this.index[A],A+=e>>Fe&Co,A=this.index[A],A=(A<<cr)+(e&lr),this.data[A];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},r}(),Cn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",vo=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var gt=0;gt<Cn.length;gt++)vo[Cn.charCodeAt(gt)]=gt;var Eo="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",Un=50,yo=1,Es=2,ys=3,Ho=4,Io=5,Fn=7,Hs=8,mn=9,ue=10,Sr=11,vn=12,Dr=13,_o=14,We=15,Tr=16,wt=17,Ge=18,bo=19,En=20,kr=21,Ne=22,ur=23,ve=24,OA=25,Je=26,Ye=27,Ee=28,xo=29,Ce=30,Lo=31,Qt=32,pt=33,Or=34,Rr=35,Mr=36,it=37,Gr=38,Tt=39,kt=40,fr=41,Is=42,Ko=43,So=[9001,65288],_s="!",oA="×",Ct="÷",Nr=Fo(Eo),re=[Ce,Mr],Vr=[yo,Es,ys,Io],bs=[ue,Hs],yn=[Ye,Je],Do=Vr.concat(bs),Hn=[Gr,Tt,kt,Or,Rr],To=[We,Dr],ko=function(r,e){e===void 0&&(e="strict");var A=[],t=[],n=[];return r.forEach(function(s,i){var a=Nr.get(s);if(a>Un?(n.push(!0),a-=Un):n.push(!1),["normal","auto","loose"].indexOf(e)!==-1&&[8208,8211,12316,12448].indexOf(s)!==-1)return t.push(i),A.push(Tr);if(a===Ho||a===Sr){if(i===0)return t.push(i),A.push(Ce);var o=A[i-1];return Do.indexOf(o)===-1?(t.push(t[i-1]),A.push(o)):(t.push(i),A.push(Ce))}if(t.push(i),a===Lo)return A.push(e==="strict"?kr:it);if(a===Is||a===xo)return A.push(Ce);if(a===Ko)return s>=131072&&s<=196605||s>=196608&&s<=262141?A.push(it):A.push(Ce);A.push(a)}),[t,A,n]},hr=function(r,e,A,t){var n=t[A];if(Array.isArray(r)?r.indexOf(n)!==-1:r===n)for(var s=A;s<=t.length;){s++;var i=t[s];if(i===e)return!0;if(i!==ue)break}if(n===ue)for(var s=A;s>0;){s--;var a=t[s];if(Array.isArray(r)?r.indexOf(a)!==-1:r===a)for(var o=A;o<=t.length;){o++;var i=t[o];if(i===e)return!0;if(i!==ue)break}if(a!==ue)break}return!1},In=function(r,e){for(var A=r;A>=0;){var t=e[A];if(t===ue)A--;else return t}return 0},Oo=function(r,e,A,t,n){if(A[t]===0)return oA;var s=t-1;if(Array.isArray(n)&&n[s]===!0)return oA;var i=s-1,a=s+1,o=e[s],c=i>=0?e[i]:0,l=e[a];if(o===Es&&l===ys)return oA;if(Vr.indexOf(o)!==-1)return _s;if(Vr.indexOf(l)!==-1||bs.indexOf(l)!==-1)return oA;if(In(s,e)===Hs)return Ct;if(Nr.get(r[s])===Sr||(o===Qt||o===pt)&&Nr.get(r[a])===Sr||o===Fn||l===Fn||o===mn||[ue,Dr,We].indexOf(o)===-1&&l===mn||[wt,Ge,bo,ve,Ee].indexOf(l)!==-1||In(s,e)===Ne||hr(ur,Ne,s,e)||hr([wt,Ge],kr,s,e)||hr(vn,vn,s,e))return oA;if(o===ue)return Ct;if(o===ur||l===ur)return oA;if(l===Tr||o===Tr)return Ct;if([Dr,We,kr].indexOf(l)!==-1||o===_o||c===Mr&&To.indexOf(o)!==-1||o===Ee&&l===Mr||l===En||re.indexOf(l)!==-1&&o===OA||re.indexOf(o)!==-1&&l===OA||o===Ye&&[it,Qt,pt].indexOf(l)!==-1||[it,Qt,pt].indexOf(o)!==-1&&l===Je||re.indexOf(o)!==-1&&yn.indexOf(l)!==-1||yn.indexOf(o)!==-1&&re.indexOf(l)!==-1||[Ye,Je].indexOf(o)!==-1&&(l===OA||[Ne,We].indexOf(l)!==-1&&e[a+1]===OA)||[Ne,We].indexOf(o)!==-1&&l===OA||o===OA&&[OA,Ee,ve].indexOf(l)!==-1)return oA;if([OA,Ee,ve,wt,Ge].indexOf(l)!==-1)for(var B=s;B>=0;){var h=e[B];if(h===OA)return oA;if([Ee,ve].indexOf(h)!==-1)B--;else break}if([Ye,Je].indexOf(l)!==-1)for(var B=[wt,Ge].indexOf(o)!==-1?i:s;B>=0;){var h=e[B];if(h===OA)return oA;if([Ee,ve].indexOf(h)!==-1)B--;else break}if(Gr===o&&[Gr,Tt,Or,Rr].indexOf(l)!==-1||[Tt,Or].indexOf(o)!==-1&&[Tt,kt].indexOf(l)!==-1||[kt,Rr].indexOf(o)!==-1&&l===kt||Hn.indexOf(o)!==-1&&[En,Je].indexOf(l)!==-1||Hn.indexOf(l)!==-1&&o===Ye||re.indexOf(o)!==-1&&re.indexOf(l)!==-1||o===ve&&re.indexOf(l)!==-1||re.concat(OA).indexOf(o)!==-1&&l===Ne&&So.indexOf(r[a])===-1||re.concat(OA).indexOf(l)!==-1&&o===Ge)return oA;if(o===fr&&l===fr){for(var u=A[s],p=1;u>0&&(u--,e[u]===fr);)p++;if(p%2!==0)return oA}return o===Qt&&l===pt?oA:Ct},Ro=function(r,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var A=ko(r,e.lineBreak),t=A[0],n=A[1],s=A[2];(e.wordBreak==="break-all"||e.wordBreak==="break-word")&&(n=n.map(function(a){return[OA,Ce,Is].indexOf(a)!==-1?it:a}));var i=e.wordBreak==="keep-all"?s.map(function(a,o){return a&&r[o]>=19968&&r[o]<=40959}):void 0;return[t,n,i]},Mo=function(){function r(e,A,t,n){this.codePoints=e,this.required=A===_s,this.start=t,this.end=n}return r.prototype.slice=function(){return EA.apply(void 0,this.codePoints.slice(this.start,this.end))},r}(),Go=function(r,e){var A=Ar(r),t=Ro(A,e),n=t[0],s=t[1],i=t[2],a=A.length,o=0,c=0;return{next:function(){if(c>=a)return{done:!0,value:null};for(var l=oA;c<a&&(l=Oo(A,s,n,++c,i))===oA;);if(l!==oA||c===a){var B=new Mo(A,l,o,c);return o=c,{value:B,done:!1}}return{done:!0,value:null}}}},No=1,Vo=2,Bt=4,_n=8,Gt=10,bn=47,$e=92,Po=9,Xo=32,Ut=34,Ve=61,Wo=35,Jo=36,Yo=37,Ft=39,mt=40,Pe=41,zo=95,kA=45,Zo=33,jo=60,qo=62,$o=64,AB=91,eB=93,tB=61,rB=123,vt=63,nB=125,xn=124,sB=126,iB=128,Ln=65533,dr=42,Ue=43,aB=44,oB=58,BB=59,at=46,cB=0,lB=8,uB=11,fB=14,hB=31,dB=127,qA=-1,xs=48,Ls=97,Ks=101,gB=102,wB=117,QB=122,Ss=65,Ds=69,Ts=70,pB=85,CB=90,KA=function(r){return r>=xs&&r<=57},UB=function(r){return r>=55296&&r<=57343},ye=function(r){return KA(r)||r>=Ss&&r<=Ts||r>=Ls&&r<=gB},FB=function(r){return r>=Ls&&r<=QB},mB=function(r){return r>=Ss&&r<=CB},vB=function(r){return FB(r)||mB(r)},EB=function(r){return r>=iB},Et=function(r){return r===Gt||r===Po||r===Xo},Nt=function(r){return vB(r)||EB(r)||r===zo},Kn=function(r){return Nt(r)||KA(r)||r===kA},yB=function(r){return r>=cB&&r<=lB||r===uB||r>=fB&&r<=hB||r===dB},ce=function(r,e){return r!==$e?!1:e!==Gt},yt=function(r,e,A){return r===kA?Nt(e)||ce(e,A):Nt(r)?!0:!!(r===$e&&ce(r,e))},gr=function(r,e,A){return r===Ue||r===kA?KA(e)?!0:e===at&&KA(A):KA(r===at?e:r)},HB=function(r){var e=0,A=1;(r[e]===Ue||r[e]===kA)&&(r[e]===kA&&(A=-1),e++);for(var t=[];KA(r[e]);)t.push(r[e++]);var n=t.length?parseInt(EA.apply(void 0,t),10):0;r[e]===at&&e++;for(var s=[];KA(r[e]);)s.push(r[e++]);var i=s.length,a=i?parseInt(EA.apply(void 0,s),10):0;(r[e]===Ds||r[e]===Ks)&&e++;var o=1;(r[e]===Ue||r[e]===kA)&&(r[e]===kA&&(o=-1),e++);for(var c=[];KA(r[e]);)c.push(r[e++]);var l=c.length?parseInt(EA.apply(void 0,c),10):0;return A*(n+a*Math.pow(10,-i))*Math.pow(10,o*l)},IB={type:2},_B={type:3},bB={type:4},xB={type:13},LB={type:8},KB={type:21},SB={type:9},DB={type:10},TB={type:11},kB={type:12},OB={type:14},Ht={type:23},RB={type:1},MB={type:25},GB={type:24},NB={type:26},VB={type:27},PB={type:28},XB={type:29},WB={type:31},Pr={type:32},ks=function(){function r(){this._value=[]}return r.prototype.write=function(e){this._value=this._value.concat(Ar(e))},r.prototype.read=function(){for(var e=[],A=this.consumeToken();A!==Pr;)e.push(A),A=this.consumeToken();return e},r.prototype.consumeToken=function(){var e=this.consumeCodePoint();switch(e){case Ut:return this.consumeStringToken(Ut);case Wo:var A=this.peekCodePoint(0),t=this.peekCodePoint(1),n=this.peekCodePoint(2);if(Kn(A)||ce(t,n)){var s=yt(A,t,n)?Vo:No,i=this.consumeName();return{type:5,value:i,flags:s}}break;case Jo:if(this.peekCodePoint(0)===Ve)return this.consumeCodePoint(),xB;break;case Ft:return this.consumeStringToken(Ft);case mt:return IB;case Pe:return _B;case dr:if(this.peekCodePoint(0)===Ve)return this.consumeCodePoint(),OB;break;case Ue:if(gr(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case aB:return bB;case kA:var a=e,o=this.peekCodePoint(0),c=this.peekCodePoint(1);if(gr(a,o,c))return this.reconsumeCodePoint(e),this.consumeNumericToken();if(yt(a,o,c))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();if(o===kA&&c===qo)return this.consumeCodePoint(),this.consumeCodePoint(),GB;break;case at:if(gr(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case bn:if(this.peekCodePoint(0)===dr)for(this.consumeCodePoint();;){var l=this.consumeCodePoint();if(l===dr&&(l=this.consumeCodePoint(),l===bn))return this.consumeToken();if(l===qA)return this.consumeToken()}break;case oB:return NB;case BB:return VB;case jo:if(this.peekCodePoint(0)===Zo&&this.peekCodePoint(1)===kA&&this.peekCodePoint(2)===kA)return this.consumeCodePoint(),this.consumeCodePoint(),MB;break;case $o:var B=this.peekCodePoint(0),h=this.peekCodePoint(1),u=this.peekCodePoint(2);if(yt(B,h,u)){var i=this.consumeName();return{type:7,value:i}}break;case AB:return PB;case $e:if(ce(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();break;case eB:return XB;case tB:if(this.peekCodePoint(0)===Ve)return this.consumeCodePoint(),LB;break;case rB:return TB;case nB:return kB;case wB:case pB:var p=this.peekCodePoint(0),f=this.peekCodePoint(1);return p===Ue&&(ye(f)||f===vt)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(e),this.consumeIdentLikeToken();case xn:if(this.peekCodePoint(0)===Ve)return this.consumeCodePoint(),SB;if(this.peekCodePoint(0)===xn)return this.consumeCodePoint(),KB;break;case sB:if(this.peekCodePoint(0)===Ve)return this.consumeCodePoint(),DB;break;case qA:return Pr}return Et(e)?(this.consumeWhiteSpace(),WB):KA(e)?(this.reconsumeCodePoint(e),this.consumeNumericToken()):Nt(e)?(this.reconsumeCodePoint(e),this.consumeIdentLikeToken()):{type:6,value:EA(e)}},r.prototype.consumeCodePoint=function(){var e=this._value.shift();return typeof e>"u"?-1:e},r.prototype.reconsumeCodePoint=function(e){this._value.unshift(e)},r.prototype.peekCodePoint=function(e){return e>=this._value.length?-1:this._value[e]},r.prototype.consumeUnicodeRangeToken=function(){for(var e=[],A=this.consumeCodePoint();ye(A)&&e.length<6;)e.push(A),A=this.consumeCodePoint();for(var t=!1;A===vt&&e.length<6;)e.push(A),A=this.consumeCodePoint(),t=!0;if(t){var n=parseInt(EA.apply(void 0,e.map(function(o){return o===vt?xs:o})),16),s=parseInt(EA.apply(void 0,e.map(function(o){return o===vt?Ts:o})),16);return{type:30,start:n,end:s}}var i=parseInt(EA.apply(void 0,e),16);if(this.peekCodePoint(0)===kA&&ye(this.peekCodePoint(1))){this.consumeCodePoint(),A=this.consumeCodePoint();for(var a=[];ye(A)&&a.length<6;)a.push(A),A=this.consumeCodePoint();var s=parseInt(EA.apply(void 0,a),16);return{type:30,start:i,end:s}}else return{type:30,start:i,end:i}},r.prototype.consumeIdentLikeToken=function(){var e=this.consumeName();return e.toLowerCase()==="url"&&this.peekCodePoint(0)===mt?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===mt?(this.consumeCodePoint(),{type:19,value:e}):{type:20,value:e}},r.prototype.consumeUrlToken=function(){var e=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===qA)return{type:22,value:""};var A=this.peekCodePoint(0);if(A===Ft||A===Ut){var t=this.consumeStringToken(this.consumeCodePoint());return t.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===qA||this.peekCodePoint(0)===Pe)?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),Ht)}for(;;){var n=this.consumeCodePoint();if(n===qA||n===Pe)return{type:22,value:EA.apply(void 0,e)};if(Et(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===qA||this.peekCodePoint(0)===Pe?(this.consumeCodePoint(),{type:22,value:EA.apply(void 0,e)}):(this.consumeBadUrlRemnants(),Ht);if(n===Ut||n===Ft||n===mt||yB(n))return this.consumeBadUrlRemnants(),Ht;if(n===$e)if(ce(n,this.peekCodePoint(0)))e.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),Ht;else e.push(n)}},r.prototype.consumeWhiteSpace=function(){for(;Et(this.peekCodePoint(0));)this.consumeCodePoint()},r.prototype.consumeBadUrlRemnants=function(){for(;;){var e=this.consumeCodePoint();if(e===Pe||e===qA)return;ce(e,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},r.prototype.consumeStringSlice=function(e){for(var A=5e4,t="";e>0;){var n=Math.min(A,e);t+=EA.apply(void 0,this._value.splice(0,n)),e-=n}return this._value.shift(),t},r.prototype.consumeStringToken=function(e){var A="",t=0;do{var n=this._value[t];if(n===qA||n===void 0||n===e)return A+=this.consumeStringSlice(t),{type:0,value:A};if(n===Gt)return this._value.splice(0,t),RB;if(n===$e){var s=this._value[t+1];s!==qA&&s!==void 0&&(s===Gt?(A+=this.consumeStringSlice(t),t=-1,this._value.shift()):ce(n,s)&&(A+=this.consumeStringSlice(t),A+=EA(this.consumeEscapedCodePoint()),t=-1))}t++}while(!0)},r.prototype.consumeNumber=function(){var e=[],A=Bt,t=this.peekCodePoint(0);for((t===Ue||t===kA)&&e.push(this.consumeCodePoint());KA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());t=this.peekCodePoint(0);var n=this.peekCodePoint(1);if(t===at&&KA(n))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),A=_n;KA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());t=this.peekCodePoint(0),n=this.peekCodePoint(1);var s=this.peekCodePoint(2);if((t===Ds||t===Ks)&&((n===Ue||n===kA)&&KA(s)||KA(n)))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),A=_n;KA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());return[HB(e),A]},r.prototype.consumeNumericToken=function(){var e=this.consumeNumber(),A=e[0],t=e[1],n=this.peekCodePoint(0),s=this.peekCodePoint(1),i=this.peekCodePoint(2);if(yt(n,s,i)){var a=this.consumeName();return{type:15,number:A,flags:t,unit:a}}return n===Yo?(this.consumeCodePoint(),{type:16,number:A,flags:t}):{type:17,number:A,flags:t}},r.prototype.consumeEscapedCodePoint=function(){var e=this.consumeCodePoint();if(ye(e)){for(var A=EA(e);ye(this.peekCodePoint(0))&&A.length<6;)A+=EA(this.consumeCodePoint());Et(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(A,16);return t===0||UB(t)||t>1114111?Ln:t}return e===qA?Ln:e},r.prototype.consumeName=function(){for(var e="";;){var A=this.consumeCodePoint();if(Kn(A))e+=EA(A);else if(ce(A,this.peekCodePoint(0)))e+=EA(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(A),e}},r}(),Os=function(){function r(e){this._tokens=e}return r.create=function(e){var A=new ks;return A.write(e),new r(A.read())},r.parseValue=function(e){return r.create(e).parseComponentValue()},r.parseValues=function(e){return r.create(e).parseComponentValues()},r.prototype.parseComponentValue=function(){for(var e=this.consumeToken();e.type===31;)e=this.consumeToken();if(e.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(e);var A=this.consumeComponentValue();do e=this.consumeToken();while(e.type===31);if(e.type===32)return A;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},r.prototype.parseComponentValues=function(){for(var e=[];;){var A=this.consumeComponentValue();if(A.type===32)return e;e.push(A),e.push()}},r.prototype.consumeComponentValue=function(){var e=this.consumeToken();switch(e.type){case 11:case 28:case 2:return this.consumeSimpleBlock(e.type);case 19:return this.consumeFunction(e)}return e},r.prototype.consumeSimpleBlock=function(e){for(var A={type:e,values:[]},t=this.consumeToken();;){if(t.type===32||YB(t,e))return A;this.reconsumeToken(t),A.values.push(this.consumeComponentValue()),t=this.consumeToken()}},r.prototype.consumeFunction=function(e){for(var A={name:e.value,values:[],type:18};;){var t=this.consumeToken();if(t.type===32||t.type===3)return A;this.reconsumeToken(t),A.values.push(this.consumeComponentValue())}},r.prototype.consumeToken=function(){var e=this._tokens.shift();return typeof e>"u"?Pr:e},r.prototype.reconsumeToken=function(e){this._tokens.unshift(e)},r}(),ct=function(r){return r.type===15},ke=function(r){return r.type===17},gA=function(r){return r.type===20},JB=function(r){return r.type===0},Xr=function(r,e){return gA(r)&&r.value===e},Rs=function(r){return r.type!==31},Se=function(r){return r.type!==31&&r.type!==4},Ae=function(r){var e=[],A=[];return r.forEach(function(t){if(t.type===4){if(A.length===0)throw new Error("Error parsing function args, zero tokens for arg");e.push(A),A=[];return}t.type!==31&&A.push(t)}),A.length&&e.push(A),e},YB=function(r,e){return e===11&&r.type===12||e===28&&r.type===29?!0:e===2&&r.type===3},Qe=function(r){return r.type===17||r.type===15},yA=function(r){return r.type===16||Qe(r)},Ms=function(r){return r.length>1?[r[0],r[1]]:[r[0]]},xA={type:17,number:0,flags:Bt},sn={type:16,number:50,flags:Bt},fe={type:16,number:100,flags:Bt},ze=function(r,e,A){var t=r[0],n=r[1];return[QA(t,e),QA(typeof n<"u"?n:t,A)]},QA=function(r,e){if(r.type===16)return r.number/100*e;if(ct(r))switch(r.unit){case"rem":case"em":return 16*r.number;case"px":default:return r.number}return r.number},Gs="deg",Ns="grad",Vs="rad",Ps="turn",er={name:"angle",parse:function(r,e){if(e.type===15)switch(e.unit){case Gs:return Math.PI*e.number/180;case Ns:return Math.PI/200*e.number;case Vs:return e.number;case Ps:return Math.PI*2*e.number}throw new Error("Unsupported angle type")}},Xs=function(r){return r.type===15&&(r.unit===Gs||r.unit===Ns||r.unit===Vs||r.unit===Ps)},Ws=function(r){var e=r.filter(gA).map(function(A){return A.value}).join(" ");switch(e){case"to bottom right":case"to right bottom":case"left top":case"top left":return[xA,xA];case"to top":case"bottom":return VA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[xA,fe];case"to right":case"left":return VA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[fe,fe];case"to bottom":case"top":return VA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[fe,xA];case"to left":case"right":return VA(270)}return 0},VA=function(r){return Math.PI*r/180},ge={name:"color",parse:function(r,e){if(e.type===18){var A=zB[e.name];if(typeof A>"u")throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return A(r,e.values)}if(e.type===5){if(e.value.length===3){var t=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3);return he(parseInt(t+t,16),parseInt(n+n,16),parseInt(s+s,16),1)}if(e.value.length===4){var t=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3),i=e.value.substring(3,4);return he(parseInt(t+t,16),parseInt(n+n,16),parseInt(s+s,16),parseInt(i+i,16)/255)}if(e.value.length===6){var t=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6);return he(parseInt(t,16),parseInt(n,16),parseInt(s,16),1)}if(e.value.length===8){var t=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6),i=e.value.substring(6,8);return he(parseInt(t,16),parseInt(n,16),parseInt(s,16),parseInt(i,16)/255)}}if(e.type===20){var a=ie[e.value.toUpperCase()];if(typeof a<"u")return a}return ie.TRANSPARENT}},we=function(r){return(255&r)===0},_A=function(r){var e=255&r,A=255&r>>8,t=255&r>>16,n=255&r>>24;return e<255?"rgba("+n+","+t+","+A+","+e/255+")":"rgb("+n+","+t+","+A+")"},he=function(r,e,A,t){return(r<<24|e<<16|A<<8|Math.round(t*255)<<0)>>>0},Sn=function(r,e){if(r.type===17)return r.number;if(r.type===16){var A=e===3?1:255;return e===3?r.number/100*A:Math.round(r.number/100*A)}return 0},Dn=function(r,e){var A=e.filter(Se);if(A.length===3){var t=A.map(Sn),n=t[0],s=t[1],i=t[2];return he(n,s,i,1)}if(A.length===4){var a=A.map(Sn),n=a[0],s=a[1],i=a[2],o=a[3];return he(n,s,i,o)}return 0};function wr(r,e,A){return A<0&&(A+=1),A>=1&&(A-=1),A<1/6?(e-r)*A*6+r:A<1/2?e:A<2/3?(e-r)*6*(2/3-A)+r:r}var Tn=function(r,e){var A=e.filter(Se),t=A[0],n=A[1],s=A[2],i=A[3],a=(t.type===17?VA(t.number):er.parse(r,t))/(Math.PI*2),o=yA(n)?n.number/100:0,c=yA(s)?s.number/100:0,l=typeof i<"u"&&yA(i)?QA(i,1):1;if(o===0)return he(c*255,c*255,c*255,1);var B=c<=.5?c*(o+1):c+o-c*o,h=c*2-B,u=wr(h,B,a+1/3),p=wr(h,B,a),f=wr(h,B,a-1/3);return he(u*255,p*255,f*255,l)},zB={hsl:Tn,hsla:Tn,rgb:Dn,rgba:Dn},At=function(r,e){return ge.parse(r,Os.create(e).parseComponentValue())},ie={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},ZB={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(r,e){return e.map(function(A){if(gA(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},jB={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},tr=function(r,e){var A=ge.parse(r,e[0]),t=e[1];return t&&yA(t)?{color:A,stop:t}:{color:A,stop:null}},kn=function(r,e){var A=r[0],t=r[r.length-1];A.stop===null&&(A.stop=xA),t.stop===null&&(t.stop=fe);for(var n=[],s=0,i=0;i<r.length;i++){var a=r[i].stop;if(a!==null){var o=QA(a,e);o>s?n.push(o):n.push(s),s=o}else n.push(null)}for(var c=null,i=0;i<n.length;i++){var l=n[i];if(l===null)c===null&&(c=i);else if(c!==null){for(var B=i-c,h=n[c-1],u=(l-h)/(B+1),p=1;p<=B;p++)n[c+p-1]=u*p;c=null}}return r.map(function(f,w){var g=f.color;return{color:g,stop:Math.max(Math.min(1,n[w]/e),0)}})},qB=function(r,e,A){var t=e/2,n=A/2,s=QA(r[0],e)-t,i=n-QA(r[1],A);return(Math.atan2(i,s)+Math.PI*2)%(Math.PI*2)},$B=function(r,e,A){var t=typeof r=="number"?r:qB(r,e,A),n=Math.abs(e*Math.sin(t))+Math.abs(A*Math.cos(t)),s=e/2,i=A/2,a=n/2,o=Math.sin(t-Math.PI/2)*a,c=Math.cos(t-Math.PI/2)*a;return[n,s-c,s+c,i-o,i+o]},WA=function(r,e){return Math.sqrt(r*r+e*e)},On=function(r,e,A,t,n){var s=[[0,0],[0,e],[r,0],[r,e]];return s.reduce(function(i,a){var o=a[0],c=a[1],l=WA(A-o,t-c);return(n?l<i.optimumDistance:l>i.optimumDistance)?{optimumCorner:a,optimumDistance:l}:i},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},Ac=function(r,e,A,t,n){var s=0,i=0;switch(r.size){case 0:r.shape===0?s=i=Math.min(Math.abs(e),Math.abs(e-t),Math.abs(A),Math.abs(A-n)):r.shape===1&&(s=Math.min(Math.abs(e),Math.abs(e-t)),i=Math.min(Math.abs(A),Math.abs(A-n)));break;case 2:if(r.shape===0)s=i=Math.min(WA(e,A),WA(e,A-n),WA(e-t,A),WA(e-t,A-n));else if(r.shape===1){var a=Math.min(Math.abs(A),Math.abs(A-n))/Math.min(Math.abs(e),Math.abs(e-t)),o=On(t,n,e,A,!0),c=o[0],l=o[1];s=WA(c-e,(l-A)/a),i=a*s}break;case 1:r.shape===0?s=i=Math.max(Math.abs(e),Math.abs(e-t),Math.abs(A),Math.abs(A-n)):r.shape===1&&(s=Math.max(Math.abs(e),Math.abs(e-t)),i=Math.max(Math.abs(A),Math.abs(A-n)));break;case 3:if(r.shape===0)s=i=Math.max(WA(e,A),WA(e,A-n),WA(e-t,A),WA(e-t,A-n));else if(r.shape===1){var a=Math.max(Math.abs(A),Math.abs(A-n))/Math.max(Math.abs(e),Math.abs(e-t)),B=On(t,n,e,A,!1),c=B[0],l=B[1];s=WA(c-e,(l-A)/a),i=a*s}break}return Array.isArray(r.size)&&(s=QA(r.size[0],t),i=r.size.length===2?QA(r.size[1],n):s),[s,i]},ec=function(r,e){var A=VA(180),t=[];return Ae(e).forEach(function(n,s){if(s===0){var i=n[0];if(i.type===20&&i.value==="to"){A=Ws(n);return}else if(Xs(i)){A=er.parse(r,i);return}}var a=tr(r,n);t.push(a)}),{angle:A,stops:t,type:1}},It=function(r,e){var A=VA(180),t=[];return Ae(e).forEach(function(n,s){if(s===0){var i=n[0];if(i.type===20&&["top","left","right","bottom"].indexOf(i.value)!==-1){A=Ws(n);return}else if(Xs(i)){A=(er.parse(r,i)+VA(270))%VA(360);return}}var a=tr(r,n);t.push(a)}),{angle:A,stops:t,type:1}},tc=function(r,e){var A=VA(180),t=[],n=1,s=0,i=3,a=[];return Ae(e).forEach(function(o,c){var l=o[0];if(c===0){if(gA(l)&&l.value==="linear"){n=1;return}else if(gA(l)&&l.value==="radial"){n=2;return}}if(l.type===18){if(l.name==="from"){var B=ge.parse(r,l.values[0]);t.push({stop:xA,color:B})}else if(l.name==="to"){var B=ge.parse(r,l.values[0]);t.push({stop:fe,color:B})}else if(l.name==="color-stop"){var h=l.values.filter(Se);if(h.length===2){var B=ge.parse(r,h[1]),u=h[0];ke(u)&&t.push({stop:{type:16,number:u.number*100,flags:u.flags},color:B})}}}}),n===1?{angle:(A+VA(180))%VA(360),stops:t,type:n}:{size:i,shape:s,stops:t,position:a,type:n}},Js="closest-side",Ys="farthest-side",zs="closest-corner",Zs="farthest-corner",js="circle",qs="ellipse",$s="cover",Ai="contain",rc=function(r,e){var A=0,t=3,n=[],s=[];return Ae(e).forEach(function(i,a){var o=!0;if(a===0){var c=!1;o=i.reduce(function(B,h){if(c)if(gA(h))switch(h.value){case"center":return s.push(sn),B;case"top":case"left":return s.push(xA),B;case"right":case"bottom":return s.push(fe),B}else(yA(h)||Qe(h))&&s.push(h);else if(gA(h))switch(h.value){case js:return A=0,!1;case qs:return A=1,!1;case"at":return c=!0,!1;case Js:return t=0,!1;case $s:case Ys:return t=1,!1;case Ai:case zs:return t=2,!1;case Zs:return t=3,!1}else if(Qe(h)||yA(h))return Array.isArray(t)||(t=[]),t.push(h),!1;return B},o)}if(o){var l=tr(r,i);n.push(l)}}),{size:t,shape:A,stops:n,position:s,type:2}},_t=function(r,e){var A=0,t=3,n=[],s=[];return Ae(e).forEach(function(i,a){var o=!0;if(a===0?o=i.reduce(function(l,B){if(gA(B))switch(B.value){case"center":return s.push(sn),!1;case"top":case"left":return s.push(xA),!1;case"right":case"bottom":return s.push(fe),!1}else if(yA(B)||Qe(B))return s.push(B),!1;return l},o):a===1&&(o=i.reduce(function(l,B){if(gA(B))switch(B.value){case js:return A=0,!1;case qs:return A=1,!1;case Ai:case Js:return t=0,!1;case Ys:return t=1,!1;case zs:return t=2,!1;case $s:case Zs:return t=3,!1}else if(Qe(B)||yA(B))return Array.isArray(t)||(t=[]),t.push(B),!1;return l},o)),o){var c=tr(r,i);n.push(c)}}),{size:t,shape:A,stops:n,position:s,type:2}},nc=function(r){return r.type===1},sc=function(r){return r.type===2},an={name:"image",parse:function(r,e){if(e.type===22){var A={url:e.value,type:0};return r.cache.addImage(e.value),A}if(e.type===18){var t=ei[e.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return t(r,e.values)}throw new Error("Unsupported image type "+e.type)}};function ic(r){return!(r.type===20&&r.value==="none")&&(r.type!==18||!!ei[r.name])}var ei={"linear-gradient":ec,"-moz-linear-gradient":It,"-ms-linear-gradient":It,"-o-linear-gradient":It,"-webkit-linear-gradient":It,"radial-gradient":rc,"-moz-radial-gradient":_t,"-ms-radial-gradient":_t,"-o-radial-gradient":_t,"-webkit-radial-gradient":_t,"-webkit-gradient":tc},ac={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(r,e){if(e.length===0)return[];var A=e[0];return A.type===20&&A.value==="none"?[]:e.filter(function(t){return Se(t)&&ic(t)}).map(function(t){return an.parse(r,t)})}},oc={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(r,e){return e.map(function(A){if(gA(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Bc={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(r,e){return Ae(e).map(function(A){return A.filter(yA)}).map(Ms)}},cc={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(r,e){return Ae(e).map(function(A){return A.filter(gA).map(function(t){return t.value}).join(" ")}).map(lc)}},lc=function(r){switch(r){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},Ke;(function(r){r.AUTO="auto",r.CONTAIN="contain",r.COVER="cover"})(Ke||(Ke={}));var uc={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(r,e){return Ae(e).map(function(A){return A.filter(fc)})}},fc=function(r){return gA(r)||yA(r)},rr=function(r){return{name:"border-"+r+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},hc=rr("top"),dc=rr("right"),gc=rr("bottom"),wc=rr("left"),nr=function(r){return{name:"border-radius-"+r,initialValue:"0 0",prefix:!1,type:1,parse:function(e,A){return Ms(A.filter(yA))}}},Qc=nr("top-left"),pc=nr("top-right"),Cc=nr("bottom-right"),Uc=nr("bottom-left"),sr=function(r){return{name:"border-"+r+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(e,A){switch(A){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Fc=sr("top"),mc=sr("right"),vc=sr("bottom"),Ec=sr("left"),ir=function(r){return{name:"border-"+r+"-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return ct(A)?A.number:0}}},yc=ir("top"),Hc=ir("right"),Ic=ir("bottom"),_c=ir("left"),bc={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},xc={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(r,e){switch(e){case"rtl":return 1;case"ltr":default:return 0}}},Lc={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(r,e){return e.filter(gA).reduce(function(A,t){return A|Kc(t.value)},0)}},Kc=function(r){switch(r){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Sc={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Dc={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(r,e){return e.type===20&&e.value==="normal"?0:e.type===17||e.type===15?e.number:0}},Vt;(function(r){r.NORMAL="normal",r.STRICT="strict"})(Vt||(Vt={}));var Tc={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"strict":return Vt.STRICT;case"normal":default:return Vt.NORMAL}}},kc={name:"line-height",initialValue:"normal",prefix:!1,type:4},Rn=function(r,e){return gA(r)&&r.value==="normal"?1.2*e:r.type===17?e*r.number:yA(r)?QA(r,e):e},Oc={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(r,e){return e.type===20&&e.value==="none"?null:an.parse(r,e)}},Rc={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(r,e){switch(e){case"inside":return 0;case"outside":default:return 1}}},Wr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},ar=function(r){return{name:"margin-"+r,initialValue:"0",prefix:!1,type:4}},Mc=ar("top"),Gc=ar("right"),Nc=ar("bottom"),Vc=ar("left"),Pc={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(r,e){return e.filter(gA).map(function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},Xc={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"break-word":return"break-word";case"normal":default:return"normal"}}},or=function(r){return{name:"padding-"+r,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Wc=or("top"),Jc=or("right"),Yc=or("bottom"),zc=or("left"),Zc={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(r,e){switch(e){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},jc={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(r,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},qc={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(r,e){return e.length===1&&Xr(e[0],"none")?[]:Ae(e).map(function(A){for(var t={color:ie.TRANSPARENT,offsetX:xA,offsetY:xA,blur:xA},n=0,s=0;s<A.length;s++){var i=A[s];Qe(i)?(n===0?t.offsetX=i:n===1?t.offsetY=i:t.blur=i,n++):t.color=ge.parse(r,i)}return t})}},$c={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},Al={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(r,e){if(e.type===20&&e.value==="none")return null;if(e.type===18){var A=rl[e.name];if(typeof A>"u")throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return A(e.values)}return null}},el=function(r){var e=r.filter(function(A){return A.type===17}).map(function(A){return A.number});return e.length===6?e:null},tl=function(r){var e=r.filter(function(o){return o.type===17}).map(function(o){return o.number}),A=e[0],t=e[1];e[2],e[3];var n=e[4],s=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var i=e[12],a=e[13];return e[14],e[15],e.length===16?[A,t,n,s,i,a]:null},rl={matrix:el,matrix3d:tl},Mn={type:16,number:50,flags:Bt},nl=[Mn,Mn],sl={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(r,e){var A=e.filter(yA);return A.length!==2?nl:[A[0],A[1]]}},il={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(r,e){switch(e){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},et;(function(r){r.NORMAL="normal",r.BREAK_ALL="break-all",r.KEEP_ALL="keep-all"})(et||(et={}));var al={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"break-all":return et.BREAK_ALL;case"keep-all":return et.KEEP_ALL;case"normal":default:return et.NORMAL}}},ol={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(r,e){if(e.type===20)return{auto:!0,order:0};if(ke(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},ti={name:"time",parse:function(r,e){if(e.type===15)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")}},Bl={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(r,e){return ke(e)?e.number:1}},cl={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},ll={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(r,e){return e.filter(gA).map(function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(A){return A!==0})}},ul={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(r,e){var A=[],t=[];return e.forEach(function(n){switch(n.type){case 20:case 0:A.push(n.value);break;case 17:A.push(n.number.toString());break;case 4:t.push(A.join(" ")),A.length=0;break}}),A.length&&t.push(A.join(" ")),t.map(function(n){return n.indexOf(" ")===-1?n:"'"+n+"'"})}},fl={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},hl={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(r,e){if(ke(e))return e.number;if(gA(e))switch(e.value){case"bold":return 700;case"normal":default:return 400}return 400}},dl={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(r,e){return e.filter(gA).map(function(A){return A.value})}},gl={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(r,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},HA=function(r,e){return(r&e)!==0},wl={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(r,e){if(e.length===0)return[];var A=e[0];return A.type===20&&A.value==="none"?[]:e}},Ql={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(r,e){if(e.length===0)return null;var A=e[0];if(A.type===20&&A.value==="none")return null;for(var t=[],n=e.filter(Rs),s=0;s<n.length;s++){var i=n[s],a=n[s+1];if(i.type===20){var o=a&&ke(a)?a.number:1;t.push({counter:i.value,increment:o})}}return t}},pl={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(r,e){if(e.length===0)return[];for(var A=[],t=e.filter(Rs),n=0;n<t.length;n++){var s=t[n],i=t[n+1];if(gA(s)&&s.value!=="none"){var a=i&&ke(i)?i.number:0;A.push({counter:s.value,reset:a})}}return A}},Cl={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(r,e){return e.filter(ct).map(function(A){return ti.parse(r,A)})}},Ul={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(r,e){if(e.length===0)return null;var A=e[0];if(A.type===20&&A.value==="none")return null;var t=[],n=e.filter(JB);if(n.length%2!==0)return null;for(var s=0;s<n.length;s+=2){var i=n[s].value,a=n[s+1].value;t.push({open:i,close:a})}return t}},Gn=function(r,e,A){if(!r)return"";var t=r[Math.min(e,r.length-1)];return t?A?t.open:t.close:""},Fl={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(r,e){return e.length===1&&Xr(e[0],"none")?[]:Ae(e).map(function(A){for(var t={color:255,offsetX:xA,offsetY:xA,blur:xA,spread:xA,inset:!1},n=0,s=0;s<A.length;s++){var i=A[s];Xr(i,"inset")?t.inset=!0:Qe(i)?(n===0?t.offsetX=i:n===1?t.offsetY=i:n===2?t.blur=i:t.spread=i,n++):t.color=ge.parse(r,i)}return t})}},ml={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(r,e){var A=[0,1,2],t=[];return e.filter(gA).forEach(function(n){switch(n.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2);break}}),A.forEach(function(n){t.indexOf(n)===-1&&t.push(n)}),t}},vl={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},El={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(r,e){return ct(e)?e.number:0}},yl=function(){function r(e,A){var t,n;this.animationDuration=j(e,Cl,A.animationDuration),this.backgroundClip=j(e,ZB,A.backgroundClip),this.backgroundColor=j(e,jB,A.backgroundColor),this.backgroundImage=j(e,ac,A.backgroundImage),this.backgroundOrigin=j(e,oc,A.backgroundOrigin),this.backgroundPosition=j(e,Bc,A.backgroundPosition),this.backgroundRepeat=j(e,cc,A.backgroundRepeat),this.backgroundSize=j(e,uc,A.backgroundSize),this.borderTopColor=j(e,hc,A.borderTopColor),this.borderRightColor=j(e,dc,A.borderRightColor),this.borderBottomColor=j(e,gc,A.borderBottomColor),this.borderLeftColor=j(e,wc,A.borderLeftColor),this.borderTopLeftRadius=j(e,Qc,A.borderTopLeftRadius),this.borderTopRightRadius=j(e,pc,A.borderTopRightRadius),this.borderBottomRightRadius=j(e,Cc,A.borderBottomRightRadius),this.borderBottomLeftRadius=j(e,Uc,A.borderBottomLeftRadius),this.borderTopStyle=j(e,Fc,A.borderTopStyle),this.borderRightStyle=j(e,mc,A.borderRightStyle),this.borderBottomStyle=j(e,vc,A.borderBottomStyle),this.borderLeftStyle=j(e,Ec,A.borderLeftStyle),this.borderTopWidth=j(e,yc,A.borderTopWidth),this.borderRightWidth=j(e,Hc,A.borderRightWidth),this.borderBottomWidth=j(e,Ic,A.borderBottomWidth),this.borderLeftWidth=j(e,_c,A.borderLeftWidth),this.boxShadow=j(e,Fl,A.boxShadow),this.color=j(e,bc,A.color),this.direction=j(e,xc,A.direction),this.display=j(e,Lc,A.display),this.float=j(e,Sc,A.cssFloat),this.fontFamily=j(e,ul,A.fontFamily),this.fontSize=j(e,fl,A.fontSize),this.fontStyle=j(e,gl,A.fontStyle),this.fontVariant=j(e,dl,A.fontVariant),this.fontWeight=j(e,hl,A.fontWeight),this.letterSpacing=j(e,Dc,A.letterSpacing),this.lineBreak=j(e,Tc,A.lineBreak),this.lineHeight=j(e,kc,A.lineHeight),this.listStyleImage=j(e,Oc,A.listStyleImage),this.listStylePosition=j(e,Rc,A.listStylePosition),this.listStyleType=j(e,Wr,A.listStyleType),this.marginTop=j(e,Mc,A.marginTop),this.marginRight=j(e,Gc,A.marginRight),this.marginBottom=j(e,Nc,A.marginBottom),this.marginLeft=j(e,Vc,A.marginLeft),this.opacity=j(e,Bl,A.opacity);var s=j(e,Pc,A.overflow);this.overflowX=s[0],this.overflowY=s[s.length>1?1:0],this.overflowWrap=j(e,Xc,A.overflowWrap),this.paddingTop=j(e,Wc,A.paddingTop),this.paddingRight=j(e,Jc,A.paddingRight),this.paddingBottom=j(e,Yc,A.paddingBottom),this.paddingLeft=j(e,zc,A.paddingLeft),this.paintOrder=j(e,ml,A.paintOrder),this.position=j(e,jc,A.position),this.textAlign=j(e,Zc,A.textAlign),this.textDecorationColor=j(e,cl,(t=A.textDecorationColor)!==null&&t!==void 0?t:A.color),this.textDecorationLine=j(e,ll,(n=A.textDecorationLine)!==null&&n!==void 0?n:A.textDecoration),this.textShadow=j(e,qc,A.textShadow),this.textTransform=j(e,$c,A.textTransform),this.transform=j(e,Al,A.transform),this.transformOrigin=j(e,sl,A.transformOrigin),this.visibility=j(e,il,A.visibility),this.webkitTextStrokeColor=j(e,vl,A.webkitTextStrokeColor),this.webkitTextStrokeWidth=j(e,El,A.webkitTextStrokeWidth),this.wordBreak=j(e,al,A.wordBreak),this.zIndex=j(e,ol,A.zIndex)}return r.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},r.prototype.isTransparent=function(){return we(this.backgroundColor)},r.prototype.isTransformed=function(){return this.transform!==null},r.prototype.isPositioned=function(){return this.position!==0},r.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},r.prototype.isFloating=function(){return this.float!==0},r.prototype.isInlineLevel=function(){return HA(this.display,4)||HA(this.display,33554432)||HA(this.display,268435456)||HA(this.display,536870912)||HA(this.display,67108864)||HA(this.display,134217728)},r}(),Hl=function(){function r(e,A){this.content=j(e,wl,A.content),this.quotes=j(e,Ul,A.quotes)}return r}(),Nn=function(){function r(e,A){this.counterIncrement=j(e,Ql,A.counterIncrement),this.counterReset=j(e,pl,A.counterReset)}return r}(),j=function(r,e,A){var t=new ks,n=A!==null&&typeof A<"u"?A.toString():e.initialValue;t.write(n);var s=new Os(t.read());switch(e.type){case 2:var i=s.parseComponentValue();return e.parse(r,gA(i)?i.value:e.initialValue);case 0:return e.parse(r,s.parseComponentValue());case 1:return e.parse(r,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(e.format){case"angle":return er.parse(r,s.parseComponentValue());case"color":return ge.parse(r,s.parseComponentValue());case"image":return an.parse(r,s.parseComponentValue());case"length":var a=s.parseComponentValue();return Qe(a)?a:xA;case"length-percentage":var o=s.parseComponentValue();return yA(o)?o:xA;case"time":return ti.parse(r,s.parseComponentValue())}break}},Il="data-html2canvas-debug",_l=function(r){var e=r.getAttribute(Il);switch(e){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Jr=function(r,e){var A=_l(r);return A===1||e===A},ee=function(){function r(e,A){if(this.context=e,this.textNodes=[],this.elements=[],this.flags=0,Jr(A,3))debugger;this.styles=new yl(e,window.getComputedStyle(A,null)),Zr(A)&&(this.styles.animationDuration.some(function(t){return t>0})&&(A.style.animationDuration="0s"),this.styles.transform!==null&&(A.style.transform="none")),this.bounds=$t(this.context,A),Jr(A,4)&&(this.flags|=16)}return r}(),bl="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",Vn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ze=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var bt=0;bt<Vn.length;bt++)Ze[Vn.charCodeAt(bt)]=bt;var xl=function(r){var e=r.length*.75,A=r.length,t,n=0,s,i,a,o;r[r.length-1]==="="&&(e--,r[r.length-2]==="="&&e--);var c=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),l=Array.isArray(c)?c:new Uint8Array(c);for(t=0;t<A;t+=4)s=Ze[r.charCodeAt(t)],i=Ze[r.charCodeAt(t+1)],a=Ze[r.charCodeAt(t+2)],o=Ze[r.charCodeAt(t+3)],l[n++]=s<<2|i>>4,l[n++]=(i&15)<<4|a>>2,l[n++]=(a&3)<<6|o&63;return c},Ll=function(r){for(var e=r.length,A=[],t=0;t<e;t+=2)A.push(r[t+1]<<8|r[t]);return A},Kl=function(r){for(var e=r.length,A=[],t=0;t<e;t+=4)A.push(r[t+3]<<24|r[t+2]<<16|r[t+1]<<8|r[t]);return A},me=5,on=6+5,Qr=2,Sl=on-me,ri=65536>>me,Dl=1<<me,pr=Dl-1,Tl=1024>>me,kl=ri+Tl,Ol=kl,Rl=32,Ml=Ol+Rl,Gl=65536>>on,Nl=1<<Sl,Vl=Nl-1,Pn=function(r,e,A){return r.slice?r.slice(e,A):new Uint16Array(Array.prototype.slice.call(r,e,A))},Pl=function(r,e,A){return r.slice?r.slice(e,A):new Uint32Array(Array.prototype.slice.call(r,e,A))},Xl=function(r,e){var A=xl(r),t=Array.isArray(A)?Kl(A):new Uint32Array(A),n=Array.isArray(A)?Ll(A):new Uint16Array(A),s=24,i=Pn(n,s/2,t[4]/2),a=t[5]===2?Pn(n,(s+t[4])/2):Pl(t,Math.ceil((s+t[4])/4));return new Wl(t[0],t[1],t[2],t[3],i,a)},Wl=function(){function r(e,A,t,n,s,i){this.initialValue=e,this.errorValue=A,this.highStart=t,this.highValueIndex=n,this.index=s,this.data=i}return r.prototype.get=function(e){var A;if(e>=0){if(e<55296||e>56319&&e<=65535)return A=this.index[e>>me],A=(A<<Qr)+(e&pr),this.data[A];if(e<=65535)return A=this.index[ri+(e-55296>>me)],A=(A<<Qr)+(e&pr),this.data[A];if(e<this.highStart)return A=Ml-Gl+(e>>on),A=this.index[A],A+=e>>me&Vl,A=this.index[A],A=(A<<Qr)+(e&pr),this.data[A];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},r}(),Xn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Jl=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var xt=0;xt<Xn.length;xt++)Jl[Xn.charCodeAt(xt)]=xt;var Yl=1,Cr=2,Ur=3,Wn=4,Jn=5,zl=7,Yn=8,Fr=9,mr=10,zn=11,Zn=12,jn=13,qn=14,vr=15,Zl=function(r){for(var e=[],A=0,t=r.length;A<t;){var n=r.charCodeAt(A++);if(n>=55296&&n<=56319&&A<t){var s=r.charCodeAt(A++);(s&64512)===56320?e.push(((n&1023)<<10)+(s&1023)+65536):(e.push(n),A--)}else e.push(n)}return e},jl=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,r);var A=r.length;if(!A)return"";for(var t=[],n=-1,s="";++n<A;){var i=r[n];i<=65535?t.push(i):(i-=65536,t.push((i>>10)+55296,i%1024+56320)),(n+1===A||t.length>16384)&&(s+=String.fromCharCode.apply(String,t),t.length=0)}return s},ql=Xl(bl),GA="×",Er="÷",$l=function(r){return ql.get(r)},Au=function(r,e,A){var t=A-2,n=e[t],s=e[A-1],i=e[A];if(s===Cr&&i===Ur)return GA;if(s===Cr||s===Ur||s===Wn||i===Cr||i===Ur||i===Wn)return Er;if(s===Yn&&[Yn,Fr,zn,Zn].indexOf(i)!==-1||(s===zn||s===Fr)&&(i===Fr||i===mr)||(s===Zn||s===mr)&&i===mr||i===jn||i===Jn||i===zl||s===Yl)return GA;if(s===jn&&i===qn){for(;n===Jn;)n=e[--t];if(n===qn)return GA}if(s===vr&&i===vr){for(var a=0;n===vr;)a++,n=e[--t];if(a%2===0)return GA}return Er},eu=function(r){var e=Zl(r),A=e.length,t=0,n=0,s=e.map($l);return{next:function(){if(t>=A)return{done:!0,value:null};for(var i=GA;t<A&&(i=Au(e,s,++t))===GA;);if(i!==GA||t===A){var a=jl.apply(null,e.slice(n,t));return n=t,{value:a,done:!1}}return{done:!0,value:null}}}},tu=function(r){for(var e=eu(r),A=[],t;!(t=e.next()).done;)t.value&&A.push(t.value.slice());return A},ru=function(r){var e=123;if(r.createRange){var A=r.createRange();if(A.getBoundingClientRect){var t=r.createElement("boundtest");t.style.height=e+"px",t.style.display="block",r.body.appendChild(t),A.selectNode(t);var n=A.getBoundingClientRect(),s=Math.round(n.height);if(r.body.removeChild(t),s===e)return!0}}return!1},nu=function(r){var e=r.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",r.body.appendChild(e);var A=r.createRange();e.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var t=e.firstChild,n=Ar(t.data).map(function(o){return EA(o)}),s=0,i={},a=n.every(function(o,c){A.setStart(t,s),A.setEnd(t,s+o.length);var l=A.getBoundingClientRect();s+=o.length;var B=l.x>i.x||l.y>i.y;return i=l,c===0?!0:B});return r.body.removeChild(e),a},su=function(){return typeof new Image().crossOrigin<"u"},iu=function(){return typeof new XMLHttpRequest().responseType=="string"},au=function(r){var e=new Image,A=r.createElement("canvas"),t=A.getContext("2d");if(!t)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{t.drawImage(e,0,0),A.toDataURL()}catch{return!1}return!0},$n=function(r){return r[0]===0&&r[1]===255&&r[2]===0&&r[3]===255},ou=function(r){var e=r.createElement("canvas"),A=100;e.width=A,e.height=A;var t=e.getContext("2d");if(!t)return Promise.reject(!1);t.fillStyle="rgb(0, 255, 0)",t.fillRect(0,0,A,A);var n=new Image,s=e.toDataURL();n.src=s;var i=Yr(A,A,0,0,n);return t.fillStyle="red",t.fillRect(0,0,A,A),As(i).then(function(a){t.drawImage(a,0,0);var o=t.getImageData(0,0,A,A).data;t.fillStyle="red",t.fillRect(0,0,A,A);var c=r.createElement("div");return c.style.backgroundImage="url("+s+")",c.style.height=A+"px",$n(o)?As(Yr(A,A,0,0,c)):Promise.reject(!1)}).then(function(a){return t.drawImage(a,0,0),$n(t.getImageData(0,0,A,A).data)}).catch(function(){return!1})},Yr=function(r,e,A,t,n){var s="http://www.w3.org/2000/svg",i=document.createElementNS(s,"svg"),a=document.createElementNS(s,"foreignObject");return i.setAttributeNS(null,"width",r.toString()),i.setAttributeNS(null,"height",e.toString()),a.setAttributeNS(null,"width","100%"),a.setAttributeNS(null,"height","100%"),a.setAttributeNS(null,"x",A.toString()),a.setAttributeNS(null,"y",t.toString()),a.setAttributeNS(null,"externalResourcesRequired","true"),i.appendChild(a),a.appendChild(n),i},As=function(r){return new Promise(function(e,A){var t=new Image;t.onload=function(){return e(t)},t.onerror=A,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(r))})},bA={get SUPPORT_RANGE_BOUNDS(){var r=ru(document);return Object.defineProperty(bA,"SUPPORT_RANGE_BOUNDS",{value:r}),r},get SUPPORT_WORD_BREAKING(){var r=bA.SUPPORT_RANGE_BOUNDS&&nu(document);return Object.defineProperty(bA,"SUPPORT_WORD_BREAKING",{value:r}),r},get SUPPORT_SVG_DRAWING(){var r=au(document);return Object.defineProperty(bA,"SUPPORT_SVG_DRAWING",{value:r}),r},get SUPPORT_FOREIGNOBJECT_DRAWING(){var r=typeof Array.from=="function"&&typeof window.fetch=="function"?ou(document):Promise.resolve(!1);return Object.defineProperty(bA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:r}),r},get SUPPORT_CORS_IMAGES(){var r=su();return Object.defineProperty(bA,"SUPPORT_CORS_IMAGES",{value:r}),r},get SUPPORT_RESPONSE_TYPE(){var r=iu();return Object.defineProperty(bA,"SUPPORT_RESPONSE_TYPE",{value:r}),r},get SUPPORT_CORS_XHR(){var r="withCredentials"in new XMLHttpRequest;return Object.defineProperty(bA,"SUPPORT_CORS_XHR",{value:r}),r},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var r=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(bA,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:r}),r}},tt=function(){function r(e,A){this.text=e,this.bounds=A}return r}(),Bu=function(r,e,A,t){var n=uu(e,A),s=[],i=0;return n.forEach(function(a){if(A.textDecorationLine.length||a.trim().length>0)if(bA.SUPPORT_RANGE_BOUNDS){var o=es(t,i,a.length).getClientRects();if(o.length>1){var c=Bn(a),l=0;c.forEach(function(h){s.push(new tt(h,ae.fromDOMRectList(r,es(t,l+i,h.length).getClientRects()))),l+=h.length})}else s.push(new tt(a,ae.fromDOMRectList(r,o)))}else{var B=t.splitText(a.length);s.push(new tt(a,cu(r,t))),t=B}else bA.SUPPORT_RANGE_BOUNDS||(t=t.splitText(a.length));i+=a.length}),s},cu=function(r,e){var A=e.ownerDocument;if(A){var t=A.createElement("html2canvaswrapper");t.appendChild(e.cloneNode(!0));var n=e.parentNode;if(n){n.replaceChild(t,e);var s=$t(r,t);return t.firstChild&&n.replaceChild(t.firstChild,t),s}}return ae.EMPTY},es=function(r,e,A){var t=r.ownerDocument;if(!t)throw new Error("Node has no owner document");var n=t.createRange();return n.setStart(r,e),n.setEnd(r,e+A),n},Bn=function(r){if(bA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(r)).map(function(A){return A.segment})}return tu(r)},lu=function(r,e){if(bA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(A.segment(r)).map(function(t){return t.segment})}return hu(r,e)},uu=function(r,e){return e.letterSpacing!==0?Bn(r):lu(r,e)},fu=[32,160,4961,65792,65793,4153,4241],hu=function(r,e){for(var A=Go(r,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap==="break-word"?"break-word":e.wordBreak}),t=[],n,s=function(){if(n.value){var i=n.value.slice(),a=Ar(i),o="";a.forEach(function(c){fu.indexOf(c)===-1?o+=EA(c):(o.length&&t.push(o),t.push(EA(c)),o="")}),o.length&&t.push(o)}};!(n=A.next()).done;)s();return t},du=function(){function r(e,A,t){this.text=gu(A.data,t.textTransform),this.textBounds=Bu(e,this.text,t,A)}return r}(),gu=function(r,e){switch(e){case 1:return r.toLowerCase();case 3:return r.replace(wu,Qu);case 2:return r.toUpperCase();default:return r}},wu=/(^|\s|:|-|\(|\))([a-z])/g,Qu=function(r,e,A){return r.length>0?e+A.toUpperCase():r},ni=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;return n.src=t.currentSrc||t.src,n.intrinsicWidth=t.naturalWidth,n.intrinsicHeight=t.naturalHeight,n.context.cache.addImage(n.src),n}return e}(ee),si=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;return n.canvas=t,n.intrinsicWidth=t.width,n.intrinsicHeight=t.height,n}return e}(ee),ii=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this,s=new XMLSerializer,i=$t(A,t);return t.setAttribute("width",i.width+"px"),t.setAttribute("height",i.height+"px"),n.svg="data:image/svg+xml,"+encodeURIComponent(s.serializeToString(t)),n.intrinsicWidth=t.width.baseVal.value,n.intrinsicHeight=t.height.baseVal.value,n.context.cache.addImage(n.svg),n}return e}(ee),ai=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;return n.value=t.value,n}return e}(ee),zr=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;return n.start=t.start,n.reversed=typeof t.reversed=="boolean"&&t.reversed===!0,n}return e}(ee),pu=[{type:15,flags:0,unit:"px",number:3}],Cu=[{type:16,flags:0,number:50}],Uu=function(r){return r.width>r.height?new ae(r.left+(r.width-r.height)/2,r.top,r.height,r.height):r.width<r.height?new ae(r.left,r.top+(r.height-r.width)/2,r.width,r.width):r},Fu=function(r){var e=r.type===mu?new Array(r.value.length+1).join("•"):r.value;return e.length===0?r.placeholder||"":e},Pt="checkbox",Xt="radio",mu="password",ts=707406591,cn=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;switch(n.type=t.type.toLowerCase(),n.checked=t.checked,n.value=Fu(t),(n.type===Pt||n.type===Xt)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=1,n.styles.backgroundClip=[0],n.styles.backgroundOrigin=[0],n.bounds=Uu(n.bounds)),n.type){case Pt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=pu;break;case Xt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Cu;break}return n}return e}(ee),oi=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this,s=t.options[t.selectedIndex||0];return n.value=s&&s.text||"",n}return e}(ee),Bi=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;return n.value=t.value,n}return e}(ee),ci=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;n.src=t.src,n.width=parseInt(t.width,10)||0,n.height=parseInt(t.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){n.tree=ui(A,t.contentWindow.document.documentElement);var s=t.contentWindow.document.documentElement?At(A,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):ie.TRANSPARENT,i=t.contentWindow.document.body?At(A,getComputedStyle(t.contentWindow.document.body).backgroundColor):ie.TRANSPARENT;n.backgroundColor=we(s)?we(i)?n.styles.backgroundColor:i:s}}catch{}return n}return e}(ee),vu=["OL","UL","MENU"],Ot=function(r,e,A,t){for(var n=e.firstChild,s=void 0;n;n=s)if(s=n.nextSibling,fi(n)&&n.data.trim().length>0)A.textNodes.push(new du(r,n,A.styles));else if(Le(n))if(wi(n)&&n.assignedNodes)n.assignedNodes().forEach(function(a){return Ot(r,a,A,t)});else{var i=li(r,n);i.styles.isVisible()&&(Eu(n,i,t)?i.flags|=4:yu(i.styles)&&(i.flags|=2),vu.indexOf(n.tagName)!==-1&&(i.flags|=8),A.elements.push(i),n.slot,n.shadowRoot?Ot(r,n.shadowRoot,i,t):!Wt(n)&&!hi(n)&&!Jt(n)&&Ot(r,n,i,t))}},li=function(r,e){return jr(e)?new ni(r,e):di(e)?new si(r,e):hi(e)?new ii(r,e):Hu(e)?new ai(r,e):Iu(e)?new zr(r,e):_u(e)?new cn(r,e):Jt(e)?new oi(r,e):Wt(e)?new Bi(r,e):gi(e)?new ci(r,e):new ee(r,e)},ui=function(r,e){var A=li(r,e);return A.flags|=4,Ot(r,e,A,A),A},Eu=function(r,e,A){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||ln(r)&&A.styles.isTransparent()},yu=function(r){return r.isPositioned()||r.isFloating()},fi=function(r){return r.nodeType===Node.TEXT_NODE},Le=function(r){return r.nodeType===Node.ELEMENT_NODE},Zr=function(r){return Le(r)&&typeof r.style<"u"&&!Rt(r)},Rt=function(r){return typeof r.className=="object"},Hu=function(r){return r.tagName==="LI"},Iu=function(r){return r.tagName==="OL"},_u=function(r){return r.tagName==="INPUT"},bu=function(r){return r.tagName==="HTML"},hi=function(r){return r.tagName==="svg"},ln=function(r){return r.tagName==="BODY"},di=function(r){return r.tagName==="CANVAS"},rs=function(r){return r.tagName==="VIDEO"},jr=function(r){return r.tagName==="IMG"},gi=function(r){return r.tagName==="IFRAME"},ns=function(r){return r.tagName==="STYLE"},xu=function(r){return r.tagName==="SCRIPT"},Wt=function(r){return r.tagName==="TEXTAREA"},Jt=function(r){return r.tagName==="SELECT"},wi=function(r){return r.tagName==="SLOT"},ss=function(r){return r.tagName.indexOf("-")>0},Lu=function(){function r(){this.counters={}}return r.prototype.getCounterValue=function(e){var A=this.counters[e];return A&&A.length?A[A.length-1]:1},r.prototype.getCounterValues=function(e){var A=this.counters[e];return A||[]},r.prototype.pop=function(e){var A=this;e.forEach(function(t){return A.counters[t].pop()})},r.prototype.parse=function(e){var A=this,t=e.counterIncrement,n=e.counterReset,s=!0;t!==null&&t.forEach(function(a){var o=A.counters[a.counter];o&&a.increment!==0&&(s=!1,o.length||o.push(1),o[Math.max(0,o.length-1)]+=a.increment)});var i=[];return s&&n.forEach(function(a){var o=A.counters[a.counter];i.push(a.counter),o||(o=A.counters[a.counter]=[]),o.push(a.reset)}),i},r}(),is={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},as={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},Ku={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},Su={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},He=function(r,e,A,t,n,s){return r<e||r>A?ot(r,n,s.length>0):t.integers.reduce(function(i,a,o){for(;r>=a;)r-=a,i+=t.values[o];return i},"")+s},Qi=function(r,e,A,t){var n="";do A||r--,n=t(r)+n,r/=e;while(r*e>=e);return n},mA=function(r,e,A,t,n){var s=A-e+1;return(r<0?"-":"")+(Qi(Math.abs(r),s,t,function(i){return EA(Math.floor(i%s)+e)})+n)},pe=function(r,e,A){A===void 0&&(A=". ");var t=e.length;return Qi(Math.abs(r),t,!1,function(n){return e[Math.floor(n%t)]})+A},be=1,oe=2,Be=4,je=8,ne=function(r,e,A,t,n,s){if(r<-9999||r>9999)return ot(r,4,n.length>0);var i=Math.abs(r),a=n;if(i===0)return e[0]+a;for(var o=0;i>0&&o<=4;o++){var c=i%10;c===0&&HA(s,be)&&a!==""?a=e[c]+a:c>1||c===1&&o===0||c===1&&o===1&&HA(s,oe)||c===1&&o===1&&HA(s,Be)&&r>100||c===1&&o>1&&HA(s,je)?a=e[c]+(o>0?A[o-1]:"")+a:c===1&&o>0&&(a=A[o-1]+a),i=Math.floor(i/10)}return(r<0?t:"")+a},os="十百千萬",Bs="拾佰仟萬",cs="マイナス",yr="마이너스",ot=function(r,e,A){var t=A?". ":"",n=A?"、":"",s=A?", ":"",i=A?" ":"";switch(e){case 0:return"•"+i;case 1:return"◦"+i;case 2:return"◾"+i;case 5:var a=mA(r,48,57,!0,t);return a.length<4?"0"+a:a;case 4:return pe(r,"〇一二三四五六七八九",n);case 6:return He(r,1,3999,is,3,t).toLowerCase();case 7:return He(r,1,3999,is,3,t);case 8:return mA(r,945,969,!1,t);case 9:return mA(r,97,122,!1,t);case 10:return mA(r,65,90,!1,t);case 11:return mA(r,1632,1641,!0,t);case 12:case 49:return He(r,1,9999,as,3,t);case 35:return He(r,1,9999,as,3,t).toLowerCase();case 13:return mA(r,2534,2543,!0,t);case 14:case 30:return mA(r,6112,6121,!0,t);case 15:return pe(r,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return pe(r,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return ne(r,"零一二三四五六七八九",os,"負",n,oe|Be|je);case 47:return ne(r,"零壹貳參肆伍陸柒捌玖",Bs,"負",n,be|oe|Be|je);case 42:return ne(r,"零一二三四五六七八九",os,"负",n,oe|Be|je);case 41:return ne(r,"零壹贰叁肆伍陆柒捌玖",Bs,"负",n,be|oe|Be|je);case 26:return ne(r,"〇一二三四五六七八九","十百千万",cs,n,0);case 25:return ne(r,"零壱弐参四伍六七八九","拾百千万",cs,n,be|oe|Be);case 31:return ne(r,"영일이삼사오육칠팔구","십백천만",yr,s,be|oe|Be);case 33:return ne(r,"零一二三四五六七八九","十百千萬",yr,s,0);case 32:return ne(r,"零壹貳參四五六七八九","拾百千",yr,s,be|oe|Be);case 18:return mA(r,2406,2415,!0,t);case 20:return He(r,1,19999,Su,3,t);case 21:return mA(r,2790,2799,!0,t);case 22:return mA(r,2662,2671,!0,t);case 22:return He(r,1,10999,Ku,3,t);case 23:return pe(r,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return pe(r,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return mA(r,3302,3311,!0,t);case 28:return pe(r,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return pe(r,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return mA(r,3792,3801,!0,t);case 37:return mA(r,6160,6169,!0,t);case 38:return mA(r,4160,4169,!0,t);case 39:return mA(r,2918,2927,!0,t);case 40:return mA(r,1776,1785,!0,t);case 43:return mA(r,3046,3055,!0,t);case 44:return mA(r,3174,3183,!0,t);case 45:return mA(r,3664,3673,!0,t);case 46:return mA(r,3872,3881,!0,t);case 3:default:return mA(r,48,57,!0,t)}},pi="data-html2canvas-ignore",ls=function(){function r(e,A,t){if(this.context=e,this.options=t,this.scrolledElements=[],this.referenceElement=A,this.counters=new Lu,this.quoteDepth=0,!A.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(A.ownerDocument.documentElement,!1)}return r.prototype.toIFrame=function(e,A){var t=this,n=Du(e,A);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var s=e.defaultView.pageXOffset,i=e.defaultView.pageYOffset,a=n.contentWindow,o=a.document,c=Ou(n).then(function(){return TA(t,void 0,void 0,function(){var l,B;return LA(this,function(h){switch(h.label){case 0:return this.scrolledElements.forEach(Nu),a&&(a.scrollTo(A.left,A.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(a.scrollY!==A.top||a.scrollX!==A.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(a.scrollX-A.left,a.scrollY-A.top,0,0))),l=this.options.onclone,B=this.clonedReferenceElement,typeof B>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:o.fonts&&o.fonts.ready?[4,o.fonts.ready]:[3,2];case 1:h.sent(),h.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,ku(o)]:[3,4];case 3:h.sent(),h.label=4;case 4:return typeof l=="function"?[2,Promise.resolve().then(function(){return l(o,B)}).then(function(){return n})]:[2,n]}})})});return o.open(),o.write(Mu(document.doctype)+"<html></html>"),Gu(this.referenceElement.ownerDocument,s,i),o.replaceChild(o.adoptNode(this.documentElement),o.documentElement),o.close(),c},r.prototype.createElementClone=function(e){if(Jr(e,2))debugger;if(di(e))return this.createCanvasClone(e);if(rs(e))return this.createVideoClone(e);if(ns(e))return this.createStyleClone(e);var A=e.cloneNode(!1);return jr(A)&&(jr(e)&&e.currentSrc&&e.currentSrc!==e.src&&(A.src=e.currentSrc,A.srcset=""),A.loading==="lazy"&&(A.loading="eager")),ss(A)?this.createCustomElementClone(A):A},r.prototype.createCustomElementClone=function(e){var A=document.createElement("html2canvascustomelement");return Hr(e.style,A),A},r.prototype.createStyleClone=function(e){try{var A=e.sheet;if(A&&A.cssRules){var t=[].slice.call(A.cssRules,0).reduce(function(s,i){return i&&typeof i.cssText=="string"?s+i.cssText:s},""),n=e.cloneNode(!1);return n.textContent=t,n}}catch(s){if(this.context.logger.error("Unable to access cssRules property",s),s.name!=="SecurityError")throw s}return e.cloneNode(!1)},r.prototype.createCanvasClone=function(e){var A;if(this.options.inlineImages&&e.ownerDocument){var t=e.ownerDocument.createElement("img");try{return t.src=e.toDataURL(),t}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",e)}}var n=e.cloneNode(!1);try{n.width=e.width,n.height=e.height;var s=e.getContext("2d"),i=n.getContext("2d");if(i)if(!this.options.allowTaint&&s)i.putImageData(s.getImageData(0,0,e.width,e.height),0,0);else{var a=(A=e.getContext("webgl2"))!==null&&A!==void 0?A:e.getContext("webgl");if(a){var o=a.getContextAttributes();(o==null?void 0:o.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",e)}i.drawImage(e,0,0)}return n}catch{this.context.logger.info("Unable to clone canvas as it is tainted",e)}return n},r.prototype.createVideoClone=function(e){var A=e.ownerDocument.createElement("canvas");A.width=e.offsetWidth,A.height=e.offsetHeight;var t=A.getContext("2d");try{return t&&(t.drawImage(e,0,0,A.width,A.height),this.options.allowTaint||t.getImageData(0,0,A.width,A.height)),A}catch{this.context.logger.info("Unable to clone video as it is tainted",e)}var n=e.ownerDocument.createElement("canvas");return n.width=e.offsetWidth,n.height=e.offsetHeight,n},r.prototype.appendChildNode=function(e,A,t){(!Le(A)||!xu(A)&&!A.hasAttribute(pi)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(A)))&&(!this.options.copyStyles||!Le(A)||!ns(A))&&e.appendChild(this.cloneNode(A,t))},r.prototype.cloneChildNodes=function(e,A,t){for(var n=this,s=e.shadowRoot?e.shadowRoot.firstChild:e.firstChild;s;s=s.nextSibling)if(Le(s)&&wi(s)&&typeof s.assignedNodes=="function"){var i=s.assignedNodes();i.length&&i.forEach(function(a){return n.appendChildNode(A,a,t)})}else this.appendChildNode(A,s,t)},r.prototype.cloneNode=function(e,A){if(fi(e))return document.createTextNode(e.data);if(!e.ownerDocument)return e.cloneNode(!1);var t=e.ownerDocument.defaultView;if(t&&Le(e)&&(Zr(e)||Rt(e))){var n=this.createElementClone(e);n.style.transitionProperty="none";var s=t.getComputedStyle(e),i=t.getComputedStyle(e,":before"),a=t.getComputedStyle(e,":after");this.referenceElement===e&&Zr(n)&&(this.clonedReferenceElement=n),ln(n)&&Xu(n);var o=this.counters.parse(new Nn(this.context,s)),c=this.resolvePseudoContent(e,n,i,rt.BEFORE);ss(e)&&(A=!0),rs(e)||this.cloneChildNodes(e,n,A),c&&n.insertBefore(c,n.firstChild);var l=this.resolvePseudoContent(e,n,a,rt.AFTER);return l&&n.appendChild(l),this.counters.pop(o),(s&&(this.options.copyStyles||Rt(e))&&!gi(e)||A)&&Hr(s,n),(e.scrollTop!==0||e.scrollLeft!==0)&&this.scrolledElements.push([n,e.scrollLeft,e.scrollTop]),(Wt(e)||Jt(e))&&(Wt(n)||Jt(n))&&(n.value=e.value),n}return e.cloneNode(!1)},r.prototype.resolvePseudoContent=function(e,A,t,n){var s=this;if(t){var i=t.content,a=A.ownerDocument;if(!(!a||!i||i==="none"||i==="-moz-alt-content"||t.display==="none")){this.counters.parse(new Nn(this.context,t));var o=new Hl(this.context,t),c=a.createElement("html2canvaspseudoelement");Hr(t,c),o.content.forEach(function(B){if(B.type===0)c.appendChild(a.createTextNode(B.value));else if(B.type===22){var h=a.createElement("img");h.src=B.value,h.style.opacity="1",c.appendChild(h)}else if(B.type===18){if(B.name==="attr"){var u=B.values.filter(gA);u.length&&c.appendChild(a.createTextNode(e.getAttribute(u[0].value)||""))}else if(B.name==="counter"){var p=B.values.filter(Se),f=p[0],w=p[1];if(f&&gA(f)){var g=s.counters.getCounterValue(f.value),C=w&&gA(w)?Wr.parse(s.context,w.value):3;c.appendChild(a.createTextNode(ot(g,C,!1)))}}else if(B.name==="counters"){var F=B.values.filter(Se),f=F[0],y=F[1],w=F[2];if(f&&gA(f)){var v=s.counters.getCounterValues(f.value),H=w&&gA(w)?Wr.parse(s.context,w.value):3,b=y&&y.type===0?y.value:"",T=v.map(function(Z){return ot(Z,H,!1)}).join(b);c.appendChild(a.createTextNode(T))}}}else if(B.type===20)switch(B.value){case"open-quote":c.appendChild(a.createTextNode(Gn(o.quotes,s.quoteDepth++,!0)));break;case"close-quote":c.appendChild(a.createTextNode(Gn(o.quotes,--s.quoteDepth,!1)));break;default:c.appendChild(a.createTextNode(B.value))}}),c.className=qr+" "+$r;var l=n===rt.BEFORE?" "+qr:" "+$r;return Rt(A)?A.className.baseValue+=l:A.className+=l,c}}},r.destroy=function(e){return e.parentNode?(e.parentNode.removeChild(e),!0):!1},r}(),rt;(function(r){r[r.BEFORE=0]="BEFORE",r[r.AFTER=1]="AFTER"})(rt||(rt={}));var Du=function(r,e){var A=r.createElement("iframe");return A.className="html2canvas-container",A.style.visibility="hidden",A.style.position="fixed",A.style.left="-10000px",A.style.top="0px",A.style.border="0",A.width=e.width.toString(),A.height=e.height.toString(),A.scrolling="no",A.setAttribute(pi,"true"),r.body.appendChild(A),A},Tu=function(r){return new Promise(function(e){if(r.complete){e();return}if(!r.src){e();return}r.onload=e,r.onerror=e})},ku=function(r){return Promise.all([].slice.call(r.images,0).map(Tu))},Ou=function(r){return new Promise(function(e,A){var t=r.contentWindow;if(!t)return A("No window assigned for iframe");var n=t.document;t.onload=r.onload=function(){t.onload=r.onload=null;var s=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(s),e(r))},50)}})},Ru=["all","d","content"],Hr=function(r,e){for(var A=r.length-1;A>=0;A--){var t=r.item(A);Ru.indexOf(t)===-1&&e.style.setProperty(t,r.getPropertyValue(t))}return e},Mu=function(r){var e="";return r&&(e+="<!DOCTYPE ",r.name&&(e+=r.name),r.internalSubset&&(e+=r.internalSubset),r.publicId&&(e+='"'+r.publicId+'"'),r.systemId&&(e+='"'+r.systemId+'"'),e+=">"),e},Gu=function(r,e,A){r&&r.defaultView&&(e!==r.defaultView.pageXOffset||A!==r.defaultView.pageYOffset)&&r.defaultView.scrollTo(e,A)},Nu=function(r){var e=r[0],A=r[1],t=r[2];e.scrollLeft=A,e.scrollTop=t},Vu=":before",Pu=":after",qr="___html2canvas___pseudoelement_before",$r="___html2canvas___pseudoelement_after",us=`{
    content: "" !important;
    display: none !important;
}`,Xu=function(r){Wu(r,"."+qr+Vu+us+`
         .`+$r+Pu+us)},Wu=function(r,e){var A=r.ownerDocument;if(A){var t=A.createElement("style");t.textContent=e,r.appendChild(t)}},Ci=function(){function r(){}return r.getOrigin=function(e){var A=r._link;return A?(A.href=e,A.href=A.href,A.protocol+A.hostname+A.port):"about:blank"},r.isSameOrigin=function(e){return r.getOrigin(e)===r._origin},r.setContext=function(e){r._link=e.document.createElement("a"),r._origin=r.getOrigin(e.location.href)},r._origin="about:blank",r}(),Ju=function(){function r(e,A){this.context=e,this._options=A,this._cache={}}return r.prototype.addImage=function(e){var A=Promise.resolve();return this.has(e)||(_r(e)||ju(e))&&(this._cache[e]=this.loadImage(e)).catch(function(){}),A},r.prototype.match=function(e){return this._cache[e]},r.prototype.loadImage=function(e){return TA(this,void 0,void 0,function(){var A,t,n,s,i=this;return LA(this,function(a){switch(a.label){case 0:return A=Ci.isSameOrigin(e),t=!Ir(e)&&this._options.useCORS===!0&&bA.SUPPORT_CORS_IMAGES&&!A,n=!Ir(e)&&!A&&!_r(e)&&typeof this._options.proxy=="string"&&bA.SUPPORT_CORS_XHR&&!t,!A&&this._options.allowTaint===!1&&!Ir(e)&&!_r(e)&&!n&&!t?[2]:(s=e,n?[4,this.proxy(s)]:[3,2]);case 1:s=a.sent(),a.label=2;case 2:return this.context.logger.debug("Added image "+e.substring(0,256)),[4,new Promise(function(o,c){var l=new Image;l.onload=function(){return o(l)},l.onerror=c,(qu(s)||t)&&(l.crossOrigin="anonymous"),l.src=s,l.complete===!0&&setTimeout(function(){return o(l)},500),i._options.imageTimeout>0&&setTimeout(function(){return c("Timed out ("+i._options.imageTimeout+"ms) loading image")},i._options.imageTimeout)})];case 3:return[2,a.sent()]}})})},r.prototype.has=function(e){return typeof this._cache[e]<"u"},r.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},r.prototype.proxy=function(e){var A=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var n=e.substring(0,256);return new Promise(function(s,i){var a=bA.SUPPORT_RESPONSE_TYPE?"blob":"text",o=new XMLHttpRequest;o.onload=function(){if(o.status===200)if(a==="text")s(o.response);else{var B=new FileReader;B.addEventListener("load",function(){return s(B.result)},!1),B.addEventListener("error",function(h){return i(h)},!1),B.readAsDataURL(o.response)}else i("Failed to proxy resource "+n+" with status code "+o.status)},o.onerror=i;var c=t.indexOf("?")>-1?"&":"?";if(o.open("GET",""+t+c+"url="+encodeURIComponent(e)+"&responseType="+a),a!=="text"&&o instanceof XMLHttpRequest&&(o.responseType=a),A._options.imageTimeout){var l=A._options.imageTimeout;o.timeout=l,o.ontimeout=function(){return i("Timed out ("+l+"ms) proxying "+n)}}o.send()})},r}(),Yu=/^data:image\/svg\+xml/i,zu=/^data:image\/.*;base64,/i,Zu=/^data:image\/.*/i,ju=function(r){return bA.SUPPORT_SVG_DRAWING||!$u(r)},Ir=function(r){return Zu.test(r)},qu=function(r){return zu.test(r)},_r=function(r){return r.substr(0,4)==="blob"},$u=function(r){return r.substr(-3).toLowerCase()==="svg"||Yu.test(r)},z=function(){function r(e,A){this.type=0,this.x=e,this.y=A}return r.prototype.add=function(e,A){return new r(this.x+e,this.y+A)},r}(),Ie=function(r,e,A){return new z(r.x+(e.x-r.x)*A,r.y+(e.y-r.y)*A)},Lt=function(){function r(e,A,t,n){this.type=1,this.start=e,this.startControl=A,this.endControl=t,this.end=n}return r.prototype.subdivide=function(e,A){var t=Ie(this.start,this.startControl,e),n=Ie(this.startControl,this.endControl,e),s=Ie(this.endControl,this.end,e),i=Ie(t,n,e),a=Ie(n,s,e),o=Ie(i,a,e);return A?new r(this.start,t,i,o):new r(o,a,s,this.end)},r.prototype.add=function(e,A){return new r(this.start.add(e,A),this.startControl.add(e,A),this.endControl.add(e,A),this.end.add(e,A))},r.prototype.reverse=function(){return new r(this.end,this.endControl,this.startControl,this.start)},r}(),NA=function(r){return r.type===1},Af=function(){function r(e){var A=e.styles,t=e.bounds,n=ze(A.borderTopLeftRadius,t.width,t.height),s=n[0],i=n[1],a=ze(A.borderTopRightRadius,t.width,t.height),o=a[0],c=a[1],l=ze(A.borderBottomRightRadius,t.width,t.height),B=l[0],h=l[1],u=ze(A.borderBottomLeftRadius,t.width,t.height),p=u[0],f=u[1],w=[];w.push((s+o)/t.width),w.push((p+B)/t.width),w.push((i+f)/t.height),w.push((c+h)/t.height);var g=Math.max.apply(Math,w);g>1&&(s/=g,i/=g,o/=g,c/=g,B/=g,h/=g,p/=g,f/=g);var C=t.width-o,F=t.height-h,y=t.width-B,v=t.height-f,H=A.borderTopWidth,b=A.borderRightWidth,T=A.borderBottomWidth,_=A.borderLeftWidth,R=QA(A.paddingTop,e.bounds.width),Z=QA(A.paddingRight,e.bounds.width),E=QA(A.paddingBottom,e.bounds.width),K=QA(A.paddingLeft,e.bounds.width);this.topLeftBorderDoubleOuterBox=s>0||i>0?pA(t.left+_/3,t.top+H/3,s-_/3,i-H/3,fA.TOP_LEFT):new z(t.left+_/3,t.top+H/3),this.topRightBorderDoubleOuterBox=s>0||i>0?pA(t.left+C,t.top+H/3,o-b/3,c-H/3,fA.TOP_RIGHT):new z(t.left+t.width-b/3,t.top+H/3),this.bottomRightBorderDoubleOuterBox=B>0||h>0?pA(t.left+y,t.top+F,B-b/3,h-T/3,fA.BOTTOM_RIGHT):new z(t.left+t.width-b/3,t.top+t.height-T/3),this.bottomLeftBorderDoubleOuterBox=p>0||f>0?pA(t.left+_/3,t.top+v,p-_/3,f-T/3,fA.BOTTOM_LEFT):new z(t.left+_/3,t.top+t.height-T/3),this.topLeftBorderDoubleInnerBox=s>0||i>0?pA(t.left+_*2/3,t.top+H*2/3,s-_*2/3,i-H*2/3,fA.TOP_LEFT):new z(t.left+_*2/3,t.top+H*2/3),this.topRightBorderDoubleInnerBox=s>0||i>0?pA(t.left+C,t.top+H*2/3,o-b*2/3,c-H*2/3,fA.TOP_RIGHT):new z(t.left+t.width-b*2/3,t.top+H*2/3),this.bottomRightBorderDoubleInnerBox=B>0||h>0?pA(t.left+y,t.top+F,B-b*2/3,h-T*2/3,fA.BOTTOM_RIGHT):new z(t.left+t.width-b*2/3,t.top+t.height-T*2/3),this.bottomLeftBorderDoubleInnerBox=p>0||f>0?pA(t.left+_*2/3,t.top+v,p-_*2/3,f-T*2/3,fA.BOTTOM_LEFT):new z(t.left+_*2/3,t.top+t.height-T*2/3),this.topLeftBorderStroke=s>0||i>0?pA(t.left+_/2,t.top+H/2,s-_/2,i-H/2,fA.TOP_LEFT):new z(t.left+_/2,t.top+H/2),this.topRightBorderStroke=s>0||i>0?pA(t.left+C,t.top+H/2,o-b/2,c-H/2,fA.TOP_RIGHT):new z(t.left+t.width-b/2,t.top+H/2),this.bottomRightBorderStroke=B>0||h>0?pA(t.left+y,t.top+F,B-b/2,h-T/2,fA.BOTTOM_RIGHT):new z(t.left+t.width-b/2,t.top+t.height-T/2),this.bottomLeftBorderStroke=p>0||f>0?pA(t.left+_/2,t.top+v,p-_/2,f-T/2,fA.BOTTOM_LEFT):new z(t.left+_/2,t.top+t.height-T/2),this.topLeftBorderBox=s>0||i>0?pA(t.left,t.top,s,i,fA.TOP_LEFT):new z(t.left,t.top),this.topRightBorderBox=o>0||c>0?pA(t.left+C,t.top,o,c,fA.TOP_RIGHT):new z(t.left+t.width,t.top),this.bottomRightBorderBox=B>0||h>0?pA(t.left+y,t.top+F,B,h,fA.BOTTOM_RIGHT):new z(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=p>0||f>0?pA(t.left,t.top+v,p,f,fA.BOTTOM_LEFT):new z(t.left,t.top+t.height),this.topLeftPaddingBox=s>0||i>0?pA(t.left+_,t.top+H,Math.max(0,s-_),Math.max(0,i-H),fA.TOP_LEFT):new z(t.left+_,t.top+H),this.topRightPaddingBox=o>0||c>0?pA(t.left+Math.min(C,t.width-b),t.top+H,C>t.width+b?0:Math.max(0,o-b),Math.max(0,c-H),fA.TOP_RIGHT):new z(t.left+t.width-b,t.top+H),this.bottomRightPaddingBox=B>0||h>0?pA(t.left+Math.min(y,t.width-_),t.top+Math.min(F,t.height-T),Math.max(0,B-b),Math.max(0,h-T),fA.BOTTOM_RIGHT):new z(t.left+t.width-b,t.top+t.height-T),this.bottomLeftPaddingBox=p>0||f>0?pA(t.left+_,t.top+Math.min(v,t.height-T),Math.max(0,p-_),Math.max(0,f-T),fA.BOTTOM_LEFT):new z(t.left+_,t.top+t.height-T),this.topLeftContentBox=s>0||i>0?pA(t.left+_+K,t.top+H+R,Math.max(0,s-(_+K)),Math.max(0,i-(H+R)),fA.TOP_LEFT):new z(t.left+_+K,t.top+H+R),this.topRightContentBox=o>0||c>0?pA(t.left+Math.min(C,t.width+_+K),t.top+H+R,C>t.width+_+K?0:o-_+K,c-(H+R),fA.TOP_RIGHT):new z(t.left+t.width-(b+Z),t.top+H+R),this.bottomRightContentBox=B>0||h>0?pA(t.left+Math.min(y,t.width-(_+K)),t.top+Math.min(F,t.height+H+R),Math.max(0,B-(b+Z)),h-(T+E),fA.BOTTOM_RIGHT):new z(t.left+t.width-(b+Z),t.top+t.height-(T+E)),this.bottomLeftContentBox=p>0||f>0?pA(t.left+_+K,t.top+v,Math.max(0,p-(_+K)),f-(T+E),fA.BOTTOM_LEFT):new z(t.left+_+K,t.top+t.height-(T+E))}return r}(),fA;(function(r){r[r.TOP_LEFT=0]="TOP_LEFT",r[r.TOP_RIGHT=1]="TOP_RIGHT",r[r.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",r[r.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(fA||(fA={}));var pA=function(r,e,A,t,n){var s=4*((Math.sqrt(2)-1)/3),i=A*s,a=t*s,o=r+A,c=e+t;switch(n){case fA.TOP_LEFT:return new Lt(new z(r,c),new z(r,c-a),new z(o-i,e),new z(o,e));case fA.TOP_RIGHT:return new Lt(new z(r,e),new z(r+i,e),new z(o,c-a),new z(o,c));case fA.BOTTOM_RIGHT:return new Lt(new z(o,e),new z(o,e+a),new z(r+i,c),new z(r,c));case fA.BOTTOM_LEFT:default:return new Lt(new z(o,c),new z(o-i,c),new z(r,e+a),new z(r,e))}},Yt=function(r){return[r.topLeftBorderBox,r.topRightBorderBox,r.bottomRightBorderBox,r.bottomLeftBorderBox]},ef=function(r){return[r.topLeftContentBox,r.topRightContentBox,r.bottomRightContentBox,r.bottomLeftContentBox]},zt=function(r){return[r.topLeftPaddingBox,r.topRightPaddingBox,r.bottomRightPaddingBox,r.bottomLeftPaddingBox]},tf=function(){function r(e,A,t){this.offsetX=e,this.offsetY=A,this.matrix=t,this.type=0,this.target=6}return r}(),Kt=function(){function r(e,A){this.path=e,this.target=A,this.type=1}return r}(),rf=function(){function r(e){this.opacity=e,this.type=2,this.target=6}return r}(),nf=function(r){return r.type===0},Ui=function(r){return r.type===1},sf=function(r){return r.type===2},fs=function(r,e){return r.length===e.length?r.some(function(A,t){return A===e[t]}):!1},af=function(r,e,A,t,n){return r.map(function(s,i){switch(i){case 0:return s.add(e,A);case 1:return s.add(e+t,A);case 2:return s.add(e+t,A+n);case 3:return s.add(e,A+n)}return s})},Fi=function(){function r(e){this.element=e,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return r}(),mi=function(){function r(e,A){if(this.container=e,this.parent=A,this.effects=[],this.curves=new Af(this.container),this.container.styles.opacity<1&&this.effects.push(new rf(this.container.styles.opacity)),this.container.styles.transform!==null){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,n=this.container.bounds.top+this.container.styles.transformOrigin[1].number,s=this.container.styles.transform;this.effects.push(new tf(t,n,s))}if(this.container.styles.overflowX!==0){var i=Yt(this.curves),a=zt(this.curves);fs(i,a)?this.effects.push(new Kt(i,6)):(this.effects.push(new Kt(i,2)),this.effects.push(new Kt(a,4)))}}return r.prototype.getEffects=function(e){for(var A=[2,3].indexOf(this.container.styles.position)===-1,t=this.parent,n=this.effects.slice(0);t;){var s=t.effects.filter(function(o){return!Ui(o)});if(A||t.container.styles.position!==0||!t.parent){if(n.unshift.apply(n,s),A=[2,3].indexOf(t.container.styles.position)===-1,t.container.styles.overflowX!==0){var i=Yt(t.curves),a=zt(t.curves);fs(i,a)||n.unshift(new Kt(a,6))}}else n.unshift.apply(n,s);t=t.parent}return n.filter(function(o){return HA(o.target,e)})},r}(),An=function(r,e,A,t){r.container.elements.forEach(function(n){var s=HA(n.flags,4),i=HA(n.flags,2),a=new mi(n,r);HA(n.styles.display,2048)&&t.push(a);var o=HA(n.flags,8)?[]:t;if(s||i){var c=s||n.styles.isPositioned()?A:e,l=new Fi(a);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var B=n.styles.zIndex.order;if(B<0){var h=0;c.negativeZIndex.some(function(p,f){return B>p.element.container.styles.zIndex.order?(h=f,!1):h>0}),c.negativeZIndex.splice(h,0,l)}else if(B>0){var u=0;c.positiveZIndex.some(function(p,f){return B>=p.element.container.styles.zIndex.order?(u=f+1,!1):u>0}),c.positiveZIndex.splice(u,0,l)}else c.zeroOrAutoZIndexOrTransformedOrOpacity.push(l)}else n.styles.isFloating()?c.nonPositionedFloats.push(l):c.nonPositionedInlineLevel.push(l);An(a,l,s?l:A,o)}else n.styles.isInlineLevel()?e.inlineLevel.push(a):e.nonInlineLevel.push(a),An(a,e,A,o);HA(n.flags,8)&&vi(n,o)})},vi=function(r,e){for(var A=r instanceof zr?r.start:1,t=r instanceof zr?r.reversed:!1,n=0;n<e.length;n++){var s=e[n];s.container instanceof ai&&typeof s.container.value=="number"&&s.container.value!==0&&(A=s.container.value),s.listValue=ot(A,s.container.styles.listStyleType,!0),A+=t?-1:1}},of=function(r){var e=new mi(r,null),A=new Fi(e),t=[];return An(e,A,A,t),vi(e.container,t),A},hs=function(r,e){switch(e){case 0:return PA(r.topLeftBorderBox,r.topLeftPaddingBox,r.topRightBorderBox,r.topRightPaddingBox);case 1:return PA(r.topRightBorderBox,r.topRightPaddingBox,r.bottomRightBorderBox,r.bottomRightPaddingBox);case 2:return PA(r.bottomRightBorderBox,r.bottomRightPaddingBox,r.bottomLeftBorderBox,r.bottomLeftPaddingBox);case 3:default:return PA(r.bottomLeftBorderBox,r.bottomLeftPaddingBox,r.topLeftBorderBox,r.topLeftPaddingBox)}},Bf=function(r,e){switch(e){case 0:return PA(r.topLeftBorderBox,r.topLeftBorderDoubleOuterBox,r.topRightBorderBox,r.topRightBorderDoubleOuterBox);case 1:return PA(r.topRightBorderBox,r.topRightBorderDoubleOuterBox,r.bottomRightBorderBox,r.bottomRightBorderDoubleOuterBox);case 2:return PA(r.bottomRightBorderBox,r.bottomRightBorderDoubleOuterBox,r.bottomLeftBorderBox,r.bottomLeftBorderDoubleOuterBox);case 3:default:return PA(r.bottomLeftBorderBox,r.bottomLeftBorderDoubleOuterBox,r.topLeftBorderBox,r.topLeftBorderDoubleOuterBox)}},cf=function(r,e){switch(e){case 0:return PA(r.topLeftBorderDoubleInnerBox,r.topLeftPaddingBox,r.topRightBorderDoubleInnerBox,r.topRightPaddingBox);case 1:return PA(r.topRightBorderDoubleInnerBox,r.topRightPaddingBox,r.bottomRightBorderDoubleInnerBox,r.bottomRightPaddingBox);case 2:return PA(r.bottomRightBorderDoubleInnerBox,r.bottomRightPaddingBox,r.bottomLeftBorderDoubleInnerBox,r.bottomLeftPaddingBox);case 3:default:return PA(r.bottomLeftBorderDoubleInnerBox,r.bottomLeftPaddingBox,r.topLeftBorderDoubleInnerBox,r.topLeftPaddingBox)}},lf=function(r,e){switch(e){case 0:return St(r.topLeftBorderStroke,r.topRightBorderStroke);case 1:return St(r.topRightBorderStroke,r.bottomRightBorderStroke);case 2:return St(r.bottomRightBorderStroke,r.bottomLeftBorderStroke);case 3:default:return St(r.bottomLeftBorderStroke,r.topLeftBorderStroke)}},St=function(r,e){var A=[];return NA(r)?A.push(r.subdivide(.5,!1)):A.push(r),NA(e)?A.push(e.subdivide(.5,!0)):A.push(e),A},PA=function(r,e,A,t){var n=[];return NA(r)?n.push(r.subdivide(.5,!1)):n.push(r),NA(A)?n.push(A.subdivide(.5,!0)):n.push(A),NA(t)?n.push(t.subdivide(.5,!0).reverse()):n.push(t),NA(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},Ei=function(r){var e=r.bounds,A=r.styles;return e.add(A.borderLeftWidth,A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth),-(A.borderTopWidth+A.borderBottomWidth))},Zt=function(r){var e=r.styles,A=r.bounds,t=QA(e.paddingLeft,A.width),n=QA(e.paddingRight,A.width),s=QA(e.paddingTop,A.width),i=QA(e.paddingBottom,A.width);return A.add(t+e.borderLeftWidth,s+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+t+n),-(e.borderTopWidth+e.borderBottomWidth+s+i))},uf=function(r,e){return r===0?e.bounds:r===2?Zt(e):Ei(e)},ff=function(r,e){return r===0?e.bounds:r===2?Zt(e):Ei(e)},br=function(r,e,A){var t=uf(xe(r.styles.backgroundOrigin,e),r),n=ff(xe(r.styles.backgroundClip,e),r),s=hf(xe(r.styles.backgroundSize,e),A,t),i=s[0],a=s[1],o=ze(xe(r.styles.backgroundPosition,e),t.width-i,t.height-a),c=df(xe(r.styles.backgroundRepeat,e),o,s,t,n),l=Math.round(t.left+o[0]),B=Math.round(t.top+o[1]);return[c,l,B,i,a]},_e=function(r){return gA(r)&&r.value===Ke.AUTO},Dt=function(r){return typeof r=="number"},hf=function(r,e,A){var t=e[0],n=e[1],s=e[2],i=r[0],a=r[1];if(!i)return[0,0];if(yA(i)&&a&&yA(a))return[QA(i,A.width),QA(a,A.height)];var o=Dt(s);if(gA(i)&&(i.value===Ke.CONTAIN||i.value===Ke.COVER)){if(Dt(s)){var c=A.width/A.height;return c<s!=(i.value===Ke.COVER)?[A.width,A.width/s]:[A.height*s,A.height]}return[A.width,A.height]}var l=Dt(t),B=Dt(n),h=l||B;if(_e(i)&&(!a||_e(a))){if(l&&B)return[t,n];if(!o&&!h)return[A.width,A.height];if(h&&o){var u=l?t:n*s,p=B?n:t/s;return[u,p]}var f=l?t:A.width,w=B?n:A.height;return[f,w]}if(o){var g=0,C=0;return yA(i)?g=QA(i,A.width):yA(a)&&(C=QA(a,A.height)),_e(i)?g=C*s:(!a||_e(a))&&(C=g/s),[g,C]}var F=null,y=null;if(yA(i)?F=QA(i,A.width):a&&yA(a)&&(y=QA(a,A.height)),F!==null&&(!a||_e(a))&&(y=l&&B?F/t*n:A.height),y!==null&&_e(i)&&(F=l&&B?y/n*t:A.width),F!==null&&y!==null)return[F,y];throw new Error("Unable to calculate background-size for element")},xe=function(r,e){var A=r[e];return typeof A>"u"?r[0]:A},df=function(r,e,A,t,n){var s=e[0],i=e[1],a=A[0],o=A[1];switch(r){case 2:return[new z(Math.round(t.left),Math.round(t.top+i)),new z(Math.round(t.left+t.width),Math.round(t.top+i)),new z(Math.round(t.left+t.width),Math.round(o+t.top+i)),new z(Math.round(t.left),Math.round(o+t.top+i))];case 3:return[new z(Math.round(t.left+s),Math.round(t.top)),new z(Math.round(t.left+s+a),Math.round(t.top)),new z(Math.round(t.left+s+a),Math.round(t.height+t.top)),new z(Math.round(t.left+s),Math.round(t.height+t.top))];case 1:return[new z(Math.round(t.left+s),Math.round(t.top+i)),new z(Math.round(t.left+s+a),Math.round(t.top+i)),new z(Math.round(t.left+s+a),Math.round(t.top+i+o)),new z(Math.round(t.left+s),Math.round(t.top+i+o))];default:return[new z(Math.round(n.left),Math.round(n.top)),new z(Math.round(n.left+n.width),Math.round(n.top)),new z(Math.round(n.left+n.width),Math.round(n.height+n.top)),new z(Math.round(n.left),Math.round(n.height+n.top))]}},gf="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",ds="Hidden Text",wf=function(){function r(e){this._data={},this._document=e}return r.prototype.parseMetrics=function(e,A){var t=this._document.createElement("div"),n=this._document.createElement("img"),s=this._document.createElement("span"),i=this._document.body;t.style.visibility="hidden",t.style.fontFamily=e,t.style.fontSize=A,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",i.appendChild(t),n.src=gf,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",s.style.fontFamily=e,s.style.fontSize=A,s.style.margin="0",s.style.padding="0",s.appendChild(this._document.createTextNode(ds)),t.appendChild(s),t.appendChild(n);var a=n.offsetTop-s.offsetTop+2;t.removeChild(s),t.appendChild(this._document.createTextNode(ds)),t.style.lineHeight="normal",n.style.verticalAlign="super";var o=n.offsetTop-t.offsetTop+2;return i.removeChild(t),{baseline:a,middle:o}},r.prototype.getMetrics=function(e,A){var t=e+" "+A;return typeof this._data[t]>"u"&&(this._data[t]=this.parseMetrics(e,A)),this._data[t]},r}(),yi=function(){function r(e,A){this.context=e,this.options=A}return r}(),Qf=1e4,pf=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;return n._activeEffects=[],n.canvas=t.canvas?t.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),t.canvas||(n.canvas.width=Math.floor(t.width*t.scale),n.canvas.height=Math.floor(t.height*t.scale),n.canvas.style.width=t.width+"px",n.canvas.style.height=t.height+"px"),n.fontMetrics=new wf(document),n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-t.x,-t.y),n.ctx.textBaseline="bottom",n._activeEffects=[],n.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),n}return e.prototype.applyEffects=function(A){for(var t=this;this._activeEffects.length;)this.popEffect();A.forEach(function(n){return t.applyEffect(n)})},e.prototype.applyEffect=function(A){this.ctx.save(),sf(A)&&(this.ctx.globalAlpha=A.opacity),nf(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),Ui(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(A){return TA(this,void 0,void 0,function(){var t;return LA(this,function(n){switch(n.label){case 0:return t=A.element.container.styles,t.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},e.prototype.renderNode=function(A){return TA(this,void 0,void 0,function(){return LA(this,function(t){switch(t.label){case 0:if(HA(A.container.flags,16))debugger;return A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(A)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},e.prototype.renderTextWithLetterSpacing=function(A,t,n){var s=this;if(t===0)this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+n);else{var i=Bn(A.text);i.reduce(function(a,o){return s.ctx.fillText(o,a,A.bounds.top+n),a+s.ctx.measureText(o).width},A.bounds.left)}},e.prototype.createFontStyle=function(A){var t=A.fontVariant.filter(function(i){return i==="normal"||i==="small-caps"}).join(""),n=vf(A.fontFamily).join(", "),s=ct(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,t,A.fontWeight,s,n].join(" "),n,s]},e.prototype.renderTextNode=function(A,t){return TA(this,void 0,void 0,function(){var n,s,i,a,o,c,l,B,h=this;return LA(this,function(u){return n=this.createFontStyle(t),s=n[0],i=n[1],a=n[2],this.ctx.font=s,this.ctx.direction=t.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",o=this.fontMetrics.getMetrics(i,a),c=o.baseline,l=o.middle,B=t.paintOrder,A.textBounds.forEach(function(p){B.forEach(function(f){switch(f){case 0:h.ctx.fillStyle=_A(t.color),h.renderTextWithLetterSpacing(p,t.letterSpacing,c);var w=t.textShadow;w.length&&p.text.trim().length&&(w.slice(0).reverse().forEach(function(g){h.ctx.shadowColor=_A(g.color),h.ctx.shadowOffsetX=g.offsetX.number*h.options.scale,h.ctx.shadowOffsetY=g.offsetY.number*h.options.scale,h.ctx.shadowBlur=g.blur.number,h.renderTextWithLetterSpacing(p,t.letterSpacing,c)}),h.ctx.shadowColor="",h.ctx.shadowOffsetX=0,h.ctx.shadowOffsetY=0,h.ctx.shadowBlur=0),t.textDecorationLine.length&&(h.ctx.fillStyle=_A(t.textDecorationColor||t.color),t.textDecorationLine.forEach(function(g){switch(g){case 1:h.ctx.fillRect(p.bounds.left,Math.round(p.bounds.top+c),p.bounds.width,1);break;case 2:h.ctx.fillRect(p.bounds.left,Math.round(p.bounds.top),p.bounds.width,1);break;case 3:h.ctx.fillRect(p.bounds.left,Math.ceil(p.bounds.top+l),p.bounds.width,1);break}}));break;case 1:t.webkitTextStrokeWidth&&p.text.trim().length&&(h.ctx.strokeStyle=_A(t.webkitTextStrokeColor),h.ctx.lineWidth=t.webkitTextStrokeWidth,h.ctx.lineJoin=window.chrome?"miter":"round",h.ctx.strokeText(p.text,p.bounds.left,p.bounds.top+c)),h.ctx.strokeStyle="",h.ctx.lineWidth=0,h.ctx.lineJoin="miter";break}})}),[2]})})},e.prototype.renderReplacedElement=function(A,t,n){if(n&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var s=Zt(A),i=zt(t);this.path(i),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,A.intrinsicWidth,A.intrinsicHeight,s.left,s.top,s.width,s.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(A){return TA(this,void 0,void 0,function(){var t,n,s,i,a,o,C,C,c,l,B,h,y,u,p,v,f,w,g,C,F,y,v;return LA(this,function(H){switch(H.label){case 0:this.applyEffects(A.getEffects(4)),t=A.container,n=A.curves,s=t.styles,i=0,a=t.textNodes,H.label=1;case 1:return i<a.length?(o=a[i],[4,this.renderTextNode(o,s)]):[3,4];case 2:H.sent(),H.label=3;case 3:return i++,[3,1];case 4:if(!(t instanceof ni))return[3,8];H.label=5;case 5:return H.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return C=H.sent(),this.renderReplacedElement(t,n,C),[3,8];case 7:return H.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof si&&this.renderReplacedElement(t,n,t.canvas),!(t instanceof ii))return[3,12];H.label=9;case 9:return H.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return C=H.sent(),this.renderReplacedElement(t,n,C),[3,12];case 11:return H.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof ci&&t.tree?(c=new e(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}),[4,c.render(t.tree)]):[3,14];case 13:l=H.sent(),t.width&&t.height&&this.ctx.drawImage(l,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),H.label=14;case 14:if(t instanceof cn&&(B=Math.min(t.bounds.width,t.bounds.height),t.type===Pt?t.checked&&(this.ctx.save(),this.path([new z(t.bounds.left+B*.39363,t.bounds.top+B*.79),new z(t.bounds.left+B*.16,t.bounds.top+B*.5549),new z(t.bounds.left+B*.27347,t.bounds.top+B*.44071),new z(t.bounds.left+B*.39694,t.bounds.top+B*.5649),new z(t.bounds.left+B*.72983,t.bounds.top+B*.23),new z(t.bounds.left+B*.84,t.bounds.top+B*.34085),new z(t.bounds.left+B*.39363,t.bounds.top+B*.79)]),this.ctx.fillStyle=_A(ts),this.ctx.fill(),this.ctx.restore()):t.type===Xt&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+B/2,t.bounds.top+B/2,B/4,0,Math.PI*2,!0),this.ctx.fillStyle=_A(ts),this.ctx.fill(),this.ctx.restore())),Cf(t)&&t.value.length){switch(h=this.createFontStyle(s),y=h[0],u=h[1],p=this.fontMetrics.getMetrics(y,u).baseline,this.ctx.font=y,this.ctx.fillStyle=_A(s.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Ff(t.styles.textAlign),v=Zt(t),f=0,t.styles.textAlign){case 1:f+=v.width/2;break;case 2:f+=v.width;break}w=v.add(f,0,0,-v.height/2+1),this.ctx.save(),this.path([new z(v.left,v.top),new z(v.left+v.width,v.top),new z(v.left+v.width,v.top+v.height),new z(v.left,v.top+v.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new tt(t.value,w),s.letterSpacing,p),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!HA(t.styles.display,2048))return[3,20];if(t.styles.listStyleImage===null)return[3,19];if(g=t.styles.listStyleImage,g.type!==0)return[3,18];C=void 0,F=g.url,H.label=15;case 15:return H.trys.push([15,17,,18]),[4,this.context.cache.match(F)];case 16:return C=H.sent(),this.ctx.drawImage(C,t.bounds.left-(C.width+10),t.bounds.top),[3,18];case 17:return H.sent(),this.context.logger.error("Error loading list-style-image "+F),[3,18];case 18:return[3,20];case 19:A.listValue&&t.styles.listStyleType!==-1&&(y=this.createFontStyle(s)[0],this.ctx.font=y,this.ctx.fillStyle=_A(s.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",v=new ae(t.bounds.left,t.bounds.top+QA(t.styles.paddingTop,t.bounds.width),t.bounds.width,Rn(s.lineHeight,s.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new tt(A.listValue,v),s.letterSpacing,Rn(s.lineHeight,s.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),H.label=20;case 20:return[2]}})})},e.prototype.renderStackContent=function(A){return TA(this,void 0,void 0,function(){var t,n,g,s,i,g,a,o,g,c,l,g,B,h,g,u,p,g,f,w,g;return LA(this,function(C){switch(C.label){case 0:if(HA(A.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:C.sent(),t=0,n=A.negativeZIndex,C.label=2;case 2:return t<n.length?(g=n[t],[4,this.renderStack(g)]):[3,5];case 3:C.sent(),C.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:C.sent(),s=0,i=A.nonInlineLevel,C.label=7;case 7:return s<i.length?(g=i[s],[4,this.renderNode(g)]):[3,10];case 8:C.sent(),C.label=9;case 9:return s++,[3,7];case 10:a=0,o=A.nonPositionedFloats,C.label=11;case 11:return a<o.length?(g=o[a],[4,this.renderStack(g)]):[3,14];case 12:C.sent(),C.label=13;case 13:return a++,[3,11];case 14:c=0,l=A.nonPositionedInlineLevel,C.label=15;case 15:return c<l.length?(g=l[c],[4,this.renderStack(g)]):[3,18];case 16:C.sent(),C.label=17;case 17:return c++,[3,15];case 18:B=0,h=A.inlineLevel,C.label=19;case 19:return B<h.length?(g=h[B],[4,this.renderNode(g)]):[3,22];case 20:C.sent(),C.label=21;case 21:return B++,[3,19];case 22:u=0,p=A.zeroOrAutoZIndexOrTransformedOrOpacity,C.label=23;case 23:return u<p.length?(g=p[u],[4,this.renderStack(g)]):[3,26];case 24:C.sent(),C.label=25;case 25:return u++,[3,23];case 26:f=0,w=A.positiveZIndex,C.label=27;case 27:return f<w.length?(g=w[f],[4,this.renderStack(g)]):[3,30];case 28:C.sent(),C.label=29;case 29:return f++,[3,27];case 30:return[2]}})})},e.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},e.prototype.formatPath=function(A){var t=this;A.forEach(function(n,s){var i=NA(n)?n.start:n;s===0?t.ctx.moveTo(i.x,i.y):t.ctx.lineTo(i.x,i.y),NA(n)&&t.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},e.prototype.renderRepeat=function(A,t,n,s){this.path(A),this.ctx.fillStyle=t,this.ctx.translate(n,s),this.ctx.fill(),this.ctx.translate(-n,-s)},e.prototype.resizeImage=function(A,t,n){var s;if(A.width===t&&A.height===n)return A;var i=(s=this.canvas.ownerDocument)!==null&&s!==void 0?s:document,a=i.createElement("canvas");a.width=Math.max(1,t),a.height=Math.max(1,n);var o=a.getContext("2d");return o.drawImage(A,0,0,A.width,A.height,0,0,t,n),a},e.prototype.renderBackgroundImage=function(A){return TA(this,void 0,void 0,function(){var t,n,s,i,a,o;return LA(this,function(c){switch(c.label){case 0:t=A.styles.backgroundImage.length-1,n=function(l){var B,h,u,R,AA,M,K,Q,T,p,R,AA,M,K,Q,f,w,g,C,F,y,v,H,b,T,_,R,Z,E,K,Q,k,AA,M,nA,N,tA,S,x,$,Y,P;return LA(this,function(dA){switch(dA.label){case 0:if(l.type!==0)return[3,5];B=void 0,h=l.url,dA.label=1;case 1:return dA.trys.push([1,3,,4]),[4,s.context.cache.match(h)];case 2:return B=dA.sent(),[3,4];case 3:return dA.sent(),s.context.logger.error("Error loading background-image "+h),[3,4];case 4:return B&&(u=br(A,t,[B.width,B.height,B.width/B.height]),R=u[0],AA=u[1],M=u[2],K=u[3],Q=u[4],T=s.ctx.createPattern(s.resizeImage(B,K,Q),"repeat"),s.renderRepeat(R,T,AA,M)),[3,6];case 5:nc(l)?(p=br(A,t,[null,null,null]),R=p[0],AA=p[1],M=p[2],K=p[3],Q=p[4],f=$B(l.angle,K,Q),w=f[0],g=f[1],C=f[2],F=f[3],y=f[4],v=document.createElement("canvas"),v.width=K,v.height=Q,H=v.getContext("2d"),b=H.createLinearGradient(g,F,C,y),kn(l.stops,w).forEach(function(CA){return b.addColorStop(CA.stop,_A(CA.color))}),H.fillStyle=b,H.fillRect(0,0,K,Q),K>0&&Q>0&&(T=s.ctx.createPattern(v,"repeat"),s.renderRepeat(R,T,AA,M))):sc(l)&&(_=br(A,t,[null,null,null]),R=_[0],Z=_[1],E=_[2],K=_[3],Q=_[4],k=l.position.length===0?[sn]:l.position,AA=QA(k[0],K),M=QA(k[k.length-1],Q),nA=Ac(l,AA,M,K,Q),N=nA[0],tA=nA[1],N>0&&tA>0&&(S=s.ctx.createRadialGradient(Z+AA,E+M,0,Z+AA,E+M,N),kn(l.stops,N*2).forEach(function(CA){return S.addColorStop(CA.stop,_A(CA.color))}),s.path(R),s.ctx.fillStyle=S,N!==tA?(x=A.bounds.left+.5*A.bounds.width,$=A.bounds.top+.5*A.bounds.height,Y=tA/N,P=1/Y,s.ctx.save(),s.ctx.translate(x,$),s.ctx.transform(1,0,0,Y,0,0),s.ctx.translate(-x,-$),s.ctx.fillRect(Z,P*(E-$)+$,K,Q*P),s.ctx.restore()):s.ctx.fill())),dA.label=6;case 6:return t--,[2]}})},s=this,i=0,a=A.styles.backgroundImage.slice(0).reverse(),c.label=1;case 1:return i<a.length?(o=a[i],[5,n(o)]):[3,4];case 2:c.sent(),c.label=3;case 3:return i++,[3,1];case 4:return[2]}})})},e.prototype.renderSolidBorder=function(A,t,n){return TA(this,void 0,void 0,function(){return LA(this,function(s){return this.path(hs(n,t)),this.ctx.fillStyle=_A(A),this.ctx.fill(),[2]})})},e.prototype.renderDoubleBorder=function(A,t,n,s){return TA(this,void 0,void 0,function(){var i,a;return LA(this,function(o){switch(o.label){case 0:return t<3?[4,this.renderSolidBorder(A,n,s)]:[3,2];case 1:return o.sent(),[2];case 2:return i=Bf(s,n),this.path(i),this.ctx.fillStyle=_A(A),this.ctx.fill(),a=cf(s,n),this.path(a),this.ctx.fill(),[2]}})})},e.prototype.renderNodeBackgroundAndBorders=function(A){return TA(this,void 0,void 0,function(){var t,n,s,i,a,o,c,l,B=this;return LA(this,function(h){switch(h.label){case 0:return this.applyEffects(A.getEffects(2)),t=A.container.styles,n=!we(t.backgroundColor)||t.backgroundImage.length,s=[{style:t.borderTopStyle,color:t.borderTopColor,width:t.borderTopWidth},{style:t.borderRightStyle,color:t.borderRightColor,width:t.borderRightWidth},{style:t.borderBottomStyle,color:t.borderBottomColor,width:t.borderBottomWidth},{style:t.borderLeftStyle,color:t.borderLeftColor,width:t.borderLeftWidth}],i=Uf(xe(t.backgroundClip,0),A.curves),n||t.boxShadow.length?(this.ctx.save(),this.path(i),this.ctx.clip(),we(t.backgroundColor)||(this.ctx.fillStyle=_A(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:h.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach(function(u){B.ctx.save();var p=Yt(A.curves),f=u.inset?0:Qf,w=af(p,-f+(u.inset?1:-1)*u.spread.number,(u.inset?1:-1)*u.spread.number,u.spread.number*(u.inset?-2:2),u.spread.number*(u.inset?-2:2));u.inset?(B.path(p),B.ctx.clip(),B.mask(w)):(B.mask(p),B.ctx.clip(),B.path(w)),B.ctx.shadowOffsetX=u.offsetX.number+f,B.ctx.shadowOffsetY=u.offsetY.number,B.ctx.shadowColor=_A(u.color),B.ctx.shadowBlur=u.blur.number,B.ctx.fillStyle=u.inset?_A(u.color):"rgba(0,0,0,1)",B.ctx.fill(),B.ctx.restore()}),h.label=2;case 2:a=0,o=0,c=s,h.label=3;case 3:return o<c.length?(l=c[o],l.style!==0&&!we(l.color)&&l.width>0?l.style!==2?[3,5]:[4,this.renderDashedDottedBorder(l.color,l.width,a,A.curves,2)]:[3,11]):[3,13];case 4:return h.sent(),[3,11];case 5:return l.style!==3?[3,7]:[4,this.renderDashedDottedBorder(l.color,l.width,a,A.curves,3)];case 6:return h.sent(),[3,11];case 7:return l.style!==4?[3,9]:[4,this.renderDoubleBorder(l.color,l.width,a,A.curves)];case 8:return h.sent(),[3,11];case 9:return[4,this.renderSolidBorder(l.color,a,A.curves)];case 10:h.sent(),h.label=11;case 11:a++,h.label=12;case 12:return o++,[3,3];case 13:return[2]}})})},e.prototype.renderDashedDottedBorder=function(A,t,n,s,i){return TA(this,void 0,void 0,function(){var a,o,c,l,B,h,u,p,f,w,g,C,F,y,v,H,v,H;return LA(this,function(b){return this.ctx.save(),a=lf(s,n),o=hs(s,n),i===2&&(this.path(o),this.ctx.clip()),NA(o[0])?(c=o[0].start.x,l=o[0].start.y):(c=o[0].x,l=o[0].y),NA(o[1])?(B=o[1].end.x,h=o[1].end.y):(B=o[1].x,h=o[1].y),n===0||n===2?u=Math.abs(c-B):u=Math.abs(l-h),this.ctx.beginPath(),i===3?this.formatPath(a):this.formatPath(o.slice(0,2)),p=t<3?t*3:t*2,f=t<3?t*2:t,i===3&&(p=t,f=t),w=!0,u<=p*2?w=!1:u<=p*2+f?(g=u/(2*p+f),p*=g,f*=g):(C=Math.floor((u+f)/(p+f)),F=(u-C*p)/(C-1),y=(u-(C+1)*p)/C,f=y<=0||Math.abs(f-F)<Math.abs(f-y)?F:y),w&&(i===3?this.ctx.setLineDash([0,p+f]):this.ctx.setLineDash([p,f])),i===3?(this.ctx.lineCap="round",this.ctx.lineWidth=t):this.ctx.lineWidth=t*2+1.1,this.ctx.strokeStyle=_A(A),this.ctx.stroke(),this.ctx.setLineDash([]),i===2&&(NA(o[0])&&(v=o[3],H=o[0],this.ctx.beginPath(),this.formatPath([new z(v.end.x,v.end.y),new z(H.start.x,H.start.y)]),this.ctx.stroke()),NA(o[1])&&(v=o[1],H=o[2],this.ctx.beginPath(),this.formatPath([new z(v.end.x,v.end.y),new z(H.start.x,H.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},e.prototype.render=function(A){return TA(this,void 0,void 0,function(){var t;return LA(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=_A(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=of(A),[4,this.renderStack(t)];case 1:return n.sent(),this.applyEffects([]),[2,this.canvas]}})})},e}(yi),Cf=function(r){return r instanceof Bi||r instanceof oi?!0:r instanceof cn&&r.type!==Xt&&r.type!==Pt},Uf=function(r,e){switch(r){case 0:return Yt(e);case 2:return ef(e);case 1:default:return zt(e)}},Ff=function(r){switch(r){case 1:return"center";case 2:return"right";case 0:default:return"left"}},mf=["-apple-system","system-ui"],vf=function(r){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?r.filter(function(e){return mf.indexOf(e)===-1}):r},Ef=function(r){zA(e,r);function e(A,t){var n=r.call(this,A,t)||this;return n.canvas=t.canvas?t.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),n.options=t,n.canvas.width=Math.floor(t.width*t.scale),n.canvas.height=Math.floor(t.height*t.scale),n.canvas.style.width=t.width+"px",n.canvas.style.height=t.height+"px",n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-t.x,-t.y),n.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),n}return e.prototype.render=function(A){return TA(this,void 0,void 0,function(){var t,n;return LA(this,function(s){switch(s.label){case 0:return t=Yr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,yf(t)];case 1:return n=s.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=_A(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},e}(yi),yf=function(r){return new Promise(function(e,A){var t=new Image;t.onload=function(){e(t)},t.onerror=A,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(r))})},Hf=function(){function r(e){var A=e.id,t=e.enabled;this.id=A,this.enabled=t,this.start=Date.now()}return r.prototype.debug=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,ft([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},r.prototype.getTime=function(){return Date.now()-this.start},r.prototype.info=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,ft([this.id,this.getTime()+"ms"],e))},r.prototype.warn=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,ft([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},r.prototype.error=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,ft([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},r.instances={},r}(),If=function(){function r(e,A){var t;this.windowBounds=A,this.instanceName="#"+r.instanceCount++,this.logger=new Hf({id:this.instanceName,enabled:e.logging}),this.cache=(t=e.cache)!==null&&t!==void 0?t:new Ju(this,e)}return r.instanceCount=1,r}(),un=function(r,e){return e===void 0&&(e={}),_f(r,e)};typeof window<"u"&&Ci.setContext(window);var _f=function(r,e){return TA(void 0,void 0,void 0,function(){var A,t,n,s,i,a,o,c,l,B,h,u,p,f,w,g,C,F,y,v,b,H,b,T,_,R,Z,E,K,Q,k,AA,M,nA,N,tA,S,x,$,Y;return LA(this,function(P){switch(P.label){case 0:if(!r||typeof r!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(A=r.ownerDocument,!A)throw new Error("Element is not attached to a Document");if(t=A.defaultView,!t)throw new Error("Document is not attached to a Window");return n={allowTaint:(T=e.allowTaint)!==null&&T!==void 0?T:!1,imageTimeout:(_=e.imageTimeout)!==null&&_!==void 0?_:15e3,proxy:e.proxy,useCORS:(R=e.useCORS)!==null&&R!==void 0?R:!1},s=Kr({logging:(Z=e.logging)!==null&&Z!==void 0?Z:!0,cache:e.cache},n),i={windowWidth:(E=e.windowWidth)!==null&&E!==void 0?E:t.innerWidth,windowHeight:(K=e.windowHeight)!==null&&K!==void 0?K:t.innerHeight,scrollX:(Q=e.scrollX)!==null&&Q!==void 0?Q:t.pageXOffset,scrollY:(k=e.scrollY)!==null&&k!==void 0?k:t.pageYOffset},a=new ae(i.scrollX,i.scrollY,i.windowWidth,i.windowHeight),o=new If(s,a),c=(AA=e.foreignObjectRendering)!==null&&AA!==void 0?AA:!1,l={allowTaint:(M=e.allowTaint)!==null&&M!==void 0?M:!1,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:c,copyStyles:c},o.logger.debug("Starting document clone with size "+a.width+"x"+a.height+" scrolled to "+-a.left+","+-a.top),B=new ls(o,r,l),h=B.clonedReferenceElement,h?[4,B.toIFrame(A,a)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return u=P.sent(),p=ln(h)||bu(h)?so(h.ownerDocument):$t(o,h),f=p.width,w=p.height,g=p.left,C=p.top,F=bf(o,h,e.backgroundColor),y={canvas:e.canvas,backgroundColor:F,scale:(N=(nA=e.scale)!==null&&nA!==void 0?nA:t.devicePixelRatio)!==null&&N!==void 0?N:1,x:((tA=e.x)!==null&&tA!==void 0?tA:0)+g,y:((S=e.y)!==null&&S!==void 0?S:0)+C,width:(x=e.width)!==null&&x!==void 0?x:Math.ceil(f),height:($=e.height)!==null&&$!==void 0?$:Math.ceil(w)},c?(o.logger.debug("Document cloned, using foreign object rendering"),b=new Ef(o,y),[4,b.render(h)]):[3,3];case 2:return v=P.sent(),[3,5];case 3:return o.logger.debug("Document cloned, element located at "+g+","+C+" with size "+f+"x"+w+" using computed rendering"),o.logger.debug("Starting DOM parsing"),H=ui(o,h),F===H.styles.backgroundColor&&(H.styles.backgroundColor=ie.TRANSPARENT),o.logger.debug("Starting renderer for element at "+y.x+","+y.y+" with size "+y.width+"x"+y.height),b=new pf(o,y),[4,b.render(H)];case 4:v=P.sent(),P.label=5;case 5:return(!((Y=e.removeContainer)!==null&&Y!==void 0)||Y)&&(ls.destroy(u)||o.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),o.logger.debug("Finished rendering"),[2,v]}})})},bf=function(r,e,A){var t=e.ownerDocument,n=t.documentElement?At(r,getComputedStyle(t.documentElement).backgroundColor):ie.TRANSPARENT,s=t.body?At(r,getComputedStyle(t.body).backgroundColor):ie.TRANSPARENT,i=typeof A=="string"?At(r,A):A===null?ie.TRANSPARENT:4294967295;return e===t.documentElement?we(n)?we(s)?i:s:n:i};function de(r){throw new Error('Could not dynamically require "'+r+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Hi={exports:{}};(function(r,e){(function(A){r.exports=A()})(function(){return function A(t,n,s){function i(c,l){if(!n[c]){if(!t[c]){var B=typeof de=="function"&&de;if(!l&&B)return B(c,!0);if(a)return a(c,!0);var h=new Error("Cannot find module '"+c+"'");throw h.code="MODULE_NOT_FOUND",h}var u=n[c]={exports:{}};t[c][0].call(u.exports,function(p){var f=t[c][1][p];return i(f||p)},u,u.exports,A,t,n,s)}return n[c].exports}for(var a=typeof de=="function"&&de,o=0;o<s.length;o++)i(s[o]);return i}({1:[function(A,t,n){function s(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}t.exports=s,s.EventEmitter=s,s.prototype._events=void 0,s.prototype._maxListeners=void 0,s.defaultMaxListeners=10,s.prototype.setMaxListeners=function(l){if(!a(l)||l<0||isNaN(l))throw TypeError("n must be a positive number");return this._maxListeners=l,this},s.prototype.emit=function(l){var B,h,u,p,f,w;if(this._events||(this._events={}),l==="error"&&(!this._events.error||o(this._events.error)&&!this._events.error.length)){if(B=arguments[1],B instanceof Error)throw B;var g=new Error('Uncaught, unspecified "error" event. ('+B+")");throw g.context=B,g}if(h=this._events[l],c(h))return!1;if(i(h))switch(arguments.length){case 1:h.call(this);break;case 2:h.call(this,arguments[1]);break;case 3:h.call(this,arguments[1],arguments[2]);break;default:p=Array.prototype.slice.call(arguments,1),h.apply(this,p)}else if(o(h))for(p=Array.prototype.slice.call(arguments,1),w=h.slice(),u=w.length,f=0;f<u;f++)w[f].apply(this,p);return!0},s.prototype.addListener=function(l,B){var h;if(!i(B))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",l,i(B.listener)?B.listener:B),this._events[l]?o(this._events[l])?this._events[l].push(B):this._events[l]=[this._events[l],B]:this._events[l]=B,o(this._events[l])&&!this._events[l].warned&&(c(this._maxListeners)?h=s.defaultMaxListeners:h=this._maxListeners,h&&h>0&&this._events[l].length>h&&(this._events[l].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[l].length),typeof console.trace=="function"&&console.trace())),this},s.prototype.on=s.prototype.addListener,s.prototype.once=function(l,B){if(!i(B))throw TypeError("listener must be a function");var h=!1;function u(){this.removeListener(l,u),h||(h=!0,B.apply(this,arguments))}return u.listener=B,this.on(l,u),this},s.prototype.removeListener=function(l,B){var h,u,p,f;if(!i(B))throw TypeError("listener must be a function");if(!this._events||!this._events[l])return this;if(h=this._events[l],p=h.length,u=-1,h===B||i(h.listener)&&h.listener===B)delete this._events[l],this._events.removeListener&&this.emit("removeListener",l,B);else if(o(h)){for(f=p;f-- >0;)if(h[f]===B||h[f].listener&&h[f].listener===B){u=f;break}if(u<0)return this;h.length===1?(h.length=0,delete this._events[l]):h.splice(u,1),this._events.removeListener&&this.emit("removeListener",l,B)}return this},s.prototype.removeAllListeners=function(l){var B,h;if(!this._events)return this;if(!this._events.removeListener)return arguments.length===0?this._events={}:this._events[l]&&delete this._events[l],this;if(arguments.length===0){for(B in this._events)B!=="removeListener"&&this.removeAllListeners(B);return this.removeAllListeners("removeListener"),this._events={},this}if(h=this._events[l],i(h))this.removeListener(l,h);else if(h)for(;h.length;)this.removeListener(l,h[h.length-1]);return delete this._events[l],this},s.prototype.listeners=function(l){var B;return!this._events||!this._events[l]?B=[]:i(this._events[l])?B=[this._events[l]]:B=this._events[l].slice(),B},s.prototype.listenerCount=function(l){if(this._events){var B=this._events[l];if(i(B))return 1;if(B)return B.length}return 0},s.listenerCount=function(l,B){return l.listenerCount(B)};function i(l){return typeof l=="function"}function a(l){return typeof l=="number"}function o(l){return typeof l=="object"&&l!==null}function c(l){return l===void 0}},{}],2:[function(A,t,n){var s,i,a,o,c;c=navigator.userAgent.toLowerCase(),o=navigator.platform.toLowerCase(),s=c.match(/(opera|ie|firefox|chrome|version)[\s\/:]([\w\d\.]+)?.*?(safari|version[\s\/:]([\w\d\.]+)|$)/)||[null,"unknown",0],a=s[1]==="ie"&&document.documentMode,i={name:s[1]==="version"?s[3]:s[1],version:a||parseFloat(s[1]==="opera"&&s[4]?s[4]:s[2]),platform:{name:c.match(/ip(?:ad|od|hone)/)?"ios":(c.match(/(?:webos|android)/)||o.match(/mac|win|linux/)||["other"])[0]}},i[i.name]=!0,i[i.name+parseInt(i.version,10)]=!0,i.platform[i.platform.name]=!0,t.exports=i},{}],3:[function(A,t,n){var s,i,a,o=function(h,u){for(var p in u)c.call(u,p)&&(h[p]=u[p]);function f(){this.constructor=h}return f.prototype=u.prototype,h.prototype=new f,h.__super__=u.prototype,h},c={}.hasOwnProperty,l=[].indexOf||function(h){for(var u=0,p=this.length;u<p;u++)if(u in this&&this[u]===h)return u;return-1},B=[].slice;s=A("events").EventEmitter,a=A("./browser.coffee"),i=function(h){var u,p;o(f,h),u={workerScript:"gif.worker.js",workers:2,repeat:0,background:"#fff",quality:10,width:null,height:null,transparent:null,debug:!1,dither:!1},p={delay:500,copy:!1};function f(w){var g,C,F;this.running=!1,this.options={},this.frames=[],this.freeWorkers=[],this.activeWorkers=[],this.setOptions(w);for(C in u)F=u[C],(g=this.options)[C]==null&&(g[C]=F)}return f.prototype.setOption=function(w,g){if(this.options[w]=g,this._canvas!=null&&(w==="width"||w==="height"))return this._canvas[w]=g},f.prototype.setOptions=function(w){var g,C,F;C=[];for(g in w)c.call(w,g)&&(F=w[g],C.push(this.setOption(g,F)));return C},f.prototype.addFrame=function(w,g){var C,F;g==null&&(g={}),C={},C.transparent=this.options.transparent;for(F in p)C[F]=g[F]||p[F];if(this.options.width==null&&this.setOption("width",w.width),this.options.height==null&&this.setOption("height",w.height),typeof ImageData<"u"&&ImageData!==null&&w instanceof ImageData)C.data=w.data;else if(typeof CanvasRenderingContext2D<"u"&&CanvasRenderingContext2D!==null&&w instanceof CanvasRenderingContext2D||typeof WebGLRenderingContext<"u"&&WebGLRenderingContext!==null&&w instanceof WebGLRenderingContext)g.copy?C.data=this.getContextData(w):C.context=w;else if(w.childNodes!=null)g.copy?C.data=this.getImageData(w):C.image=w;else throw new Error("Invalid image");return this.frames.push(C)},f.prototype.render=function(){var w,g,C;if(this.running)throw new Error("Already running");if(this.options.width==null||this.options.height==null)throw new Error("Width and height must be set prior to rendering");if(this.running=!0,this.nextFrame=0,this.finishedFrames=0,this.imageParts=(function(){var F,y,v;for(v=[],F=0,y=this.frames.length;0<=y?F<y:F>y;0<=y?++F:--F)v.push(null);return v}).call(this),g=this.spawnWorkers(),this.options.globalPalette===!0)this.renderNextFrame();else for(w=0,C=g;0<=C?w<C:w>C;0<=C?++w:--w)this.renderNextFrame();return this.emit("start"),this.emit("progress",0)},f.prototype.abort=function(){for(var w;w=this.activeWorkers.shift(),w!=null;)this.log("killing active worker"),w.terminate();return this.running=!1,this.emit("abort")},f.prototype.spawnWorkers=function(){var w,g,C;return w=Math.min(this.options.workers,this.frames.length),(function(){C=[];for(var F=g=this.freeWorkers.length;g<=w?F<w:F>w;g<=w?F++:F--)C.push(F);return C}).apply(this).forEach(function(F){return function(y){var v;return F.log("spawning worker "+y),v=new Worker(F.options.workerScript),v.onmessage=function(H){return F.activeWorkers.splice(F.activeWorkers.indexOf(v),1),F.freeWorkers.push(v),F.frameFinished(H.data)},F.freeWorkers.push(v)}}(this)),w},f.prototype.frameFinished=function(w){var g,C;if(this.log("frame "+w.index+" finished - "+this.activeWorkers.length+" active"),this.finishedFrames++,this.emit("progress",this.finishedFrames/this.frames.length),this.imageParts[w.index]=w,this.options.globalPalette===!0&&(this.options.globalPalette=w.globalPalette,this.log("global palette analyzed"),this.frames.length>2))for(g=1,C=this.freeWorkers.length;1<=C?g<C:g>C;1<=C?++g:--g)this.renderNextFrame();return l.call(this.imageParts,null)>=0?this.renderNextFrame():this.finishRendering()},f.prototype.finishRendering=function(){var w,g,C,F,y,v,H,b,T,_,R,Z,E,K,Q,k;for(b=0,K=this.imageParts,y=0,T=K.length;y<T;y++)g=K[y],b+=(g.data.length-1)*g.pageSize+g.cursor;for(b+=g.pageSize-g.cursor,this.log("rendering finished - filesize "+Math.round(b/1e3)+"kb"),w=new Uint8Array(b),Z=0,Q=this.imageParts,v=0,_=Q.length;v<_;v++)for(g=Q[v],k=g.data,C=H=0,R=k.length;H<R;C=++H)E=k[C],w.set(E,Z),C===g.data.length-1?Z+=g.cursor:Z+=g.pageSize;return F=new Blob([w],{type:"image/gif"}),this.emit("finished",F,w)},f.prototype.renderNextFrame=function(){var w,g,C;if(this.freeWorkers.length===0)throw new Error("No free workers");if(!(this.nextFrame>=this.frames.length))return w=this.frames[this.nextFrame++],C=this.freeWorkers.shift(),g=this.getTask(w),this.log("starting frame "+(g.index+1)+" of "+this.frames.length),this.activeWorkers.push(C),C.postMessage(g)},f.prototype.getContextData=function(w){return w.getImageData(0,0,this.options.width,this.options.height).data},f.prototype.getImageData=function(w){var g;return this._canvas==null&&(this._canvas=document.createElement("canvas"),this._canvas.width=this.options.width,this._canvas.height=this.options.height),g=this._canvas.getContext("2d"),g.setFill=this.options.background,g.fillRect(0,0,this.options.width,this.options.height),g.drawImage(w,0,0),this.getContextData(g)},f.prototype.getTask=function(w){var g,C;if(g=this.frames.indexOf(w),C={index:g,last:g===this.frames.length-1,delay:w.delay,transparent:w.transparent,width:this.options.width,height:this.options.height,quality:this.options.quality,dither:this.options.dither,globalPalette:this.options.globalPalette,repeat:this.options.repeat,canTransfer:a.name==="chrome"},w.data!=null)C.data=w.data;else if(w.context!=null)C.data=this.getContextData(w.context);else if(w.image!=null)C.data=this.getImageData(w.image);else throw new Error("Invalid frame");return C},f.prototype.log=function(){var w;if(w=1<=arguments.length?B.call(arguments,0):[],!!this.options.debug)return console.log.apply(console,w)},f}(s),t.exports=i},{"./browser.coffee":2,events:1}]},{},[3])(3)})})(Hi);var xf=Hi.exports;const Lf=tn(xf);function Ii(){const r=DA("");return{imageUrl:r,captureHtmlToImage:async(A,t={})=>{const n=await un(A,{...t});r.value=n.toDataURL()}}}const Kf={class:"wtc-button"},Sf={class:"label",for:"json"},Df={__name:"GeneTemplate",setup(r){const{imageUrl:e,captureHtmlToImage:A}=Ii(),{useChatStore:t,useTemplateStore:n}=JA(),s=Ri({templateTitle:""}),i=DA(!1),a=()=>{s.templateTitle=`聊天模板${nt().format("YYYY-MM-DD HH:mm:ss")}`,i.value=!0},o=async()=>{if(!s.templateTitle.trim()){le({type:"warning",content:"请输入模板标题"});return}const h=document.querySelector(".phone-wrap");await A(h),n.add({title:s.templateTitle,chatList:t.chatList,snapshot:e.value}),i.value=!1,le({type:"success",content:"模板保存成功，请在模板管理栏目查看"})},c=()=>{i.value=!1},l=()=>{const u={chatList:t.chatList},p=new Blob([JSON.stringify(u,null,2)],{type:"application/json"});no.saveAs(p,`聊天记录 - ${nt().format("YYYYMMDDHHmmss")}.json`)},B=h=>{const u=h.target.files[0];if(!u.name.includes("json")){le({type:"warning",content:"只能插入json文件"});return}const p=new FileReader;p.readAsText(u),p.onload=f=>{const{chatList:w}=JSON.parse(f.target.result);w&&w.length?t.chatList=w:le({type:"warning",content:"JSON文件中的格式有问题，请检查后重试"})}};return(h,u)=>{const p=lA("a-tooltip"),f=lA("a-input"),w=lA("a-form-item"),g=lA("a-form"),C=lA("a-modal");return sA(),iA(Te,null,[eA(p,{title:"将当前对话保存为模板",placement:"right"},{default:aA(()=>[J("div",{class:"wtc-button",onClick:a},"存为模板")]),_:1}),eA(p,{title:"导出聊天为JSON文件",placement:"right"},{default:aA(()=>[J("div",{class:"wtc-button",onClick:l},"导出聊天")]),_:1}),eA(p,{title:"导入JSON文件为聊天",placement:"right"},{default:aA(()=>[J("div",Kf,[J("label",Sf,[RA(" 导入聊天 "),J("input",{id:"json",type:"file",accept:".json",hidden:"",onChange:B},null,32)])])]),_:1}),eA(C,{open:i.value,"onUpdate:open":u[1]||(u[1]=F=>i.value=F),centered:"",title:"保存模板",width:400,onCancel:c,onOk:o},{default:aA(()=>[eA(g,{model:s,"label-col":{style:{width:"80px"}}},{default:aA(()=>[eA(w,{label:"模板标题",style:{"margin-top":"40px"}},{default:aA(()=>[eA(f,{value:s.templateTitle,"onUpdate:value":u[0]||(u[0]=F=>s.templateTitle=F),placeholder:"请输入模板标题"},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},Tf=["src"],kf={__name:"GenePng",setup(r){const{imageUrl:e,captureHtmlToImage:A}=Ii(),{useSystemStore:t}=JA(),n=DA(!1),s=DA(""),i=()=>{const l=document.querySelector(".phone-wrap");A(l),n.value=!0,s.value="生成图片"},a=()=>{const l=document.querySelector(".wechat-content"),B=document.querySelector(".phone-wrap"),h=document.querySelector(".phone-bg"),u=t.appearance.chatBackground,p=document.querySelector("#phone"),f=document.querySelector(".phone-body"),w=t.phoneHeight>l.scrollHeight+264+269?t.phoneHeight:l.scrollHeight+264+269;p.style.height=w+"px",f.scrollTop=0,u&&(f.style.background=`url(${u}) repeat-y center top`,f.style.backgroundSize="1125px 2036px",h.querySelector("img").setAttribute("src","")),A(B,{height:w*.32}),p.style.height=t.phoneHeight+"px",u&&(h.querySelector("img").setAttribute("src",u),f.style.background=""),n.value=!0,s.value="生成长图"},o=()=>{n.value=!1,e.value=""},c=()=>{const l=document.createElement("a");l.href=e.value,l.download=`微信聊天图片 - ${nt().format("YYYYMMDDHHmmss")}.png`,l.target="_blank",l.rel="noopener noreferrer",document.body.appendChild(l),l.click(),document.body.removeChild(l)};return(l,B)=>{const h=lA("a-tooltip"),u=lA("a-button"),p=lA("a-space"),f=lA("a-drawer");return sA(),iA(Te,null,[eA(h,{title:"滚动到哪截到哪",placement:"right"},{default:aA(()=>[J("div",{class:"wtc-button",onClick:i},"生成图片")]),_:1}),eA(h,{title:"顾名思义",placement:"right"},{default:aA(()=>[J("div",{class:"wtc-button",onClick:a},"生成长图")]),_:1}),eA(f,{width:500,title:s.value,placement:"right",closable:!1,destroyOnClose:!0,open:n.value,onClose:o},{extra:aA(()=>[eA(p,null,{default:aA(()=>[eA(u,{type:"primary",disabled:!hA(e),onClick:c},{default:aA(()=>[RA("下载")]),_:1},8,["disabled"]),eA(u,{danger:"",type:"link",shape:"circle",icon:st(hA(rn)),disabled:!hA(e),onClick:o},null,8,["icon","disabled"])]),_:1})]),default:aA(()=>[J("img",{src:hA(e),alt:""},null,8,Tf)]),_:1},8,["title","open"])],64)}}},Of=J("p",null,"~",-1),Rf=J("p",{class:"form-tip"},[RA("会话间隔可以理解为"),J("b",null,"回复考虑时长"),RA("，考虑时长为"),J("b",null,"最小间隔"),RA("~"),J("b",null,"最大间隔"),RA("的随机数")],-1),Mf=J("p",null,"1、通过网页生成的动图、视频会有模糊、重影的问题无法规避，所以就在考量在线生成动图、视频的必要性，最终决定生成出带有编号的图片序列，用户下载压缩包后自行通过视频剪辑软件剪辑。",-1),Gf=J("p",null,"2、当然你也可以右键保存单张图片！",-1),Nf={__name:"GeneConfig",setup(r){const{useChatStore:e}=JA(),A=DA(!1),t=()=>{A.value=!0};Mt(()=>e.generateConfig.minInterval,(s,i)=>{s>e.generateConfig.maxInterval&&(le({type:"warning",content:"最小间隔需小于等于最大间隔！"}),e.generateConfig.minInterval=i)}),Mt(()=>e.generateConfig.maxInterval,(s,i)=>{s<e.generateConfig.minInterval&&(le({type:"warning",content:"最大间隔需大于等于最小间隔！"}),e.generateConfig.maxInterval=i)});const n=()=>{A.value=!1};return(s,i)=>{const a=lA("a-tooltip"),o=lA("a-input-number"),c=lA("a-space"),l=lA("a-form-item"),B=lA("a-form"),h=lA("a-button"),u=lA("a-row"),p=lA("a-modal");return sA(),iA(Te,null,[eA(a,{title:"生成动图、视频配置",placement:"right"},{default:aA(()=>[J("div",{class:"wtc-button",onClick:t},"配置")]),_:1}),eA(p,{open:A.value,"onUpdate:open":i[2]||(i[2]=f=>A.value=f),centered:"",title:"动图、视频生成配置",width:600,footer:null,onCancel:n},{default:aA(()=>[eA(B,{model:s.configState,"label-col":{style:{width:"80px"}}},{default:aA(()=>[eA(l,{label:"会话间隔",style:{"margin-top":"40px"}},{default:aA(()=>[eA(c,null,{default:aA(()=>[eA(a,{title:"最小间隔"},{default:aA(()=>[eA(o,{value:hA(e).generateConfig.minInterval,"onUpdate:value":i[0]||(i[0]=f=>hA(e).generateConfig.minInterval=f),min:0,max:1e4,placeholder:"最小间隔",precision:0,"addon-after":"ms",style:{width:"160px"}},null,8,["value"])]),_:1}),Of,eA(a,{title:"最大间隔"},{default:aA(()=>[eA(o,{value:hA(e).generateConfig.maxInterval,"onUpdate:value":i[1]||(i[1]=f=>hA(e).generateConfig.maxInterval=f),min:0,max:1e4,placeholder:"最大间隔",precision:0,"addon-after":"ms",style:{width:"160px"}},null,8,["value"])]),_:1})]),_:1}),Rf]),_:1}),eA(l,{label:"思考",style:{"margin-top":"40px"}},{default:aA(()=>[Mf,Gf]),_:1})]),_:1},8,["model"]),eA(u,{align:"center",style:{"margin-top":"40px"}},{default:aA(()=>[eA(h,{onClick:n},{default:aA(()=>[RA("关闭")]),_:1})]),_:1})]),_:1},8,["open"])],64)}}},Vf={id:"imgBox"},Pf=["src"],Xf={key:1,class:"default-loading"},Wf=1e3,Jf={__name:"GeneGif",setup(r){const{useChatStore:e,useSystemStore:A}=JA(),t=DA(!1),n=DA(""),s=async()=>{t.value=!0;let c=ps.cloneDeep(e.chatList);document.getElementById("imgBox").innerHTML="";let l=[];e.chatList=[],l.push(i("chat-0"));for(let B=0;B<c.length;B++){await Cs(200),e.chatList.push(c[B]),en.emit("sentChat");const h=e.generateConfig.maxInterval,u=e.generateConfig.minInterval;l.push(i(c[B].id,c[B].intervalTime||Math.floor(Math.random()*(h-u+1))+u))}Promise.all(l).then(B=>{if(B){const h=new Lf({quality:10,width:A.phoneWidth*A.phoneScale,height:A.phoneHeight*A.phoneScale});for(let u=0;u<B.length;u++)h.addFrame(B[u],{delay:B[u].id.split("-")[2]});h.on("finished",u=>{n.value=URL.createObjectURL(u)}),h.render()}})},i=(c,l)=>new Promise(async(B,h)=>{let u=l||Wf,p=document.querySelector(".phone-wrap");setTimeout(()=>{un(p).then(f=>{let w=new Image;w.src=f.toDataURL(),w.id=`${c}-${u}`,w.className="imgPiece",document.getElementById("imgBox").appendChild(w),B(document.getElementById(`${c}-${u}`))}).catch(function(f){h(f)})},0)}),a=()=>{t.value=!1,n.value=""},o=()=>{const c=document.createElement("a");c.href=n.value,c.download=`微信聊天图片 - ${nt().format("YYYYMMDDHHmmss")}.gif`,c.target="_blank",c.rel="noopener noreferrer",document.body.appendChild(c),c.click(),document.body.removeChild(c)};return(c,l)=>{const B=lA("a-tooltip"),h=lA("a-button"),u=lA("a-space"),p=lA("a-spin"),f=lA("a-drawer");return sA(),iA(Te,null,[eA(B,{title:"生成动图前可以修改配置",placement:"right"},{default:aA(()=>[J("div",{class:"wtc-button",onClick:s},[RA("生成动图"),ws(J("div",Vf,null,512),[[Qs,!1]])])]),_:1}),eA(f,{width:500,title:"生成动图",placement:"right",closable:!1,destroyOnClose:!0,open:t.value,onClose:a},{extra:aA(()=>[eA(u,null,{default:aA(()=>[eA(h,{type:"primary",disabled:!n.value,onClick:o},{default:aA(()=>[RA("下载")]),_:1},8,["disabled"]),eA(h,{danger:"",type:"link",shape:"circle",icon:st(hA(rn)),disabled:!n.value,onClick:a},null,8,["icon","disabled"])]),_:1})]),default:aA(()=>[n.value?(sA(),iA("img",{key:0,src:n.value,alt:""},null,8,Pf)):(sA(),iA("div",Xf,[eA(p,{tip:"生成中...",size:"large"})]))]),_:1},8,["open"])],64)}}};var _i={exports:{}};/*!

JSZip v3.10.1 - A JavaScript class for generating and reading zip files
<http://stuartk.com/jszip>

(c) 2009-2016 Stuart Knightley <stuart [at] stuartk.com>
Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/main/LICENSE.markdown.

JSZip uses the library pako released under the MIT license :
https://github.com/nodeca/pako/blob/main/LICENSE
*/(function(r,e){(function(A){r.exports=A()})(function(){return function A(t,n,s){function i(c,l){if(!n[c]){if(!t[c]){var B=typeof de=="function"&&de;if(!l&&B)return B(c,!0);if(a)return a(c,!0);var h=new Error("Cannot find module '"+c+"'");throw h.code="MODULE_NOT_FOUND",h}var u=n[c]={exports:{}};t[c][0].call(u.exports,function(p){var f=t[c][1][p];return i(f||p)},u,u.exports,A,t,n,s)}return n[c].exports}for(var a=typeof de=="function"&&de,o=0;o<s.length;o++)i(s[o]);return i}({1:[function(A,t,n){var s=A("./utils"),i=A("./support"),a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";n.encode=function(o){for(var c,l,B,h,u,p,f,w=[],g=0,C=o.length,F=C,y=s.getTypeOf(o)!=="string";g<o.length;)F=C-g,B=y?(c=o[g++],l=g<C?o[g++]:0,g<C?o[g++]:0):(c=o.charCodeAt(g++),l=g<C?o.charCodeAt(g++):0,g<C?o.charCodeAt(g++):0),h=c>>2,u=(3&c)<<4|l>>4,p=1<F?(15&l)<<2|B>>6:64,f=2<F?63&B:64,w.push(a.charAt(h)+a.charAt(u)+a.charAt(p)+a.charAt(f));return w.join("")},n.decode=function(o){var c,l,B,h,u,p,f=0,w=0,g="data:";if(o.substr(0,g.length)===g)throw new Error("Invalid base64 input, it looks like a data url.");var C,F=3*(o=o.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(o.charAt(o.length-1)===a.charAt(64)&&F--,o.charAt(o.length-2)===a.charAt(64)&&F--,F%1!=0)throw new Error("Invalid base64 input, bad content length.");for(C=i.uint8array?new Uint8Array(0|F):new Array(0|F);f<o.length;)c=a.indexOf(o.charAt(f++))<<2|(h=a.indexOf(o.charAt(f++)))>>4,l=(15&h)<<4|(u=a.indexOf(o.charAt(f++)))>>2,B=(3&u)<<6|(p=a.indexOf(o.charAt(f++))),C[w++]=c,u!==64&&(C[w++]=l),p!==64&&(C[w++]=B);return C}},{"./support":30,"./utils":32}],2:[function(A,t,n){var s=A("./external"),i=A("./stream/DataWorker"),a=A("./stream/Crc32Probe"),o=A("./stream/DataLengthProbe");function c(l,B,h,u,p){this.compressedSize=l,this.uncompressedSize=B,this.crc32=h,this.compression=u,this.compressedContent=p}c.prototype={getContentWorker:function(){var l=new i(s.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new o("data_length")),B=this;return l.on("end",function(){if(this.streamInfo.data_length!==B.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),l},getCompressedWorker:function(){return new i(s.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},c.createWorkerFrom=function(l,B,h){return l.pipe(new a).pipe(new o("uncompressedSize")).pipe(B.compressWorker(h)).pipe(new o("compressedSize")).withStreamInfo("compression",B)},t.exports=c},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(A,t,n){var s=A("./stream/GenericWorker");n.STORE={magic:"\0\0",compressWorker:function(){return new s("STORE compression")},uncompressWorker:function(){return new s("STORE decompression")}},n.DEFLATE=A("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(A,t,n){var s=A("./utils"),i=function(){for(var a,o=[],c=0;c<256;c++){a=c;for(var l=0;l<8;l++)a=1&a?3988292384^a>>>1:a>>>1;o[c]=a}return o}();t.exports=function(a,o){return a!==void 0&&a.length?s.getTypeOf(a)!=="string"?function(c,l,B,h){var u=i,p=h+B;c^=-1;for(var f=h;f<p;f++)c=c>>>8^u[255&(c^l[f])];return-1^c}(0|o,a,a.length,0):function(c,l,B,h){var u=i,p=h+B;c^=-1;for(var f=h;f<p;f++)c=c>>>8^u[255&(c^l.charCodeAt(f))];return-1^c}(0|o,a,a.length,0):0}},{"./utils":32}],5:[function(A,t,n){n.base64=!1,n.binary=!1,n.dir=!1,n.createFolders=!0,n.date=null,n.compression=null,n.compressionOptions=null,n.comment=null,n.unixPermissions=null,n.dosPermissions=null},{}],6:[function(A,t,n){var s=null;s=typeof Promise<"u"?Promise:A("lie"),t.exports={Promise:s}},{lie:37}],7:[function(A,t,n){var s=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Uint32Array<"u",i=A("pako"),a=A("./utils"),o=A("./stream/GenericWorker"),c=s?"uint8array":"array";function l(B,h){o.call(this,"FlateWorker/"+B),this._pako=null,this._pakoAction=B,this._pakoOptions=h,this.meta={}}n.magic="\b\0",a.inherits(l,o),l.prototype.processChunk=function(B){this.meta=B.meta,this._pako===null&&this._createPako(),this._pako.push(a.transformTo(c,B.data),!1)},l.prototype.flush=function(){o.prototype.flush.call(this),this._pako===null&&this._createPako(),this._pako.push([],!0)},l.prototype.cleanUp=function(){o.prototype.cleanUp.call(this),this._pako=null},l.prototype._createPako=function(){this._pako=new i[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var B=this;this._pako.onData=function(h){B.push({data:h,meta:B.meta})}},n.compressWorker=function(B){return new l("Deflate",B)},n.uncompressWorker=function(){return new l("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(A,t,n){function s(u,p){var f,w="";for(f=0;f<p;f++)w+=String.fromCharCode(255&u),u>>>=8;return w}function i(u,p,f,w,g,C){var F,y,v=u.file,H=u.compression,b=C!==c.utf8encode,T=a.transformTo("string",C(v.name)),_=a.transformTo("string",c.utf8encode(v.name)),R=v.comment,Z=a.transformTo("string",C(R)),E=a.transformTo("string",c.utf8encode(R)),K=_.length!==v.name.length,Q=E.length!==R.length,k="",AA="",M="",nA=v.dir,N=v.date,tA={crc32:0,compressedSize:0,uncompressedSize:0};p&&!f||(tA.crc32=u.crc32,tA.compressedSize=u.compressedSize,tA.uncompressedSize=u.uncompressedSize);var S=0;p&&(S|=8),b||!K&&!Q||(S|=2048);var x=0,$=0;nA&&(x|=16),g==="UNIX"?($=798,x|=function(P,dA){var CA=P;return P||(CA=dA?16893:33204),(65535&CA)<<16}(v.unixPermissions,nA)):($=20,x|=function(P){return 63&(P||0)}(v.dosPermissions)),F=N.getUTCHours(),F<<=6,F|=N.getUTCMinutes(),F<<=5,F|=N.getUTCSeconds()/2,y=N.getUTCFullYear()-1980,y<<=4,y|=N.getUTCMonth()+1,y<<=5,y|=N.getUTCDate(),K&&(AA=s(1,1)+s(l(T),4)+_,k+="up"+s(AA.length,2)+AA),Q&&(M=s(1,1)+s(l(Z),4)+E,k+="uc"+s(M.length,2)+M);var Y="";return Y+=`
\0`,Y+=s(S,2),Y+=H.magic,Y+=s(F,2),Y+=s(y,2),Y+=s(tA.crc32,4),Y+=s(tA.compressedSize,4),Y+=s(tA.uncompressedSize,4),Y+=s(T.length,2),Y+=s(k.length,2),{fileRecord:B.LOCAL_FILE_HEADER+Y+T+k,dirRecord:B.CENTRAL_FILE_HEADER+s($,2)+Y+s(Z.length,2)+"\0\0\0\0"+s(x,4)+s(w,4)+T+k+Z}}var a=A("../utils"),o=A("../stream/GenericWorker"),c=A("../utf8"),l=A("../crc32"),B=A("../signature");function h(u,p,f,w){o.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=p,this.zipPlatform=f,this.encodeFileName=w,this.streamFiles=u,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}a.inherits(h,o),h.prototype.push=function(u){var p=u.meta.percent||0,f=this.entriesCount,w=this._sources.length;this.accumulate?this.contentBuffer.push(u):(this.bytesWritten+=u.data.length,o.prototype.push.call(this,{data:u.data,meta:{currentFile:this.currentFile,percent:f?(p+100*(f-w-1))/f:100}}))},h.prototype.openedSource=function(u){this.currentSourceOffset=this.bytesWritten,this.currentFile=u.file.name;var p=this.streamFiles&&!u.file.dir;if(p){var f=i(u,p,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:f.fileRecord,meta:{percent:0}})}else this.accumulate=!0},h.prototype.closedSource=function(u){this.accumulate=!1;var p=this.streamFiles&&!u.file.dir,f=i(u,p,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(f.dirRecord),p)this.push({data:function(w){return B.DATA_DESCRIPTOR+s(w.crc32,4)+s(w.compressedSize,4)+s(w.uncompressedSize,4)}(u),meta:{percent:100}});else for(this.push({data:f.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},h.prototype.flush=function(){for(var u=this.bytesWritten,p=0;p<this.dirRecords.length;p++)this.push({data:this.dirRecords[p],meta:{percent:100}});var f=this.bytesWritten-u,w=function(g,C,F,y,v){var H=a.transformTo("string",v(y));return B.CENTRAL_DIRECTORY_END+"\0\0\0\0"+s(g,2)+s(g,2)+s(C,4)+s(F,4)+s(H.length,2)+H}(this.dirRecords.length,f,u,this.zipComment,this.encodeFileName);this.push({data:w,meta:{percent:100}})},h.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},h.prototype.registerPrevious=function(u){this._sources.push(u);var p=this;return u.on("data",function(f){p.processChunk(f)}),u.on("end",function(){p.closedSource(p.previous.streamInfo),p._sources.length?p.prepareNextSource():p.end()}),u.on("error",function(f){p.error(f)}),this},h.prototype.resume=function(){return!!o.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},h.prototype.error=function(u){var p=this._sources;if(!o.prototype.error.call(this,u))return!1;for(var f=0;f<p.length;f++)try{p[f].error(u)}catch{}return!0},h.prototype.lock=function(){o.prototype.lock.call(this);for(var u=this._sources,p=0;p<u.length;p++)u[p].lock()},t.exports=h},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(A,t,n){var s=A("../compressions"),i=A("./ZipFileWorker");n.generateWorker=function(a,o,c){var l=new i(o.streamFiles,c,o.platform,o.encodeFileName),B=0;try{a.forEach(function(h,u){B++;var p=function(C,F){var y=C||F,v=s[y];if(!v)throw new Error(y+" is not a valid compression method !");return v}(u.options.compression,o.compression),f=u.options.compressionOptions||o.compressionOptions||{},w=u.dir,g=u.date;u._compressWorker(p,f).withStreamInfo("file",{name:h,dir:w,date:g,comment:u.comment||"",unixPermissions:u.unixPermissions,dosPermissions:u.dosPermissions}).pipe(l)}),l.entriesCount=B}catch(h){l.error(h)}return l}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(A,t,n){function s(){if(!(this instanceof s))return new s;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var i=new s;for(var a in this)typeof this[a]!="function"&&(i[a]=this[a]);return i}}(s.prototype=A("./object")).loadAsync=A("./load"),s.support=A("./support"),s.defaults=A("./defaults"),s.version="3.10.1",s.loadAsync=function(i,a){return new s().loadAsync(i,a)},s.external=A("./external"),t.exports=s},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(A,t,n){var s=A("./utils"),i=A("./external"),a=A("./utf8"),o=A("./zipEntries"),c=A("./stream/Crc32Probe"),l=A("./nodejsUtils");function B(h){return new i.Promise(function(u,p){var f=h.decompressed.getContentWorker().pipe(new c);f.on("error",function(w){p(w)}).on("end",function(){f.streamInfo.crc32!==h.decompressed.crc32?p(new Error("Corrupted zip : CRC32 mismatch")):u()}).resume()})}t.exports=function(h,u){var p=this;return u=s.extend(u||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:a.utf8decode}),l.isNode&&l.isStream(h)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):s.prepareContent("the loaded zip file",h,!0,u.optimizedBinaryString,u.base64).then(function(f){var w=new o(u);return w.load(f),w}).then(function(f){var w=[i.Promise.resolve(f)],g=f.files;if(u.checkCRC32)for(var C=0;C<g.length;C++)w.push(B(g[C]));return i.Promise.all(w)}).then(function(f){for(var w=f.shift(),g=w.files,C=0;C<g.length;C++){var F=g[C],y=F.fileNameStr,v=s.resolve(F.fileNameStr);p.file(v,F.decompressed,{binary:!0,optimizedBinaryString:!0,date:F.date,dir:F.dir,comment:F.fileCommentStr.length?F.fileCommentStr:null,unixPermissions:F.unixPermissions,dosPermissions:F.dosPermissions,createFolders:u.createFolders}),F.dir||(p.file(v).unsafeOriginalName=y)}return w.zipComment.length&&(p.comment=w.zipComment),p})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(A,t,n){var s=A("../utils"),i=A("../stream/GenericWorker");function a(o,c){i.call(this,"Nodejs stream input adapter for "+o),this._upstreamEnded=!1,this._bindStream(c)}s.inherits(a,i),a.prototype._bindStream=function(o){var c=this;(this._stream=o).pause(),o.on("data",function(l){c.push({data:l,meta:{percent:0}})}).on("error",function(l){c.isPaused?this.generatedError=l:c.error(l)}).on("end",function(){c.isPaused?c._upstreamEnded=!0:c.end()})},a.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},t.exports=a},{"../stream/GenericWorker":28,"../utils":32}],13:[function(A,t,n){var s=A("readable-stream").Readable;function i(a,o,c){s.call(this,o),this._helper=a;var l=this;a.on("data",function(B,h){l.push(B)||l._helper.pause(),c&&c(h)}).on("error",function(B){l.emit("error",B)}).on("end",function(){l.push(null)})}A("../utils").inherits(i,s),i.prototype._read=function(){this._helper.resume()},t.exports=i},{"../utils":32,"readable-stream":16}],14:[function(A,t,n){t.exports={isNode:typeof Buffer<"u",newBufferFrom:function(s,i){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(s,i);if(typeof s=="number")throw new Error('The "data" argument must not be a number');return new Buffer(s,i)},allocBuffer:function(s){if(Buffer.alloc)return Buffer.alloc(s);var i=new Buffer(s);return i.fill(0),i},isBuffer:function(s){return Buffer.isBuffer(s)},isStream:function(s){return s&&typeof s.on=="function"&&typeof s.pause=="function"&&typeof s.resume=="function"}}},{}],15:[function(A,t,n){function s(v,H,b){var T,_=a.getTypeOf(H),R=a.extend(b||{},l);R.date=R.date||new Date,R.compression!==null&&(R.compression=R.compression.toUpperCase()),typeof R.unixPermissions=="string"&&(R.unixPermissions=parseInt(R.unixPermissions,8)),R.unixPermissions&&16384&R.unixPermissions&&(R.dir=!0),R.dosPermissions&&16&R.dosPermissions&&(R.dir=!0),R.dir&&(v=g(v)),R.createFolders&&(T=w(v))&&C.call(this,T,!0);var Z=_==="string"&&R.binary===!1&&R.base64===!1;b&&b.binary!==void 0||(R.binary=!Z),(H instanceof B&&H.uncompressedSize===0||R.dir||!H||H.length===0)&&(R.base64=!1,R.binary=!0,H="",R.compression="STORE",_="string");var E=null;E=H instanceof B||H instanceof o?H:p.isNode&&p.isStream(H)?new f(v,H):a.prepareContent(v,H,R.binary,R.optimizedBinaryString,R.base64);var K=new h(v,E,R);this.files[v]=K}var i=A("./utf8"),a=A("./utils"),o=A("./stream/GenericWorker"),c=A("./stream/StreamHelper"),l=A("./defaults"),B=A("./compressedObject"),h=A("./zipObject"),u=A("./generate"),p=A("./nodejsUtils"),f=A("./nodejs/NodejsStreamInputAdapter"),w=function(v){v.slice(-1)==="/"&&(v=v.substring(0,v.length-1));var H=v.lastIndexOf("/");return 0<H?v.substring(0,H):""},g=function(v){return v.slice(-1)!=="/"&&(v+="/"),v},C=function(v,H){return H=H!==void 0?H:l.createFolders,v=g(v),this.files[v]||s.call(this,v,null,{dir:!0,createFolders:H}),this.files[v]};function F(v){return Object.prototype.toString.call(v)==="[object RegExp]"}var y={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(v){var H,b,T;for(H in this.files)T=this.files[H],(b=H.slice(this.root.length,H.length))&&H.slice(0,this.root.length)===this.root&&v(b,T)},filter:function(v){var H=[];return this.forEach(function(b,T){v(b,T)&&H.push(T)}),H},file:function(v,H,b){if(arguments.length!==1)return v=this.root+v,s.call(this,v,H,b),this;if(F(v)){var T=v;return this.filter(function(R,Z){return!Z.dir&&T.test(R)})}var _=this.files[this.root+v];return _&&!_.dir?_:null},folder:function(v){if(!v)return this;if(F(v))return this.filter(function(_,R){return R.dir&&v.test(_)});var H=this.root+v,b=C.call(this,H),T=this.clone();return T.root=b.name,T},remove:function(v){v=this.root+v;var H=this.files[v];if(H||(v.slice(-1)!=="/"&&(v+="/"),H=this.files[v]),H&&!H.dir)delete this.files[v];else for(var b=this.filter(function(_,R){return R.name.slice(0,v.length)===v}),T=0;T<b.length;T++)delete this.files[b[T].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(v){var H,b={};try{if((b=a.extend(v||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:i.utf8encode})).type=b.type.toLowerCase(),b.compression=b.compression.toUpperCase(),b.type==="binarystring"&&(b.type="string"),!b.type)throw new Error("No output type specified.");a.checkSupport(b.type),b.platform!=="darwin"&&b.platform!=="freebsd"&&b.platform!=="linux"&&b.platform!=="sunos"||(b.platform="UNIX"),b.platform==="win32"&&(b.platform="DOS");var T=b.comment||this.comment||"";H=u.generateWorker(this,b,T)}catch(_){(H=new o("error")).error(_)}return new c(H,b.type||"string",b.mimeType)},generateAsync:function(v,H){return this.generateInternalStream(v).accumulate(H)},generateNodeStream:function(v,H){return(v=v||{}).type||(v.type="nodebuffer"),this.generateInternalStream(v).toNodejsStream(H)}};t.exports=y},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(A,t,n){t.exports=A("stream")},{stream:void 0}],17:[function(A,t,n){var s=A("./DataReader");function i(a){s.call(this,a);for(var o=0;o<this.data.length;o++)a[o]=255&a[o]}A("../utils").inherits(i,s),i.prototype.byteAt=function(a){return this.data[this.zero+a]},i.prototype.lastIndexOfSignature=function(a){for(var o=a.charCodeAt(0),c=a.charCodeAt(1),l=a.charCodeAt(2),B=a.charCodeAt(3),h=this.length-4;0<=h;--h)if(this.data[h]===o&&this.data[h+1]===c&&this.data[h+2]===l&&this.data[h+3]===B)return h-this.zero;return-1},i.prototype.readAndCheckSignature=function(a){var o=a.charCodeAt(0),c=a.charCodeAt(1),l=a.charCodeAt(2),B=a.charCodeAt(3),h=this.readData(4);return o===h[0]&&c===h[1]&&l===h[2]&&B===h[3]},i.prototype.readData=function(a){if(this.checkOffset(a),a===0)return[];var o=this.data.slice(this.zero+this.index,this.zero+this.index+a);return this.index+=a,o},t.exports=i},{"../utils":32,"./DataReader":18}],18:[function(A,t,n){var s=A("../utils");function i(a){this.data=a,this.length=a.length,this.index=0,this.zero=0}i.prototype={checkOffset:function(a){this.checkIndex(this.index+a)},checkIndex:function(a){if(this.length<this.zero+a||a<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+a+"). Corrupted zip ?")},setIndex:function(a){this.checkIndex(a),this.index=a},skip:function(a){this.setIndex(this.index+a)},byteAt:function(){},readInt:function(a){var o,c=0;for(this.checkOffset(a),o=this.index+a-1;o>=this.index;o--)c=(c<<8)+this.byteAt(o);return this.index+=a,c},readString:function(a){return s.transformTo("string",this.readData(a))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var a=this.readInt(4);return new Date(Date.UTC(1980+(a>>25&127),(a>>21&15)-1,a>>16&31,a>>11&31,a>>5&63,(31&a)<<1))}},t.exports=i},{"../utils":32}],19:[function(A,t,n){var s=A("./Uint8ArrayReader");function i(a){s.call(this,a)}A("../utils").inherits(i,s),i.prototype.readData=function(a){this.checkOffset(a);var o=this.data.slice(this.zero+this.index,this.zero+this.index+a);return this.index+=a,o},t.exports=i},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(A,t,n){var s=A("./DataReader");function i(a){s.call(this,a)}A("../utils").inherits(i,s),i.prototype.byteAt=function(a){return this.data.charCodeAt(this.zero+a)},i.prototype.lastIndexOfSignature=function(a){return this.data.lastIndexOf(a)-this.zero},i.prototype.readAndCheckSignature=function(a){return a===this.readData(4)},i.prototype.readData=function(a){this.checkOffset(a);var o=this.data.slice(this.zero+this.index,this.zero+this.index+a);return this.index+=a,o},t.exports=i},{"../utils":32,"./DataReader":18}],21:[function(A,t,n){var s=A("./ArrayReader");function i(a){s.call(this,a)}A("../utils").inherits(i,s),i.prototype.readData=function(a){if(this.checkOffset(a),a===0)return new Uint8Array(0);var o=this.data.subarray(this.zero+this.index,this.zero+this.index+a);return this.index+=a,o},t.exports=i},{"../utils":32,"./ArrayReader":17}],22:[function(A,t,n){var s=A("../utils"),i=A("../support"),a=A("./ArrayReader"),o=A("./StringReader"),c=A("./NodeBufferReader"),l=A("./Uint8ArrayReader");t.exports=function(B){var h=s.getTypeOf(B);return s.checkSupport(h),h!=="string"||i.uint8array?h==="nodebuffer"?new c(B):i.uint8array?new l(s.transformTo("uint8array",B)):new a(s.transformTo("array",B)):new o(B)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(A,t,n){n.LOCAL_FILE_HEADER="PK",n.CENTRAL_FILE_HEADER="PK",n.CENTRAL_DIRECTORY_END="PK",n.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK\x07",n.ZIP64_CENTRAL_DIRECTORY_END="PK",n.DATA_DESCRIPTOR="PK\x07\b"},{}],24:[function(A,t,n){var s=A("./GenericWorker"),i=A("../utils");function a(o){s.call(this,"ConvertWorker to "+o),this.destType=o}i.inherits(a,s),a.prototype.processChunk=function(o){this.push({data:i.transformTo(this.destType,o.data),meta:o.meta})},t.exports=a},{"../utils":32,"./GenericWorker":28}],25:[function(A,t,n){var s=A("./GenericWorker"),i=A("../crc32");function a(){s.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}A("../utils").inherits(a,s),a.prototype.processChunk=function(o){this.streamInfo.crc32=i(o.data,this.streamInfo.crc32||0),this.push(o)},t.exports=a},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(A,t,n){var s=A("../utils"),i=A("./GenericWorker");function a(o){i.call(this,"DataLengthProbe for "+o),this.propName=o,this.withStreamInfo(o,0)}s.inherits(a,i),a.prototype.processChunk=function(o){if(o){var c=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=c+o.data.length}i.prototype.processChunk.call(this,o)},t.exports=a},{"../utils":32,"./GenericWorker":28}],27:[function(A,t,n){var s=A("../utils"),i=A("./GenericWorker");function a(o){i.call(this,"DataWorker");var c=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,o.then(function(l){c.dataIsReady=!0,c.data=l,c.max=l&&l.length||0,c.type=s.getTypeOf(l),c.isPaused||c._tickAndRepeat()},function(l){c.error(l)})}s.inherits(a,i),a.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,s.delay(this._tickAndRepeat,[],this)),!0)},a.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(s.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},a.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var o=null,c=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":o=this.data.substring(this.index,c);break;case"uint8array":o=this.data.subarray(this.index,c);break;case"array":case"nodebuffer":o=this.data.slice(this.index,c)}return this.index=c,this.push({data:o,meta:{percent:this.max?this.index/this.max*100:0}})},t.exports=a},{"../utils":32,"./GenericWorker":28}],28:[function(A,t,n){function s(i){this.name=i||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}s.prototype={push:function(i){this.emit("data",i)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(i){this.emit("error",i)}return!0},error:function(i){return!this.isFinished&&(this.isPaused?this.generatedError=i:(this.isFinished=!0,this.emit("error",i),this.previous&&this.previous.error(i),this.cleanUp()),!0)},on:function(i,a){return this._listeners[i].push(a),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(i,a){if(this._listeners[i])for(var o=0;o<this._listeners[i].length;o++)this._listeners[i][o].call(this,a)},pipe:function(i){return i.registerPrevious(this)},registerPrevious:function(i){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=i.streamInfo,this.mergeStreamInfo(),this.previous=i;var a=this;return i.on("data",function(o){a.processChunk(o)}),i.on("end",function(){a.end()}),i.on("error",function(o){a.error(o)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var i=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),i=!0),this.previous&&this.previous.resume(),!i},flush:function(){},processChunk:function(i){this.push(i)},withStreamInfo:function(i,a){return this.extraStreamInfo[i]=a,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var i in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,i)&&(this.streamInfo[i]=this.extraStreamInfo[i])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var i="Worker "+this.name;return this.previous?this.previous+" -> "+i:i}},t.exports=s},{}],29:[function(A,t,n){var s=A("../utils"),i=A("./ConvertWorker"),a=A("./GenericWorker"),o=A("../base64"),c=A("../support"),l=A("../external"),B=null;if(c.nodestream)try{B=A("../nodejs/NodejsStreamOutputAdapter")}catch{}function h(p,f){return new l.Promise(function(w,g){var C=[],F=p._internalType,y=p._outputType,v=p._mimeType;p.on("data",function(H,b){C.push(H),f&&f(b)}).on("error",function(H){C=[],g(H)}).on("end",function(){try{var H=function(b,T,_){switch(b){case"blob":return s.newBlob(s.transformTo("arraybuffer",T),_);case"base64":return o.encode(T);default:return s.transformTo(b,T)}}(y,function(b,T){var _,R=0,Z=null,E=0;for(_=0;_<T.length;_++)E+=T[_].length;switch(b){case"string":return T.join("");case"array":return Array.prototype.concat.apply([],T);case"uint8array":for(Z=new Uint8Array(E),_=0;_<T.length;_++)Z.set(T[_],R),R+=T[_].length;return Z;case"nodebuffer":return Buffer.concat(T);default:throw new Error("concat : unsupported type '"+b+"'")}}(F,C),v);w(H)}catch(b){g(b)}C=[]}).resume()})}function u(p,f,w){var g=f;switch(f){case"blob":case"arraybuffer":g="uint8array";break;case"base64":g="string"}try{this._internalType=g,this._outputType=f,this._mimeType=w,s.checkSupport(g),this._worker=p.pipe(new i(g)),p.lock()}catch(C){this._worker=new a("error"),this._worker.error(C)}}u.prototype={accumulate:function(p){return h(this,p)},on:function(p,f){var w=this;return p==="data"?this._worker.on(p,function(g){f.call(w,g.data,g.meta)}):this._worker.on(p,function(){s.delay(f,arguments,w)}),this},resume:function(){return s.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(p){if(s.checkSupport("nodestream"),this._outputType!=="nodebuffer")throw new Error(this._outputType+" is not supported by this method");return new B(this,{objectMode:this._outputType!=="nodebuffer"},p)}},t.exports=u},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(A,t,n){if(n.base64=!0,n.array=!0,n.string=!0,n.arraybuffer=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u",n.nodebuffer=typeof Buffer<"u",n.uint8array=typeof Uint8Array<"u",typeof ArrayBuffer>"u")n.blob=!1;else{var s=new ArrayBuffer(0);try{n.blob=new Blob([s],{type:"application/zip"}).size===0}catch{try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(s),n.blob=i.getBlob("application/zip").size===0}catch{n.blob=!1}}}try{n.nodestream=!!A("readable-stream").Readable}catch{n.nodestream=!1}},{"readable-stream":16}],31:[function(A,t,n){for(var s=A("./utils"),i=A("./support"),a=A("./nodejsUtils"),o=A("./stream/GenericWorker"),c=new Array(256),l=0;l<256;l++)c[l]=252<=l?6:248<=l?5:240<=l?4:224<=l?3:192<=l?2:1;c[254]=c[254]=1;function B(){o.call(this,"utf-8 decode"),this.leftOver=null}function h(){o.call(this,"utf-8 encode")}n.utf8encode=function(u){return i.nodebuffer?a.newBufferFrom(u,"utf-8"):function(p){var f,w,g,C,F,y=p.length,v=0;for(C=0;C<y;C++)(64512&(w=p.charCodeAt(C)))==55296&&C+1<y&&(64512&(g=p.charCodeAt(C+1)))==56320&&(w=65536+(w-55296<<10)+(g-56320),C++),v+=w<128?1:w<2048?2:w<65536?3:4;for(f=i.uint8array?new Uint8Array(v):new Array(v),C=F=0;F<v;C++)(64512&(w=p.charCodeAt(C)))==55296&&C+1<y&&(64512&(g=p.charCodeAt(C+1)))==56320&&(w=65536+(w-55296<<10)+(g-56320),C++),w<128?f[F++]=w:(w<2048?f[F++]=192|w>>>6:(w<65536?f[F++]=224|w>>>12:(f[F++]=240|w>>>18,f[F++]=128|w>>>12&63),f[F++]=128|w>>>6&63),f[F++]=128|63&w);return f}(u)},n.utf8decode=function(u){return i.nodebuffer?s.transformTo("nodebuffer",u).toString("utf-8"):function(p){var f,w,g,C,F=p.length,y=new Array(2*F);for(f=w=0;f<F;)if((g=p[f++])<128)y[w++]=g;else if(4<(C=c[g]))y[w++]=65533,f+=C-1;else{for(g&=C===2?31:C===3?15:7;1<C&&f<F;)g=g<<6|63&p[f++],C--;1<C?y[w++]=65533:g<65536?y[w++]=g:(g-=65536,y[w++]=55296|g>>10&1023,y[w++]=56320|1023&g)}return y.length!==w&&(y.subarray?y=y.subarray(0,w):y.length=w),s.applyFromCharCode(y)}(u=s.transformTo(i.uint8array?"uint8array":"array",u))},s.inherits(B,o),B.prototype.processChunk=function(u){var p=s.transformTo(i.uint8array?"uint8array":"array",u.data);if(this.leftOver&&this.leftOver.length){if(i.uint8array){var f=p;(p=new Uint8Array(f.length+this.leftOver.length)).set(this.leftOver,0),p.set(f,this.leftOver.length)}else p=this.leftOver.concat(p);this.leftOver=null}var w=function(C,F){var y;for((F=F||C.length)>C.length&&(F=C.length),y=F-1;0<=y&&(192&C[y])==128;)y--;return y<0||y===0?F:y+c[C[y]]>F?y:F}(p),g=p;w!==p.length&&(i.uint8array?(g=p.subarray(0,w),this.leftOver=p.subarray(w,p.length)):(g=p.slice(0,w),this.leftOver=p.slice(w,p.length))),this.push({data:n.utf8decode(g),meta:u.meta})},B.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:n.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},n.Utf8DecodeWorker=B,s.inherits(h,o),h.prototype.processChunk=function(u){this.push({data:n.utf8encode(u.data),meta:u.meta})},n.Utf8EncodeWorker=h},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(A,t,n){var s=A("./support"),i=A("./base64"),a=A("./nodejsUtils"),o=A("./external");function c(f){return f}function l(f,w){for(var g=0;g<f.length;++g)w[g]=255&f.charCodeAt(g);return w}A("setimmediate"),n.newBlob=function(f,w){n.checkSupport("blob");try{return new Blob([f],{type:w})}catch{try{var g=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return g.append(f),g.getBlob(w)}catch{throw new Error("Bug : can't construct the Blob.")}}};var B={stringifyByChunk:function(f,w,g){var C=[],F=0,y=f.length;if(y<=g)return String.fromCharCode.apply(null,f);for(;F<y;)w==="array"||w==="nodebuffer"?C.push(String.fromCharCode.apply(null,f.slice(F,Math.min(F+g,y)))):C.push(String.fromCharCode.apply(null,f.subarray(F,Math.min(F+g,y)))),F+=g;return C.join("")},stringifyByChar:function(f){for(var w="",g=0;g<f.length;g++)w+=String.fromCharCode(f[g]);return w},applyCanBeUsed:{uint8array:function(){try{return s.uint8array&&String.fromCharCode.apply(null,new Uint8Array(1)).length===1}catch{return!1}}(),nodebuffer:function(){try{return s.nodebuffer&&String.fromCharCode.apply(null,a.allocBuffer(1)).length===1}catch{return!1}}()}};function h(f){var w=65536,g=n.getTypeOf(f),C=!0;if(g==="uint8array"?C=B.applyCanBeUsed.uint8array:g==="nodebuffer"&&(C=B.applyCanBeUsed.nodebuffer),C)for(;1<w;)try{return B.stringifyByChunk(f,g,w)}catch{w=Math.floor(w/2)}return B.stringifyByChar(f)}function u(f,w){for(var g=0;g<f.length;g++)w[g]=f[g];return w}n.applyFromCharCode=h;var p={};p.string={string:c,array:function(f){return l(f,new Array(f.length))},arraybuffer:function(f){return p.string.uint8array(f).buffer},uint8array:function(f){return l(f,new Uint8Array(f.length))},nodebuffer:function(f){return l(f,a.allocBuffer(f.length))}},p.array={string:h,array:c,arraybuffer:function(f){return new Uint8Array(f).buffer},uint8array:function(f){return new Uint8Array(f)},nodebuffer:function(f){return a.newBufferFrom(f)}},p.arraybuffer={string:function(f){return h(new Uint8Array(f))},array:function(f){return u(new Uint8Array(f),new Array(f.byteLength))},arraybuffer:c,uint8array:function(f){return new Uint8Array(f)},nodebuffer:function(f){return a.newBufferFrom(new Uint8Array(f))}},p.uint8array={string:h,array:function(f){return u(f,new Array(f.length))},arraybuffer:function(f){return f.buffer},uint8array:c,nodebuffer:function(f){return a.newBufferFrom(f)}},p.nodebuffer={string:h,array:function(f){return u(f,new Array(f.length))},arraybuffer:function(f){return p.nodebuffer.uint8array(f).buffer},uint8array:function(f){return u(f,new Uint8Array(f.length))},nodebuffer:c},n.transformTo=function(f,w){if(w=w||"",!f)return w;n.checkSupport(f);var g=n.getTypeOf(w);return p[g][f](w)},n.resolve=function(f){for(var w=f.split("/"),g=[],C=0;C<w.length;C++){var F=w[C];F==="."||F===""&&C!==0&&C!==w.length-1||(F===".."?g.pop():g.push(F))}return g.join("/")},n.getTypeOf=function(f){return typeof f=="string"?"string":Object.prototype.toString.call(f)==="[object Array]"?"array":s.nodebuffer&&a.isBuffer(f)?"nodebuffer":s.uint8array&&f instanceof Uint8Array?"uint8array":s.arraybuffer&&f instanceof ArrayBuffer?"arraybuffer":void 0},n.checkSupport=function(f){if(!s[f.toLowerCase()])throw new Error(f+" is not supported by this platform")},n.MAX_VALUE_16BITS=65535,n.MAX_VALUE_32BITS=-1,n.pretty=function(f){var w,g,C="";for(g=0;g<(f||"").length;g++)C+="\\x"+((w=f.charCodeAt(g))<16?"0":"")+w.toString(16).toUpperCase();return C},n.delay=function(f,w,g){setImmediate(function(){f.apply(g||null,w||[])})},n.inherits=function(f,w){function g(){}g.prototype=w.prototype,f.prototype=new g},n.extend=function(){var f,w,g={};for(f=0;f<arguments.length;f++)for(w in arguments[f])Object.prototype.hasOwnProperty.call(arguments[f],w)&&g[w]===void 0&&(g[w]=arguments[f][w]);return g},n.prepareContent=function(f,w,g,C,F){return o.Promise.resolve(w).then(function(y){return s.blob&&(y instanceof Blob||["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(y))!==-1)&&typeof FileReader<"u"?new o.Promise(function(v,H){var b=new FileReader;b.onload=function(T){v(T.target.result)},b.onerror=function(T){H(T.target.error)},b.readAsArrayBuffer(y)}):y}).then(function(y){var v=n.getTypeOf(y);return v?(v==="arraybuffer"?y=n.transformTo("uint8array",y):v==="string"&&(F?y=i.decode(y):g&&C!==!0&&(y=function(H){return l(H,s.uint8array?new Uint8Array(H.length):new Array(H.length))}(y))),y):o.Promise.reject(new Error("Can't read the data of '"+f+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(A,t,n){var s=A("./reader/readerFor"),i=A("./utils"),a=A("./signature"),o=A("./zipEntry"),c=A("./support");function l(B){this.files=[],this.loadOptions=B}l.prototype={checkSignature:function(B){if(!this.reader.readAndCheckSignature(B)){this.reader.index-=4;var h=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+i.pretty(h)+", expected "+i.pretty(B)+")")}},isSignature:function(B,h){var u=this.reader.index;this.reader.setIndex(B);var p=this.reader.readString(4)===h;return this.reader.setIndex(u),p},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var B=this.reader.readData(this.zipCommentLength),h=c.uint8array?"uint8array":"array",u=i.transformTo(h,B);this.zipComment=this.loadOptions.decodeFileName(u)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var B,h,u,p=this.zip64EndOfCentralSize-44;0<p;)B=this.reader.readInt(2),h=this.reader.readInt(4),u=this.reader.readData(h),this.zip64ExtensibleData[B]={id:B,length:h,value:u}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var B,h;for(B=0;B<this.files.length;B++)h=this.files[B],this.reader.setIndex(h.localHeaderOffset),this.checkSignature(a.LOCAL_FILE_HEADER),h.readLocalPart(this.reader),h.handleUTF8(),h.processAttributes()},readCentralDir:function(){var B;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(a.CENTRAL_FILE_HEADER);)(B=new o({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(B);if(this.centralDirRecords!==this.files.length&&this.centralDirRecords!==0&&this.files.length===0)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var B=this.reader.lastIndexOfSignature(a.CENTRAL_DIRECTORY_END);if(B<0)throw this.isSignature(0,a.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(B);var h=B;if(this.checkSignature(a.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,(B=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(B),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,a.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var u=this.centralDirOffset+this.centralDirSize;this.zip64&&(u+=20,u+=12+this.zip64EndOfCentralSize);var p=h-u;if(0<p)this.isSignature(h,a.CENTRAL_FILE_HEADER)||(this.reader.zero=p);else if(p<0)throw new Error("Corrupted zip: missing "+Math.abs(p)+" bytes.")},prepareReader:function(B){this.reader=s(B)},load:function(B){this.prepareReader(B),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=l},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(A,t,n){var s=A("./reader/readerFor"),i=A("./utils"),a=A("./compressedObject"),o=A("./crc32"),c=A("./utf8"),l=A("./compressions"),B=A("./support");function h(u,p){this.options=u,this.loadOptions=p}h.prototype={isEncrypted:function(){return(1&this.bitFlag)==1},useUTF8:function(){return(2048&this.bitFlag)==2048},readLocalPart:function(u){var p,f;if(u.skip(22),this.fileNameLength=u.readInt(2),f=u.readInt(2),this.fileName=u.readData(this.fileNameLength),u.skip(f),this.compressedSize===-1||this.uncompressedSize===-1)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if((p=function(w){for(var g in l)if(Object.prototype.hasOwnProperty.call(l,g)&&l[g].magic===w)return l[g];return null}(this.compressionMethod))===null)throw new Error("Corrupted zip : compression "+i.pretty(this.compressionMethod)+" unknown (inner file : "+i.transformTo("string",this.fileName)+")");this.decompressed=new a(this.compressedSize,this.uncompressedSize,this.crc32,p,u.readData(this.compressedSize))},readCentralPart:function(u){this.versionMadeBy=u.readInt(2),u.skip(2),this.bitFlag=u.readInt(2),this.compressionMethod=u.readString(2),this.date=u.readDate(),this.crc32=u.readInt(4),this.compressedSize=u.readInt(4),this.uncompressedSize=u.readInt(4);var p=u.readInt(2);if(this.extraFieldsLength=u.readInt(2),this.fileCommentLength=u.readInt(2),this.diskNumberStart=u.readInt(2),this.internalFileAttributes=u.readInt(2),this.externalFileAttributes=u.readInt(4),this.localHeaderOffset=u.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");u.skip(p),this.readExtraFields(u),this.parseZIP64ExtraField(u),this.fileComment=u.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var u=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),u==0&&(this.dosPermissions=63&this.externalFileAttributes),u==3&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||this.fileNameStr.slice(-1)!=="/"||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var u=s(this.extraFields[1].value);this.uncompressedSize===i.MAX_VALUE_32BITS&&(this.uncompressedSize=u.readInt(8)),this.compressedSize===i.MAX_VALUE_32BITS&&(this.compressedSize=u.readInt(8)),this.localHeaderOffset===i.MAX_VALUE_32BITS&&(this.localHeaderOffset=u.readInt(8)),this.diskNumberStart===i.MAX_VALUE_32BITS&&(this.diskNumberStart=u.readInt(4))}},readExtraFields:function(u){var p,f,w,g=u.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});u.index+4<g;)p=u.readInt(2),f=u.readInt(2),w=u.readData(f),this.extraFields[p]={id:p,length:f,value:w};u.setIndex(g)},handleUTF8:function(){var u=B.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=c.utf8decode(this.fileName),this.fileCommentStr=c.utf8decode(this.fileComment);else{var p=this.findExtraFieldUnicodePath();if(p!==null)this.fileNameStr=p;else{var f=i.transformTo(u,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(f)}var w=this.findExtraFieldUnicodeComment();if(w!==null)this.fileCommentStr=w;else{var g=i.transformTo(u,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(g)}}},findExtraFieldUnicodePath:function(){var u=this.extraFields[28789];if(u){var p=s(u.value);return p.readInt(1)!==1||o(this.fileName)!==p.readInt(4)?null:c.utf8decode(p.readData(u.length-5))}return null},findExtraFieldUnicodeComment:function(){var u=this.extraFields[25461];if(u){var p=s(u.value);return p.readInt(1)!==1||o(this.fileComment)!==p.readInt(4)?null:c.utf8decode(p.readData(u.length-5))}return null}},t.exports=h},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(A,t,n){function s(p,f,w){this.name=p,this.dir=w.dir,this.date=w.date,this.comment=w.comment,this.unixPermissions=w.unixPermissions,this.dosPermissions=w.dosPermissions,this._data=f,this._dataBinary=w.binary,this.options={compression:w.compression,compressionOptions:w.compressionOptions}}var i=A("./stream/StreamHelper"),a=A("./stream/DataWorker"),o=A("./utf8"),c=A("./compressedObject"),l=A("./stream/GenericWorker");s.prototype={internalStream:function(p){var f=null,w="string";try{if(!p)throw new Error("No output type specified.");var g=(w=p.toLowerCase())==="string"||w==="text";w!=="binarystring"&&w!=="text"||(w="string"),f=this._decompressWorker();var C=!this._dataBinary;C&&!g&&(f=f.pipe(new o.Utf8EncodeWorker)),!C&&g&&(f=f.pipe(new o.Utf8DecodeWorker))}catch(F){(f=new l("error")).error(F)}return new i(f,w,"")},async:function(p,f){return this.internalStream(p).accumulate(f)},nodeStream:function(p,f){return this.internalStream(p||"nodebuffer").toNodejsStream(f)},_compressWorker:function(p,f){if(this._data instanceof c&&this._data.compression.magic===p.magic)return this._data.getCompressedWorker();var w=this._decompressWorker();return this._dataBinary||(w=w.pipe(new o.Utf8EncodeWorker)),c.createWorkerFrom(w,p,f)},_decompressWorker:function(){return this._data instanceof c?this._data.getContentWorker():this._data instanceof l?this._data:new a(this._data)}};for(var B=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],h=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},u=0;u<B.length;u++)s.prototype[B[u]]=h;t.exports=s},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(A,t,n){(function(s){var i,a,o=s.MutationObserver||s.WebKitMutationObserver;if(o){var c=0,l=new o(p),B=s.document.createTextNode("");l.observe(B,{characterData:!0}),i=function(){B.data=c=++c%2}}else if(s.setImmediate||s.MessageChannel===void 0)i="document"in s&&"onreadystatechange"in s.document.createElement("script")?function(){var f=s.document.createElement("script");f.onreadystatechange=function(){p(),f.onreadystatechange=null,f.parentNode.removeChild(f),f=null},s.document.documentElement.appendChild(f)}:function(){setTimeout(p,0)};else{var h=new s.MessageChannel;h.port1.onmessage=p,i=function(){h.port2.postMessage(0)}}var u=[];function p(){var f,w;a=!0;for(var g=u.length;g;){for(w=u,u=[],f=-1;++f<g;)w[f]();g=u.length}a=!1}t.exports=function(f){u.push(f)!==1||a||i()}}).call(this,typeof se<"u"?se:typeof self<"u"?self:typeof window<"u"?window:{})},{}],37:[function(A,t,n){var s=A("immediate");function i(){}var a={},o=["REJECTED"],c=["FULFILLED"],l=["PENDING"];function B(g){if(typeof g!="function")throw new TypeError("resolver must be a function");this.state=l,this.queue=[],this.outcome=void 0,g!==i&&f(this,g)}function h(g,C,F){this.promise=g,typeof C=="function"&&(this.onFulfilled=C,this.callFulfilled=this.otherCallFulfilled),typeof F=="function"&&(this.onRejected=F,this.callRejected=this.otherCallRejected)}function u(g,C,F){s(function(){var y;try{y=C(F)}catch(v){return a.reject(g,v)}y===g?a.reject(g,new TypeError("Cannot resolve promise with itself")):a.resolve(g,y)})}function p(g){var C=g&&g.then;if(g&&(typeof g=="object"||typeof g=="function")&&typeof C=="function")return function(){C.apply(g,arguments)}}function f(g,C){var F=!1;function y(b){F||(F=!0,a.reject(g,b))}function v(b){F||(F=!0,a.resolve(g,b))}var H=w(function(){C(v,y)});H.status==="error"&&y(H.value)}function w(g,C){var F={};try{F.value=g(C),F.status="success"}catch(y){F.status="error",F.value=y}return F}(t.exports=B).prototype.finally=function(g){if(typeof g!="function")return this;var C=this.constructor;return this.then(function(F){return C.resolve(g()).then(function(){return F})},function(F){return C.resolve(g()).then(function(){throw F})})},B.prototype.catch=function(g){return this.then(null,g)},B.prototype.then=function(g,C){if(typeof g!="function"&&this.state===c||typeof C!="function"&&this.state===o)return this;var F=new this.constructor(i);return this.state!==l?u(F,this.state===c?g:C,this.outcome):this.queue.push(new h(F,g,C)),F},h.prototype.callFulfilled=function(g){a.resolve(this.promise,g)},h.prototype.otherCallFulfilled=function(g){u(this.promise,this.onFulfilled,g)},h.prototype.callRejected=function(g){a.reject(this.promise,g)},h.prototype.otherCallRejected=function(g){u(this.promise,this.onRejected,g)},a.resolve=function(g,C){var F=w(p,C);if(F.status==="error")return a.reject(g,F.value);var y=F.value;if(y)f(g,y);else{g.state=c,g.outcome=C;for(var v=-1,H=g.queue.length;++v<H;)g.queue[v].callFulfilled(C)}return g},a.reject=function(g,C){g.state=o,g.outcome=C;for(var F=-1,y=g.queue.length;++F<y;)g.queue[F].callRejected(C);return g},B.resolve=function(g){return g instanceof this?g:a.resolve(new this(i),g)},B.reject=function(g){var C=new this(i);return a.reject(C,g)},B.all=function(g){var C=this;if(Object.prototype.toString.call(g)!=="[object Array]")return this.reject(new TypeError("must be an array"));var F=g.length,y=!1;if(!F)return this.resolve([]);for(var v=new Array(F),H=0,b=-1,T=new this(i);++b<F;)_(g[b],b);return T;function _(R,Z){C.resolve(R).then(function(E){v[Z]=E,++H!==F||y||(y=!0,a.resolve(T,v))},function(E){y||(y=!0,a.reject(T,E))})}},B.race=function(g){var C=this;if(Object.prototype.toString.call(g)!=="[object Array]")return this.reject(new TypeError("must be an array"));var F=g.length,y=!1;if(!F)return this.resolve([]);for(var v=-1,H=new this(i);++v<F;)b=g[v],C.resolve(b).then(function(T){y||(y=!0,a.resolve(H,T))},function(T){y||(y=!0,a.reject(H,T))});var b;return H}},{immediate:36}],38:[function(A,t,n){var s={};(0,A("./lib/utils/common").assign)(s,A("./lib/deflate"),A("./lib/inflate"),A("./lib/zlib/constants")),t.exports=s},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(A,t,n){var s=A("./zlib/deflate"),i=A("./utils/common"),a=A("./utils/strings"),o=A("./zlib/messages"),c=A("./zlib/zstream"),l=Object.prototype.toString,B=0,h=-1,u=0,p=8;function f(g){if(!(this instanceof f))return new f(g);this.options=i.assign({level:h,method:p,chunkSize:16384,windowBits:15,memLevel:8,strategy:u,to:""},g||{});var C=this.options;C.raw&&0<C.windowBits?C.windowBits=-C.windowBits:C.gzip&&0<C.windowBits&&C.windowBits<16&&(C.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new c,this.strm.avail_out=0;var F=s.deflateInit2(this.strm,C.level,C.method,C.windowBits,C.memLevel,C.strategy);if(F!==B)throw new Error(o[F]);if(C.header&&s.deflateSetHeader(this.strm,C.header),C.dictionary){var y;if(y=typeof C.dictionary=="string"?a.string2buf(C.dictionary):l.call(C.dictionary)==="[object ArrayBuffer]"?new Uint8Array(C.dictionary):C.dictionary,(F=s.deflateSetDictionary(this.strm,y))!==B)throw new Error(o[F]);this._dict_set=!0}}function w(g,C){var F=new f(C);if(F.push(g,!0),F.err)throw F.msg||o[F.err];return F.result}f.prototype.push=function(g,C){var F,y,v=this.strm,H=this.options.chunkSize;if(this.ended)return!1;y=C===~~C?C:C===!0?4:0,typeof g=="string"?v.input=a.string2buf(g):l.call(g)==="[object ArrayBuffer]"?v.input=new Uint8Array(g):v.input=g,v.next_in=0,v.avail_in=v.input.length;do{if(v.avail_out===0&&(v.output=new i.Buf8(H),v.next_out=0,v.avail_out=H),(F=s.deflate(v,y))!==1&&F!==B)return this.onEnd(F),!(this.ended=!0);v.avail_out!==0&&(v.avail_in!==0||y!==4&&y!==2)||(this.options.to==="string"?this.onData(a.buf2binstring(i.shrinkBuf(v.output,v.next_out))):this.onData(i.shrinkBuf(v.output,v.next_out)))}while((0<v.avail_in||v.avail_out===0)&&F!==1);return y===4?(F=s.deflateEnd(this.strm),this.onEnd(F),this.ended=!0,F===B):y!==2||(this.onEnd(B),!(v.avail_out=0))},f.prototype.onData=function(g){this.chunks.push(g)},f.prototype.onEnd=function(g){g===B&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=g,this.msg=this.strm.msg},n.Deflate=f,n.deflate=w,n.deflateRaw=function(g,C){return(C=C||{}).raw=!0,w(g,C)},n.gzip=function(g,C){return(C=C||{}).gzip=!0,w(g,C)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(A,t,n){var s=A("./zlib/inflate"),i=A("./utils/common"),a=A("./utils/strings"),o=A("./zlib/constants"),c=A("./zlib/messages"),l=A("./zlib/zstream"),B=A("./zlib/gzheader"),h=Object.prototype.toString;function u(f){if(!(this instanceof u))return new u(f);this.options=i.assign({chunkSize:16384,windowBits:0,to:""},f||{});var w=this.options;w.raw&&0<=w.windowBits&&w.windowBits<16&&(w.windowBits=-w.windowBits,w.windowBits===0&&(w.windowBits=-15)),!(0<=w.windowBits&&w.windowBits<16)||f&&f.windowBits||(w.windowBits+=32),15<w.windowBits&&w.windowBits<48&&!(15&w.windowBits)&&(w.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var g=s.inflateInit2(this.strm,w.windowBits);if(g!==o.Z_OK)throw new Error(c[g]);this.header=new B,s.inflateGetHeader(this.strm,this.header)}function p(f,w){var g=new u(w);if(g.push(f,!0),g.err)throw g.msg||c[g.err];return g.result}u.prototype.push=function(f,w){var g,C,F,y,v,H,b=this.strm,T=this.options.chunkSize,_=this.options.dictionary,R=!1;if(this.ended)return!1;C=w===~~w?w:w===!0?o.Z_FINISH:o.Z_NO_FLUSH,typeof f=="string"?b.input=a.binstring2buf(f):h.call(f)==="[object ArrayBuffer]"?b.input=new Uint8Array(f):b.input=f,b.next_in=0,b.avail_in=b.input.length;do{if(b.avail_out===0&&(b.output=new i.Buf8(T),b.next_out=0,b.avail_out=T),(g=s.inflate(b,o.Z_NO_FLUSH))===o.Z_NEED_DICT&&_&&(H=typeof _=="string"?a.string2buf(_):h.call(_)==="[object ArrayBuffer]"?new Uint8Array(_):_,g=s.inflateSetDictionary(this.strm,H)),g===o.Z_BUF_ERROR&&R===!0&&(g=o.Z_OK,R=!1),g!==o.Z_STREAM_END&&g!==o.Z_OK)return this.onEnd(g),!(this.ended=!0);b.next_out&&(b.avail_out!==0&&g!==o.Z_STREAM_END&&(b.avail_in!==0||C!==o.Z_FINISH&&C!==o.Z_SYNC_FLUSH)||(this.options.to==="string"?(F=a.utf8border(b.output,b.next_out),y=b.next_out-F,v=a.buf2string(b.output,F),b.next_out=y,b.avail_out=T-y,y&&i.arraySet(b.output,b.output,F,y,0),this.onData(v)):this.onData(i.shrinkBuf(b.output,b.next_out)))),b.avail_in===0&&b.avail_out===0&&(R=!0)}while((0<b.avail_in||b.avail_out===0)&&g!==o.Z_STREAM_END);return g===o.Z_STREAM_END&&(C=o.Z_FINISH),C===o.Z_FINISH?(g=s.inflateEnd(this.strm),this.onEnd(g),this.ended=!0,g===o.Z_OK):C!==o.Z_SYNC_FLUSH||(this.onEnd(o.Z_OK),!(b.avail_out=0))},u.prototype.onData=function(f){this.chunks.push(f)},u.prototype.onEnd=function(f){f===o.Z_OK&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=f,this.msg=this.strm.msg},n.Inflate=u,n.inflate=p,n.inflateRaw=function(f,w){return(w=w||{}).raw=!0,p(f,w)},n.ungzip=p},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(A,t,n){var s=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Int32Array<"u";n.assign=function(o){for(var c=Array.prototype.slice.call(arguments,1);c.length;){var l=c.shift();if(l){if(typeof l!="object")throw new TypeError(l+"must be non-object");for(var B in l)l.hasOwnProperty(B)&&(o[B]=l[B])}}return o},n.shrinkBuf=function(o,c){return o.length===c?o:o.subarray?o.subarray(0,c):(o.length=c,o)};var i={arraySet:function(o,c,l,B,h){if(c.subarray&&o.subarray)o.set(c.subarray(l,l+B),h);else for(var u=0;u<B;u++)o[h+u]=c[l+u]},flattenChunks:function(o){var c,l,B,h,u,p;for(c=B=0,l=o.length;c<l;c++)B+=o[c].length;for(p=new Uint8Array(B),c=h=0,l=o.length;c<l;c++)u=o[c],p.set(u,h),h+=u.length;return p}},a={arraySet:function(o,c,l,B,h){for(var u=0;u<B;u++)o[h+u]=c[l+u]},flattenChunks:function(o){return[].concat.apply([],o)}};n.setTyped=function(o){o?(n.Buf8=Uint8Array,n.Buf16=Uint16Array,n.Buf32=Int32Array,n.assign(n,i)):(n.Buf8=Array,n.Buf16=Array,n.Buf32=Array,n.assign(n,a))},n.setTyped(s)},{}],42:[function(A,t,n){var s=A("./common"),i=!0,a=!0;try{String.fromCharCode.apply(null,[0])}catch{i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{a=!1}for(var o=new s.Buf8(256),c=0;c<256;c++)o[c]=252<=c?6:248<=c?5:240<=c?4:224<=c?3:192<=c?2:1;function l(B,h){if(h<65537&&(B.subarray&&a||!B.subarray&&i))return String.fromCharCode.apply(null,s.shrinkBuf(B,h));for(var u="",p=0;p<h;p++)u+=String.fromCharCode(B[p]);return u}o[254]=o[254]=1,n.string2buf=function(B){var h,u,p,f,w,g=B.length,C=0;for(f=0;f<g;f++)(64512&(u=B.charCodeAt(f)))==55296&&f+1<g&&(64512&(p=B.charCodeAt(f+1)))==56320&&(u=65536+(u-55296<<10)+(p-56320),f++),C+=u<128?1:u<2048?2:u<65536?3:4;for(h=new s.Buf8(C),f=w=0;w<C;f++)(64512&(u=B.charCodeAt(f)))==55296&&f+1<g&&(64512&(p=B.charCodeAt(f+1)))==56320&&(u=65536+(u-55296<<10)+(p-56320),f++),u<128?h[w++]=u:(u<2048?h[w++]=192|u>>>6:(u<65536?h[w++]=224|u>>>12:(h[w++]=240|u>>>18,h[w++]=128|u>>>12&63),h[w++]=128|u>>>6&63),h[w++]=128|63&u);return h},n.buf2binstring=function(B){return l(B,B.length)},n.binstring2buf=function(B){for(var h=new s.Buf8(B.length),u=0,p=h.length;u<p;u++)h[u]=B.charCodeAt(u);return h},n.buf2string=function(B,h){var u,p,f,w,g=h||B.length,C=new Array(2*g);for(u=p=0;u<g;)if((f=B[u++])<128)C[p++]=f;else if(4<(w=o[f]))C[p++]=65533,u+=w-1;else{for(f&=w===2?31:w===3?15:7;1<w&&u<g;)f=f<<6|63&B[u++],w--;1<w?C[p++]=65533:f<65536?C[p++]=f:(f-=65536,C[p++]=55296|f>>10&1023,C[p++]=56320|1023&f)}return l(C,p)},n.utf8border=function(B,h){var u;for((h=h||B.length)>B.length&&(h=B.length),u=h-1;0<=u&&(192&B[u])==128;)u--;return u<0||u===0?h:u+o[B[u]]>h?u:h}},{"./common":41}],43:[function(A,t,n){t.exports=function(s,i,a,o){for(var c=65535&s|0,l=s>>>16&65535|0,B=0;a!==0;){for(a-=B=2e3<a?2e3:a;l=l+(c=c+i[o++]|0)|0,--B;);c%=65521,l%=65521}return c|l<<16|0}},{}],44:[function(A,t,n){t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(A,t,n){var s=function(){for(var i,a=[],o=0;o<256;o++){i=o;for(var c=0;c<8;c++)i=1&i?3988292384^i>>>1:i>>>1;a[o]=i}return a}();t.exports=function(i,a,o,c){var l=s,B=c+o;i^=-1;for(var h=c;h<B;h++)i=i>>>8^l[255&(i^a[h])];return-1^i}},{}],46:[function(A,t,n){var s,i=A("../utils/common"),a=A("./trees"),o=A("./adler32"),c=A("./crc32"),l=A("./messages"),B=0,h=4,u=0,p=-2,f=-1,w=4,g=2,C=8,F=9,y=286,v=30,H=19,b=2*y+1,T=15,_=3,R=258,Z=R+_+1,E=42,K=113,Q=1,k=2,AA=3,M=4;function nA(d,O){return d.msg=l[O],O}function N(d){return(d<<1)-(4<d?9:0)}function tA(d){for(var O=d.length;0<=--O;)d[O]=0}function S(d){var O=d.state,D=O.pending;D>d.avail_out&&(D=d.avail_out),D!==0&&(i.arraySet(d.output,O.pending_buf,O.pending_out,D,d.next_out),d.next_out+=D,O.pending_out+=D,d.total_out+=D,d.avail_out-=D,O.pending-=D,O.pending===0&&(O.pending_out=0))}function x(d,O){a._tr_flush_block(d,0<=d.block_start?d.block_start:-1,d.strstart-d.block_start,O),d.block_start=d.strstart,S(d.strm)}function $(d,O){d.pending_buf[d.pending++]=O}function Y(d,O){d.pending_buf[d.pending++]=O>>>8&255,d.pending_buf[d.pending++]=255&O}function P(d,O){var D,m,U=d.max_chain_length,I=d.strstart,G=d.prev_length,V=d.nice_match,L=d.strstart>d.w_size-Z?d.strstart-(d.w_size-Z):0,X=d.window,q=d.w_mask,W=d.prev,rA=d.strstart+R,wA=X[I+G-1],cA=X[I+G];d.prev_length>=d.good_match&&(U>>=2),V>d.lookahead&&(V=d.lookahead);do if(X[(D=O)+G]===cA&&X[D+G-1]===wA&&X[D]===X[I]&&X[++D]===X[I+1]){I+=2,D++;do;while(X[++I]===X[++D]&&X[++I]===X[++D]&&X[++I]===X[++D]&&X[++I]===X[++D]&&X[++I]===X[++D]&&X[++I]===X[++D]&&X[++I]===X[++D]&&X[++I]===X[++D]&&I<rA);if(m=R-(rA-I),I=rA-R,G<m){if(d.match_start=O,V<=(G=m))break;wA=X[I+G-1],cA=X[I+G]}}while((O=W[O&q])>L&&--U!=0);return G<=d.lookahead?G:d.lookahead}function dA(d){var O,D,m,U,I,G,V,L,X,q,W=d.w_size;do{if(U=d.window_size-d.lookahead-d.strstart,d.strstart>=W+(W-Z)){for(i.arraySet(d.window,d.window,W,W,0),d.match_start-=W,d.strstart-=W,d.block_start-=W,O=D=d.hash_size;m=d.head[--O],d.head[O]=W<=m?m-W:0,--D;);for(O=D=W;m=d.prev[--O],d.prev[O]=W<=m?m-W:0,--D;);U+=W}if(d.strm.avail_in===0)break;if(G=d.strm,V=d.window,L=d.strstart+d.lookahead,X=U,q=void 0,q=G.avail_in,X<q&&(q=X),D=q===0?0:(G.avail_in-=q,i.arraySet(V,G.input,G.next_in,q,L),G.state.wrap===1?G.adler=o(G.adler,V,q,L):G.state.wrap===2&&(G.adler=c(G.adler,V,q,L)),G.next_in+=q,G.total_in+=q,q),d.lookahead+=D,d.lookahead+d.insert>=_)for(I=d.strstart-d.insert,d.ins_h=d.window[I],d.ins_h=(d.ins_h<<d.hash_shift^d.window[I+1])&d.hash_mask;d.insert&&(d.ins_h=(d.ins_h<<d.hash_shift^d.window[I+_-1])&d.hash_mask,d.prev[I&d.w_mask]=d.head[d.ins_h],d.head[d.ins_h]=I,I++,d.insert--,!(d.lookahead+d.insert<_)););}while(d.lookahead<Z&&d.strm.avail_in!==0)}function CA(d,O){for(var D,m;;){if(d.lookahead<Z){if(dA(d),d.lookahead<Z&&O===B)return Q;if(d.lookahead===0)break}if(D=0,d.lookahead>=_&&(d.ins_h=(d.ins_h<<d.hash_shift^d.window[d.strstart+_-1])&d.hash_mask,D=d.prev[d.strstart&d.w_mask]=d.head[d.ins_h],d.head[d.ins_h]=d.strstart),D!==0&&d.strstart-D<=d.w_size-Z&&(d.match_length=P(d,D)),d.match_length>=_)if(m=a._tr_tally(d,d.strstart-d.match_start,d.match_length-_),d.lookahead-=d.match_length,d.match_length<=d.max_lazy_match&&d.lookahead>=_){for(d.match_length--;d.strstart++,d.ins_h=(d.ins_h<<d.hash_shift^d.window[d.strstart+_-1])&d.hash_mask,D=d.prev[d.strstart&d.w_mask]=d.head[d.ins_h],d.head[d.ins_h]=d.strstart,--d.match_length!=0;);d.strstart++}else d.strstart+=d.match_length,d.match_length=0,d.ins_h=d.window[d.strstart],d.ins_h=(d.ins_h<<d.hash_shift^d.window[d.strstart+1])&d.hash_mask;else m=a._tr_tally(d,0,d.window[d.strstart]),d.lookahead--,d.strstart++;if(m&&(x(d,!1),d.strm.avail_out===0))return Q}return d.insert=d.strstart<_-1?d.strstart:_-1,O===h?(x(d,!0),d.strm.avail_out===0?AA:M):d.last_lit&&(x(d,!1),d.strm.avail_out===0)?Q:k}function BA(d,O){for(var D,m,U;;){if(d.lookahead<Z){if(dA(d),d.lookahead<Z&&O===B)return Q;if(d.lookahead===0)break}if(D=0,d.lookahead>=_&&(d.ins_h=(d.ins_h<<d.hash_shift^d.window[d.strstart+_-1])&d.hash_mask,D=d.prev[d.strstart&d.w_mask]=d.head[d.ins_h],d.head[d.ins_h]=d.strstart),d.prev_length=d.match_length,d.prev_match=d.match_start,d.match_length=_-1,D!==0&&d.prev_length<d.max_lazy_match&&d.strstart-D<=d.w_size-Z&&(d.match_length=P(d,D),d.match_length<=5&&(d.strategy===1||d.match_length===_&&4096<d.strstart-d.match_start)&&(d.match_length=_-1)),d.prev_length>=_&&d.match_length<=d.prev_length){for(U=d.strstart+d.lookahead-_,m=a._tr_tally(d,d.strstart-1-d.prev_match,d.prev_length-_),d.lookahead-=d.prev_length-1,d.prev_length-=2;++d.strstart<=U&&(d.ins_h=(d.ins_h<<d.hash_shift^d.window[d.strstart+_-1])&d.hash_mask,D=d.prev[d.strstart&d.w_mask]=d.head[d.ins_h],d.head[d.ins_h]=d.strstart),--d.prev_length!=0;);if(d.match_available=0,d.match_length=_-1,d.strstart++,m&&(x(d,!1),d.strm.avail_out===0))return Q}else if(d.match_available){if((m=a._tr_tally(d,0,d.window[d.strstart-1]))&&x(d,!1),d.strstart++,d.lookahead--,d.strm.avail_out===0)return Q}else d.match_available=1,d.strstart++,d.lookahead--}return d.match_available&&(m=a._tr_tally(d,0,d.window[d.strstart-1]),d.match_available=0),d.insert=d.strstart<_-1?d.strstart:_-1,O===h?(x(d,!0),d.strm.avail_out===0?AA:M):d.last_lit&&(x(d,!1),d.strm.avail_out===0)?Q:k}function uA(d,O,D,m,U){this.good_length=d,this.max_lazy=O,this.nice_length=D,this.max_chain=m,this.func=U}function IA(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=C,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(2*b),this.dyn_dtree=new i.Buf16(2*(2*v+1)),this.bl_tree=new i.Buf16(2*(2*H+1)),tA(this.dyn_ltree),tA(this.dyn_dtree),tA(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(T+1),this.heap=new i.Buf16(2*y+1),tA(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(2*y+1),tA(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function UA(d){var O;return d&&d.state?(d.total_in=d.total_out=0,d.data_type=g,(O=d.state).pending=0,O.pending_out=0,O.wrap<0&&(O.wrap=-O.wrap),O.status=O.wrap?E:K,d.adler=O.wrap===2?0:1,O.last_flush=B,a._tr_init(O),u):nA(d,p)}function ZA(d){var O=UA(d);return O===u&&function(D){D.window_size=2*D.w_size,tA(D.head),D.max_lazy_match=s[D.level].max_lazy,D.good_match=s[D.level].good_length,D.nice_match=s[D.level].nice_length,D.max_chain_length=s[D.level].max_chain,D.strstart=0,D.block_start=0,D.lookahead=0,D.insert=0,D.match_length=D.prev_length=_-1,D.match_available=0,D.ins_h=0}(d.state),O}function XA(d,O,D,m,U,I){if(!d)return p;var G=1;if(O===f&&(O=6),m<0?(G=0,m=-m):15<m&&(G=2,m-=16),U<1||F<U||D!==C||m<8||15<m||O<0||9<O||I<0||w<I)return nA(d,p);m===8&&(m=9);var V=new IA;return(d.state=V).strm=d,V.wrap=G,V.gzhead=null,V.w_bits=m,V.w_size=1<<V.w_bits,V.w_mask=V.w_size-1,V.hash_bits=U+7,V.hash_size=1<<V.hash_bits,V.hash_mask=V.hash_size-1,V.hash_shift=~~((V.hash_bits+_-1)/_),V.window=new i.Buf8(2*V.w_size),V.head=new i.Buf16(V.hash_size),V.prev=new i.Buf16(V.w_size),V.lit_bufsize=1<<U+6,V.pending_buf_size=4*V.lit_bufsize,V.pending_buf=new i.Buf8(V.pending_buf_size),V.d_buf=1*V.lit_bufsize,V.l_buf=3*V.lit_bufsize,V.level=O,V.strategy=I,V.method=D,ZA(d)}s=[new uA(0,0,0,0,function(d,O){var D=65535;for(D>d.pending_buf_size-5&&(D=d.pending_buf_size-5);;){if(d.lookahead<=1){if(dA(d),d.lookahead===0&&O===B)return Q;if(d.lookahead===0)break}d.strstart+=d.lookahead,d.lookahead=0;var m=d.block_start+D;if((d.strstart===0||d.strstart>=m)&&(d.lookahead=d.strstart-m,d.strstart=m,x(d,!1),d.strm.avail_out===0)||d.strstart-d.block_start>=d.w_size-Z&&(x(d,!1),d.strm.avail_out===0))return Q}return d.insert=0,O===h?(x(d,!0),d.strm.avail_out===0?AA:M):(d.strstart>d.block_start&&(x(d,!1),d.strm.avail_out),Q)}),new uA(4,4,8,4,CA),new uA(4,5,16,8,CA),new uA(4,6,32,32,CA),new uA(4,4,16,16,BA),new uA(8,16,32,32,BA),new uA(8,16,128,128,BA),new uA(8,32,128,256,BA),new uA(32,128,258,1024,BA),new uA(32,258,258,4096,BA)],n.deflateInit=function(d,O){return XA(d,O,C,15,8,0)},n.deflateInit2=XA,n.deflateReset=ZA,n.deflateResetKeep=UA,n.deflateSetHeader=function(d,O){return d&&d.state?d.state.wrap!==2?p:(d.state.gzhead=O,u):p},n.deflate=function(d,O){var D,m,U,I;if(!d||!d.state||5<O||O<0)return d?nA(d,p):p;if(m=d.state,!d.output||!d.input&&d.avail_in!==0||m.status===666&&O!==h)return nA(d,d.avail_out===0?-5:p);if(m.strm=d,D=m.last_flush,m.last_flush=O,m.status===E)if(m.wrap===2)d.adler=0,$(m,31),$(m,139),$(m,8),m.gzhead?($(m,(m.gzhead.text?1:0)+(m.gzhead.hcrc?2:0)+(m.gzhead.extra?4:0)+(m.gzhead.name?8:0)+(m.gzhead.comment?16:0)),$(m,255&m.gzhead.time),$(m,m.gzhead.time>>8&255),$(m,m.gzhead.time>>16&255),$(m,m.gzhead.time>>24&255),$(m,m.level===9?2:2<=m.strategy||m.level<2?4:0),$(m,255&m.gzhead.os),m.gzhead.extra&&m.gzhead.extra.length&&($(m,255&m.gzhead.extra.length),$(m,m.gzhead.extra.length>>8&255)),m.gzhead.hcrc&&(d.adler=c(d.adler,m.pending_buf,m.pending,0)),m.gzindex=0,m.status=69):($(m,0),$(m,0),$(m,0),$(m,0),$(m,0),$(m,m.level===9?2:2<=m.strategy||m.level<2?4:0),$(m,3),m.status=K);else{var G=C+(m.w_bits-8<<4)<<8;G|=(2<=m.strategy||m.level<2?0:m.level<6?1:m.level===6?2:3)<<6,m.strstart!==0&&(G|=32),G+=31-G%31,m.status=K,Y(m,G),m.strstart!==0&&(Y(m,d.adler>>>16),Y(m,65535&d.adler)),d.adler=1}if(m.status===69)if(m.gzhead.extra){for(U=m.pending;m.gzindex<(65535&m.gzhead.extra.length)&&(m.pending!==m.pending_buf_size||(m.gzhead.hcrc&&m.pending>U&&(d.adler=c(d.adler,m.pending_buf,m.pending-U,U)),S(d),U=m.pending,m.pending!==m.pending_buf_size));)$(m,255&m.gzhead.extra[m.gzindex]),m.gzindex++;m.gzhead.hcrc&&m.pending>U&&(d.adler=c(d.adler,m.pending_buf,m.pending-U,U)),m.gzindex===m.gzhead.extra.length&&(m.gzindex=0,m.status=73)}else m.status=73;if(m.status===73)if(m.gzhead.name){U=m.pending;do{if(m.pending===m.pending_buf_size&&(m.gzhead.hcrc&&m.pending>U&&(d.adler=c(d.adler,m.pending_buf,m.pending-U,U)),S(d),U=m.pending,m.pending===m.pending_buf_size)){I=1;break}I=m.gzindex<m.gzhead.name.length?255&m.gzhead.name.charCodeAt(m.gzindex++):0,$(m,I)}while(I!==0);m.gzhead.hcrc&&m.pending>U&&(d.adler=c(d.adler,m.pending_buf,m.pending-U,U)),I===0&&(m.gzindex=0,m.status=91)}else m.status=91;if(m.status===91)if(m.gzhead.comment){U=m.pending;do{if(m.pending===m.pending_buf_size&&(m.gzhead.hcrc&&m.pending>U&&(d.adler=c(d.adler,m.pending_buf,m.pending-U,U)),S(d),U=m.pending,m.pending===m.pending_buf_size)){I=1;break}I=m.gzindex<m.gzhead.comment.length?255&m.gzhead.comment.charCodeAt(m.gzindex++):0,$(m,I)}while(I!==0);m.gzhead.hcrc&&m.pending>U&&(d.adler=c(d.adler,m.pending_buf,m.pending-U,U)),I===0&&(m.status=103)}else m.status=103;if(m.status===103&&(m.gzhead.hcrc?(m.pending+2>m.pending_buf_size&&S(d),m.pending+2<=m.pending_buf_size&&($(m,255&d.adler),$(m,d.adler>>8&255),d.adler=0,m.status=K)):m.status=K),m.pending!==0){if(S(d),d.avail_out===0)return m.last_flush=-1,u}else if(d.avail_in===0&&N(O)<=N(D)&&O!==h)return nA(d,-5);if(m.status===666&&d.avail_in!==0)return nA(d,-5);if(d.avail_in!==0||m.lookahead!==0||O!==B&&m.status!==666){var V=m.strategy===2?function(L,X){for(var q;;){if(L.lookahead===0&&(dA(L),L.lookahead===0)){if(X===B)return Q;break}if(L.match_length=0,q=a._tr_tally(L,0,L.window[L.strstart]),L.lookahead--,L.strstart++,q&&(x(L,!1),L.strm.avail_out===0))return Q}return L.insert=0,X===h?(x(L,!0),L.strm.avail_out===0?AA:M):L.last_lit&&(x(L,!1),L.strm.avail_out===0)?Q:k}(m,O):m.strategy===3?function(L,X){for(var q,W,rA,wA,cA=L.window;;){if(L.lookahead<=R){if(dA(L),L.lookahead<=R&&X===B)return Q;if(L.lookahead===0)break}if(L.match_length=0,L.lookahead>=_&&0<L.strstart&&(W=cA[rA=L.strstart-1])===cA[++rA]&&W===cA[++rA]&&W===cA[++rA]){wA=L.strstart+R;do;while(W===cA[++rA]&&W===cA[++rA]&&W===cA[++rA]&&W===cA[++rA]&&W===cA[++rA]&&W===cA[++rA]&&W===cA[++rA]&&W===cA[++rA]&&rA<wA);L.match_length=R-(wA-rA),L.match_length>L.lookahead&&(L.match_length=L.lookahead)}if(L.match_length>=_?(q=a._tr_tally(L,1,L.match_length-_),L.lookahead-=L.match_length,L.strstart+=L.match_length,L.match_length=0):(q=a._tr_tally(L,0,L.window[L.strstart]),L.lookahead--,L.strstart++),q&&(x(L,!1),L.strm.avail_out===0))return Q}return L.insert=0,X===h?(x(L,!0),L.strm.avail_out===0?AA:M):L.last_lit&&(x(L,!1),L.strm.avail_out===0)?Q:k}(m,O):s[m.level].func(m,O);if(V!==AA&&V!==M||(m.status=666),V===Q||V===AA)return d.avail_out===0&&(m.last_flush=-1),u;if(V===k&&(O===1?a._tr_align(m):O!==5&&(a._tr_stored_block(m,0,0,!1),O===3&&(tA(m.head),m.lookahead===0&&(m.strstart=0,m.block_start=0,m.insert=0))),S(d),d.avail_out===0))return m.last_flush=-1,u}return O!==h?u:m.wrap<=0?1:(m.wrap===2?($(m,255&d.adler),$(m,d.adler>>8&255),$(m,d.adler>>16&255),$(m,d.adler>>24&255),$(m,255&d.total_in),$(m,d.total_in>>8&255),$(m,d.total_in>>16&255),$(m,d.total_in>>24&255)):(Y(m,d.adler>>>16),Y(m,65535&d.adler)),S(d),0<m.wrap&&(m.wrap=-m.wrap),m.pending!==0?u:1)},n.deflateEnd=function(d){var O;return d&&d.state?(O=d.state.status)!==E&&O!==69&&O!==73&&O!==91&&O!==103&&O!==K&&O!==666?nA(d,p):(d.state=null,O===K?nA(d,-3):u):p},n.deflateSetDictionary=function(d,O){var D,m,U,I,G,V,L,X,q=O.length;if(!d||!d.state||(I=(D=d.state).wrap)===2||I===1&&D.status!==E||D.lookahead)return p;for(I===1&&(d.adler=o(d.adler,O,q,0)),D.wrap=0,q>=D.w_size&&(I===0&&(tA(D.head),D.strstart=0,D.block_start=0,D.insert=0),X=new i.Buf8(D.w_size),i.arraySet(X,O,q-D.w_size,D.w_size,0),O=X,q=D.w_size),G=d.avail_in,V=d.next_in,L=d.input,d.avail_in=q,d.next_in=0,d.input=O,dA(D);D.lookahead>=_;){for(m=D.strstart,U=D.lookahead-(_-1);D.ins_h=(D.ins_h<<D.hash_shift^D.window[m+_-1])&D.hash_mask,D.prev[m&D.w_mask]=D.head[D.ins_h],D.head[D.ins_h]=m,m++,--U;);D.strstart=m,D.lookahead=_-1,dA(D)}return D.strstart+=D.lookahead,D.block_start=D.strstart,D.insert=D.lookahead,D.lookahead=0,D.match_length=D.prev_length=_-1,D.match_available=0,d.next_in=V,d.input=L,d.avail_in=G,D.wrap=I,u},n.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(A,t,n){t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(A,t,n){t.exports=function(s,i){var a,o,c,l,B,h,u,p,f,w,g,C,F,y,v,H,b,T,_,R,Z,E,K,Q,k;a=s.state,o=s.next_in,Q=s.input,c=o+(s.avail_in-5),l=s.next_out,k=s.output,B=l-(i-s.avail_out),h=l+(s.avail_out-257),u=a.dmax,p=a.wsize,f=a.whave,w=a.wnext,g=a.window,C=a.hold,F=a.bits,y=a.lencode,v=a.distcode,H=(1<<a.lenbits)-1,b=(1<<a.distbits)-1;A:do{F<15&&(C+=Q[o++]<<F,F+=8,C+=Q[o++]<<F,F+=8),T=y[C&H];e:for(;;){if(C>>>=_=T>>>24,F-=_,(_=T>>>16&255)===0)k[l++]=65535&T;else{if(!(16&_)){if(!(64&_)){T=y[(65535&T)+(C&(1<<_)-1)];continue e}if(32&_){a.mode=12;break A}s.msg="invalid literal/length code",a.mode=30;break A}R=65535&T,(_&=15)&&(F<_&&(C+=Q[o++]<<F,F+=8),R+=C&(1<<_)-1,C>>>=_,F-=_),F<15&&(C+=Q[o++]<<F,F+=8,C+=Q[o++]<<F,F+=8),T=v[C&b];t:for(;;){if(C>>>=_=T>>>24,F-=_,!(16&(_=T>>>16&255))){if(!(64&_)){T=v[(65535&T)+(C&(1<<_)-1)];continue t}s.msg="invalid distance code",a.mode=30;break A}if(Z=65535&T,F<(_&=15)&&(C+=Q[o++]<<F,(F+=8)<_&&(C+=Q[o++]<<F,F+=8)),u<(Z+=C&(1<<_)-1)){s.msg="invalid distance too far back",a.mode=30;break A}if(C>>>=_,F-=_,(_=l-B)<Z){if(f<(_=Z-_)&&a.sane){s.msg="invalid distance too far back",a.mode=30;break A}if(K=g,(E=0)===w){if(E+=p-_,_<R){for(R-=_;k[l++]=g[E++],--_;);E=l-Z,K=k}}else if(w<_){if(E+=p+w-_,(_-=w)<R){for(R-=_;k[l++]=g[E++],--_;);if(E=0,w<R){for(R-=_=w;k[l++]=g[E++],--_;);E=l-Z,K=k}}}else if(E+=w-_,_<R){for(R-=_;k[l++]=g[E++],--_;);E=l-Z,K=k}for(;2<R;)k[l++]=K[E++],k[l++]=K[E++],k[l++]=K[E++],R-=3;R&&(k[l++]=K[E++],1<R&&(k[l++]=K[E++]))}else{for(E=l-Z;k[l++]=k[E++],k[l++]=k[E++],k[l++]=k[E++],2<(R-=3););R&&(k[l++]=k[E++],1<R&&(k[l++]=k[E++]))}break}}break}}while(o<c&&l<h);o-=R=F>>3,C&=(1<<(F-=R<<3))-1,s.next_in=o,s.next_out=l,s.avail_in=o<c?c-o+5:5-(o-c),s.avail_out=l<h?h-l+257:257-(l-h),a.hold=C,a.bits=F}},{}],49:[function(A,t,n){var s=A("../utils/common"),i=A("./adler32"),a=A("./crc32"),o=A("./inffast"),c=A("./inftrees"),l=1,B=2,h=0,u=-2,p=1,f=852,w=592;function g(E){return(E>>>24&255)+(E>>>8&65280)+((65280&E)<<8)+((255&E)<<24)}function C(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new s.Buf16(320),this.work=new s.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function F(E){var K;return E&&E.state?(K=E.state,E.total_in=E.total_out=K.total=0,E.msg="",K.wrap&&(E.adler=1&K.wrap),K.mode=p,K.last=0,K.havedict=0,K.dmax=32768,K.head=null,K.hold=0,K.bits=0,K.lencode=K.lendyn=new s.Buf32(f),K.distcode=K.distdyn=new s.Buf32(w),K.sane=1,K.back=-1,h):u}function y(E){var K;return E&&E.state?((K=E.state).wsize=0,K.whave=0,K.wnext=0,F(E)):u}function v(E,K){var Q,k;return E&&E.state?(k=E.state,K<0?(Q=0,K=-K):(Q=1+(K>>4),K<48&&(K&=15)),K&&(K<8||15<K)?u:(k.window!==null&&k.wbits!==K&&(k.window=null),k.wrap=Q,k.wbits=K,y(E))):u}function H(E,K){var Q,k;return E?(k=new C,(E.state=k).window=null,(Q=v(E,K))!==h&&(E.state=null),Q):u}var b,T,_=!0;function R(E){if(_){var K;for(b=new s.Buf32(512),T=new s.Buf32(32),K=0;K<144;)E.lens[K++]=8;for(;K<256;)E.lens[K++]=9;for(;K<280;)E.lens[K++]=7;for(;K<288;)E.lens[K++]=8;for(c(l,E.lens,0,288,b,0,E.work,{bits:9}),K=0;K<32;)E.lens[K++]=5;c(B,E.lens,0,32,T,0,E.work,{bits:5}),_=!1}E.lencode=b,E.lenbits=9,E.distcode=T,E.distbits=5}function Z(E,K,Q,k){var AA,M=E.state;return M.window===null&&(M.wsize=1<<M.wbits,M.wnext=0,M.whave=0,M.window=new s.Buf8(M.wsize)),k>=M.wsize?(s.arraySet(M.window,K,Q-M.wsize,M.wsize,0),M.wnext=0,M.whave=M.wsize):(k<(AA=M.wsize-M.wnext)&&(AA=k),s.arraySet(M.window,K,Q-k,AA,M.wnext),(k-=AA)?(s.arraySet(M.window,K,Q-k,k,0),M.wnext=k,M.whave=M.wsize):(M.wnext+=AA,M.wnext===M.wsize&&(M.wnext=0),M.whave<M.wsize&&(M.whave+=AA))),0}n.inflateReset=y,n.inflateReset2=v,n.inflateResetKeep=F,n.inflateInit=function(E){return H(E,15)},n.inflateInit2=H,n.inflate=function(E,K){var Q,k,AA,M,nA,N,tA,S,x,$,Y,P,dA,CA,BA,uA,IA,UA,ZA,XA,d,O,D,m,U=0,I=new s.Buf8(4),G=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!E||!E.state||!E.output||!E.input&&E.avail_in!==0)return u;(Q=E.state).mode===12&&(Q.mode=13),nA=E.next_out,AA=E.output,tA=E.avail_out,M=E.next_in,k=E.input,N=E.avail_in,S=Q.hold,x=Q.bits,$=N,Y=tA,O=h;A:for(;;)switch(Q.mode){case p:if(Q.wrap===0){Q.mode=13;break}for(;x<16;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(2&Q.wrap&&S===35615){I[Q.check=0]=255&S,I[1]=S>>>8&255,Q.check=a(Q.check,I,2,0),x=S=0,Q.mode=2;break}if(Q.flags=0,Q.head&&(Q.head.done=!1),!(1&Q.wrap)||(((255&S)<<8)+(S>>8))%31){E.msg="incorrect header check",Q.mode=30;break}if((15&S)!=8){E.msg="unknown compression method",Q.mode=30;break}if(x-=4,d=8+(15&(S>>>=4)),Q.wbits===0)Q.wbits=d;else if(d>Q.wbits){E.msg="invalid window size",Q.mode=30;break}Q.dmax=1<<d,E.adler=Q.check=1,Q.mode=512&S?10:12,x=S=0;break;case 2:for(;x<16;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(Q.flags=S,(255&Q.flags)!=8){E.msg="unknown compression method",Q.mode=30;break}if(57344&Q.flags){E.msg="unknown header flags set",Q.mode=30;break}Q.head&&(Q.head.text=S>>8&1),512&Q.flags&&(I[0]=255&S,I[1]=S>>>8&255,Q.check=a(Q.check,I,2,0)),x=S=0,Q.mode=3;case 3:for(;x<32;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}Q.head&&(Q.head.time=S),512&Q.flags&&(I[0]=255&S,I[1]=S>>>8&255,I[2]=S>>>16&255,I[3]=S>>>24&255,Q.check=a(Q.check,I,4,0)),x=S=0,Q.mode=4;case 4:for(;x<16;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}Q.head&&(Q.head.xflags=255&S,Q.head.os=S>>8),512&Q.flags&&(I[0]=255&S,I[1]=S>>>8&255,Q.check=a(Q.check,I,2,0)),x=S=0,Q.mode=5;case 5:if(1024&Q.flags){for(;x<16;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}Q.length=S,Q.head&&(Q.head.extra_len=S),512&Q.flags&&(I[0]=255&S,I[1]=S>>>8&255,Q.check=a(Q.check,I,2,0)),x=S=0}else Q.head&&(Q.head.extra=null);Q.mode=6;case 6:if(1024&Q.flags&&(N<(P=Q.length)&&(P=N),P&&(Q.head&&(d=Q.head.extra_len-Q.length,Q.head.extra||(Q.head.extra=new Array(Q.head.extra_len)),s.arraySet(Q.head.extra,k,M,P,d)),512&Q.flags&&(Q.check=a(Q.check,k,P,M)),N-=P,M+=P,Q.length-=P),Q.length))break A;Q.length=0,Q.mode=7;case 7:if(2048&Q.flags){if(N===0)break A;for(P=0;d=k[M+P++],Q.head&&d&&Q.length<65536&&(Q.head.name+=String.fromCharCode(d)),d&&P<N;);if(512&Q.flags&&(Q.check=a(Q.check,k,P,M)),N-=P,M+=P,d)break A}else Q.head&&(Q.head.name=null);Q.length=0,Q.mode=8;case 8:if(4096&Q.flags){if(N===0)break A;for(P=0;d=k[M+P++],Q.head&&d&&Q.length<65536&&(Q.head.comment+=String.fromCharCode(d)),d&&P<N;);if(512&Q.flags&&(Q.check=a(Q.check,k,P,M)),N-=P,M+=P,d)break A}else Q.head&&(Q.head.comment=null);Q.mode=9;case 9:if(512&Q.flags){for(;x<16;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(S!==(65535&Q.check)){E.msg="header crc mismatch",Q.mode=30;break}x=S=0}Q.head&&(Q.head.hcrc=Q.flags>>9&1,Q.head.done=!0),E.adler=Q.check=0,Q.mode=12;break;case 10:for(;x<32;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}E.adler=Q.check=g(S),x=S=0,Q.mode=11;case 11:if(Q.havedict===0)return E.next_out=nA,E.avail_out=tA,E.next_in=M,E.avail_in=N,Q.hold=S,Q.bits=x,2;E.adler=Q.check=1,Q.mode=12;case 12:if(K===5||K===6)break A;case 13:if(Q.last){S>>>=7&x,x-=7&x,Q.mode=27;break}for(;x<3;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}switch(Q.last=1&S,x-=1,3&(S>>>=1)){case 0:Q.mode=14;break;case 1:if(R(Q),Q.mode=20,K!==6)break;S>>>=2,x-=2;break A;case 2:Q.mode=17;break;case 3:E.msg="invalid block type",Q.mode=30}S>>>=2,x-=2;break;case 14:for(S>>>=7&x,x-=7&x;x<32;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if((65535&S)!=(S>>>16^65535)){E.msg="invalid stored block lengths",Q.mode=30;break}if(Q.length=65535&S,x=S=0,Q.mode=15,K===6)break A;case 15:Q.mode=16;case 16:if(P=Q.length){if(N<P&&(P=N),tA<P&&(P=tA),P===0)break A;s.arraySet(AA,k,M,P,nA),N-=P,M+=P,tA-=P,nA+=P,Q.length-=P;break}Q.mode=12;break;case 17:for(;x<14;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(Q.nlen=257+(31&S),S>>>=5,x-=5,Q.ndist=1+(31&S),S>>>=5,x-=5,Q.ncode=4+(15&S),S>>>=4,x-=4,286<Q.nlen||30<Q.ndist){E.msg="too many length or distance symbols",Q.mode=30;break}Q.have=0,Q.mode=18;case 18:for(;Q.have<Q.ncode;){for(;x<3;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}Q.lens[G[Q.have++]]=7&S,S>>>=3,x-=3}for(;Q.have<19;)Q.lens[G[Q.have++]]=0;if(Q.lencode=Q.lendyn,Q.lenbits=7,D={bits:Q.lenbits},O=c(0,Q.lens,0,19,Q.lencode,0,Q.work,D),Q.lenbits=D.bits,O){E.msg="invalid code lengths set",Q.mode=30;break}Q.have=0,Q.mode=19;case 19:for(;Q.have<Q.nlen+Q.ndist;){for(;uA=(U=Q.lencode[S&(1<<Q.lenbits)-1])>>>16&255,IA=65535&U,!((BA=U>>>24)<=x);){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(IA<16)S>>>=BA,x-=BA,Q.lens[Q.have++]=IA;else{if(IA===16){for(m=BA+2;x<m;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(S>>>=BA,x-=BA,Q.have===0){E.msg="invalid bit length repeat",Q.mode=30;break}d=Q.lens[Q.have-1],P=3+(3&S),S>>>=2,x-=2}else if(IA===17){for(m=BA+3;x<m;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}x-=BA,d=0,P=3+(7&(S>>>=BA)),S>>>=3,x-=3}else{for(m=BA+7;x<m;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}x-=BA,d=0,P=11+(127&(S>>>=BA)),S>>>=7,x-=7}if(Q.have+P>Q.nlen+Q.ndist){E.msg="invalid bit length repeat",Q.mode=30;break}for(;P--;)Q.lens[Q.have++]=d}}if(Q.mode===30)break;if(Q.lens[256]===0){E.msg="invalid code -- missing end-of-block",Q.mode=30;break}if(Q.lenbits=9,D={bits:Q.lenbits},O=c(l,Q.lens,0,Q.nlen,Q.lencode,0,Q.work,D),Q.lenbits=D.bits,O){E.msg="invalid literal/lengths set",Q.mode=30;break}if(Q.distbits=6,Q.distcode=Q.distdyn,D={bits:Q.distbits},O=c(B,Q.lens,Q.nlen,Q.ndist,Q.distcode,0,Q.work,D),Q.distbits=D.bits,O){E.msg="invalid distances set",Q.mode=30;break}if(Q.mode=20,K===6)break A;case 20:Q.mode=21;case 21:if(6<=N&&258<=tA){E.next_out=nA,E.avail_out=tA,E.next_in=M,E.avail_in=N,Q.hold=S,Q.bits=x,o(E,Y),nA=E.next_out,AA=E.output,tA=E.avail_out,M=E.next_in,k=E.input,N=E.avail_in,S=Q.hold,x=Q.bits,Q.mode===12&&(Q.back=-1);break}for(Q.back=0;uA=(U=Q.lencode[S&(1<<Q.lenbits)-1])>>>16&255,IA=65535&U,!((BA=U>>>24)<=x);){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(uA&&!(240&uA)){for(UA=BA,ZA=uA,XA=IA;uA=(U=Q.lencode[XA+((S&(1<<UA+ZA)-1)>>UA)])>>>16&255,IA=65535&U,!(UA+(BA=U>>>24)<=x);){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}S>>>=UA,x-=UA,Q.back+=UA}if(S>>>=BA,x-=BA,Q.back+=BA,Q.length=IA,uA===0){Q.mode=26;break}if(32&uA){Q.back=-1,Q.mode=12;break}if(64&uA){E.msg="invalid literal/length code",Q.mode=30;break}Q.extra=15&uA,Q.mode=22;case 22:if(Q.extra){for(m=Q.extra;x<m;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}Q.length+=S&(1<<Q.extra)-1,S>>>=Q.extra,x-=Q.extra,Q.back+=Q.extra}Q.was=Q.length,Q.mode=23;case 23:for(;uA=(U=Q.distcode[S&(1<<Q.distbits)-1])>>>16&255,IA=65535&U,!((BA=U>>>24)<=x);){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(!(240&uA)){for(UA=BA,ZA=uA,XA=IA;uA=(U=Q.distcode[XA+((S&(1<<UA+ZA)-1)>>UA)])>>>16&255,IA=65535&U,!(UA+(BA=U>>>24)<=x);){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}S>>>=UA,x-=UA,Q.back+=UA}if(S>>>=BA,x-=BA,Q.back+=BA,64&uA){E.msg="invalid distance code",Q.mode=30;break}Q.offset=IA,Q.extra=15&uA,Q.mode=24;case 24:if(Q.extra){for(m=Q.extra;x<m;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}Q.offset+=S&(1<<Q.extra)-1,S>>>=Q.extra,x-=Q.extra,Q.back+=Q.extra}if(Q.offset>Q.dmax){E.msg="invalid distance too far back",Q.mode=30;break}Q.mode=25;case 25:if(tA===0)break A;if(P=Y-tA,Q.offset>P){if((P=Q.offset-P)>Q.whave&&Q.sane){E.msg="invalid distance too far back",Q.mode=30;break}dA=P>Q.wnext?(P-=Q.wnext,Q.wsize-P):Q.wnext-P,P>Q.length&&(P=Q.length),CA=Q.window}else CA=AA,dA=nA-Q.offset,P=Q.length;for(tA<P&&(P=tA),tA-=P,Q.length-=P;AA[nA++]=CA[dA++],--P;);Q.length===0&&(Q.mode=21);break;case 26:if(tA===0)break A;AA[nA++]=Q.length,tA--,Q.mode=21;break;case 27:if(Q.wrap){for(;x<32;){if(N===0)break A;N--,S|=k[M++]<<x,x+=8}if(Y-=tA,E.total_out+=Y,Q.total+=Y,Y&&(E.adler=Q.check=Q.flags?a(Q.check,AA,Y,nA-Y):i(Q.check,AA,Y,nA-Y)),Y=tA,(Q.flags?S:g(S))!==Q.check){E.msg="incorrect data check",Q.mode=30;break}x=S=0}Q.mode=28;case 28:if(Q.wrap&&Q.flags){for(;x<32;){if(N===0)break A;N--,S+=k[M++]<<x,x+=8}if(S!==(4294967295&Q.total)){E.msg="incorrect length check",Q.mode=30;break}x=S=0}Q.mode=29;case 29:O=1;break A;case 30:O=-3;break A;case 31:return-4;case 32:default:return u}return E.next_out=nA,E.avail_out=tA,E.next_in=M,E.avail_in=N,Q.hold=S,Q.bits=x,(Q.wsize||Y!==E.avail_out&&Q.mode<30&&(Q.mode<27||K!==4))&&Z(E,E.output,E.next_out,Y-E.avail_out)?(Q.mode=31,-4):($-=E.avail_in,Y-=E.avail_out,E.total_in+=$,E.total_out+=Y,Q.total+=Y,Q.wrap&&Y&&(E.adler=Q.check=Q.flags?a(Q.check,AA,Y,E.next_out-Y):i(Q.check,AA,Y,E.next_out-Y)),E.data_type=Q.bits+(Q.last?64:0)+(Q.mode===12?128:0)+(Q.mode===20||Q.mode===15?256:0),($==0&&Y===0||K===4)&&O===h&&(O=-5),O)},n.inflateEnd=function(E){if(!E||!E.state)return u;var K=E.state;return K.window&&(K.window=null),E.state=null,h},n.inflateGetHeader=function(E,K){var Q;return E&&E.state&&2&(Q=E.state).wrap?((Q.head=K).done=!1,h):u},n.inflateSetDictionary=function(E,K){var Q,k=K.length;return E&&E.state?(Q=E.state).wrap!==0&&Q.mode!==11?u:Q.mode===11&&i(1,K,k,0)!==Q.check?-3:Z(E,K,k,k)?(Q.mode=31,-4):(Q.havedict=1,h):u},n.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(A,t,n){var s=A("../utils/common"),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],a=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],o=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],c=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(l,B,h,u,p,f,w,g){var C,F,y,v,H,b,T,_,R,Z=g.bits,E=0,K=0,Q=0,k=0,AA=0,M=0,nA=0,N=0,tA=0,S=0,x=null,$=0,Y=new s.Buf16(16),P=new s.Buf16(16),dA=null,CA=0;for(E=0;E<=15;E++)Y[E]=0;for(K=0;K<u;K++)Y[B[h+K]]++;for(AA=Z,k=15;1<=k&&Y[k]===0;k--);if(k<AA&&(AA=k),k===0)return p[f++]=20971520,p[f++]=20971520,g.bits=1,0;for(Q=1;Q<k&&Y[Q]===0;Q++);for(AA<Q&&(AA=Q),E=N=1;E<=15;E++)if(N<<=1,(N-=Y[E])<0)return-1;if(0<N&&(l===0||k!==1))return-1;for(P[1]=0,E=1;E<15;E++)P[E+1]=P[E]+Y[E];for(K=0;K<u;K++)B[h+K]!==0&&(w[P[B[h+K]]++]=K);if(b=l===0?(x=dA=w,19):l===1?(x=i,$-=257,dA=a,CA-=257,256):(x=o,dA=c,-1),E=Q,H=f,nA=K=S=0,y=-1,v=(tA=1<<(M=AA))-1,l===1&&852<tA||l===2&&592<tA)return 1;for(;;){for(T=E-nA,R=w[K]<b?(_=0,w[K]):w[K]>b?(_=dA[CA+w[K]],x[$+w[K]]):(_=96,0),C=1<<E-nA,Q=F=1<<M;p[H+(S>>nA)+(F-=C)]=T<<24|_<<16|R|0,F!==0;);for(C=1<<E-1;S&C;)C>>=1;if(C!==0?(S&=C-1,S+=C):S=0,K++,--Y[E]==0){if(E===k)break;E=B[h+w[K]]}if(AA<E&&(S&v)!==y){for(nA===0&&(nA=AA),H+=Q,N=1<<(M=E-nA);M+nA<k&&!((N-=Y[M+nA])<=0);)M++,N<<=1;if(tA+=1<<M,l===1&&852<tA||l===2&&592<tA)return 1;p[y=S&v]=AA<<24|M<<16|H-f|0}}return S!==0&&(p[H+S]=E-nA<<24|64<<16|0),g.bits=AA,0}},{"../utils/common":41}],51:[function(A,t,n){t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(A,t,n){var s=A("../utils/common"),i=0,a=1;function o(U){for(var I=U.length;0<=--I;)U[I]=0}var c=0,l=29,B=256,h=B+1+l,u=30,p=19,f=2*h+1,w=15,g=16,C=7,F=256,y=16,v=17,H=18,b=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],T=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],_=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],R=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Z=new Array(2*(h+2));o(Z);var E=new Array(2*u);o(E);var K=new Array(512);o(K);var Q=new Array(256);o(Q);var k=new Array(l);o(k);var AA,M,nA,N=new Array(u);function tA(U,I,G,V,L){this.static_tree=U,this.extra_bits=I,this.extra_base=G,this.elems=V,this.max_length=L,this.has_stree=U&&U.length}function S(U,I){this.dyn_tree=U,this.max_code=0,this.stat_desc=I}function x(U){return U<256?K[U]:K[256+(U>>>7)]}function $(U,I){U.pending_buf[U.pending++]=255&I,U.pending_buf[U.pending++]=I>>>8&255}function Y(U,I,G){U.bi_valid>g-G?(U.bi_buf|=I<<U.bi_valid&65535,$(U,U.bi_buf),U.bi_buf=I>>g-U.bi_valid,U.bi_valid+=G-g):(U.bi_buf|=I<<U.bi_valid&65535,U.bi_valid+=G)}function P(U,I,G){Y(U,G[2*I],G[2*I+1])}function dA(U,I){for(var G=0;G|=1&U,U>>>=1,G<<=1,0<--I;);return G>>>1}function CA(U,I,G){var V,L,X=new Array(w+1),q=0;for(V=1;V<=w;V++)X[V]=q=q+G[V-1]<<1;for(L=0;L<=I;L++){var W=U[2*L+1];W!==0&&(U[2*L]=dA(X[W]++,W))}}function BA(U){var I;for(I=0;I<h;I++)U.dyn_ltree[2*I]=0;for(I=0;I<u;I++)U.dyn_dtree[2*I]=0;for(I=0;I<p;I++)U.bl_tree[2*I]=0;U.dyn_ltree[2*F]=1,U.opt_len=U.static_len=0,U.last_lit=U.matches=0}function uA(U){8<U.bi_valid?$(U,U.bi_buf):0<U.bi_valid&&(U.pending_buf[U.pending++]=U.bi_buf),U.bi_buf=0,U.bi_valid=0}function IA(U,I,G,V){var L=2*I,X=2*G;return U[L]<U[X]||U[L]===U[X]&&V[I]<=V[G]}function UA(U,I,G){for(var V=U.heap[G],L=G<<1;L<=U.heap_len&&(L<U.heap_len&&IA(I,U.heap[L+1],U.heap[L],U.depth)&&L++,!IA(I,V,U.heap[L],U.depth));)U.heap[G]=U.heap[L],G=L,L<<=1;U.heap[G]=V}function ZA(U,I,G){var V,L,X,q,W=0;if(U.last_lit!==0)for(;V=U.pending_buf[U.d_buf+2*W]<<8|U.pending_buf[U.d_buf+2*W+1],L=U.pending_buf[U.l_buf+W],W++,V===0?P(U,L,I):(P(U,(X=Q[L])+B+1,I),(q=b[X])!==0&&Y(U,L-=k[X],q),P(U,X=x(--V),G),(q=T[X])!==0&&Y(U,V-=N[X],q)),W<U.last_lit;);P(U,F,I)}function XA(U,I){var G,V,L,X=I.dyn_tree,q=I.stat_desc.static_tree,W=I.stat_desc.has_stree,rA=I.stat_desc.elems,wA=-1;for(U.heap_len=0,U.heap_max=f,G=0;G<rA;G++)X[2*G]!==0?(U.heap[++U.heap_len]=wA=G,U.depth[G]=0):X[2*G+1]=0;for(;U.heap_len<2;)X[2*(L=U.heap[++U.heap_len]=wA<2?++wA:0)]=1,U.depth[L]=0,U.opt_len--,W&&(U.static_len-=q[2*L+1]);for(I.max_code=wA,G=U.heap_len>>1;1<=G;G--)UA(U,X,G);for(L=rA;G=U.heap[1],U.heap[1]=U.heap[U.heap_len--],UA(U,X,1),V=U.heap[1],U.heap[--U.heap_max]=G,U.heap[--U.heap_max]=V,X[2*L]=X[2*G]+X[2*V],U.depth[L]=(U.depth[G]>=U.depth[V]?U.depth[G]:U.depth[V])+1,X[2*G+1]=X[2*V+1]=L,U.heap[1]=L++,UA(U,X,1),2<=U.heap_len;);U.heap[--U.heap_max]=U.heap[1],function(cA,MA){var Oe,jA,Re,FA,lt,Br,te=MA.dyn_tree,fn=MA.max_code,bi=MA.stat_desc.static_tree,xi=MA.stat_desc.has_stree,Li=MA.stat_desc.extra_bits,hn=MA.stat_desc.extra_base,Me=MA.stat_desc.max_length,ut=0;for(FA=0;FA<=w;FA++)cA.bl_count[FA]=0;for(te[2*cA.heap[cA.heap_max]+1]=0,Oe=cA.heap_max+1;Oe<f;Oe++)Me<(FA=te[2*te[2*(jA=cA.heap[Oe])+1]+1]+1)&&(FA=Me,ut++),te[2*jA+1]=FA,fn<jA||(cA.bl_count[FA]++,lt=0,hn<=jA&&(lt=Li[jA-hn]),Br=te[2*jA],cA.opt_len+=Br*(FA+lt),xi&&(cA.static_len+=Br*(bi[2*jA+1]+lt)));if(ut!==0){do{for(FA=Me-1;cA.bl_count[FA]===0;)FA--;cA.bl_count[FA]--,cA.bl_count[FA+1]+=2,cA.bl_count[Me]--,ut-=2}while(0<ut);for(FA=Me;FA!==0;FA--)for(jA=cA.bl_count[FA];jA!==0;)fn<(Re=cA.heap[--Oe])||(te[2*Re+1]!==FA&&(cA.opt_len+=(FA-te[2*Re+1])*te[2*Re],te[2*Re+1]=FA),jA--)}}(U,I),CA(X,wA,U.bl_count)}function d(U,I,G){var V,L,X=-1,q=I[1],W=0,rA=7,wA=4;for(q===0&&(rA=138,wA=3),I[2*(G+1)+1]=65535,V=0;V<=G;V++)L=q,q=I[2*(V+1)+1],++W<rA&&L===q||(W<wA?U.bl_tree[2*L]+=W:L!==0?(L!==X&&U.bl_tree[2*L]++,U.bl_tree[2*y]++):W<=10?U.bl_tree[2*v]++:U.bl_tree[2*H]++,X=L,wA=(W=0)===q?(rA=138,3):L===q?(rA=6,3):(rA=7,4))}function O(U,I,G){var V,L,X=-1,q=I[1],W=0,rA=7,wA=4;for(q===0&&(rA=138,wA=3),V=0;V<=G;V++)if(L=q,q=I[2*(V+1)+1],!(++W<rA&&L===q)){if(W<wA)for(;P(U,L,U.bl_tree),--W!=0;);else L!==0?(L!==X&&(P(U,L,U.bl_tree),W--),P(U,y,U.bl_tree),Y(U,W-3,2)):W<=10?(P(U,v,U.bl_tree),Y(U,W-3,3)):(P(U,H,U.bl_tree),Y(U,W-11,7));X=L,wA=(W=0)===q?(rA=138,3):L===q?(rA=6,3):(rA=7,4)}}o(N);var D=!1;function m(U,I,G,V){Y(U,(c<<1)+(V?1:0),3),function(L,X,q,W){uA(L),W&&($(L,q),$(L,~q)),s.arraySet(L.pending_buf,L.window,X,q,L.pending),L.pending+=q}(U,I,G,!0)}n._tr_init=function(U){D||(function(){var I,G,V,L,X,q=new Array(w+1);for(L=V=0;L<l-1;L++)for(k[L]=V,I=0;I<1<<b[L];I++)Q[V++]=L;for(Q[V-1]=L,L=X=0;L<16;L++)for(N[L]=X,I=0;I<1<<T[L];I++)K[X++]=L;for(X>>=7;L<u;L++)for(N[L]=X<<7,I=0;I<1<<T[L]-7;I++)K[256+X++]=L;for(G=0;G<=w;G++)q[G]=0;for(I=0;I<=143;)Z[2*I+1]=8,I++,q[8]++;for(;I<=255;)Z[2*I+1]=9,I++,q[9]++;for(;I<=279;)Z[2*I+1]=7,I++,q[7]++;for(;I<=287;)Z[2*I+1]=8,I++,q[8]++;for(CA(Z,h+1,q),I=0;I<u;I++)E[2*I+1]=5,E[2*I]=dA(I,5);AA=new tA(Z,b,B+1,h,w),M=new tA(E,T,0,u,w),nA=new tA(new Array(0),_,0,p,C)}(),D=!0),U.l_desc=new S(U.dyn_ltree,AA),U.d_desc=new S(U.dyn_dtree,M),U.bl_desc=new S(U.bl_tree,nA),U.bi_buf=0,U.bi_valid=0,BA(U)},n._tr_stored_block=m,n._tr_flush_block=function(U,I,G,V){var L,X,q=0;0<U.level?(U.strm.data_type===2&&(U.strm.data_type=function(W){var rA,wA=4093624447;for(rA=0;rA<=31;rA++,wA>>>=1)if(1&wA&&W.dyn_ltree[2*rA]!==0)return i;if(W.dyn_ltree[18]!==0||W.dyn_ltree[20]!==0||W.dyn_ltree[26]!==0)return a;for(rA=32;rA<B;rA++)if(W.dyn_ltree[2*rA]!==0)return a;return i}(U)),XA(U,U.l_desc),XA(U,U.d_desc),q=function(W){var rA;for(d(W,W.dyn_ltree,W.l_desc.max_code),d(W,W.dyn_dtree,W.d_desc.max_code),XA(W,W.bl_desc),rA=p-1;3<=rA&&W.bl_tree[2*R[rA]+1]===0;rA--);return W.opt_len+=3*(rA+1)+5+5+4,rA}(U),L=U.opt_len+3+7>>>3,(X=U.static_len+3+7>>>3)<=L&&(L=X)):L=X=G+5,G+4<=L&&I!==-1?m(U,I,G,V):U.strategy===4||X===L?(Y(U,2+(V?1:0),3),ZA(U,Z,E)):(Y(U,4+(V?1:0),3),function(W,rA,wA,cA){var MA;for(Y(W,rA-257,5),Y(W,wA-1,5),Y(W,cA-4,4),MA=0;MA<cA;MA++)Y(W,W.bl_tree[2*R[MA]+1],3);O(W,W.dyn_ltree,rA-1),O(W,W.dyn_dtree,wA-1)}(U,U.l_desc.max_code+1,U.d_desc.max_code+1,q+1),ZA(U,U.dyn_ltree,U.dyn_dtree)),BA(U),V&&uA(U)},n._tr_tally=function(U,I,G){return U.pending_buf[U.d_buf+2*U.last_lit]=I>>>8&255,U.pending_buf[U.d_buf+2*U.last_lit+1]=255&I,U.pending_buf[U.l_buf+U.last_lit]=255&G,U.last_lit++,I===0?U.dyn_ltree[2*G]++:(U.matches++,I--,U.dyn_ltree[2*(Q[G]+B+1)]++,U.dyn_dtree[2*x(I)]++),U.last_lit===U.lit_bufsize-1},n._tr_align=function(U){Y(U,2,3),P(U,F,Z),function(I){I.bi_valid===16?($(I,I.bi_buf),I.bi_buf=0,I.bi_valid=0):8<=I.bi_valid&&(I.pending_buf[I.pending++]=255&I.bi_buf,I.bi_buf>>=8,I.bi_valid-=8)}(U)}},{"../utils/common":41}],53:[function(A,t,n){t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(A,t,n){(function(s){(function(i,a){if(!i.setImmediate){var o,c,l,B,h=1,u={},p=!1,f=i.document,w=Object.getPrototypeOf&&Object.getPrototypeOf(i);w=w&&w.setTimeout?w:i,o={}.toString.call(i.process)==="[object process]"?function(y){process.nextTick(function(){C(y)})}:function(){if(i.postMessage&&!i.importScripts){var y=!0,v=i.onmessage;return i.onmessage=function(){y=!1},i.postMessage("","*"),i.onmessage=v,y}}()?(B="setImmediate$"+Math.random()+"$",i.addEventListener?i.addEventListener("message",F,!1):i.attachEvent("onmessage",F),function(y){i.postMessage(B+y,"*")}):i.MessageChannel?((l=new MessageChannel).port1.onmessage=function(y){C(y.data)},function(y){l.port2.postMessage(y)}):f&&"onreadystatechange"in f.createElement("script")?(c=f.documentElement,function(y){var v=f.createElement("script");v.onreadystatechange=function(){C(y),v.onreadystatechange=null,c.removeChild(v),v=null},c.appendChild(v)}):function(y){setTimeout(C,0,y)},w.setImmediate=function(y){typeof y!="function"&&(y=new Function(""+y));for(var v=new Array(arguments.length-1),H=0;H<v.length;H++)v[H]=arguments[H+1];var b={callback:y,args:v};return u[h]=b,o(h),h++},w.clearImmediate=g}function g(y){delete u[y]}function C(y){if(p)setTimeout(C,0,y);else{var v=u[y];if(v){p=!0;try{(function(H){var b=H.callback,T=H.args;switch(T.length){case 0:b();break;case 1:b(T[0]);break;case 2:b(T[0],T[1]);break;case 3:b(T[0],T[1],T[2]);break;default:b.apply(a,T)}})(v)}finally{g(y),p=!1}}}}function F(y){y.source===i&&typeof y.data=="string"&&y.data.indexOf(B)===0&&C(+y.data.slice(B.length))}})(typeof self>"u"?s===void 0?this:s:self)}).call(this,typeof se<"u"?se:typeof self<"u"?self:typeof window<"u"?window:{})},{}]},{},[10])(10)})})(_i);var Yf=_i.exports;const zf=tn(Yf),Zf=J("p",null,"1、通过网页生成的视频会有模糊、重影的问题无法规避，所以就在考量在线生成视频的必要性，最终决定生成出带有编号的图片序列，用户下载压缩包后自行通过视频剪辑软件剪辑。",-1),jf=J("p",null,"2、当然你也可以右键保存单张图片！",-1),qf={id:"videoBox"},$f={key:0,class:"default-loading"},Ah=1e3,eh={__name:"GeneVideo",setup(r){const{useChatStore:e}=JA(),A=DA(!1),t=DA(""),n=new zf,s=async()=>{A.value=!0;let c=ps.cloneDeep(e.chatList);xr(()=>{document.getElementById("videoBox").innerHTML=""});let l=[];e.chatList=[],l.push(i("chat-0"));for(let B=0;B<c.length;B++){await Cs(200),e.chatList.push(c[B]),en.emit("sentChat");const h=e.generateConfig.maxInterval,u=e.generateConfig.minInterval;l.push(i(c[B].id,c[B].intervalTime||Math.floor(Math.random()*(h-u+1))+u))}Promise.all(l).then(B=>{B&&n.generateAsync({type:"blob"}).then(h=>{t.value=URL.createObjectURL(h)})})},i=(c,l)=>new Promise(async(B,h)=>{let u=l||Ah,p=document.querySelector(".phone-wrap");setTimeout(()=>{un(p).then(f=>{let w=new Image;w.src=f.toDataURL(),w.id=`${c}-${u}`,w.className="imgPiece",xr(()=>{document.getElementById("videoBox").appendChild(w)}),f.toBlob(g=>{n.file(`${c}-${u}.png`,g),B(document.getElementById(`${c}-${u}`))},"image/png")}).catch(function(f){h(f)})},0)}),a=()=>{A.value=!1,t.value=""},o=()=>{const c=document.createElement("a");c.href=t.value,c.download=`微信聊天视频 - ${nt().format("YYYYMMDDHHmmss")}.zip`,c.target="_blank",c.rel="noopener noreferrer",document.body.appendChild(c),c.click(),document.body.removeChild(c)};return(c,l)=>{const B=lA("a-tooltip"),h=lA("a-button"),u=lA("a-space"),p=lA("a-spin"),f=lA("a-drawer");return sA(),iA(Te,null,[eA(B,{title:"生成视频前可以修改配置",placement:"right"},{default:aA(()=>[J("div",{class:"wtc-button",onClick:s},"生成视频")]),_:1}),eA(f,{width:500,title:"生成视频",placement:"right",closable:!1,destroyOnClose:!0,open:A.value,onClose:a},{extra:aA(()=>[eA(u,null,{default:aA(()=>[eA(h,{type:"primary",disabled:!t.value,onClick:o},{default:aA(()=>[RA("下载Zip")]),_:1},8,["disabled"]),eA(h,{danger:"",type:"link",shape:"circle",icon:st(hA(rn)),disabled:!c.generated,onClick:a},null,8,["icon","disabled"])]),_:1})]),default:aA(()=>[Zf,jf,ws(J("div",qf,null,512),[[Qs,t.value]]),t.value?$A("",!0):(sA(),iA("div",$f,[eA(p,{tip:"生成中...",size:"large"})]))]),_:1},8,["open"])],64)}}};const th={class:"wt-ctrl"},rh={__name:"PhoneGenerate",setup(r){return(e,A)=>{const t=lA("a-divider");return sA(),iA("div",th,[eA(Df),eA(t,{style:{"border-color":"var(--theme-color)"}}),eA(kf),eA(t,{style:{"border-color":"var(--theme-color)"}}),eA(Nf),eA(Jf),eA(eh)])}}};const nh={class:"phone-tools"},sh={__name:"PhoneTools",setup(r){const{useSystemStore:e,useChatStore:A}=JA(),t=()=>{e.resetAppearance(),le({type:"success",content:"重置外观成功！"})},n=()=>{A.chatList=[],le({type:"success",content:"清空对话成功！"})};return(s,i)=>{const a=lA("a-button"),o=lA("a-tooltip"),c=lA("a-popconfirm");return sA(),iA("div",nh,[eA(c,{title:"确认重置外观？",placement:"right",onConfirm:t},{default:aA(()=>[eA(o,{title:"重置外观",placement:"left"},{default:aA(()=>[eA(a,{type:"primary",shape:"circle",icon:st(hA(Mi))},null,8,["icon"])]),_:1})]),_:1}),eA(c,{title:"确认清空对话？",placement:"right",onConfirm:n},{default:aA(()=>[eA(o,{title:"清空对话",placement:"left"},{default:aA(()=>[eA(a,{danger:"",type:"primary",shape:"circle",icon:st(hA(Gi))},null,8,["icon"])]),_:1})]),_:1})])}}},ih=De(sh,[["__scopeId","data-v-8f64af19"]]);const ah={class:"wt-content"},oh={class:"wt-preview"},Bh={class:"phone-bg"},ch=["src"],lh={__name:"WtContent",setup(r){const{useSystemStore:e}=JA(),A=DA(1125),t=DA(2436),n=DA(1),s=DA({}),i=DA(0);return Mt(()=>e.appearance,a=>{s.value=a;const{width:o,height:c}=Ni.find(l=>l.value===a.model);e.phoneWidth=A.value=o,e.phoneHeight=t.value=c,e.phoneScale=n.value=(360/o).toFixed(2),i.value=parseInt(c*n.value)},{immediate:!0}),(a,o)=>{const c=lA("perfect-scrollbar");return sA(),Vi(c,null,{default:aA(()=>[J("div",ah,[J("div",oh,[J("div",{class:"phone-wrap",style:qe({height:i.value+"px"})},[J("div",{class:"phone-scale",style:qe({transform:`scale(${n.value})`})},[J("div",{id:"phone",class:SA(["phone",{dark:hA(e).appearance.darkMode}]),style:qe({width:A.value+"px",height:t.value+"px"})},[eA(Zi,{appearance:s.value},null,8,["appearance"]),eA(ia,{appearance:s.value},null,8,["appearance"]),J("div",Bh,[hA(e).appearance.chatBackground?(sA(),iA("img",{key:0,src:hA(e).appearance.chatBackground,class:"phone-bg-for-height"},null,8,ch)):$A("",!0)]),eA(Za,{appearance:s.value,emojiBase64:hA(gn)},null,8,["appearance","emojiBase64"]),eA(to,{appearance:s.value,emojiBase64:hA(gn)},null,8,["appearance","emojiBase64"])],6)],4)],4),eA(ih),eA(rh)])])]),_:1})}}},hh=De(lh,[["__scopeId","data-v-6d137356"]]);export{hh as default};
