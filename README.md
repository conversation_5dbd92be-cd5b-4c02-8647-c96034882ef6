# Hello World 程序

这是一个简单的Hello World程序，可以打包成Windows .exe文件。

## 文件说明

- `hello_world.py` - 主程序文件，包含图形界面
- `requirements.txt` - Python依赖包列表
- `build_exe.bat` - 自动构建脚本

## 使用方法

### 方法1：运行Python脚本
```bash
python hello_world.py
```

### 方法2：打包成exe文件
1. 双击运行 `build_exe.bat` 文件
2. 等待安装依赖和打包完成
3. 在 `dist` 文件夹中找到 `HelloWorld.exe` 文件
4. 双击 `HelloWorld.exe` 即可运行

## 程序功能

- 显示一个包含"Hello World!"文本的窗口
- 点击"点击我"按钮会弹出消息框
- 点击"退出"按钮关闭程序

## 系统要求

- Windows 10 或更高版本
- Python 3.6 或更高版本（如果运行Python脚本）

## 注意事项

- 第一次运行 `build_exe.bat` 时需要网络连接来下载依赖
- 生成的exe文件可以在没有Python环境的电脑上运行 