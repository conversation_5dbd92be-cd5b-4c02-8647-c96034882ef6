<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php $this->need('header.php'); ?>

<div class="main-container">
    <div class="error-page">
        <div class="error-code">404</div>
        <h1 class="error-title">页面未找到</h1>
        <p class="error-desc">抱歉，您访问的页面不存在或已被删除</p>
        <div class="error-actions">
            <a href="<?php $this->options->siteUrl(); ?>" class="btn-primary">返回首页</a>
            <a href="javascript:history.back();" class="btn-secondary">返回上一页</a>
        </div>
    </div>
</div>

<style>
    .error-page {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 100px 20px;
        text-align: center;
    }
    
    .error-code {
        font-size: 120px;
        font-weight: 700;
        color: #FE2C55;
        line-height: 1;
        margin-bottom: 20px;
        text-shadow: 0 10px 20px rgba(254, 44, 85, 0.2);
    }
    
    .error-title {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 15px;
    }
    
    .error-desc {
        font-size: 16px;
        color: #aaa;
        margin-bottom: 30px;
        max-width: 500px;
    }
    
    .error-actions {
        display: flex;
        gap: 15px;
    }
    
    .btn-primary {
        background-color: #FE2C55;
        color: #fff;
        border: none;
        border-radius: 30px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s;
    }
    
    .btn-primary:hover {
        background-color: #e01b41;
    }
    
    .btn-secondary {
        background-color: #333;
        color: #fff;
        border: none;
        border-radius: 30px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s;
    }
    
    .btn-secondary:hover {
        background-color: #444;
    }
</style>

<?php $this->need('footer.php'); ?> 