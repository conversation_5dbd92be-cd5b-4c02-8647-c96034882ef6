<?php
/**
 * 抖音风格的简约大气主题，集成会员和付费功能
 *
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.1
 * @link https://www.example.com
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;
$this->need('header.php');
?>

<!-- 主要内容区 -->
<div class="main-container">
    <!-- 文章流 -->
    <main class="content-feed">
        <div class="feed-header">
            <h2 class="feed-title">最新文章</h2>
            <div class="feed-filters">
                <button class="filter-btn active">全部</button>
                <button class="filter-btn">热门</button>
                <button class="filter-btn">精选</button>
                <button class="filter-btn">最新</button>
            </div>
        </div>

        <div class="articles-grid">
                <?php
                // 处理置顶文章
                $sticky = $this->options->sticky; // 置顶文章ID，多个用逗号隔开
                if($sticky){
                    $sticky_ids = explode(',', $sticky);
                    $sticky_posts = array();
                    foreach($sticky_ids as $id){
                        $sticky_post = $this->db->fetchRow($this->db->select()
                            ->from('table.contents')
                            ->where('cid = ?', $id)
                            ->where('status = ?', 'publish')
                            ->where('type = ?', 'post')
                            ->where('created < ?', $this->options->time)
                            ->limit(1));
                        if($sticky_post){
                            $sticky_posts[] = $sticky_post;
                        }
                    }

                    foreach($sticky_posts as $key => $sticky_post){
                        $this->push($sticky_post);
                ?>
                <!-- 置顶文章卡片 -->
                <article class="article-card" style="animation-delay: <?php echo 0.1 * $key; ?>s;">
                    <div class="article-img">
                        <?php if($this->fields->thumb): ?>
                        <img src="<?php echo $this->fields->thumb; ?>" alt="<?php $this->title() ?>">
                        <?php else: ?>
                        <img src="<?php $this->options->themeUrl('assets/img/default.jpg'); ?>" alt="<?php $this->title() ?>">
                        <?php endif; ?>
                        <?php if($this->fields->isPremium): ?>
                        <div class="premium-tag">会员专享</div>
                        <?php endif; ?>
                    </div>
                    <div class="article-content">
                        <h3 class="article-title"><a href="<?php $this->permalink() ?>"><?php $this->title() ?></a></h3>
                        <p class="article-excerpt"><?php $this->excerpt(60, '...'); ?></p>
                        <div class="article-meta">
                            <div class="article-author">
                                <img src="<?php echo getUserAvatar($this->author->mail, 20); ?>" alt="<?php $this->author(); ?>" class="author-avatar">
                                <span><?php $this->author(); ?></span>
                            </div>
                            <div class="article-stats">
                                <div class="stat-item">
                                    <i class="ri-eye-line"></i>
                                    <span><?php echo getPostViews($this); ?></span>
                                </div>
                                <div class="stat-item">
                                    <i class="ri-heart-line"></i>
                                    <span><?php echo getLikes($this); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </article>
                <?php
                    }
                }
                ?>

                <?php
                $index = 0;
                while($this->next()):
                $index++;
                ?>
                <!-- 普通文章卡片 -->
                <article class="article-card" style="animation-delay: <?php echo 0.1 * $index; ?>s;">
                    <div class="article-img">
                        <?php if($this->fields->thumb): ?>
                        <img src="<?php echo $this->fields->thumb; ?>" alt="<?php $this->title() ?>">
                        <?php else: ?>
                        <img src="<?php $this->options->themeUrl('assets/img/default.jpg'); ?>" alt="<?php $this->title() ?>">
                        <?php endif; ?>
                        <?php if($this->fields->isPremium): ?>
                        <div class="premium-tag">会员专享</div>
                        <?php endif; ?>
                    </div>
                    <div class="article-content">
                        <h3 class="article-title"><a href="<?php $this->permalink() ?>"><?php $this->title() ?></a></h3>
                        <p class="article-excerpt"><?php $this->excerpt(60, '...'); ?></p>
                        <div class="article-meta">
                            <div class="article-author">
                                <img src="<?php echo getUserAvatar($this->author->mail, 20); ?>" alt="<?php $this->author(); ?>" class="author-avatar">
                                <span><?php $this->author(); ?></span>
                            </div>
                            <div class="article-stats">
                                <div class="stat-item">
                                    <i class="ri-eye-line"></i>
                                    <span><?php echo getPostViews($this); ?></span>
                                </div>
                                <div class="stat-item">
                                    <i class="ri-heart-line"></i>
                                    <span><?php echo getLikes($this); ?></span>
                                </div>
                                <?php if($this->fields->isPremium): ?>
                                <div class="stat-item premium-item">
                                    <i class="ri-vip-crown-line"></i>
                                    <span>会员</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </article>
                <?php endwhile; ?>
            </div>

            <!-- 加载更多 -->
            <div class="loader">
                <div class="loader-icon"></div>
            </div>
        </main>

        <!-- 侧边栏 -->
        <?php $this->need('sidebar.php'); ?>
    </div>

    <!-- 页脚 -->
    <?php $this->need('footer.php'); ?>