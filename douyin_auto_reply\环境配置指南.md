# 抖音聊天自动回复工具 - 环境配置指南

## 🎯 配置概览

本工具需要以下环境和依赖：
- Python 3.7+ 
- Tesseract OCR (支持中文)
- Python依赖包 (自动安装)

## 🚀 快速配置 (推荐)

### 方法1：一键配置脚本
1. **运行自动配置脚本**
   ```cmd
   双击运行: setup_env.bat
   ```
   
2. **安装Tesseract OCR** (手动步骤)
   - 下载地址: https://github.com/UB-Mannheim/tesseract/wiki
   - 选择Windows版本下载
   - ⚠️ **安装时务必勾选中文语言包 (chi_sim)**
   - 记住安装路径 (例如: `C:\Program Files\Tesseract-OCR\`)

3. **验证环境**
   ```cmd
   python check_env.py
   ```

## 🔧 手动配置 (详细步骤)

### 步骤1: 检查Python环境
```cmd
python --version
```
如果没有Python，请到 https://www.python.org/downloads/ 下载安装

### 步骤2: 创建虚拟环境
```cmd
# 进入项目目录
cd douyin_auto_reply

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (macOS/Linux)
source venv/bin/activate
```

### 步骤3: 安装Python依赖
```cmd
# 升级pip
python -m pip install --upgrade pip

# 安装依赖包
pip install -r requirements.txt
```

**如果安装速度慢，可使用国内镜像源：**
```cmd
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
```

### 步骤4: 安装Tesseract OCR

#### Windows系统
1. 下载安装包: https://github.com/UB-Mannheim/tesseract/wiki
2. 运行安装程序
3. **重要**: 安装时选择"Additional language data" → 勾选"Chinese (Simplified)"
4. 安装完成后，将安装路径添加到系统PATH环境变量

#### 验证Tesseract安装
```cmd
tesseract --version
```

### 步骤5: 验证环境
```cmd
python check_env.py
```

## 📦 依赖包说明

### 核心依赖
- **pyautogui** - 界面自动化操作
- **opencv-python** - 图像处理和匹配
- **pytesseract** - OCR文字识别
- **Pillow** - 图像处理库
- **numpy** - 数值计算
- **pywin32** - Windows API调用
- **loguru** - 日志记录

### 工具库
- **requests** - HTTP请求 (用于AI接口)
- **configparser** - 配置文件处理

## 🚨 常见问题解决

### 问题1: pip安装失败
```cmd
# 解决方案1: 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple [包名]

# 解决方案2: 升级pip
python -m pip install --upgrade pip

# 解决方案3: 清理缓存
pip cache purge
```

### 问题2: Tesseract未找到
```cmd
# 错误信息: tesseract is not installed or it's not in your PATH

# 解决方案:
# 1. 确认Tesseract已安装
# 2. 检查PATH环境变量
# 3. 手动指定路径在配置文件中
```

### 问题3: 中文识别不准确
- 确保安装了中文语言包 (chi_sim)
- 检查语言包路径: `[Tesseract安装目录]\tessdata\`
- 应该存在文件: `chi_sim.traineddata`

### 问题4: pywin32安装失败
```cmd
# 解决方案1: 使用预编译版本
pip install pywin32

# 解决方案2: 手动安装
# 到 https://github.com/mhammond/pywin32/releases 下载对应版本
```

## 🔍 环境验证检查项

运行 `python check_env.py` 会检查以下项目：

✅ **Python版本** (≥3.7)
✅ **核心依赖包** (pyautogui, opencv, etc.)
✅ **Tesseract OCR** (版本和中文支持)
✅ **Windows API支持** (pywin32)

## ⚡ 启动程序

### 方法1: 批处理脚本 (推荐)
```cmd
双击运行: run.bat
```

### 方法2: 命令行
```cmd
# 激活虚拟环境
venv\Scripts\activate

# 运行程序
python main.py
```

## 🎨 自定义配置

程序首次运行会自动生成配置文件：
- `config/app_config.ini` - 主配置文件
- `config/replies.json` - 回复模板文件

可以根据需要修改这些配置文件。

## 📞 技术支持

如果遇到配置问题：
1. 先运行 `python check_env.py` 检查环境
2. 查看本文档的常见问题部分
3. 检查日志文件获取详细错误信息

---

🎉 **配置完成后，您就可以开始使用抖音聊天自动回复工具了！** 