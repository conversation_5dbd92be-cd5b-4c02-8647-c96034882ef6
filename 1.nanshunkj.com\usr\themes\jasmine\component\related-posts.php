<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<div class="related-posts bg-white dark:bg-[#161829] rounded-lg p-6 border border-stone-100 dark:border-neutral-600">
    <div class="text-center mb-8">
        <h3 class="text-lg font-medium text-black dark:text-gray-300 flex items-center justify-center">
            <span class="emoji mr-2">📚</span>相关推荐
        </h3>
    </div>
    
    <?php
    // 使用 Typecho 原生方法获取相关文章
    $this->related(6)->to($relatedPosts);
    
    if ($relatedPosts->have()): 
        // 数字emoji数组
        $numberEmojis = ['1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣'];
        $posts = [];
        $index = 0;
        while ($relatedPosts->next()) {
            $posts[] = [
                'permalink' => $relatedPosts->permalink,
                'title' => $relatedPosts->title,
                'index' => $index++
            ];
        }
        
        // 将文章分成两列
        $leftPosts = array_slice($posts, 0, 3);
        $rightPosts = array_slice($posts, 3, 3);
    ?>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
            <!-- 左列 -->
            <div class="space-y-3">
                <?php foreach ($leftPosts as $post): ?>
                    <a href="<?php echo $post['permalink']; ?>" class="related-post-item block group">
                        <div class="flex items-center gap-3">
                            <span class="emoji flex-shrink-0 opacity-80"><?php echo $numberEmojis[$post['index']]; ?></span>
                            <span class="text-base text-black dark:text-gray-300 line-clamp-1 transition-colors duration-300">
                                <?php echo $post['title']; ?>
                            </span>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
            
            <!-- 右列 -->
            <div class="space-y-3">
                <?php foreach ($rightPosts as $post): ?>
                    <a href="<?php echo $post['permalink']; ?>" class="related-post-item block group">
                        <div class="flex items-center gap-3">
                            <span class="emoji flex-shrink-0 opacity-80"><?php echo $numberEmojis[$post['index']]; ?></span>
                            <span class="text-base text-black dark:text-gray-300 line-clamp-1 transition-colors duration-300">
                                <?php echo $post['title']; ?>
                            </span>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            暂无相关文章推荐
        </div>
    <?php endif; ?>
</div> 