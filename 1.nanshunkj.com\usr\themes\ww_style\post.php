<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php $this->need('header.php'); ?>

<div class="main-container">
    <div class="content-wrapper">
        <div class="post-container">
            <article class="post-content" itemscope itemtype="http://schema.org/BlogPosting">
                <!-- 文章头部信息 -->
                <div class="post-header">
                    <div class="post-meta">
                        <span class="meta-category"><?php $this->category(','); ?></span>
                        <span class="meta-date"><i class="ri-time-line"></i> <?php $this->date('Y-m-d'); ?></span>
                        <span class="meta-author"><i class="ri-user-line"></i> <?php $this->author(); ?></span>
                        <span class="meta-views"><i class="ri-eye-line"></i> <?php echo getPostViews($this); ?> 阅读</span>
                        <span class="meta-comments"><i class="ri-message-2-line"></i> <?php $this->commentsNum('0 评论', '1 评论', '%d 评论'); ?></span>
                    </div>

                    <h1 class="post-title" itemprop="name headline"><?php $this->title() ?></h1>

                    <?php if ($this->fields->description): ?>
                    <div class="post-desc"><?php echo $this->fields->description; ?></div>
                    <?php endif; ?>
                </div>

                <?php if($this->fields->thumb): ?>
                <div class="post-thumb">
                    <img src="<?php echo $this->fields->thumb; ?>" alt="<?php $this->title() ?>">
                </div>
                <?php endif; ?>

                <!-- 文章内容 -->
                <div class="post-body" itemprop="articleBody">
                    <?php
                    // 判断是否为会员专享内容
                    $isPremium = $this->fields->isPremium;

                    if ($isPremium && !isMemberAccess($this)) {
                        $content = $this->content;
                        $limit = 300; // 预览字数

                        // 截取内容前部分
                        $preview = mb_substr(strip_tags($content), 0, $limit, 'utf-8') . '...';
                        echo '<div class="content-preview">' . $preview . '</div>';

                        // 会员解锁区域
                        echo '<div class="premium-overlay">
                            <div class="premium-info">
                                <div class="premium-icon"><i class="ri-vip-crown-line"></i></div>
                                <div class="premium-title">会员专享内容</div>
                                <div class="premium-desc">开通会员即可查看完整内容</div>';

                                if ($this->user->hasLogin()) {
                                    echo '<a href="' . $this->options->siteUrl . 'index.php/payment" class="btn-primary premium-btn">立即开通</a>';
                                } else {
                                    echo '<a href="' . $this->options->siteUrl . 'index.php/login" class="btn-primary premium-btn">登录后开通</a>';
                                }

                        echo '</div>
                        </div>';
                    } else {
                        // 普通文章或会员已解锁
                        $this->content();
                    }
                    ?>
                </div>

                <!-- 文章底部区域 -->
                <div class="post-footer">
                    <!-- 文章标签 -->
                    <div class="post-tags">
                        <?php $this->tags(' ', true, '<span>暂无标签</span>'); ?>
                    </div>

                    <!-- 点赞和分享 -->
                    <div class="post-actions">
                        <div class="action-like post-like-button" data-cid="<?php $this->cid(); ?>">
                            <i class="ri-heart-line"></i>
                            <span class="like-count"><?php echo getLikes($this); ?></span>
                        </div>
                        <div class="action-share" id="share-btn">
                            <i class="ri-share-line"></i> 分享
                        </div>
                    </div>

                    <!-- 上一篇/下一篇 -->
                    <div class="post-nav">
                        <div class="post-nav-prev">
                            <?php $this->thePrev('<i class="ri-arrow-left-line"></i> %s', '<span class="empty-nav">没有了</span>'); ?>
                        </div>
                        <div class="post-nav-next">
                            <?php $this->theNext('%s <i class="ri-arrow-right-line"></i>', '<span class="empty-nav">没有了</span>'); ?>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 作者信息 -->
            <div class="author-info">
                <div class="author-avatar">
                    <img src="<?php echo getUserAvatar($this->author->mail, 80); ?>" alt="<?php $this->author(); ?>">
                </div>
                <div class="author-data">
                    <div class="author-name"><?php $this->author(); ?></div>
                    <div class="author-desc"><?php echo getUserBio($this->author->uid); ?></div>
                    <div class="author-stats">
                        <span class="stat-item"><i class="ri-file-list-line"></i> <?php echo getUserPostCount($this->author->uid); ?> 文章</span>
                        <span class="stat-item"><i class="ri-eye-line"></i> <?php echo getUserViews($this->author->uid); ?> 浏览</span>
                    </div>
                </div>
                <a href="<?php $this->author->permalink(); ?>" class="btn-secondary author-btn">查看更多文章</a>
            </div>

            <!-- 相关文章 -->
            <?php $this->related(4)->to($relatedPosts); ?>
            <?php if ($relatedPosts->have()): ?>
            <div class="related-posts">
                <h3 class="related-title"><i class="ri-links-line"></i> 相关文章</h3>
                <div class="related-grid">
                    <?php while ($relatedPosts->next()): ?>
                    <div class="related-item">
                        <div class="related-image">
                            <?php if($relatedPosts->fields->thumb): ?>
                            <img src="<?php echo $relatedPosts->fields->thumb; ?>" alt="<?php $relatedPosts->title() ?>">
                            <?php else: ?>
                            <img src="<?php $this->options->themeUrl('assets/img/default-small.jpg'); ?>" alt="<?php $relatedPosts->title() ?>">
                            <?php endif; ?>
                        </div>
                        <div class="related-info">
                            <a href="<?php $relatedPosts->permalink() ?>" class="related-title"><?php $relatedPosts->title() ?></a>
                            <div class="related-date"><?php $relatedPosts->date('Y-m-d'); ?></div>
                        </div>
                    </div>
                    <?php endwhile; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- 评论区 -->
            <?php $this->need('comments.php'); ?>
        </div>

        <!-- 侧边栏 -->
        <?php $this->need('sidebar.php'); ?>
    </div>
</div>

<!-- 分享弹窗 -->
<div class="share-modal" id="share-modal">
    <div class="share-content">
        <div class="share-header">
            <h3 class="share-title">分享到</h3>
            <div class="share-close" id="share-close"><i class="ri-close-line"></i></div>
        </div>
        <div class="share-body">
            <div class="share-item" data-type="weibo">
                <i class="ri-weibo-line"></i>
                <span>微博</span>
            </div>
            <div class="share-item" data-type="wechat">
                <i class="ri-wechat-line"></i>
                <span>微信</span>
            </div>
            <div class="share-item" data-type="qq">
                <i class="ri-qq-line"></i>
                <span>QQ</span>
            </div>
            <div class="share-item" data-type="twitter">
                <i class="ri-twitter-line"></i>
                <span>Twitter</span>
            </div>
            <div class="share-item" data-type="facebook">
                <i class="ri-facebook-line"></i>
                <span>Facebook</span>
            </div>
            <div class="share-item" id="copy-link" data-type="copy">
                <i class="ri-link"></i>
                <span>复制链接</span>
            </div>
        </div>
    </div>
</div>

<?php $this->need('footer.php'); ?>