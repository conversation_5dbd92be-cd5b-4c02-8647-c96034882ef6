/* 阅读进度条样式 */
.reading-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: #007bff;
    z-index: 9999;
    transition: width 0.2s ease;
}

/* 社交分享样式 */
.social-share {
    margin: 0;
    text-align: center;
    padding: 4px 0;
}

.social-share button {
    margin: 0 8px;
    padding: 4px 10px;
    border: none;
    border-radius: 4px;
    background: #f0f0f0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9em;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.social-share button:hover {
    background: #e0e0e0;
    transform: translateY(-1px);
}

.social-share iconify-icon {
    font-size: 1.1em;
    color: #666;
}

.social-share button:hover iconify-icon {
    color: #333;
}

/* 微信分享弹窗样式 */
.wechat-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.wechat-modal-content {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    max-width: 300px;
    width: 90%;
}

.wechat-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.wechat-modal-header h3 {
    margin: 0;
    font-size: 1.1em;
}

.wechat-modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
}

.wechat-modal-close:hover {
    color: #333;
}

.wechat-modal-body {
    text-align: center;
}

#wechat-qrcode {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 10px;
}

.wechat-tip {
    font-size: 0.9em;
    color: #666;
    margin: 10px 0 0;
}

/* 暗色模式适配 */
.dark .social-share button {
    background: #2d2d2d;
    color: #e0e0e0;
}

.dark .social-share button:hover {
    background: #3d3d3d;
}

.dark .social-share iconify-icon {
    color: #e0e0e0;
}

.dark .social-share button:hover iconify-icon {
    color: #ffffff;
}

.dark .wechat-modal-content {
    background: #1a1b1f;
    color: #e0e0e0;
}

.dark .wechat-modal-close {
    color: #e0e0e0;
}

.dark .wechat-modal-close:hover {
    color: #fff;
}

.dark .wechat-tip {
    color: #999;
}

/* 确保二维码在暗色模式下依然清晰可见 */
.dark #wechat-qrcode {
    background: #fff;
}

/* 复制成功提示样式 */
.copy-toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 10000;
    animation: fadeInOut 2s ease;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, 20px); }
    15% { opacity: 1; transform: translate(-50%, 0); }
    85% { opacity: 1; transform: translate(-50%, 0); }
    100% { opacity: 0; transform: translate(-50%, -20px); }
}

/* 优化二维码图片显示 */
#wechat-qrcode img {
    display: block;
    margin: 0 auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark #wechat-qrcode img {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
} 