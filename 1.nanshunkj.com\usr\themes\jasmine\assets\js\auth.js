// 认证相关的 JavaScript 代码
class AuthDialog {
    constructor(options = {}) {
        this.currentDialog = null;
        this.onLoginSuccess = options.onLoginSuccess || function() {
            window.location.reload();
        };
    }

    // 创建对话框基础结构
    createDialog({ title, content }) {
        const dialog = document.createElement('div');
        dialog.className = 'login-dialog';
        dialog.innerHTML = `
            <div class="relative w-full max-w-md mx-4 bg-white dark:bg-[#0d1117] rounded-xl shadow-2xl overflow-hidden">
                <div class="absolute top-4 right-4">
                    <button class="close-dialog-btn p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-bold mb-6 text-center dark:text-white">${title}</h3>
                    ${content}
                </div>
            </div>
        `;

        // 关闭现有对话框
        if (this.currentDialog) {
            this.currentDialog.remove();
        }

        // 点击遮罩层或关闭按钮关闭对话框
        dialog.addEventListener('click', e => {
            if (e.target === dialog || e.target.closest('.close-dialog-btn')) {
                dialog.remove();
                this.currentDialog = null;
            }
        });

        document.body.appendChild(dialog);
        this.currentDialog = dialog;
        return dialog;
    }

    // 显示选择登录方式的对话框
    showLoginDialog() {
        const dialog = this.createDialog({
            title: '欢迎回来',
            content: `
                <div class="flex flex-col gap-4">
                    <div class="text-center text-gray-600 dark:text-gray-400">选择登录方式继续评论</div>
                    <button class="login-btn flex items-center justify-center gap-2 w-full py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-all duration-200 transform hover:scale-[1.02]">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>账号密码登录</span>
                    </button>
                    <button class="register-btn flex items-center justify-center gap-2 w-full py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200 transform hover:scale-[1.02]">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                        </svg>
                        <span>注册新账号</span>
                    </button>
                </div>
            `
        });

        // 绑定按钮事件
        dialog.querySelector('.login-btn').onclick = (e) => {
            e.preventDefault();
            this.showLoginForm();
        };
        
        dialog.querySelector('.register-btn').onclick = (e) => {
            e.preventDefault();
            this.showRegisterForm();
        };
    }

    // 显示登录表单
    showLoginForm() {
        const dialog = this.createDialog({
            title: '登录',
            content: `
                <form id="login-form" method="post" action="/index.php/action/login" class="flex flex-col gap-4">
                    <div class="relative">
                        <input type="text" name="name" required
                               class="peer w-full px-4 py-2 border rounded-lg outline-none transition-all duration-200
                                      focus:border-black dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300
                                      dark:focus:border-gray-500"
                               placeholder=" ">
                        <label class="absolute left-2 -top-2.5 px-2 text-sm bg-white dark:bg-[#0d1117] text-gray-600 dark:text-gray-400
                                    transition-all duration-200 peer-placeholder-shown:top-2.5 peer-placeholder-shown:left-4
                                    peer-focus:-top-2.5 peer-focus:left-2 peer-focus:bg-white dark:peer-focus:bg-[#0d1117]">
                            用户名
                        </label>
                    </div>
                    <div class="relative">
                        <input type="password" name="password" required
                               class="peer w-full px-4 py-2 border rounded-lg outline-none transition-all duration-200
                                      focus:border-black dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300
                                      dark:focus:border-gray-500"
                               placeholder=" ">
                        <label class="absolute left-2 -top-2.5 px-2 text-sm bg-white dark:bg-[#0d1117] text-gray-600 dark:text-gray-400
                                    transition-all duration-200 peer-placeholder-shown:top-2.5 peer-placeholder-shown:left-4
                                    peer-focus:-top-2.5 peer-focus:left-2 peer-focus:bg-white dark:peer-focus:bg-[#0d1117]">
                            密码
                        </label>
                    </div>
                    <div class="flex items-center gap-2 mt-2">
                        <input type="checkbox" id="remember" name="remember" value="1" class="rounded border-gray-300 text-black focus:ring-black dark:border-gray-600 dark:bg-gray-800">
                        <label for="remember" class="text-sm text-gray-600 dark:text-gray-400">记住我</label>
                    </div>
                    <input type="hidden" name="referer" value="${window.location.href}">
                    <button type="submit" class="w-full py-2 mt-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-all duration-200 transform hover:scale-[1.02]">
                        登录
                    </button>
                </form>
            `
        });

        this.setupLoginForm(dialog);
    }

    // 显示注册表单
    showRegisterForm() {
        const dialog = this.createDialog({
            title: '创建账号',
            content: `
                <form id="register-form" method="post" action="/index.php/action/register" class="flex flex-col gap-4">
                    <div class="relative">
                        <input type="text" name="name" required
                               class="peer w-full px-4 py-2 border rounded-lg outline-none transition-all duration-200
                                      focus:border-black dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300
                                      dark:focus:border-gray-500"
                               placeholder=" ">
                        <label class="absolute left-2 -top-2.5 px-2 text-sm bg-white dark:bg-[#0d1117] text-gray-600 dark:text-gray-400
                                    transition-all duration-200 peer-placeholder-shown:top-2.5 peer-placeholder-shown:left-4
                                    peer-focus:-top-2.5 peer-focus:left-2 peer-focus:bg-white dark:peer-focus:bg-[#0d1117]">
                            用户名
                        </label>
                    </div>
                    <div class="relative">
                        <input type="email" name="mail" required
                               class="peer w-full px-4 py-2 border rounded-lg outline-none transition-all duration-200
                                      focus:border-black dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300
                                      dark:focus:border-gray-500"
                               placeholder=" ">
                        <label class="absolute left-2 -top-2.5 px-2 text-sm bg-white dark:bg-[#0d1117] text-gray-600 dark:text-gray-400
                                    transition-all duration-200 peer-placeholder-shown:top-2.5 peer-placeholder-shown:left-4
                                    peer-focus:-top-2.5 peer-focus:left-2 peer-focus:bg-white dark:peer-focus:bg-[#0d1117]">
                            邮箱
                        </label>
                    </div>
                    <div class="relative">
                        <input type="password" name="password" required
                               class="peer w-full px-4 py-2 border rounded-lg outline-none transition-all duration-200
                                      focus:border-black dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300
                                      dark:focus:border-gray-500"
                               placeholder=" ">
                        <label class="absolute left-2 -top-2.5 px-2 text-sm bg-white dark:bg-[#0d1117] text-gray-600 dark:text-gray-400
                                    transition-all duration-200 peer-placeholder-shown:top-2.5 peer-placeholder-shown:left-4
                                    peer-focus:-top-2.5 peer-focus:left-2 peer-focus:bg-white dark:peer-focus:bg-[#0d1117]">
                            密码
                        </label>
                    </div>
                    <input type="hidden" name="referer" value="${window.location.href}">
                    <button type="submit" class="w-full py-2 mt-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-all duration-200 transform hover:scale-[1.02]">
                        创建账号
                    </button>
                </form>
            `
        });

        this.setupRegisterForm(dialog);
    }

    // 设置登录表单处理
    setupLoginForm(dialog) {
        const form = dialog.querySelector('#login-form');
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> 登录中...';
            
            try {
                const formData = new FormData();
                formData.append('name', form.querySelector('[name="name"]').value);
                formData.append('password', form.querySelector('[name="password"]').value);
                
                const rememberCheckbox = form.querySelector('[name="remember"]');
                if (rememberCheckbox && rememberCheckbox.checked) {
                    formData.append('remember', '1');
                }
                
                formData.append('referer', window.location.href);
                
                const response = await fetch('/action/login', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });
                
                if (response.ok) {
                    window.location.reload();
                } else {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '登录';
                    alert('登录失败，请检查用户名和密码');
                }
            } catch (error) {
                console.error('Login error:', error);
                submitBtn.disabled = false;
                submitBtn.innerHTML = '登录';
                alert('登录失败，请稍后重试');
            }
        });
    }

    // 设置注册表单处理
    setupRegisterForm(dialog) {
        const form = dialog.querySelector('#register-form');
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> 注册中...';
            
            try {
                const formData = new FormData();
                formData.append('name', form.querySelector('[name="name"]').value);
                formData.append('mail', form.querySelector('[name="mail"]').value);
                formData.append('password', form.querySelector('[name="password"]').value);
                formData.append('referer', window.location.href);
                
                const response = await fetch('/action/register', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });
                
                if (response.ok) {
                    window.location.reload();
                } else {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '创建账号';
                    alert('注册失败，请检查输入信息');
                }
            } catch (error) {
                console.error('Registration error:', error);
                submitBtn.disabled = false;
                submitBtn.innerHTML = '创建账号';
                alert('注册失败，请稍后重试');
            }
        });
    }
}

// 导出认证对话框类
window.AuthDialog = AuthDialog; 