// 处理搜索功能
function handleSearch(formId, inputId) {
    const form = document.getElementById(formId);
    const input = document.getElementById(inputId);
    
    if (form && input) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchQuery = input.value.trim();
            if (searchQuery) {
                window.location.href = `${window.location.origin}/search/${searchQuery}`;
            }
        });
    }
}

// 初始化两个搜索表单
document.addEventListener('DOMContentLoaded', function() {
    handleSearch('search-mobile', 'search-input-mobile');
    handleSearch('search-desktop', 'search-input-desktop');
}); 