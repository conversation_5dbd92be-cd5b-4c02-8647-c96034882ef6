<?php
/**
 * AJAX 处理入口
 *
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 引入主题函数
require_once 'functions.php';

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 根据操作类型执行相应的处理函数
switch ($action) {
    case 'like':
        // 处理点赞请求
        ajaxLikePost();
        break;

    case 'favorite':
        // 处理收藏请求
        ajaxFavoritePost();
        break;

    case 'update-profile':
        // 处理更新个人资料请求
        ajaxUpdateProfile();
        break;

    case 'update-order-status':
        // 处理更新订单状态请求
        ajaxUpdateOrderStatus();
        break;

    default:
        // 未知操作
        echo json_encode([
            'success' => false,
            'message' => '未知操作'
        ]);
        break;
}

/**
 * 处理更新个人资料请求
 */
function ajaxUpdateProfile() {
    // 检查用户是否登录
    if (!Typecho_Widget::widget('Widget_User')->hasLogin()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录'
        ]);
        exit;
    }

    // 获取用户ID
    $uid = Typecho_Widget::widget('Widget_User')->uid;

    // 获取表单数据
    $nickname = isset($_POST['nickname']) ? $_POST['nickname'] : '';
    $bio = isset($_POST['bio']) ? $_POST['bio'] : '';

    // 验证数据
    if (empty($nickname)) {
        echo json_encode([
            'success' => false,
            'message' => '昵称不能为空'
        ]);
        exit;
    }

    // 更新用户资料
    $db = Typecho_Db::get();

    try {
        // 更新昵称
        $db->query($db->update('table.users')
            ->rows(['screenName' => $nickname])
            ->where('uid = ?', $uid));

        // 更新个人简介
        $bioRow = $db->fetchRow($db->select('cid')
            ->from('table.fields')
            ->where('cid = ?', $uid)
            ->where('name = ?', 'bio'));

        if ($bioRow) {
            $db->query($db->update('table.fields')
                ->rows(['str_value' => $bio])
                ->where('cid = ?', $uid)
                ->where('name = ?', 'bio'));
        } else {
            $db->query($db->insert('table.fields')
                ->rows([
                    'cid' => $uid,
                    'name' => 'bio',
                    'type' => 'str',
                    'str_value' => $bio
                ]));
        }

        echo json_encode([
            'success' => true,
            'message' => '个人资料更新成功'
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '更新失败: ' . $e->getMessage()
        ]);
    }

    exit;
}

/**
 * 处理更新订单状态请求
 */
function ajaxUpdateOrderStatus() {
    // 检查用户是否登录
    if (!Typecho_Widget::widget('Widget_User')->hasLogin()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录'
        ]);
        exit;
    }

    // 获取订单ID
    $orderId = isset($_POST['order_id']) ? $_POST['order_id'] : '';

    if (empty($orderId)) {
        echo json_encode([
            'success' => false,
            'message' => '订单ID不能为空'
        ]);
        exit;
    }

    // 模拟支付成功，更新订单状态
    $result = handlePaymentCallback($orderId, 'TX' . time() . rand(1000, 9999));

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => '支付成功',
            'redirect' => Typecho_Widget::widget('Widget_Options')->siteUrl . 'index.php/member.html'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '支付处理失败，请稍后再试'
        ]);
    }

    exit;
}

/**
 * 处理文章点赞请求
 */
function ajaxLikePost() {
    // 检查用户是否登录
    if (!Typecho_Widget::widget('Widget_User')->hasLogin()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录'
        ]);
        exit;
    }

    // 获取文章ID
    $cid = isset($_GET['cid']) ? intval($_GET['cid']) : 0;

    if (empty($cid)) {
        echo json_encode([
            'success' => false,
            'message' => '文章ID不能为空'
        ]);
        exit;
    }

    // 获取用户ID
    $uid = Typecho_Widget::widget('Widget_User')->uid;

    // 检查用户是否已点赞
    $db = Typecho_Db::get();

    $likedRow = $db->fetchRow($db->select('str_value')
        ->from('table.fields')
        ->where('cid = ?', $cid)
        ->where('name = ?', 'liked_user_' . $uid));

    if ($likedRow && $likedRow['str_value'] == 'yes') {
        echo json_encode([
            'success' => false,
            'message' => '您已经点赞过该文章'
        ]);
        exit;
    }

    // 获取当前点赞数
    $likesRow = $db->fetchRow($db->select('str_value')
        ->from('table.fields')
        ->where('cid = ?', $cid)
        ->where('name = ?', 'likes'));

    $likes = $likesRow ? intval($likesRow['str_value']) : 0;
    $likes++;

    // 更新点赞数
    try {
        if ($likesRow) {
            $db->query($db->update('table.fields')
                ->rows(['str_value' => $likes])
                ->where('cid = ?', $cid)
                ->where('name = ?', 'likes'));
        } else {
            $db->query($db->insert('table.fields')
                ->rows([
                    'cid' => $cid,
                    'name' => 'likes',
                    'type' => 'str',
                    'str_value' => $likes
                ]));
        }

        // 记录用户点赞
        if ($likedRow) {
            $db->query($db->update('table.fields')
                ->rows(['str_value' => 'yes'])
                ->where('cid = ?', $cid)
                ->where('name = ?', 'liked_user_' . $uid));
        } else {
            $db->query($db->insert('table.fields')
                ->rows([
                    'cid' => $cid,
                    'name' => 'liked_user_' . $uid,
                    'type' => 'str',
                    'str_value' => 'yes'
                ]));
        }

        echo json_encode([
            'success' => true,
            'message' => '点赞成功',
            'count' => $likes
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '点赞失败: ' . $e->getMessage()
        ]);
    }

    exit;
}

/**
 * 处理文章收藏请求
 */
function ajaxFavoritePost() {
    // 检查用户是否登录
    if (!Typecho_Widget::widget('Widget_User')->hasLogin()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录'
        ]);
        exit;
    }

    // 获取文章ID和操作类型
    $cid = isset($_POST['cid']) ? intval($_POST['cid']) : 0;
    $action = isset($_POST['action']) ? $_POST['action'] : 'add';

    if (empty($cid)) {
        echo json_encode([
            'success' => false,
            'message' => '文章ID不能为空'
        ]);
        exit;
    }

    // 获取用户ID
    $uid = Typecho_Widget::widget('Widget_User')->uid;

    // 数据库操作
    $db = Typecho_Db::get();

    // 检查文章是否存在
    $post = $db->fetchRow($db->select('title')
        ->from('table.contents')
        ->where('cid = ?', $cid)
        ->where('status = ?', 'publish'));

    if (!$post) {
        echo json_encode([
            'success' => false,
            'message' => '文章不存在或已被删除'
        ]);
        exit;
    }

    // 检查是否已收藏
    $favoriteRow = $db->fetchRow($db->select('cid')
        ->from('table.fields')
        ->where('cid = ?', $uid)
        ->where('name = ?', 'favorite')
        ->where('str_value = ?', $cid));

    if ($action == 'add') {
        // 添加收藏
        if ($favoriteRow) {
            echo json_encode([
                'success' => false,
                'message' => '您已经收藏过该文章'
            ]);
            exit;
        }

        try {
            $db->query($db->insert('table.fields')
                ->rows([
                    'cid' => $uid,
                    'name' => 'favorite',
                    'type' => 'str',
                    'str_value' => $cid,
                    'int_value' => time()
                ]));

            echo json_encode([
                'success' => true,
                'message' => '收藏成功'
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '收藏失败: ' . $e->getMessage()
            ]);
        }
    } else {
        // 取消收藏
        if (!$favoriteRow) {
            echo json_encode([
                'success' => false,
                'message' => '您尚未收藏该文章'
            ]);
            exit;
        }

        try {
            $db->query($db->delete('table.fields')
                ->where('cid = ?', $uid)
                ->where('name = ?', 'favorite')
                ->where('str_value = ?', $cid));

            echo json_encode([
                'success' => true,
                'message' => '取消收藏成功'
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '取消收藏失败: ' . $e->getMessage()
            ]);
        }
    }

    exit;
}
