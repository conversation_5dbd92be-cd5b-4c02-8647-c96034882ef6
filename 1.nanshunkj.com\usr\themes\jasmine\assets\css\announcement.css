/* 公告滑动容器 */
.announcement-slide {
    position: fixed;
    right: -380px;
    top: 50%;
    transform: translateY(-50%);
    width: 340px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px 0 0 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    padding: 24px;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    z-index: 9999;
    border: 1px solid rgba(0, 0, 0, 0.05);
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

.announcement-slide.active {
    right: 0;
    transform: translateY(-50%);
}

/* 公告内容区域 */
.announcement-content {
    color: #1d1d1f;
    font-size: 15px;
    line-height: 1.6;
    margin-right: 20px;
    margin-bottom: 0;
    word-break: break-word;
    max-height: 240px;
    overflow-y: auto;
    padding-right: 10px;
    font-weight: 400;
    letter-spacing: -0.022em;
}

/* 自定义滚动条 */
.announcement-content::-webkit-scrollbar {
    width: 8px;
}

.announcement-content::-webkit-scrollbar-track {
    background: transparent;
}

.announcement-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.announcement-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* 关闭按钮 */
.announcement-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #86868b;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    font-size: 14px;
    transition: all 0.2s ease;
    opacity: 0.8;
    transform: scale(1);
}

.announcement-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.announcement-close:active {
    transform: scale(0.95);
}

/* 暗色模式适配 */
.dark .announcement-slide {
    background: rgba(28, 28, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.08);
}

.dark .announcement-content {
    color: #f5f5f7;
}

.dark .announcement-close {
    color: #98989d;
    background: rgba(255, 255, 255, 0.1);
}

.dark .announcement-close:hover {
    background: rgba(255, 255, 255, 0.15);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .announcement-slide {
        width: 300px;
        right: -340px;
        padding: 20px;
    }
    
    .announcement-content {
        font-size: 14px;
        max-height: 200px;
    }
    
    .announcement-close {
        top: 14px;
        right: 14px;
        width: 22px;
        height: 22px;
    }
}

/* 动画效果 */
@keyframes announcement-in {
    from {
        opacity: 0;
        transform: translateY(-50%) translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

.announcement-slide.active {
    animation: announcement-in 0.4s cubic-bezier(0.16, 1, 0.3, 1);
} 