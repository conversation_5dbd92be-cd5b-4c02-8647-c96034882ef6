<div align="center">
<h1>Jasmine</h1>
  <p>
    一款 Typecho 博客类主题，采用响应式设计，整体简洁、精致、美观。
  </p>
<h4>

<a href="#项目说明">项目说明</a>
  <span> · </span>
    <a href="#主题下载">主题下载</a>
  <span> · </span>
    <a href="#主题文档">主题文档</a>
  <span> · </span>
    <a href="#许可协议">许可协议</a>
  <span> · </span>
    <a href="#打赏一下">打赏一下</a>
  </h4>
</div>

## 项目说明

Jasmine 是 Typecho 的一款主题。专为博客类网站开发，在简洁的基础之上，尽量体现出精致与美观。主题整体采用响应式设计，在移动端也有不错体验。除此之外，主题也针对 SEO 、夜间模式、代码高亮等内容也进行了针对性优化，使其简洁但不简陋。

> 如果觉得主题还不错，请帮忙点个 star 。
> 
> 您的 star 是这个项目维护下去的坚实动力。
> 
> 欢迎进群交流主题、Typecho 等相关问题。 QQ 群：[539165194](https://qm.qq.com/cgi-bin/qm/qr?k=oXM0EmLxXmgKfE1UDRlBY-g7Rkrx30oL&jump_from=webapi&authKey=uQdwWraveNKYBm/BQs88WXkNagEUr9tCkf/gbdQ9FasOviKYVhUd/wUME0q0AtnI)

演示地址：[南巷清风](https://www.liaocp.cn/)

![主题图片](./docs/theme.png)

主题包括但不限于以下亮点（等你发现）：

* 响应式设计
* 针对 SEO 优化
* 支持切换夜间模式
* 无刷新跳转页面
* 说说功能
* 支持置顶文章显示
* 文章阅读量显示
* 支持评论 QQ 头像显示
* 支持代码高亮
* 支持随机文章跳转
* 支持文章缩略图设置
* 支持外观设置备份
* 主题更新检测
* ……

## 主题下载

[jasmine.zip](https://github.com/liaocp666/Jasmine/releases/latest/download/jasmine.zip)

## 主题文档

[Github](https://github.com/liaocp666/Jasmine/wiki) | [Gitee（国内）](https://gitee.com/LiaoChunping/Jasmine/wikis/pages)

## 开发文档

### 开发之前

需要提前安装 [Node.js](https://nodejs.org/) ，然后依次执行下面命令：

**安装 pnpm**

```shell
npm install -g pnpm
```

**安装依赖**

```shell
pnpm install
```

### 开发主题

主题代码中不包含样式代码，运行下面命令，用于开发时监听文件变动，生成样式文件。

> 使用 [Tailwind CSS](https://tailwindcss.com/) 样式框架

```shell
pnpm run dev
```

### 开发之后

**格式化代码**

```shell
pnpm run lint
pnpm run prettier
```

**构建主题**

```shell
pnpm run build
```

**打包主题**

运行下面命令后，将在代码根目录生成 jasmine.zip 文件

```shell
bash release
```

## 许可协议

* Jasmine 主题使用 [GPL V3.0](https://github.com/liaocp666/theme-jasmine/blob/main/LICENSE) 协议开源。

* 您必需遵守 [GPL V3.0](https://github.com/liaocp666/theme-jasmine/blob/main/LICENSE) 协议进行二次开发或移植主题，以及声明原主题名称及其链接。

* **您可以删除页脚的作者信息。**

* **您必须在页脚保留 Jasmine 主题的名称及其链接。**

## 打赏一下

如果觉得主题制作的不错，不如使用微信扫一扫，请作者喝杯咖啡~~

![wx_pay](https://user-images.githubusercontent.com/27202776/227807562-5340971b-a292-4c70-afbb-1a7d242e46db.jpg)

