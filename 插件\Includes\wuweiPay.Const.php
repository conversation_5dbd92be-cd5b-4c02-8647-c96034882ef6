<?php
/**
 * 吴畏支付常量定义
 * 
 * @package WuweiPay
 * <AUTHOR>
 * @link https://8ww.fun
 */
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 插件路径常量
define('WUWEIPAY_PLUGIN_DIR', __DIR__ . '/../');
define('WUWEIPAY_INCLUDES_DIR', __DIR__ . '/');
define('WUWEIPAY_ASSETS_DIR', WUWEIPAY_PLUGIN_DIR . 'Assets/');
define('WUWEIPAY_CLASS_DIR', WUWEIPAY_INCLUDES_DIR . 'class/');

// 插件URL常量
define('WUWEIPAY_PLUGIN_URL', Helper::options()->pluginUrl . '/WuweiPay/');
define('WUWEIPAY_ASSETS_URL', WUWEIPAY_PLUGIN_URL . 'Assets/');

// 支付相关常量
define('WUWEIPAY_ORDER_PREFIX', 'WUWP'); // 订单前缀
define('WUWEIPAY_ORDER_EXPIRE', 3600); // 订单过期时间（秒）

// 支付状态常量
define('WUWEIPAY_STATUS_UNPAID', 0); // 未支付
define('WUWEIPAY_STATUS_PAID', 1); // 已支付

// 支付方式常量
define('WUWEIPAY_METHOD_ALIPAY', 'alipay'); // 支付宝
define('WUWEIPAY_METHOD_WECHAT', 'wechat'); // 微信支付

// 订单类型常量
define('WUWEIPAY_TYPE_GENERAL', 'general'); // 普通订单
define('WUWEIPAY_TYPE_DONATION', 'donation'); // 打赏/捐赠
define('WUWEIPAY_TYPE_VIP', 'vip'); // VIP会员

// 错误码常量
define('WUWEIPAY_ERROR_NONE', 0); // 无错误
define('WUWEIPAY_ERROR_PARAM', 1); // 参数错误
define('WUWEIPAY_ERROR_ORDER', 2); // 订单错误
define('WUWEIPAY_ERROR_PAYMENT', 3); // 支付错误
define('WUWEIPAY_ERROR_SIGN', 4); // 签名错误
define('WUWEIPAY_ERROR_UNKNOWN', 99); // 未知错误
