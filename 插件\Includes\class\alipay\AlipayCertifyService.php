<?php ?><?php // /* *****  * @自助授权：https://sq.shilin.studio  * @自助下单：https://order.shilin.studio  * @Author: 诗林工作室  * @AuthorUri: https://shilin.studio  * @Date: 2025-03-30 20:49:54  * @LastEditTime: 2025-03-30 05:48:38  * Copyright (c) 2024 by Shilin Studio All Rights Reserved. */ - by 贝塔PHP加密|https://sg.bt58.vip ?><?php
if(!function_exists('sg_load')){$__v=phpversion();$__x=explode('.',$__v);$__v2=$__x[0].'.'.(int)$__x[1];$__u=strtolower(substr(php_uname(),0,3));$__ts=(@constant('PHP_ZTS') || @constant('ZEND_THREAD_SAFE')?'ts':'');$__f=$__f0='ixed.'.$__v2.$__ts.'.'.$__u;$__ff=$__ff0='ixed.'.$__v2.'.'.(int)$__x[2].$__ts.'.'.$__u;$__ed=@ini_get('extension_dir');$__e=$__e0=@realpath($__ed);$__dl=function_exists('dl') && function_exists('file_exists') && @ini_get('enable_dl') && !@ini_get('safe_mode');if($__dl && $__e && version_compare($__v,'5.2.5','<') && function_exists('getcwd') && function_exists('dirname')){$__d=$__d0=getcwd();if(@$__d[1]==':') {$__d=str_replace('\\','/',substr($__d,2));$__e=str_replace('\\','/',substr($__e,2));}$__e.=($__h=str_repeat('/..',substr_count($__e,'/')));$__f='/ixed/'.$__f0;$__ff='/ixed/'.$__ff0;while(!file_exists($__e.$__d.$__ff) && !file_exists($__e.$__d.$__f) && strlen($__d)>1){$__d=dirname($__d);}if(file_exists($__e.$__d.$__ff)) dl($__h.$__d.$__ff); else if(file_exists($__e.$__d.$__f)) dl($__h.$__d.$__f);}if(!function_exists('sg_load') && $__dl && $__e0){if(file_exists($__e0.'/'.$__ff0)) dl($__ff0); else if(file_exists($__e0.'/'.$__f0)) dl($__f0);}if(!function_exists('sg_load')){$__ixedurl='https://www.sourceguardian.com/loaders/download.php?php_v='.urlencode($__v).'&php_ts='.($__ts?'1':'0').'&php_is='.@constant('PHP_INT_SIZE').'&os_s='.urlencode(php_uname('s')).'&os_r='.urlencode(php_uname('r')).'&os_m='.urlencode(php_uname('m'));$__sapi=php_sapi_name();if(!$__e0) $__e0=$__ed;if(function_exists('php_ini_loaded_file')) $__ini=php_ini_loaded_file(); else $__ini='php.ini';if((substr($__sapi,0,3)=='cgi')||($__sapi=='cli')||($__sapi=='embed')){$__msg="\nPHP script '".__FILE__."' is protected by SourceGuardian and requires a SourceGuardian loader '".$__f0."' to be installed.\n\n1) Download the required loader '".$__f0."' from the SourceGuardian site: ".$__ixedurl."\n2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="\n3) Edit ".$__ini." and add 'extension=".$__f0."' directive";}}$__msg.="\n\n";}else{$__msg="<html><body>PHP script '".__FILE__."' is protected by <a href=\"https://www.sourceguardian.com/\">SourceGuardian</a> and requires a SourceGuardian loader '".$__f0."' to be installed.<br><br>1) <a href=\"".$__ixedurl."\" target=\"_blank\">Click here</a> to download the required '".$__f0."' loader from the SourceGuardian site<br>2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="<br>3) Edit ".$__ini." and add 'extension=".$__f0."' directive<br>4) Restart the web server";}}$__msg.="</body></html>";}die($__msg);exit();}}return sg_load('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');
