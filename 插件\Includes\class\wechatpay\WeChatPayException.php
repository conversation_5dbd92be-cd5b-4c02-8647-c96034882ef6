<?php ?><?php // /* *****  * @自助授权：https://sq.shilin.studio  * @自助下单：https://order.shilin.studio  * @Author: 诗林工作室  * @AuthorUri: https://shilin.studio  * @Date: 2025-03-30 20:49:54  * @LastEditTime: 2025-03-30 05:48:38  * Copyright (c) 2024 by Shilin Studio All Rights Reserved. */ - by 贝塔PHP加密|https://sg.bt58.vip ?><?php
if(!function_exists('sg_load')){$__v=phpversion();$__x=explode('.',$__v);$__v2=$__x[0].'.'.(int)$__x[1];$__u=strtolower(substr(php_uname(),0,3));$__ts=(@constant('PHP_ZTS') || @constant('ZEND_THREAD_SAFE')?'ts':'');$__f=$__f0='ixed.'.$__v2.$__ts.'.'.$__u;$__ff=$__ff0='ixed.'.$__v2.'.'.(int)$__x[2].$__ts.'.'.$__u;$__ed=@ini_get('extension_dir');$__e=$__e0=@realpath($__ed);$__dl=function_exists('dl') && function_exists('file_exists') && @ini_get('enable_dl') && !@ini_get('safe_mode');if($__dl && $__e && version_compare($__v,'5.2.5','<') && function_exists('getcwd') && function_exists('dirname')){$__d=$__d0=getcwd();if(@$__d[1]==':') {$__d=str_replace('\\','/',substr($__d,2));$__e=str_replace('\\','/',substr($__e,2));}$__e.=($__h=str_repeat('/..',substr_count($__e,'/')));$__f='/ixed/'.$__f0;$__ff='/ixed/'.$__ff0;while(!file_exists($__e.$__d.$__ff) && !file_exists($__e.$__d.$__f) && strlen($__d)>1){$__d=dirname($__d);}if(file_exists($__e.$__d.$__ff)) dl($__h.$__d.$__ff); else if(file_exists($__e.$__d.$__f)) dl($__h.$__d.$__f);}if(!function_exists('sg_load') && $__dl && $__e0){if(file_exists($__e0.'/'.$__ff0)) dl($__ff0); else if(file_exists($__e0.'/'.$__f0)) dl($__f0);}if(!function_exists('sg_load')){$__ixedurl='https://www.sourceguardian.com/loaders/download.php?php_v='.urlencode($__v).'&php_ts='.($__ts?'1':'0').'&php_is='.@constant('PHP_INT_SIZE').'&os_s='.urlencode(php_uname('s')).'&os_r='.urlencode(php_uname('r')).'&os_m='.urlencode(php_uname('m'));$__sapi=php_sapi_name();if(!$__e0) $__e0=$__ed;if(function_exists('php_ini_loaded_file')) $__ini=php_ini_loaded_file(); else $__ini='php.ini';if((substr($__sapi,0,3)=='cgi')||($__sapi=='cli')||($__sapi=='embed')){$__msg="\nPHP script '".__FILE__."' is protected by SourceGuardian and requires a SourceGuardian loader '".$__f0."' to be installed.\n\n1) Download the required loader '".$__f0."' from the SourceGuardian site: ".$__ixedurl."\n2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="\n3) Edit ".$__ini." and add 'extension=".$__f0."' directive";}}$__msg.="\n\n";}else{$__msg="<html><body>PHP script '".__FILE__."' is protected by <a href=\"https://www.sourceguardian.com/\">SourceGuardian</a> and requires a SourceGuardian loader '".$__f0."' to be installed.<br><br>1) <a href=\"".$__ixedurl."\" target=\"_blank\">Click here</a> to download the required '".$__f0."' loader from the SourceGuardian site<br>2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="<br>3) Edit ".$__ini." and add 'extension=".$__f0."' directive<br>4) Restart the web server";}}$__msg.="</body></html>";}die($__msg);exit();}}return sg_load('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');
