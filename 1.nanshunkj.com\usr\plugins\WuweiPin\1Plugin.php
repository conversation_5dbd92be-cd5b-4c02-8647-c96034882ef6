<?php
/**
 * 文章置顶插件
 * 
 * @package WuweiPin
 * <AUTHOR>
 * @version 1.4.0
 * @link https://bolg.nanshunkj.com
 */
class WuweiPin_Plugin implements Typecho_Plugin_Interface
{
    /**
     * 激活插件方法
     */
    public static function activate()
    {
        Typecho_Plugin::factory('Widget_Archive')->header = array('WuweiPin_Plugin', 'headerScript');
        Typecho_Plugin::factory('Widget_Archive')->indexHandle = array('WuweiPin_Plugin', 'sticky');
        Typecho_Plugin::factory('Widget_Archive')->categoryHandle = array('WuweiPin_Plugin', 'stickyC');
        Typecho_Plugin::factory('admin/write-post.php')->option = array('WuweiPin_Plugin', 'addStickyOption');
        Typecho_Plugin::factory('Widget_Contents_Post_Edit')->finishPublish = array('WuweiPin_Plugin', 'updateStickyStatus');
        Typecho_Plugin::factory('Widget_Contents_Post_Edit')->finishSave = array('WuweiPin_Plugin', 'updateStickyStatus');
        
        return _t('插件启用成功');
    }

    /**
     * 禁用插件方法
     */
    public static function deactivate()
    {
        return _t('插件禁用成功');
    }

    /**
     * 获取插件配置面板
     */
    public static function config(Typecho_Widget_Helper_Form $form)
    {
        // 置顶文章ID设置
        $sticky_cids = new Typecho_Widget_Helper_Form_Element_Textarea(
            'sticky_cids', 
            NULL, 
            '',
            '置顶文章ID',
            '输入要置顶的文章ID，多个ID用英文逗号分隔'
        );
        $form->addInput($sticky_cids);

        // 置顶样式选择
        $style_type = new Typecho_Widget_Helper_Form_Element_Select(
            'style_type',
            array(
                'ribbon' => '丝带样式',
                'corner' => '斜角丝带',
                'dot' => '圆点样式'
            ),
            'ribbon',
            '置顶样式',
            '选择置顶文章的显示样式'
        );
        $form->addInput($style_type);

        // 置顶标记文字
        $sticky_text = new Typecho_Widget_Helper_Form_Element_Text(
            'sticky_text',
            NULL,
            '置顶',
            '置顶标记文字',
            '自定义置顶标记显示的文字，默认为"置顶"'
        );
        $form->addInput($sticky_text);

        // 添加分类页显示选项
        $show_in_category = new Typecho_Widget_Helper_Form_Element_Radio(
            'show_in_category',
            array(
                '0' => '不显示',
                '1' => '显示'
            ),
            '0',
            '分类页面显示',
            '置顶文章是否在分类页面中显示'
        );
        $form->addInput($show_in_category);
    }

    /**
     * 个人用户的配置面板
     */
    public static function personalConfig(Typecho_Widget_Helper_Form $form){}

    /**
     * 在文章编辑页面添加置顶选项
     */
    public static function addStickyOption($post)
    {
        $config = Typecho_Widget::widget('Widget_Options')->plugin('WuweiPin');
        $sticky_cids = $config->sticky_cids ? explode(',', strtr($config->sticky_cids, ' ', ',')) : array();
        $checked = in_array($post->cid, $sticky_cids) ? ' checked' : '';
        
        echo '<section class="typecho-post-option">
                <label for="sticky" class="typecho-label">文章置顶</label>
                <p><input type="checkbox" id="sticky" name="sticky" value="1"'.$checked.' />
                   <label for="sticky">将该文章置顶</label></p>
              </section>';
    }

    /**
     * 处理置顶文章
     */
    public static function sticky($archive, $select)
    {
        $config = Typecho_Widget::widget('Widget_Options')->plugin('WuweiPin');
        self::handleSticky($archive, $select, $config);
    }

    /**
     * 分类页置顶处理
     */
    public static function stickyC($archive, $select)
    {
        $config = Typecho_Widget::widget('Widget_Options')->plugin('WuweiPin');
        self::handleSticky($archive, $select, $config);
    }

    /**
     * 处理置顶状态更新
     */
    public static function updateStickyStatus($contents, $post)
    {
        $db = Typecho_Db::get();
        $options = Helper::options();
        $pluginName = 'WuweiPin';
        
        // 获取现有配置
        $row = $db->fetchRow($db->select()->from('table.options')
            ->where('name = ?', 'plugin:' . $pluginName));
        
        if (empty($row)) {
            return;
        }
        
        // 解析现有配置
        $options = unserialize($row['value']);
        if (!is_array($options)) {
            $options = array();
        }
        
        $sticky_cids = isset($options['sticky_cids']) ? 
            explode(',', trim($options['sticky_cids'])) : array();
        $sticky_cids = array_filter($sticky_cids); // 移除空值
        
        $cid = $post->cid;
        
        if (isset($_POST['sticky']) && $_POST['sticky'] == '1') {
            if (!in_array($cid, $sticky_cids)) {
                $sticky_cids[] = $cid;
            }
        } else {
            $sticky_cids = array_diff($sticky_cids, array($cid));
        }
        
        $sticky_cids = array_values(array_unique(array_filter($sticky_cids)));
        
        // 更新配置
        $options['sticky_cids'] = implode(',', $sticky_cids);
        
        try {
            $db->query($db->update('table.options')
                ->rows(array('value' => serialize($options)))
                ->where('name = ?', 'plugin:' . $pluginName));
        } catch (Exception $e) {
            error_log("WuweiPin插件错误 - 更新顶状态失败: " . $e->getMessage());
        }
    }

    /**
     * 处理置顶文章的核心法
     */
    private static function handleSticky($archive, $select, $config)
    {
        $sticky_cids = $config->sticky_cids ? explode(',', strtr($config->sticky_cids, ' ', ',')) : array();
        if (empty($sticky_cids)) return;

        $db = Typecho_Db::get();
        $paded = $archive->request->get('page', 1);
        
        // 判断是否为分类页面
        $isCategory = $archive->is('category');
        
        foreach($sticky_cids as $cid) {
            if (!$cid) continue;
            
            try {
                // 如果是分类页面且设置为不显示，则跳过
                if ($isCategory && $config->show_in_category == '0') {
                    $select->where('table.contents.cid != ?', $cid);
                    continue;
                }

                $sticky_post = $db->fetchRow($db->select()->from('table.contents')
                    ->where('cid = ?', $cid)
                    ->where('type = ?', 'post')
                    ->where('status = ?', 'publish'));
                
                if ($sticky_post) {
                    if ($paded == 1) {
                        $sticky_post['isSticky'] = true;
                        $archive->push($sticky_post);
                    }
                    $select->where('table.contents.cid != ?', $cid);
                }
            } catch (Exception $e) {
                error_log("WuweiPin插件错误: " . $e->getMessage());
            }
        }
    }

    /**
     * 注入CSS样式到头部
     * 包含三种置顶样式：斜角丝带、丝带、圆点
     */
    public static function headerScript()
    {
        $config = Typecho_Widget::widget('Widget_Options')->plugin('WuweiPin');
        $style_type = isset($config->style_type) ? $config->style_type : 'ribbon';

        // 定义主题色变量，便于统一管理
        $primary_color = '#ff4d4f';      // 普通模式下的主题色
        $dark_primary_color = '#ff6b6b';  // 深色模式下的主题色
        
        echo '<style>';
        
        // 基础样式：为所有置顶文章添加相对定位
        echo <<<EOT
.sticky-post {
    position: relative !important;
}
EOT;

        // 根据选择的样式类型输出特定样式
        switch($style_type) {
            case 'corner':
                // 斜角丝带样式：在文章右上角显示一个带背景的斜角丝带
                echo <<<EOT
.sticky-post {
    overflow: hidden !important;  /* 确保斜角丝带不超出文章卡片 */
}
.sticky-mark {
    position: absolute !important;  /* 绝对定位 */
    top: 0 !important;
    right: 0 !important;
    width: 80px !important;        /* 设置丝带容器大小 */
    height: 80px !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}
.sticky-mark::before {
    content: attr(data-text) !important;  /* 使用data-text属性作为文字内容 */
    position: absolute !important;
    top: 20px !important;
    right: -20px !important;
    width: 100px !important;
    height: 24px !important;
    line-height: 24px !important;
    background: {$primary_color} !important;
    color: white !important;
    text-align: center !important;
    font-size: 12px !important;
    font-weight: bold !important;
    transform: rotate(45deg) !important;  /* 旋转45度 */
    transform-origin: center !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;  /* 添加阴影效果 */
}
.sticky-mark::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 0 !important;
    height: 0 !important;
    border-style: solid !important;
    border-width: 0 80px 80px 0 !important;  /* 创建三角形背景 */
    border-color: transparent {$primary_color} transparent transparent !important;
    opacity: 0.1 !important;  /* 设置透明度创建淡色背景 */
}
.dark .sticky-mark::before {
    background: {$dark_primary_color} !important;  /* 深色模式适配 */
}
.dark .sticky-mark::after {
    border-color: transparent {$dark_primary_color} transparent transparent !important;
}
EOT;
                break;
                
            case 'ribbon':
                // 丝带样式：在文章左上角显示一个带折角的丝带
                echo <<<EOT
.sticky-mark {
    display: inline-block !important;
    position: absolute !important;
    top: 0 !important;
    left: -8px !important;  /* 负边距使丝带突出文章卡片 */
    padding: 4px 8px !important;
    background: {$primary_color} !important;
    color: white !important;
    font-size: 12px !important;
    font-weight: bold !important;
    border-radius: 0 4px 4px 0 !important;  /* 右侧圆角 */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}
.sticky-mark::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    bottom: -8px !important;
    border-top: 8px solid #cc1f1a !important;  /* 创建折角效果 */
    border-left: 8px solid transparent !important;
}
.dark .sticky-mark {
    background: {$dark_primary_color} !important;
    color: white !important;
}
EOT;
                break;
                
            case 'dot':
                // 圆点样式：在文章标题前显示一个带圆点的文字标记
                echo <<<EOT
.sticky-mark {
    display: inline-block !important;
    font-size: clamp(15px, 1.5vw, 16px) !important;  /* 响应式字体大小 */
    font-weight: 600 !important;
    line-height: 28px !important;  /* 与文章标题行高保持一致 */
    color: {$primary_color} !important;
    margin-right: 6px !important;
}
.sticky-mark::before {
    content: '' !important;
    display: inline-block !important;
    width: 6px !important;
    height: 6px !important;
    background: currentColor !important;  /* 使用当前文字颜色 */
    border-radius: 50% !important;  /* 创建圆点 */
    margin-right: 4px !important;
    position: relative !important;
    top: -1px !important;  /* 微调圆点位置使其与文字对齐 */
}
.dark .sticky-mark {
    color: {$dark_primary_color} !important;  /* 深色模式适配 */
}
EOT;
                break;
        }

        echo '</style>';
    }
}