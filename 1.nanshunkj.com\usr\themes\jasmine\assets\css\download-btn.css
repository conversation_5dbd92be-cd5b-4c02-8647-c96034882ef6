.download-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    margin: 5px;
    color: white !important;
    border-radius: 6px;
    text-decoration: none !important;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    line-height: 1.5;
    white-space: nowrap;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
    opacity: 0.9;
    color: white !important;
}

.download-btn svg {
    width: 18px;
    height: 18px;
    margin-right: 6px;
    flex-shrink: 0;
}

/* 暗色模式下的阴影调整 */
.dark .download-btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark .download-btn:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4);
    color: white !important;
}

/* 确保在 Markdown 内容中正确显示 */
.markdown-body .download-btn {
    display: inline-flex !important;
    align-items: center !important;
    padding: 8px 16px !important;
    margin: 5px !important;
    color: white !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    white-space: nowrap !important;
}

.markdown-body .download-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15) !important;
    opacity: 0.9 !important;
    color: white !important;
}

.markdown-body .download-btn svg {
    width: 18px !important;
    height: 18px !important;
    margin-right: 6px !important;
    flex-shrink: 0 !important;
}

/* 暗色模式下的阴影调整 */
.dark .markdown-body .download-btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.dark .markdown-body .download-btn:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4) !important;
    color: white !important;
}

/* 下载次数样式 */
.download-count {
    font-size: 0.85em;
    margin-left: 6px;
    opacity: 0.9;
    font-weight: normal;
    display: inline-flex;
    align-items: center;
}

/* 确保在 Markdown 内容中正确显示下载次数 */
.markdown-body .download-count {
    font-size: 0.85em !important;
    margin-left: 6px !important;
    opacity: 0.9 !important;
    font-weight: normal !important;
    display: inline-flex !important;
    align-items: center !important;
}

/* 暗色模式下的下载次数样式调整 */
.dark .download-count {
    opacity: 0.95;
}

.dark .markdown-body .download-count {
    opacity: 0.95 !important;
} 