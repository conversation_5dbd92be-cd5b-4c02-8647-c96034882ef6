# WW Style 主题 - 图片资源说明

## 图片资源文件清单

主题当前包含的图片文件都是0字节的占位文件，**必须替换为有实际内容的图片**，否则将无法正常显示：

1. **default.jpg** - 文章默认封面图（推荐尺寸：800x400像素）
2. **slide1.jpg, slide2.jpg, slide3.jpg** - 首页轮播图（推荐尺寸：1920x600像素）
3. **logo.png** - 网站Logo（推荐尺寸：200x50像素，透明背景）
4. **qrcode-alipay.png** - 支付宝支付二维码（推荐尺寸：300x300像素）
5. **qrcode-wechat.png** - 微信支付二维码（推荐尺寸：300x300像素）
6. **qrcode-bank.png** - 银行卡支付二维码（推荐尺寸：300x300像素）
7. **avatar-default.png** - 默认用户头像（推荐尺寸：200x200像素）

## 临时解决方案

在您准备好实际图片之前，可以使用在线占位图服务：

1. 使用 [Placeholder.com](https://placeholder.com/) 生成占位图
2. 使用格式：`https://via.placeholder.com/尺寸?text=文字`
   例如：`https://via.placeholder.com/800x400?text=文章默认封面`

## 在代码中临时使用外部图片URL

您可以在主题设置中填入这些URL作为临时图片：

1. 轮播图示例：
   - `https://via.placeholder.com/1920x600?text=首页轮播图1`
   - `https://via.placeholder.com/1920x600?text=首页轮播图2`
   - `https://via.placeholder.com/1920x600?text=首页轮播图3`

2. 默认封面图：
   - `https://via.placeholder.com/800x400?text=默认封面`

3. 支付二维码：
   - `https://via.placeholder.com/300x300?text=支付宝收款码`
   - `https://via.placeholder.com/300x300?text=微信收款码`

## 上传图片的方法

1. 通过FTP上传图片到 `themes/ww_style/assets/img/` 目录
2. 请确保图片文件名与占位文件名完全一致
3. 图片格式建议使用JPG、PNG或WebP，文件大小建议控制在200KB以内

## 注意事项

1. 所有图片必须拥有合法的使用权或版权
2. 轮播图如果尺寸太大会影响网站加载速度，建议优化处理
3. 建议为不同设备准备不同尺寸的图片，以提升移动端体验
4. 如需更改图片文件名，请同时修改对应的代码引用

## 抖音风格设计建议

本主题采用抖音风格设计，主要特点是：

- 黑色背景：#000000（主背景色）
- 红色强调：#FE2C55（主要按钮、链接和强调元素）
- 白色文字：#FFFFFF（主要文字颜色）
- 灰色文字：#AAAAAA（次要文字颜色）
- 亮灰背景：#222222（卡片、面板背景色）

## 获取图片资源的方法

1. 使用设计软件（如Photoshop、Figma等）创建符合以上规格的图片
2. 使用在线图片生成服务，如：
   - https://placeholder.com/
   - https://via.placeholder.com/800x400?text=Default+Cover
   - https://dummyimage.com/
3. 使用AI图像生成工具创建相应的图片
4. 购买或下载免费的图片资源

注意：在实际部署前，请确保替换所有占位图片为实际使用的图片资源。 