<?php
require_once __DIR__ . '/app/Services/BaiduPushService.php';
require_once __DIR__ . '/../../../config.inc.php';  // 引入 Typecho 配置文件

// 获取最近一周文章的URL列表
$db = Typecho_Db::get();
$prefix = $db->getPrefix();

// 获取最近一周的文章
$lastWeek = date('Y-m-d', strtotime('-1 week'));
$sql = $db->select('cid', 'slug')
    ->from('table.contents')
    ->where('type = ?', 'post')
    ->where('status = ?', 'publish')
    ->where('created >= ?', strtotime($lastWeek))
    ->where('slug != ?', '')  // 过滤掉空的 slug
    ->order('created', Typecho_Db::SORT_DESC)
    ->limit(10);  // 进一步减少每次处理的数量

$posts = $db->fetchAll($sql);

// 构建URL列表
$urls = array();
foreach ($posts as $post) {
    if (!empty($post['slug'])) {  // 再次确认 slug 不为空
        $urls[] = 'https://8ww.fun/' . $post['slug'] . '.html';
    }
}

if (empty($urls)) {
    echo "No articles in the last week.\n";
    exit;
}

// 创建服务实例并推送
$baiduService = new BaiduPushService();
$result = $baiduService->pushUrls($urls);

// 处理推送结果
if (isset($result['error'])) {
    echo "Error: " . $result['message'] . "\n";
    if ($result['message'] === 'over quota') {
        echo "Daily quota exceeded. Try again tomorrow.\n";
    }
} else {
    echo "Successfully pushed URLs to Baidu.\n";
    if (isset($result['success'])) {
        echo "Success count: " . $result['success'] . "\n";
    }
    if (isset($result['remain'])) {
        echo "Remaining quota: " . $result['remain'] . "\n";
    }
}

echo "\nPushed URLs (" . count($urls) . "):\n";
foreach ($urls as $url) {
    echo $url . "\n";
} 