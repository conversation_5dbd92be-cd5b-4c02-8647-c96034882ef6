!function($){var Timepicker,isEmptyObject,extendRemove,detectSupport,convert24to12,computeEffectiveSetting,splitDateTime,parseDateTimeInternal,selectLocalTimezone;$.ui.timepicker=$.ui.timepicker||{},$.ui.timepicker.version||($.extend($.ui,{timepicker:{version:"1.4"}}),Timepicker=function(){this.regional=[],this.regional[""]={currentText:"Now",closeText:"Done",amNames:["AM","A"],pmNames:["PM","P"],timeFormat:"HH:mm",timeSuffix:"",timeOnlyTitle:"Choose Time",timeText:"Time",hourText:"Hour",minuteText:"Minute",secondText:"Second",millisecText:"Millisecond",microsecText:"Microsecond",timezoneText:"Time Zone",isRTL:!1},this._defaults={showButtonPanel:!0,timeOnly:!1,showHour:null,showMinute:null,showSecond:null,showMillisec:null,showMicrosec:null,showTimezone:null,showTime:!0,stepHour:1,stepMinute:1,stepSecond:1,stepMillisec:1,stepMicrosec:1,hour:0,minute:0,second:0,millisec:0,microsec:0,timezone:null,hourMin:0,minuteMin:0,secondMin:0,millisecMin:0,microsecMin:0,hourMax:23,minuteMax:59,secondMax:59,millisecMax:999,microsecMax:999,minDateTime:null,maxDateTime:null,onSelect:null,hourGrid:0,minuteGrid:0,secondGrid:0,millisecGrid:0,microsecGrid:0,alwaysSetTime:!0,separator:" ",altFieldTimeOnly:!0,altTimeFormat:null,altSeparator:null,altTimeSuffix:null,pickerTimeFormat:null,pickerTimeSuffix:null,showTimepicker:!0,timezoneList:null,addSliderAccess:!1,sliderAccessArgs:null,controlType:"slider",defaultValue:null,parse:"strict"},$.extend(this._defaults,this.regional[""])},$.extend(Timepicker.prototype,{$input:null,$altInput:null,$timeObj:null,inst:null,hour_slider:null,minute_slider:null,second_slider:null,millisec_slider:null,microsec_slider:null,timezone_select:null,hour:0,minute:0,second:0,millisec:0,microsec:0,timezone:null,hourMinOriginal:null,minuteMinOriginal:null,secondMinOriginal:null,millisecMinOriginal:null,microsecMinOriginal:null,hourMaxOriginal:null,minuteMaxOriginal:null,secondMaxOriginal:null,millisecMaxOriginal:null,microsecMaxOriginal:null,ampm:"",formattedDate:"",formattedTime:"",formattedDateTime:"",timezoneList:null,units:["hour","minute","second","millisec","microsec"],support:{},control:null,setDefaults:function(e){return extendRemove(this._defaults,e||{}),this},_newInst:function($input,opts){var tp_inst=new Timepicker,inlineSettings={},fns={},overrides,i,attrName;for(attrName in this._defaults)if(this._defaults.hasOwnProperty(attrName)){var attrValue=$input.attr("time:"+attrName);if(attrValue)try{inlineSettings[attrName]=eval(attrValue)}catch(err){inlineSettings[attrName]=attrValue}}for(i in overrides={beforeShow:function(e,t){if($.isFunction(tp_inst._defaults.evnts.beforeShow))return tp_inst._defaults.evnts.beforeShow.call($input[0],e,t,tp_inst)},onChangeMonthYear:function(e,t,i){tp_inst._updateDateTime(i),$.isFunction(tp_inst._defaults.evnts.onChangeMonthYear)&&tp_inst._defaults.evnts.onChangeMonthYear.call($input[0],e,t,i,tp_inst)},onClose:function(e,t){!0===tp_inst.timeDefined&&""!==$input.val()&&tp_inst._updateDateTime(t),$.isFunction(tp_inst._defaults.evnts.onClose)&&tp_inst._defaults.evnts.onClose.call($input[0],e,t,tp_inst)}},overrides)overrides.hasOwnProperty(i)&&(fns[i]=opts[i]||null);tp_inst._defaults=$.extend({},this._defaults,inlineSettings,opts,overrides,{evnts:fns,timepicker:tp_inst}),tp_inst.amNames=$.map(tp_inst._defaults.amNames,function(e){return e.toUpperCase()}),tp_inst.pmNames=$.map(tp_inst._defaults.pmNames,function(e){return e.toUpperCase()}),tp_inst.support=detectSupport(tp_inst._defaults.timeFormat+(tp_inst._defaults.pickerTimeFormat||"")+(tp_inst._defaults.altTimeFormat||"")),"string"==typeof tp_inst._defaults.controlType?("slider"===tp_inst._defaults.controlType&&void 0===$.ui.slider&&(tp_inst._defaults.controlType="select"),tp_inst.control=tp_inst._controls[tp_inst._defaults.controlType]):tp_inst.control=tp_inst._defaults.controlType;var timezoneList=[-720,-660,-600,-570,-540,-480,-420,-360,-300,-270,-240,-210,-180,-120,-60,0,60,120,180,210,240,270,300,330,345,360,390,420,480,525,540,570,600,630,660,690,720,765,780,840];null!==tp_inst._defaults.timezoneList&&(timezoneList=tp_inst._defaults.timezoneList);var tzl=timezoneList.length,tzi=0,tzv=null;if(0<tzl&&"object"!=typeof timezoneList[0])for(;tzi<tzl;tzi++)tzv=timezoneList[tzi],timezoneList[tzi]={value:tzv,label:$.timepicker.timezoneOffsetString(tzv,tp_inst.support.iso8601)};return tp_inst._defaults.timezoneList=timezoneList,tp_inst.timezone=null!==tp_inst._defaults.timezone?$.timepicker.timezoneOffsetNumber(tp_inst._defaults.timezone):-1*(new Date).getTimezoneOffset(),tp_inst.hour=tp_inst._defaults.hour<tp_inst._defaults.hourMin?tp_inst._defaults.hourMin:tp_inst._defaults.hour>tp_inst._defaults.hourMax?tp_inst._defaults.hourMax:tp_inst._defaults.hour,tp_inst.minute=tp_inst._defaults.minute<tp_inst._defaults.minuteMin?tp_inst._defaults.minuteMin:tp_inst._defaults.minute>tp_inst._defaults.minuteMax?tp_inst._defaults.minuteMax:tp_inst._defaults.minute,tp_inst.second=tp_inst._defaults.second<tp_inst._defaults.secondMin?tp_inst._defaults.secondMin:tp_inst._defaults.second>tp_inst._defaults.secondMax?tp_inst._defaults.secondMax:tp_inst._defaults.second,tp_inst.millisec=tp_inst._defaults.millisec<tp_inst._defaults.millisecMin?tp_inst._defaults.millisecMin:tp_inst._defaults.millisec>tp_inst._defaults.millisecMax?tp_inst._defaults.millisecMax:tp_inst._defaults.millisec,tp_inst.microsec=tp_inst._defaults.microsec<tp_inst._defaults.microsecMin?tp_inst._defaults.microsecMin:tp_inst._defaults.microsec>tp_inst._defaults.microsecMax?tp_inst._defaults.microsecMax:tp_inst._defaults.microsec,tp_inst.ampm="",tp_inst.$input=$input,tp_inst._defaults.altField&&(tp_inst.$altInput=$(tp_inst._defaults.altField).css({cursor:"pointer"}).focus(function(){$input.trigger("focus")})),0!==tp_inst._defaults.minDate&&0!==tp_inst._defaults.minDateTime||(tp_inst._defaults.minDate=new Date),0!==tp_inst._defaults.maxDate&&0!==tp_inst._defaults.maxDateTime||(tp_inst._defaults.maxDate=new Date),void 0!==tp_inst._defaults.minDate&&tp_inst._defaults.minDate instanceof Date&&(tp_inst._defaults.minDateTime=new Date(tp_inst._defaults.minDate.getTime())),void 0!==tp_inst._defaults.minDateTime&&tp_inst._defaults.minDateTime instanceof Date&&(tp_inst._defaults.minDate=new Date(tp_inst._defaults.minDateTime.getTime())),void 0!==tp_inst._defaults.maxDate&&tp_inst._defaults.maxDate instanceof Date&&(tp_inst._defaults.maxDateTime=new Date(tp_inst._defaults.maxDate.getTime())),void 0!==tp_inst._defaults.maxDateTime&&tp_inst._defaults.maxDateTime instanceof Date&&(tp_inst._defaults.maxDate=new Date(tp_inst._defaults.maxDateTime.getTime())),tp_inst.$input.bind("focus",function(){tp_inst._onFocus()}),tp_inst},_addTimePicker:function(e){var t=this.$altInput&&this._defaults.altFieldTimeOnly?this.$input.val()+" "+this.$altInput.val():this.$input.val();this.timeDefined=this._parseTime(t),this._limitMinMaxDateTime(e,!1),this._injectTimePicker()},_parseTime:function(t,i){if(this.inst||(this.inst=$.datepicker._getInst(this.$input[0])),i||!this._defaults.timeOnly){i=$.datepicker._get(this.inst,"dateFormat");try{var e=parseDateTimeInternal(i,this._defaults.timeFormat,t,$.datepicker._getFormatConfig(this.inst),this._defaults);if(!e.timeObj)return!1;$.extend(this,e.timeObj)}catch(e){return $.timepicker.log("Error parsing the date/time string: "+e+"\ndate/time string = "+t+"\ntimeFormat = "+this._defaults.timeFormat+"\ndateFormat = "+i),!1}return!0}t=$.datepicker.parseTime(this._defaults.timeFormat,t,this._defaults);return!!t&&($.extend(this,t),!0)},_injectTimePicker:function(){var e,t=this.inst.dpDiv,i=this.inst.settings,n=this,a="",s="",r=null,l={},o={},c=0,m=0;if(0===t.find("div.ui-timepicker-div").length&&i.showTimepicker){for(var u=' style="display:none;"',d='<div class="ui-timepicker-div'+(i.isRTL?" ui-timepicker-rtl":"")+'"><dl><dt class="ui_tpicker_time_label"'+(i.showTime?"":u)+">"+i.timeText+'</dt><dd class="ui_tpicker_time"'+(i.showTime?"":u)+"></dd>",c=0,m=this.units.length;c<m;c++){if(r=null!==i["show"+(s=(a=this.units[c]).substr(0,1).toUpperCase()+a.substr(1))]?i["show"+s]:this.support[a],l[a]=parseInt(i[a+"Max"]-(i[a+"Max"]-i[a+"Min"])%i["step"+s],10),o[a]=0,d+='<dt class="ui_tpicker_'+a+'_label"'+(r?"":u)+">"+i[a+"Text"]+'</dt><dd class="ui_tpicker_'+a+'"><div class="ui_tpicker_'+a+'_slider"'+(r?"":u)+"></div>",r&&0<i[a+"Grid"]){if(d+='<div style="padding-left: 1px"><table class="ui-tpicker-grid-label"><tr>',"hour"===a)for(var p=i[a+"Min"];p<=l[a];p+=parseInt(i[a+"Grid"],10)){o[a]++;var h=$.datepicker.formatTime(this.support.ampm?"hht":"HH",{hour:p},i);d+='<td data-for="'+a+'">'+h+"</td>"}else for(var _=i[a+"Min"];_<=l[a];_+=parseInt(i[a+"Grid"],10))o[a]++,d+='<td data-for="'+a+'">'+(_<10?"0":"")+_+"</td>";d+="</tr></table></div>"}d+="</dd>"}var f=null!==i.showTimezone?i.showTimezone:this.support.timezone;d+='<dt class="ui_tpicker_timezone_label"'+(f?"":u)+">"+i.timezoneText+"</dt>",d+='<dd class="ui_tpicker_timezone" '+(f?"":u)+"></dd>";var g=$(d+="</dl></div>");for(!0===i.timeOnly&&(g.prepend('<div class="ui-widget-header ui-helper-clearfix ui-corner-all"><div class="ui-datepicker-title">'+i.timeOnlyTitle+"</div></div>"),t.find(".ui-datepicker-header, .ui-datepicker-calendar").hide()),c=0,m=n.units.length;c<m;c++)r=null!==i["show"+(s=(a=n.units[c]).substr(0,1).toUpperCase()+a.substr(1))]?i["show"+s]:this.support[a],n[a+"_slider"]=n.control.create(n,g.find(".ui_tpicker_"+a+"_slider"),a,n[a],i[a+"Min"],l[a],i["step"+s]),r&&0<i[a+"Grid"]&&(e=100*o[a]*i[a+"Grid"]/(l[a]-i[a+"Min"]),g.find(".ui_tpicker_"+a+" table").css({width:e+"%",marginLeft:i.isRTL?"0":e/(-2*o[a])+"%",marginRight:i.isRTL?e/(-2*o[a])+"%":"0",borderCollapse:"collapse"}).find("td").click(function(e){var t=$(this),i=t.html(),s=parseInt(i.replace(/[^0-9]/g),10),i=i.replace(/[^apm]/gi),t=t.data("for");"hour"===t&&(-1!==i.indexOf("p")&&s<12?s+=12:-1!==i.indexOf("a")&&12===s&&(s=0)),n.control.value(n,n[t+"_slider"],a,s),n._onTimeChange(),n._onSelectHandler()}).css({cursor:"pointer",width:100/o[a]+"%",textAlign:"center",overflow:"hidden"}));this.timezone_select=g.find(".ui_tpicker_timezone").append("<select></select>").find("select"),$.fn.append.apply(this.timezone_select,$.map(i.timezoneList,function(e,t){return $("<option />").val("object"==typeof e?e.value:e).text("object"==typeof e?e.label:e)})),void 0!==this.timezone&&null!==this.timezone&&""!==this.timezone?-1*new Date(this.inst.selectedYear,this.inst.selectedMonth,this.inst.selectedDay,12).getTimezoneOffset()===this.timezone?selectLocalTimezone(n):this.timezone_select.val(this.timezone):void 0!==this.hour&&null!==this.hour&&""!==this.hour?this.timezone_select.val(i.timezone):selectLocalTimezone(n),this.timezone_select.change(function(){n._onTimeChange(),n._onSelectHandler()});var M,k,f=t.find(".ui-datepicker-buttonpane");f.length?f.before(g):t.append(g),this.$timeObj=g.find(".ui_tpicker_time"),null!==this.inst&&(t=this.timeDefined,this._onTimeChange(),this.timeDefined=t),this._defaults.addSliderAccess&&(M=this._defaults.sliderAccessArgs,k=this._defaults.isRTL,M.isRTL=k,setTimeout(function(){var a;0===g.find(".ui-slider-access").length&&(g.find(".ui-slider:visible").sliderAccess(M),(a=g.find(".ui-slider-access:eq(0)").outerWidth(!0))&&g.find("table:visible").each(function(){var e=$(this),t=e.outerWidth(),i=e.css(k?"marginRight":"marginLeft").toString().replace("%",""),s=t-a,n={width:s,marginRight:0,marginLeft:0};n[k?"marginRight":"marginLeft"]=i*s/t+"%",e.css(n)}))},10)),n._limitMinMaxDateTime(this.inst,!0)}},_limitMinMaxDateTime:function(e,t){var i,s,n,a,r=this._defaults,l=new Date(e.selectedYear,e.selectedMonth,e.selectedDay);this._defaults.showTimepicker&&(null!==$.datepicker._get(e,"minDateTime")&&void 0!==$.datepicker._get(e,"minDateTime")&&l&&(i=$.datepicker._get(e,"minDateTime"),s=new Date(i.getFullYear(),i.getMonth(),i.getDate(),0,0,0,0),null!==this.hourMinOriginal&&null!==this.minuteMinOriginal&&null!==this.secondMinOriginal&&null!==this.millisecMinOriginal&&null!==this.microsecMinOriginal||(this.hourMinOriginal=r.hourMin,this.minuteMinOriginal=r.minuteMin,this.secondMinOriginal=r.secondMin,this.millisecMinOriginal=r.millisecMin,this.microsecMinOriginal=r.microsecMin),e.settings.timeOnly||s.getTime()===l.getTime()?(this._defaults.hourMin=i.getHours(),this.hour<=this._defaults.hourMin?(this.hour=this._defaults.hourMin,this._defaults.minuteMin=i.getMinutes(),this.minute<=this._defaults.minuteMin?(this.minute=this._defaults.minuteMin,this._defaults.secondMin=i.getSeconds(),this.second<=this._defaults.secondMin?(this.second=this._defaults.secondMin,this._defaults.millisecMin=i.getMilliseconds(),this.millisec<=this._defaults.millisecMin?(this.millisec=this._defaults.millisecMin,this._defaults.microsecMin=i.getMicroseconds()):(this.microsec<this._defaults.microsecMin&&(this.microsec=this._defaults.microsecMin),this._defaults.microsecMin=this.microsecMinOriginal)):(this._defaults.millisecMin=this.millisecMinOriginal,this._defaults.microsecMin=this.microsecMinOriginal)):(this._defaults.secondMin=this.secondMinOriginal,this._defaults.millisecMin=this.millisecMinOriginal,this._defaults.microsecMin=this.microsecMinOriginal)):(this._defaults.minuteMin=this.minuteMinOriginal,this._defaults.secondMin=this.secondMinOriginal,this._defaults.millisecMin=this.millisecMinOriginal,this._defaults.microsecMin=this.microsecMinOriginal)):(this._defaults.hourMin=this.hourMinOriginal,this._defaults.minuteMin=this.minuteMinOriginal,this._defaults.secondMin=this.secondMinOriginal,this._defaults.millisecMin=this.millisecMinOriginal,this._defaults.microsecMin=this.microsecMinOriginal)),null!==$.datepicker._get(e,"maxDateTime")&&void 0!==$.datepicker._get(e,"maxDateTime")&&l&&(a=$.datepicker._get(e,"maxDateTime"),n=new Date(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0,0),null!==this.hourMaxOriginal&&null!==this.minuteMaxOriginal&&null!==this.secondMaxOriginal&&null!==this.millisecMaxOriginal||(this.hourMaxOriginal=r.hourMax,this.minuteMaxOriginal=r.minuteMax,this.secondMaxOriginal=r.secondMax,this.millisecMaxOriginal=r.millisecMax,this.microsecMaxOriginal=r.microsecMax),e.settings.timeOnly||n.getTime()===l.getTime()?(this._defaults.hourMax=a.getHours(),this.hour>=this._defaults.hourMax?(this.hour=this._defaults.hourMax,this._defaults.minuteMax=a.getMinutes(),this.minute>=this._defaults.minuteMax?(this.minute=this._defaults.minuteMax,this._defaults.secondMax=a.getSeconds(),this.second>=this._defaults.secondMax?(this.second=this._defaults.secondMax,this._defaults.millisecMax=a.getMilliseconds(),this.millisec>=this._defaults.millisecMax?(this.millisec=this._defaults.millisecMax,this._defaults.microsecMax=a.getMicroseconds()):(this.microsec>this._defaults.microsecMax&&(this.microsec=this._defaults.microsecMax),this._defaults.microsecMax=this.microsecMaxOriginal)):(this._defaults.millisecMax=this.millisecMaxOriginal,this._defaults.microsecMax=this.microsecMaxOriginal)):(this._defaults.secondMax=this.secondMaxOriginal,this._defaults.millisecMax=this.millisecMaxOriginal,this._defaults.microsecMax=this.microsecMaxOriginal)):(this._defaults.minuteMax=this.minuteMaxOriginal,this._defaults.secondMax=this.secondMaxOriginal,this._defaults.millisecMax=this.millisecMaxOriginal,this._defaults.microsecMax=this.microsecMaxOriginal)):(this._defaults.hourMax=this.hourMaxOriginal,this._defaults.minuteMax=this.minuteMaxOriginal,this._defaults.secondMax=this.secondMaxOriginal,this._defaults.millisecMax=this.millisecMaxOriginal,this._defaults.microsecMax=this.microsecMaxOriginal)),void 0!==t&&!0===t&&(e=parseInt(this._defaults.hourMax-(this._defaults.hourMax-this._defaults.hourMin)%this._defaults.stepHour,10),n=parseInt(this._defaults.minuteMax-(this._defaults.minuteMax-this._defaults.minuteMin)%this._defaults.stepMinute,10),l=parseInt(this._defaults.secondMax-(this._defaults.secondMax-this._defaults.secondMin)%this._defaults.stepSecond,10),a=parseInt(this._defaults.millisecMax-(this._defaults.millisecMax-this._defaults.millisecMin)%this._defaults.stepMillisec,10),t=parseInt(this._defaults.microsecMax-(this._defaults.microsecMax-this._defaults.microsecMin)%this._defaults.stepMicrosec,10),this.hour_slider&&(this.control.options(this,this.hour_slider,"hour",{min:this._defaults.hourMin,max:e}),this.control.value(this,this.hour_slider,"hour",this.hour-this.hour%this._defaults.stepHour)),this.minute_slider&&(this.control.options(this,this.minute_slider,"minute",{min:this._defaults.minuteMin,max:n}),this.control.value(this,this.minute_slider,"minute",this.minute-this.minute%this._defaults.stepMinute)),this.second_slider&&(this.control.options(this,this.second_slider,"second",{min:this._defaults.secondMin,max:l}),this.control.value(this,this.second_slider,"second",this.second-this.second%this._defaults.stepSecond)),this.millisec_slider&&(this.control.options(this,this.millisec_slider,"millisec",{min:this._defaults.millisecMin,max:a}),this.control.value(this,this.millisec_slider,"millisec",this.millisec-this.millisec%this._defaults.stepMillisec)),this.microsec_slider&&(this.control.options(this,this.microsec_slider,"microsec",{min:this._defaults.microsecMin,max:t}),this.control.value(this,this.microsec_slider,"microsec",this.microsec-this.microsec%this._defaults.stepMicrosec))))},_onTimeChange:function(){var e,t,i,s,n,a,r,l,o,c,m;this._defaults.showTimepicker&&(e=!!this.hour_slider&&this.control.value(this,this.hour_slider,"hour"),t=!!this.minute_slider&&this.control.value(this,this.minute_slider,"minute"),i=!!this.second_slider&&this.control.value(this,this.second_slider,"second"),s=!!this.millisec_slider&&this.control.value(this,this.millisec_slider,"millisec"),n=!!this.microsec_slider&&this.control.value(this,this.microsec_slider,"microsec"),a=!!this.timezone_select&&this.timezone_select.val(),l=(r=this._defaults).pickerTimeFormat||r.timeFormat,o=r.pickerTimeSuffix||r.timeSuffix,"object"==typeof t&&(t=!1),"object"==typeof i&&(i=!1),"object"==typeof s&&(s=!1),"object"==typeof n&&(n=!1),"object"==typeof a&&(a=!1),!1!==(e="object"==typeof e?!1:e)&&(e=parseInt(e,10)),!1!==t&&(t=parseInt(t,10)),!1!==i&&(i=parseInt(i,10)),!1!==s&&(s=parseInt(s,10)),!1!==n&&(n=parseInt(n,10)),c=r[e<12?"amNames":"pmNames"][0],(m=e!==this.hour||t!==this.minute||i!==this.second||s!==this.millisec||n!==this.microsec||0<this.ampm.length&&e<12!=(-1!==$.inArray(this.ampm.toUpperCase(),this.amNames))||null!==this.timezone&&a!==this.timezone)&&(!1!==e&&(this.hour=e),!1!==t&&(this.minute=t),!1!==i&&(this.second=i),!1!==s&&(this.millisec=s),!1!==n&&(this.microsec=n),!1!==a&&(this.timezone=a),this.inst||(this.inst=$.datepicker._getInst(this.$input[0])),this._limitMinMaxDateTime(this.inst,!0)),this.support.ampm&&(this.ampm=c),this.formattedTime=$.datepicker.formatTime(r.timeFormat,this,r),this.$timeObj&&(l===r.timeFormat?this.$timeObj.text(this.formattedTime+o):this.$timeObj.text($.datepicker.formatTime(l,this,r)+o)),this.timeDefined=!0,m&&this._updateDateTime())},_onSelectHandler:function(){var e=this._defaults.onSelect||this.inst.settings.onSelect,t=this.$input?this.$input[0]:null;e&&t&&e.apply(t,[this.formattedDateTime,this])},_updateDateTime:function(e){var t=0<(e=this.inst||e).currentYear?new Date(e.currentYear,e.currentMonth,e.currentDay):new Date(e.selectedYear,e.selectedMonth,e.selectedDay),i=$.datepicker._daylightSavingAdjust(t),s=$.datepicker._get(e,"dateFormat"),n=$.datepicker._getFormatConfig(e),a=null!==i&&this.timeDefined;this.formattedDate=$.datepicker.formatDate(s,null===i?new Date:i,n);t=this.formattedDate;""===e.lastVa&&(e.currentYear=e.selectedYear,e.currentMonth=e.selectedMonth,e.currentDay=e.selectedDay),!0===this._defaults.timeOnly?t=this.formattedTime:!0!==this._defaults.timeOnly&&(this._defaults.alwaysSetTime||a)&&(t+=this._defaults.separator+this.formattedTime+this._defaults.timeSuffix),this.formattedDateTime=t,this._defaults.showTimepicker?this.$altInput&&!1===this._defaults.timeOnly&&!0===this._defaults.altFieldTimeOnly?(this.$altInput.val(this.formattedTime),this.$input.val(this.formattedDate)):this.$altInput?(this.$input.val(t),s="",e=this._defaults.altSeparator||this._defaults.separator,a=this._defaults.altTimeSuffix||this._defaults.timeSuffix,this._defaults.timeOnly||(s=this._defaults.altFormat?$.datepicker.formatDate(this._defaults.altFormat,null===i?new Date:i,n):this.formattedDate)&&(s+=e),this._defaults.altTimeFormat?s+=$.datepicker.formatTime(this._defaults.altTimeFormat,this,this._defaults)+a:s+=this.formattedTime+a,this.$altInput.val(s)):this.$input.val(t):this.$input.val(this.formattedDate),this.$input.trigger("change")},_onFocus:function(){if(!this.$input.val()&&this._defaults.defaultValue){this.$input.val(this._defaults.defaultValue);var e=$.datepicker._getInst(this.$input.get(0)),t=$.datepicker._get(e,"timepicker");if(t&&t._defaults.timeOnly&&e.input.val()!==e.lastVal)try{$.datepicker._updateDatepicker(e)}catch(e){$.timepicker.log(e)}}},_controls:{slider:{create:function(i,e,s,t,n,a,r){var l=i._defaults.isRTL;return e.prop("slide",null).slider({orientation:"horizontal",value:l?-1*t:t,min:l?-1*a:n,max:l?-1*n:a,step:r,slide:function(e,t){i.control.value(i,$(this),s,l?-1*t.value:t.value),i._onTimeChange()},stop:function(e,t){i._onSelectHandler()}})},options:function(e,t,i,s,n){if(e._defaults.isRTL){if("string"==typeof s)return"min"===s||"max"===s?void 0!==n?t.slider(s,-1*n):Math.abs(t.slider(s)):t.slider(s);var a=s.min,e=s.max;return s.min=s.max=null,void 0!==a&&(s.max=-1*a),void 0!==e&&(s.min=-1*e),t.slider(s)}return"string"==typeof s&&void 0!==n?t.slider(s,n):t.slider(s)},value:function(e,t,i,s){return e._defaults.isRTL?void 0!==s?t.slider("value",-1*s):Math.abs(t.slider("value")):void 0!==s?t.slider("value",s):t.slider("value")}},select:{create:function(t,e,i,s,n,a,r){for(var l='<select class="ui-timepicker-select" data-unit="'+i+'" data-min="'+n+'" data-max="'+a+'" data-step="'+r+'">',o=t._defaults.pickerTimeFormat||t._defaults.timeFormat,c=n;c<=a;c+=r)l+='<option value="'+c+'"'+(c===s?" selected":"")+">",l+="hour"===i?$.datepicker.formatTime($.trim(o.replace(/[^ht ]/gi,"")),{hour:c},t._defaults):"millisec"===i||"microsec"===i||10<=c?c:"0"+c.toString(),l+="</option>";return l+="</select>",e.children("select").remove(),$(l).appendTo(e).change(function(e){t._onTimeChange(),t._onSelectHandler()}),e},options:function(e,t,i,s,n){var a={},r=t.children("select");if("string"==typeof s){if(void 0===n)return r.data(s);a[s]=n}else a=s;return e.control.create(e,t,r.data("unit"),r.val(),a.min||r.data("min"),a.max||r.data("max"),a.step||r.data("step"))},value:function(e,t,i,s){t=t.children("select");return void 0!==s?t.val(s):t.val()}}}}),$.fn.extend({timepicker:function(e){e=e||{};var t=Array.prototype.slice.call(arguments);return"object"==typeof e&&(t[0]=$.extend(e,{timeOnly:!0})),$(this).each(function(){$.fn.datetimepicker.apply($(this),t)})},datetimepicker:function(t){t=t||{};var i=arguments;return"string"==typeof t?"getDate"===t?$.fn.datepicker.apply($(this[0]),i):this.each(function(){var e=$(this);e.datepicker.apply(e,i)}):this.each(function(){var e=$(this);e.datepicker($.timepicker._newInst(e,t)._defaults)})}}),$.datepicker.parseDateTime=function(e,t,i,s,n){s=parseDateTimeInternal(e,t,i,s,n);return s.timeObj&&(n=s.timeObj,s.date.setHours(n.hour,n.minute,n.second,n.millisec),s.date.setMicroseconds(n.microsec)),s.date},$.datepicker.parseTime=function(e,t,i){function n(e,t,a){var i="^"+e.toString().replace(/([hH]{1,2}|mm?|ss?|[tT]{1,2}|[zZ]|[lc]|'.*?')/g,function(e){var t,i,s,n=e.length;switch(e.charAt(0).toLowerCase()){case"h":case"m":case"s":return 1===n?"(\\d?\\d)":"(\\d{"+n+"})";case"l":case"c":return"(\\d?\\d?\\d)";case"z":return"(z|[-+]\\d\\d:?\\d\\d|\\S+)?";case"t":return t=a.amNames,i=a.pmNames,s=[],t&&$.merge(s,t),i&&$.merge(s,i),"("+(s=$.map(s,function(e){return e.replace(/[.*+?|()\[\]{}\\]/g,"\\$&")})).join("|")+")?";default:return"("+e.replace(/\'/g,"").replace(/(\.|\$|\^|\\|\/|\(|\)|\[|\]|\?|\+|\*)/g,function(e){return"\\"+e})+")?"}}).replace(/\s/g,"\\s?")+a.timeSuffix+"$",s=function(e){var t=e.toLowerCase().match(/(h{1,2}|m{1,2}|s{1,2}|l{1}|c{1}|t{1,2}|z|'.*?')/g),i={h:-1,m:-1,s:-1,l:-1,c:-1,t:-1,z:-1};if(t)for(var s=0;s<t.length;s++)-1===i[t[s].toString().charAt(0)]&&(i[t[s].toString().charAt(0)]=s+1);return i}(e),e="",t=t.match(new RegExp(i,"i")),i={hour:0,minute:0,second:0,millisec:0,microsec:0};return!!t&&(-1!==s.t&&(void 0===t[s.t]||0===t[s.t].length?i.ampm=e="":(e=-1!==$.inArray(t[s.t].toUpperCase(),a.amNames)?"AM":"PM",i.ampm=a["AM"===e?"amNames":"pmNames"][0])),-1!==s.h&&("AM"===e&&"12"===t[s.h]?i.hour=0:"PM"===e&&"12"!==t[s.h]?i.hour=parseInt(t[s.h],10)+12:i.hour=Number(t[s.h])),-1!==s.m&&(i.minute=Number(t[s.m])),-1!==s.s&&(i.second=Number(t[s.s])),-1!==s.l&&(i.millisec=Number(t[s.l])),-1!==s.c&&(i.microsec=Number(t[s.c])),-1!==s.z&&void 0!==t[s.z]&&(i.timezone=$.timepicker.timezoneOffsetNumber(t[s.z])),i)}i=extendRemove(extendRemove({},$.timepicker._defaults),i||{}),e.replace(/\'.*?\'/g,"").indexOf("Z");return"function"==typeof i.parse?i.parse(e,t,i):("loose"===i.parse?function(t,i,s){try{var e=new Date("2012-01-01 "+i);if(isNaN(e.getTime())&&(e=new Date("2012-01-01T"+i),isNaN(e.getTime())&&(e=new Date("01/01/2012 "+i),isNaN(e.getTime()))))throw"Unable to parse time with native Date: "+i;return{hour:e.getHours(),minute:e.getMinutes(),second:e.getSeconds(),millisec:e.getMilliseconds(),microsec:e.getMicroseconds(),timezone:-1*e.getTimezoneOffset()}}catch(e){try{return n(t,i,s)}catch(e){$.timepicker.log("Unable to parse \ntimeString: "+i+"\ntimeFormat: "+t)}}return!1}:n)(e,t,i)},$.datepicker.formatTime=function(e,t,i){i=i||{},i=$.extend({},$.timepicker._defaults,i),t=$.extend({hour:0,minute:0,second:0,millisec:0,microsec:0,timezone:null},t);var e=e,s=i.amNames[0],n=parseInt(t.hour,10);return 11<n&&(s=i.pmNames[0]),e=e.replace(/(?:HH?|hh?|mm?|ss?|[tT]{1,2}|[zZ]|[lc]|'.*?')/g,function(e){switch(e){case"HH":return("0"+n).slice(-2);case"H":return n;case"hh":return("0"+convert24to12(n)).slice(-2);case"h":return convert24to12(n);case"mm":return("0"+t.minute).slice(-2);case"m":return t.minute;case"ss":return("0"+t.second).slice(-2);case"s":return t.second;case"l":return("00"+t.millisec).slice(-3);case"c":return("00"+t.microsec).slice(-3);case"z":return $.timepicker.timezoneOffsetString((null===t.timezone?i:t).timezone,!1);case"Z":return $.timepicker.timezoneOffsetString((null===t.timezone?i:t).timezone,!0);case"T":return s.charAt(0).toUpperCase();case"TT":return s.toUpperCase();case"t":return s.charAt(0).toLowerCase();case"tt":return s.toLowerCase();default:return e.replace(/'/g,"")}})},$.datepicker._base_selectDate=$.datepicker._selectDate,$.datepicker._selectDate=function(e,t){var i=this._getInst($(e)[0]),s=this._get(i,"timepicker");s?(s._limitMinMaxDateTime(i,!0),i.inline=i.stay_open=!0,this._base_selectDate(e,t),i.inline=i.stay_open=!1,this._notifyChange(i),this._updateDatepicker(i)):this._base_selectDate(e,t)},$.datepicker._base_updateDatepicker=$.datepicker._updateDatepicker,$.datepicker._updateDatepicker=function(e){var t=e.input[0];$.datepicker._curInst&&$.datepicker._curInst!==e&&$.datepicker._datepickerShowing&&$.datepicker._lastInput!==t||"boolean"==typeof e.stay_open&&!1!==e.stay_open||(this._base_updateDatepicker(e),(t=this._get(e,"timepicker"))&&t._addTimePicker(e))},$.datepicker._base_doKeyPress=$.datepicker._doKeyPress,$.datepicker._doKeyPress=function(e){var t=$.datepicker._getInst(e.target),i=$.datepicker._get(t,"timepicker");if(i&&$.datepicker._get(t,"constrainInput")){var s=i.support.ampm,n=null!==i._defaults.showTimezone?i._defaults.showTimezone:i.support.timezone,t=$.datepicker._possibleChars($.datepicker._get(t,"dateFormat")),n=i._defaults.timeFormat.toString().replace(/[hms]/g,"").replace(/TT/g,s?"APM":"").replace(/Tt/g,s?"AaPpMm":"").replace(/tT/g,s?"AaPpMm":"").replace(/T/g,s?"AP":"").replace(/tt/g,s?"apm":"").replace(/t/g,s?"ap":"")+" "+i._defaults.separator+i._defaults.timeSuffix+(n?i._defaults.timezoneList.join(""):"")+i._defaults.amNames.join("")+i._defaults.pmNames.join("")+t,i=String.fromCharCode(void 0===e.charCode?e.keyCode:e.charCode);return e.ctrlKey||i<" "||!t||-1<n.indexOf(i)}return $.datepicker._base_doKeyPress(e)},$.datepicker._base_updateAlternate=$.datepicker._updateAlternate,$.datepicker._updateAlternate=function(e){var t,i,s,n,a,r,l,o=this._get(e,"timepicker");o?(t=o._defaults.altField)&&(o._defaults.altFormat||o._defaults.dateFormat,i=this._getDate(e),s=$.datepicker._getFormatConfig(e),n="",a=o._defaults.altSeparator||o._defaults.separator,r=o._defaults.altTimeSuffix||o._defaults.timeSuffix,l=null!==o._defaults.altTimeFormat?o._defaults.altTimeFormat:o._defaults.timeFormat,n+=$.datepicker.formatTime(l,o,o._defaults)+r,o._defaults.timeOnly||o._defaults.altFieldTimeOnly||null===i||(n=o._defaults.altFormat?$.datepicker.formatDate(o._defaults.altFormat,i,s)+a+n:o.formattedDate+a+n),$(t).val(n)):$.datepicker._base_updateAlternate(e)},$.datepicker._base_doKeyUp=$.datepicker._doKeyUp,$.datepicker._doKeyUp=function(e){var t=$.datepicker._getInst(e.target),i=$.datepicker._get(t,"timepicker");if(i&&i._defaults.timeOnly&&t.input.val()!==t.lastVal)try{$.datepicker._updateDatepicker(t)}catch(e){$.timepicker.log(e)}return $.datepicker._base_doKeyUp(e)},$.datepicker._base_gotoToday=$.datepicker._gotoToday,$.datepicker._gotoToday=function(e){var t=this._getInst($(e)[0]),i=t.dpDiv;this._base_gotoToday(e);e=this._get(t,"timepicker");selectLocalTimezone(e);e=new Date;this._setTime(t,e),$(".ui-datepicker-today",i).click()},$.datepicker._disableTimepickerDatepicker=function(e){var t,i=this._getInst(e);i&&(t=this._get(i,"timepicker"),$(e).datepicker("getDate"),t&&(i.settings.showTimepicker=!1,t._defaults.showTimepicker=!1,t._updateDateTime(i)))},$.datepicker._enableTimepickerDatepicker=function(e){var t,i=this._getInst(e);i&&(t=this._get(i,"timepicker"),$(e).datepicker("getDate"),t&&(i.settings.showTimepicker=!0,t._defaults.showTimepicker=!0,t._addTimePicker(i),t._updateDateTime(i)))},$.datepicker._setTime=function(e,t){var i,s=this._get(e,"timepicker");s&&(i=s._defaults,s.hour=t?t.getHours():i.hour,s.minute=t?t.getMinutes():i.minute,s.second=t?t.getSeconds():i.second,s.millisec=t?t.getMilliseconds():i.millisec,s.microsec=t?t.getMicroseconds():i.microsec,s._limitMinMaxDateTime(e,!0),s._onTimeChange(),s._updateDateTime(e))},$.datepicker._setTimeDatepicker=function(e,t,i){var s,n=this._getInst(e);!n||(e=this._get(n,"timepicker"))&&(this._setDateFromField(n),t&&("string"==typeof t?(e._parseTime(t,i),(s=new Date).setHours(e.hour,e.minute,e.second,e.millisec),s.setMicroseconds(e.microsec)):(s=new Date(t.getTime())).setMicroseconds(t.getMicroseconds()),"Invalid Date"===s.toString()&&(s=void 0),this._setTime(n,s)))},$.datepicker._base_setDateDatepicker=$.datepicker._setDateDatepicker,$.datepicker._setDateDatepicker=function(e,t){var i,s,n=this._getInst(e);n&&("string"==typeof t&&((t=new Date(t)).getTime()||$.timepicker.log("Error creating Date object from string.")),i=this._get(n,"timepicker"),t instanceof Date?(s=new Date(t.getTime())).setMicroseconds(t.getMicroseconds()):s=t,i&&(i.support.timezone||null!==i._defaults.timezone||(i.timezone=-1*s.getTimezoneOffset()),t=$.timepicker.timezoneAdjust(t,i.timezone),s=$.timepicker.timezoneAdjust(s,i.timezone)),this._updateDatepicker(n),this._base_setDateDatepicker.apply(this,arguments),this._setTimeDatepicker(e,s,!0))},$.datepicker._base_getDateDatepicker=$.datepicker._getDateDatepicker,$.datepicker._getDateDatepicker=function(e,t){var i=this._getInst(e);if(i){var s=this._get(i,"timepicker");if(s){void 0===i.lastVal&&this._setDateFromField(i,t);i=this._getDate(i);return i&&s._parseTime($(e).val(),s.timeOnly)&&(i.setHours(s.hour,s.minute,s.second,s.millisec),i.setMicroseconds(s.microsec),null!=s.timezone&&(s.support.timezone||null!==s._defaults.timezone||(s.timezone=-1*i.getTimezoneOffset()),i=$.timepicker.timezoneAdjust(i,s.timezone))),i}return this._base_getDateDatepicker(e,t)}},$.datepicker._base_parseDate=$.datepicker.parseDate,$.datepicker.parseDate=function(t,i,s){var n;try{n=this._base_parseDate(t,i,s)}catch(e){if(!(0<=e.indexOf(":")))throw e;n=this._base_parseDate(t,i.substring(0,i.length-(e.length-e.indexOf(":")-2)),s),$.timepicker.log("Error parsing the date string: "+e+"\ndate string = "+i+"\ndate format = "+t)}return n},$.datepicker._base_formatDate=$.datepicker._formatDate,$.datepicker._formatDate=function(e,t,i,s){var n=this._get(e,"timepicker");return n?(n._updateDateTime(e),n.$input.val()):this._base_formatDate(e)},$.datepicker._base_optionDatepicker=$.datepicker._optionDatepicker,$.datepicker._optionDatepicker=function(e,t,i){var s,n=this._getInst(e);if(!n)return null;var a=this._get(n,"timepicker");if(a){var r,l=null,o=null,n=null,c=a._defaults.evnts,m={};if("string"==typeof t){if("minDate"===t||"minDateTime"===t)l=i;else if("maxDate"===t||"maxDateTime"===t)o=i;else if("onSelect"===t)n=i;else if(c.hasOwnProperty(t)){if(void 0===i)return c[t];m[t]=i,s={}}}else if("object"==typeof t)for(r in t.minDate?l=t.minDate:t.minDateTime?l=t.minDateTime:t.maxDate?o=t.maxDate:t.maxDateTime&&(o=t.maxDateTime),c)c.hasOwnProperty(r)&&t[r]&&(m[r]=t[r]);for(r in m)m.hasOwnProperty(r)&&(c[r]=m[r],delete(s=s||$.extend({},t))[r]);if(s&&isEmptyObject(s))return;l?(l=0===l?new Date:new Date(l),a._defaults.minDate=l,a._defaults.minDateTime=l):o?(o=0===o?new Date:new Date(o),a._defaults.maxDate=o,a._defaults.maxDateTime=o):n&&(a._defaults.onSelect=n)}return void 0===i?this._base_optionDatepicker.call($.datepicker,e,t):this._base_optionDatepicker.call($.datepicker,e,s||t,i)},isEmptyObject=function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},extendRemove=function(e,t){for(var i in $.extend(e,t),t)null!==t[i]&&void 0!==t[i]||(e[i]=t[i]);return e},detectSupport=function(e){function t(e,t){return-1!==e.indexOf(t)}var i=e.replace(/'.*?'/g,"").toLowerCase();return{hour:t(i,"h"),minute:t(i,"m"),second:t(i,"s"),millisec:t(i,"l"),microsec:t(i,"c"),timezone:t(i,"z"),ampm:t(i,"t")&&t(e,"h"),iso8601:t(e,"Z")}},convert24to12=function(e){return 0===(e%=12)&&(e=12),String(e)},computeEffectiveSetting=function(e,t){return(e&&e[t]?e:$.timepicker._defaults)[t]},splitDateTime=function(e,t){var i=computeEffectiveSetting(t,"separator"),s=computeEffectiveSetting(t,"timeFormat").split(i).length,n=e.split(i),t=n.length;return 1<t?{dateString:n.splice(0,t-s).join(i),timeString:n.splice(0,s).join(i)}:{dateString:e,timeString:""}},parseDateTimeInternal=function(e,t,i,s,n){i=splitDateTime(i,n),s=$.datepicker._base_parseDate(e,i.dateString,s);if(""===i.timeString)return{date:s};if(!(n=$.datepicker.parseTime(t,i.timeString,n)))throw"Wrong time format";return{date:s,timeObj:n}},selectLocalTimezone=function(e,t){e&&e.timezone_select&&(t=t||new Date,e.timezone_select.val(-t.getTimezoneOffset()))},$.timepicker=new Timepicker,$.timepicker.timezoneOffsetString=function(e,t){if(isNaN(e)||840<e||e<-720)return e;var i=e%60,t=t?":":"",i=(0<=e?"+":"-")+("0"+Math.abs((e-i)/60)).slice(-2)+t+("0"+Math.abs(i)).slice(-2);return"+00:00"==i?"Z":i},$.timepicker.timezoneOffsetNumber=function(e){var t=e.toString().replace(":","");return"Z"===t.toUpperCase()?0:/^(\-|\+)\d{4}$/.test(t)?("-"===t.substr(0,1)?-1:1)*(60*parseInt(t.substr(1,2),10)+parseInt(t.substr(3,2),10)):e},$.timepicker.timezoneAdjust=function(e,t){t=$.timepicker.timezoneOffsetNumber(t);return isNaN(t)||e.setMinutes(e.getMinutes()+-e.getTimezoneOffset()-t),e},$.timepicker.timeRange=function(e,t,i){return $.timepicker.handleRange("timepicker",e,t,i)},$.timepicker.datetimeRange=function(e,t,i){$.timepicker.handleRange("datetimepicker",e,t,i)},$.timepicker.dateRange=function(e,t,i){$.timepicker.handleRange("datepicker",e,t,i)},$.timepicker.handleRange=function(r,l,o,c){function i(e,t){var i,s=l[r]("getDate"),n=o[r]("getDate"),a=e[r]("getDate");null!==s&&(i=new Date(s.getTime()),e=new Date(s.getTime()),i.setMilliseconds(i.getMilliseconds()+c.minInterval),e.setMilliseconds(e.getMilliseconds()+c.maxInterval),0<c.minInterval&&n<i?o[r]("setDate",i):0<c.maxInterval&&e<n?o[r]("setDate",e):n<s&&t[r]("setDate",a))}function t(e,t,i){e.val()&&(null!==(e=e[r].call(e,"getDate"))&&0<c.minInterval&&("minDate"===i&&e.setMilliseconds(e.getMilliseconds()+c.minInterval),"maxDate"===i&&e.setMilliseconds(e.getMilliseconds()-c.minInterval)),e.getTime&&t[r].call(t,"option",i,e))}return c=$.extend({},{minInterval:0,maxInterval:0,start:{},end:{}},c),$.fn[r].call(l,$.extend({onClose:function(e,t){i($(this),o)},onSelect:function(e){t($(this),o,"minDate")}},c,c.start)),$.fn[r].call(o,$.extend({onClose:function(e,t){i($(this),l)},onSelect:function(e){t($(this),l,"maxDate")}},c,c.end)),i(l,o),t(l,o,"minDate"),t(o,l,"maxDate"),$([l.get(0),o.get(0)])},$.timepicker.log=function(e){window.console&&window.console.log(e)},$.timepicker._util={_extendRemove:extendRemove,_isEmptyObject:isEmptyObject,_convert24to12:convert24to12,_detectSupport:detectSupport,_selectLocalTimezone:selectLocalTimezone,_computeEffectiveSetting:computeEffectiveSetting,_splitDateTime:splitDateTime,_parseDateTimeInternal:parseDateTimeInternal},Date.prototype.getMicroseconds||(Date.prototype.microseconds=0,Date.prototype.getMicroseconds=function(){return this.microseconds},Date.prototype.setMicroseconds=function(e){return this.setMilliseconds(this.getMilliseconds()+Math.floor(e/1e3)),this.microseconds=e%1e3,this}),$.timepicker.version="1.4")}(jQuery);