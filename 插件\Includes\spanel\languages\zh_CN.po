# Copyright (C) 2021 Codestar
# This file is distributed under the same license as the Codestar Framework package.
msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: 2024-09-21 23:45+0800\n"
"PO-Revision-Date: 2024-09-21 23:45+0800\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2\n"
"X-Poedit-KeywordsList: esc_html__\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-SearchPath-0: .\n"

#: classes/admin-options.class.php:237
msgid "Error while saving the changes."
msgstr "保存失败"

#: classes/admin-options.class.php:297
msgid "Settings successfully imported."
msgstr "导入成功"

#: classes/admin-options.class.php:309 classes/admin-options.class.php:325
msgid "Default settings restored."
msgstr "恢复完成"

#: classes/admin-options.class.php:396
msgid "Settings saved."
msgstr "保存成功"

#: classes/admin-options.class.php:577
msgid "You have unsaved changes, save your changes!"
msgstr "配置发生改变，请勿忘记保存！"

#: classes/admin-options.class.php:579
msgid "show all settings"
msgstr "显示所有设置"

#: classes/admin-options.class.php:581 fields/map/map.php:30
msgid "Search..."
msgstr "请输入关键词"

#: classes/admin-options.class.php:584 classes/admin-options.class.php:707
#: classes/admin-options.class.php:720
msgid "Save"
msgstr "保存配置"

#: classes/admin-options.class.php:584 classes/admin-options.class.php:707
#: classes/admin-options.class.php:720
msgid "Saving..."
msgstr "正在保存"

#: classes/admin-options.class.php:585 classes/admin-options.class.php:708
#: classes/admin-options.class.php:721
msgid "Reset Section"
msgstr "恢复此页"

#: classes/admin-options.class.php:585 classes/admin-options.class.php:708
#: classes/admin-options.class.php:721
msgid "Are you sure to reset this section options?"
msgstr "单击「确定」进行恢复，当前页面的配置将会丢失！"

#: classes/admin-options.class.php:586 classes/admin-options.class.php:709
#: classes/admin-options.class.php:722
msgid "Reset All"
msgstr "恢复全部"

#: classes/admin-options.class.php:586 classes/admin-options.class.php:709
#: classes/admin-options.class.php:722 classes/comment-options.class.php:223
#: classes/metabox-options.class.php:301 fields/backup/backup.php:38
msgid "Reset"
msgstr "恢复默认"

#: classes/admin-options.class.php:586 classes/admin-options.class.php:709
#: classes/admin-options.class.php:722
msgid "Are you sure you want to reset all settings to default values?"
msgstr "单击「确定」进行恢复，所有页面的配置都将丢失！"

#: classes/admin-options.class.php:684 classes/comment-options.class.php:206
#: classes/metabox-options.class.php:284 fields/button_set/button_set.php:63
#: fields/checkbox/checkbox.php:83 fields/radio/radio.php:82
#: fields/select/select.php:120 functions/actions.php:50
msgid "No data available."
msgstr "没有可用数据"

#: classes/comment-options.class.php:224 classes/metabox-options.class.php:302
msgid "update post"
msgstr "更新文章"

#: classes/comment-options.class.php:224 classes/metabox-options.class.php:302
msgid "Cancel"
msgstr "取消"

#: classes/setup.class.php:640
msgid "Are you sure?"
msgstr "单击「确定」进行下一步操作，该操作可能会丢失部分配置！"

#: classes/setup.class.php:641
#, php-format
msgid "Please enter %s or more characters"
msgstr "请输入 %s 或更多字符"

#: classes/setup.class.php:642
msgid "Searching..."
msgstr "搜索中..."

#: classes/setup.class.php:643
msgid "No results found."
msgstr "未找到结果。"

#: classes/setup.class.php:744
msgid "Oops! Not allowed."
msgstr "哎呀！不允许。"

#: classes/setup.class.php:812 classes/setup.class.php:816
msgid "Field not found!"
msgstr "没有找到任何数据"

#: classes/shortcode-options.class.php:262 fields/group/group.php:30
msgid "Add New"
msgstr "新增"

#: classes/shortcode-options.class.php:299 functions/actions.php:23
#: functions/actions.php:77 functions/actions.php:115 functions/actions.php:150
#: functions/actions.php:179
msgid "Error: Invalid nonce verification."
msgstr "错误: 无效的nonce验证。"

#: fields/background/background.php:43 fields/media/media.php:66
msgid "Not selected"
msgstr "未选择"

#: fields/background/background.php:79 fields/date/date.php:38
#: fields/datetime/datetime.php:43
msgid "From"
msgstr "从"

#: fields/background/background.php:97 fields/date/date.php:39
#: fields/datetime/datetime.php:44
msgid "To"
msgstr "到"

#: fields/background/background.php:115
msgid "Direction"
msgstr "方向"

#: fields/background/background.php:121
msgid "Gradient Direction"
msgstr "渐变方向"

#: fields/background/background.php:122
msgid "&#8659; top to bottom"
msgstr "&#8659; 从上到下"

#: fields/background/background.php:123
msgid "&#8658; left to right"
msgstr "&#8658;从左到右"

#: fields/background/background.php:124
msgid "&#8664; corner top to right"
msgstr "&#8664; 右上角"

#: fields/background/background.php:125
msgid "&#8665; corner top to left"
msgstr "&#8664; 左上角"

#: fields/background/background.php:168
msgid "Background Position"
msgstr "背景位置"

#: fields/background/background.php:169
msgid "Left Top"
msgstr "左上"

#: fields/background/background.php:170
msgid "Left Center"
msgstr "左中"

#: fields/background/background.php:171
msgid "Left Bottom"
msgstr "左下"

#: fields/background/background.php:172
msgid "Center Top"
msgstr "中上"

#: fields/background/background.php:173
msgid "Center Center"
msgstr "居中"

#: fields/background/background.php:174
msgid "Center Bottom"
msgstr "中下"

#: fields/background/background.php:175
msgid "Right Top"
msgstr "右上"

#: fields/background/background.php:176
msgid "Right Center"
msgstr "右中"

#: fields/background/background.php:177
msgid "Right Bottom"
msgstr "右下"

#: fields/background/background.php:191
msgid "Background Repeat"
msgstr "背景重复"

#: fields/background/background.php:192
msgid "Repeat"
msgstr "重复"

#: fields/background/background.php:193
msgid "No Repeat"
msgstr "不重复"

#: fields/background/background.php:194
msgid "Repeat Horizontally"
msgstr "水平重复"

#: fields/background/background.php:195
msgid "Repeat Vertically"
msgstr "垂直重复"

#: fields/background/background.php:209
msgid "Background Attachment"
msgstr "背景附件"

#: fields/background/background.php:210
msgid "Scroll"
msgstr "滚动"

#: fields/background/background.php:211
msgid "Fixed"
msgstr "固定"

#: fields/background/background.php:225
msgid "Background Size"
msgstr "背景大小"

#: fields/background/background.php:226
msgid "Cover"
msgstr "覆盖"

#: fields/background/background.php:227
msgid "Contain"
msgstr "包含"

#: fields/background/background.php:228
msgid "Auto"
msgstr "自动"

#: fields/background/background.php:242
msgid "Background Origin"
msgstr "背景起源"

#: fields/background/background.php:243 fields/background/background.php:261
msgid "Padding Box"
msgstr "框内边距"

#: fields/background/background.php:244 fields/background/background.php:260
msgid "Border Box"
msgstr "边框"

#: fields/background/background.php:245 fields/background/background.php:262
msgid "Content Box"
msgstr "内容框"

#: fields/background/background.php:259
msgid "Background Clip"
msgstr "背景剪辑"

#: fields/background/background.php:276
msgid "Background Blend Mode"
msgstr "背景混合模式"

#: fields/background/background.php:277 fields/link_color/link_color.php:43
#: fields/typography/typography.php:193
msgid "Normal"
msgstr "正常"

#: fields/background/background.php:278
msgid "Multiply"
msgstr "乘"

#: fields/background/background.php:279
msgid "Screen"
msgstr "屏幕"

#: fields/background/background.php:280
msgid "Overlay"
msgstr "覆盖"

#: fields/background/background.php:281
msgid "Darken"
msgstr "变黑"

#: fields/background/background.php:282
msgid "Lighten"
msgstr "减轻"

#: fields/background/background.php:283
msgid "Color Dodge"
msgstr "颜色减淡"

#: fields/background/background.php:284
msgid "Saturation"
msgstr "饱和度"

#: fields/background/background.php:285
msgid "Color"
msgstr "颜色"

#: fields/background/background.php:286
msgid "Luminosity"
msgstr "光度"

#: fields/backup/backup.php:33
msgid "Import"
msgstr "导入"

#: fields/backup/backup.php:36
msgid "Export & Download"
msgstr "导出和下载"

#: fields/border/border.php:32 fields/spacing/spacing.php:32
msgid "top"
msgstr "顶部"

#: fields/border/border.php:33 fields/spacing/spacing.php:33
msgid "right"
msgstr "右"

#: fields/border/border.php:34 fields/spacing/spacing.php:34
msgid "bottom"
msgstr "底部"

#: fields/border/border.php:35 fields/spacing/spacing.php:35
msgid "left"
msgstr "左"

#: fields/border/border.php:36 fields/spacing/spacing.php:36
msgid "all"
msgstr "所有"

#: fields/border/border.php:58 fields/typography/typography.php:221
msgid "Solid"
msgstr "实线"

#: fields/border/border.php:59 fields/typography/typography.php:224
msgid "Dashed"
msgstr "虚线"

#: fields/border/border.php:60 fields/typography/typography.php:223
msgid "Dotted"
msgstr "点线"

#: fields/border/border.php:61 fields/typography/typography.php:222
msgid "Double"
msgstr "双线"

#: fields/border/border.php:62
msgid "Inset"
msgstr "插入"

#: fields/border/border.php:63
msgid "Outset"
msgstr "开始"

#: fields/border/border.php:64
msgid "Groove"
msgstr "凹槽"

#: fields/border/border.php:65
msgid "ridge"
msgstr "凸出"

#: fields/border/border.php:66 fields/typography/typography.php:206
#: fields/typography/typography.php:220
msgid "None"
msgstr "无"

#: fields/dimensions/dimensions.php:29
msgid "width"
msgstr "宽度"

#: fields/dimensions/dimensions.php:30
msgid "height"
msgstr "高度"

#: fields/gallery/gallery.php:27
msgid "Add Gallery"
msgstr "添加图库"

#: fields/gallery/gallery.php:28
msgid "Edit Gallery"
msgstr "编辑图库"

#: fields/gallery/gallery.php:29
msgid "Clear"
msgstr "清除"

#: fields/group/group.php:48 fields/repeater/repeater.php:34
msgid "Error: Field ID conflict."
msgstr "错误：字段ID冲突。"

#: fields/group/group.php:59 fields/group/group.php:114
#: fields/repeater/repeater.php:55 fields/repeater/repeater.php:83
msgid "Are you sure to delete this item?"
msgstr "确定要删除这个项目吗？"

#: fields/group/group.php:148 fields/repeater/repeater.php:96
msgid "You cannot add more."
msgstr "无法添加更多"

#: fields/group/group.php:149 fields/repeater/repeater.php:97
msgid "You cannot remove more."
msgstr "无法删除更多"

#: fields/icon/icon.php:27
msgid "Add Icon"
msgstr "添加图标"

#: fields/icon/icon.php:28
msgid "Remove Icon"
msgstr "删除图标"

#: fields/link/link.php:27
msgid "Add Link"
msgstr "添加链接"

#: fields/link/link.php:28
msgid "Edit Link"
msgstr "编辑链接"

#: fields/link/link.php:29
msgid "Remove Link"
msgstr "移除链接"

#: fields/link_color/link_color.php:44
msgid "Hover"
msgstr "悬停"

#: fields/link_color/link_color.php:45
msgid "Active"
msgstr "启用"

#: fields/link_color/link_color.php:46
msgid "Visited"
msgstr "访问"

#: fields/link_color/link_color.php:47
msgid "Focus"
msgstr "焦点"

#: fields/map/map.php:31
msgid "Latitude"
msgstr "纬度"

#: fields/map/map.php:32
msgid "Longitude"
msgstr "经度"

#: fields/media/media.php:32 fields/upload/upload.php:31
msgid "Upload"
msgstr "上传"

#: fields/media/media.php:33 fields/upload/upload.php:32
msgid "Remove"
msgstr "删除"

#: fields/sorter/sorter.php:28
msgid "Enabled"
msgstr "启用"

#: fields/sorter/sorter.php:29
msgid "Disabled"
msgstr "禁用"

#: fields/switcher/switcher.php:27
msgid "On"
msgstr "启用"

#: fields/switcher/switcher.php:28
msgid "Off"
msgstr "禁用"

#: fields/typography/typography.php:103
msgid "Font Family"
msgstr "字体"

#: fields/typography/typography.php:104
msgid "Select a font"
msgstr "选择字体"

#: fields/typography/typography.php:112
msgid "Backup Font Family"
msgstr "备份字体系列"

#: fields/typography/typography.php:126 fields/typography/typography.php:139
#: fields/typography/typography.php:152 fields/typography/typography.php:167
#: fields/typography/typography.php:183 fields/typography/typography.php:196
#: fields/typography/typography.php:210 fields/typography/typography.php:228
msgid "Default"
msgstr "默认"

#: fields/typography/typography.php:137
msgid "Font Style"
msgstr "字体样式"

#: fields/typography/typography.php:151 fields/typography/typography.php:152
msgid "Load Extra Styles"
msgstr "加载额外样式"

#: fields/typography/typography.php:165
msgid "Subset"
msgstr "子集"

#: fields/typography/typography.php:175
msgid "Text Align"
msgstr "文本对齐"

#: fields/typography/typography.php:177
msgid "Inherit"
msgstr "继承"

#: fields/typography/typography.php:178
msgid "Left"
msgstr "左侧"

#: fields/typography/typography.php:179
msgid "Center"
msgstr "居中"

#: fields/typography/typography.php:180
msgid "Right"
msgstr "右侧"

#: fields/typography/typography.php:181
msgid "Justify"
msgstr "两端对齐"

#: fields/typography/typography.php:182
msgid "Initial"
msgstr "初始"

#: fields/typography/typography.php:191
msgid "Font Variant"
msgstr "字体变体"

#: fields/typography/typography.php:194
msgid "Small Caps"
msgstr "小写"

#: fields/typography/typography.php:195
msgid "All Small Caps"
msgstr "所有字母小写"

#: fields/typography/typography.php:204
msgid "Text Transform"
msgstr "文本转换"

#: fields/typography/typography.php:207
msgid "Capitalize"
msgstr "大写"

#: fields/typography/typography.php:208
msgid "Uppercase"
msgstr "大写"

#: fields/typography/typography.php:209
msgid "Lowercase"
msgstr "小写"

#: fields/typography/typography.php:218
msgid "Text Decoration"
msgstr "文本装饰"

#: fields/typography/typography.php:225
msgid "Wavy"
msgstr "波浪"

#: fields/typography/typography.php:226
msgid "Overline"
msgstr "上划线"

#: fields/typography/typography.php:227
msgid "Line-through"
msgstr "删除线"

#: fields/typography/typography.php:240
msgid "Font Size"
msgstr "字体大小"

#: fields/typography/typography.php:252
msgid "Line Height"
msgstr "线高度"

#: fields/typography/typography.php:264
msgid "Letter Spacing"
msgstr "字母间距"

#: fields/typography/typography.php:276
msgid "Word Spacing"
msgstr "字间距"

#: fields/typography/typography.php:291
msgid "Font Color"
msgstr "字体颜色"

#: fields/typography/typography.php:302
msgid "Custom Style"
msgstr "自定义样式"

#: fields/typography/typography.php:369
msgid "Custom Web Fonts"
msgstr "自定义 Web 字体"

#: fields/typography/typography.php:375
msgid "Safe Web Fonts"
msgstr "Web 安全字体"

#: fields/typography/typography.php:395
msgid "Google Web Fonts"
msgstr "Google Web 字体"

#: functions/actions.php:81 functions/actions.php:119
msgid "Error: Invalid key."
msgstr "错误：无效密钥。"

#: functions/actions.php:123
msgid "Error: The response is not a valid JSON response."
msgstr "错误：响应不是有效的 JSON 响应。"

#: functions/actions.php:183
msgid "Error: Invalid term ID."
msgstr "错误：无效的项目ID。"

#: functions/actions.php:189
msgid "Error: You do not have permission to do that."
msgstr "错误：您无权这样做。"

#: functions/validate.php:21 functions/validate.php:93
msgid "Please enter a valid email address."
msgstr "请输入正确的电子邮件地址。"

#: functions/validate.php:39 functions/validate.php:113
msgid "Please enter a valid number."
msgstr "请输入有效的数字"

#: functions/validate.php:57 functions/validate.php:133
msgid "This field is required."
msgstr "这是必填栏。"

#: functions/validate.php:75 functions/validate.php:153
msgid "Please enter a valid URL."
msgstr "请输入有效网址"

#~ msgid "Codestar Framework"
#~ msgstr "Codestar Framework"

#~ msgid "http://codestarframework.com/"
#~ msgstr "http://codestarframework.com/"

#~ msgid "A Simple and Lightweight WordPress Option Framework for Themes and Plugins"
#~ msgstr "一个简单且轻量的 WordPress 主题和插件选项框架"

#~ msgid "Codestar"
#~ msgstr "Codestar"

#~ msgid "http://codestarthemes.com/"
#~ msgstr "http://codestarthemes.com/"
