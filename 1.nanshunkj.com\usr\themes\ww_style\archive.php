<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php $this->need('header.php'); ?>

<div class="main-container">
    <div class="breadcrumb">
        <div class="container">
            <a href="<?php $this->options->siteUrl(); ?>">首页</a> &gt; 
            <?php if ($this->is('category')): ?>
                <?php $this->category(); ?>
            <?php elseif ($this->is('tag')): ?>
                <?php $this->archiveTitle(array('tag' => _t('标签 %s 下的文章')), '', ''); ?>
            <?php elseif ($this->is('author')): ?>
                <?php $this->archiveTitle(array('author' => _t('%s 发布的文章')), '', ''); ?>
            <?php elseif ($this->is('search')): ?>
                <?php $this->archiveTitle(array('search' => _t('包含关键字 %s 的文章')), '', ''); ?>
            <?php else: ?>
                <?php $this->archiveTitle(array(
                    'category'  =>  _t('分类 %s 下的文章'),
                    'search'    =>  _t('包含关键字 %s 的文章'),
                    'tag'       =>  _t('标签 %s 下的文章'),
                    'author'    =>  _t('%s 发布的文章')
                ), '', ''); ?>
            <?php endif; ?>
        </div>
    </div>

    <div class="content-wrapper">
        <div class="article-section">
            <div class="section-header">
                <h2 class="section-title">
                    <?php if ($this->is('category')): ?>
                        <i class="ri-folder-line"></i> <?php $this->category(); ?>
                    <?php elseif ($this->is('tag')): ?>
                        <i class="ri-price-tag-3-line"></i> <?php $this->archiveTitle(array('tag' => _t('标签: %s')), '', ''); ?>
                    <?php elseif ($this->is('search')): ?>
                        <i class="ri-search-line"></i> <?php $this->archiveTitle(array('search' => _t('搜索: %s')), '', ''); ?>
                    <?php else: ?>
                        <i class="ri-archive-line"></i> <?php $this->archiveTitle(array(
                            'category'  =>  _t('分类: %s'),
                            'search'    =>  _t('搜索: %s'),
                            'tag'       =>  _t('标签: %s'),
                            'author'    =>  _t('%s 的文章')
                        ), '', ''); ?>
                    <?php endif; ?>
                </h2>
                <?php if ($this->is('category')): ?>
                <div class="archive-description">
                    <?php echo $this->getDescription(); ?>
                </div>
                <?php endif; ?>
            </div>
            
            <?php if ($this->have()): ?>
            <div class="article-grid">
                <?php while ($this->next()): ?>
                <article class="article-card">
                    <div class="article-cover">
                        <?php if ($this->fields->thumb): ?>
                        <img src="<?php echo $this->fields->thumb; ?>" alt="<?php $this->title() ?>">
                        <?php else: ?>
                        <img src="<?php $this->options->themeUrl('assets/img/default.jpg'); ?>" alt="<?php $this->title() ?>">
                        <?php endif; ?>
                        <?php if ($this->fields->isPremium): ?>
                        <div class="article-tag premium">会员专享</div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="article-content">
                        <div class="article-meta">
                            <span class="meta-category"><?php $this->category(','); ?></span>
                            <span class="meta-date"><?php $this->date('Y-m-d'); ?></span>
                        </div>
                        <h3 class="article-title"><a href="<?php $this->permalink() ?>"><?php $this->title() ?></a></h3>
                        <div class="article-excerpt"><?php $this->excerpt(100, '...'); ?></div>
                        
                        <div class="article-footer">
                            <div class="article-stats">
                                <span class="stat-item"><i class="ri-eye-line"></i> <?php echo getPostViews($this); ?></span>
                                <span class="stat-item"><i class="ri-message-2-line"></i> <?php $this->commentsNum('%d'); ?></span>
                                <span class="stat-item"><i class="ri-heart-line"></i> <?php echo getLikes($this); ?></span>
                            </div>
                            <a href="<?php $this->permalink() ?>" class="article-more">阅读全文</a>
                        </div>
                    </div>
                </article>
                <?php endwhile; ?>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <?php $this->pageNav('&laquo;', '&raquo;', 1, '...', array('wrapTag' => 'ul', 'wrapClass' => 'page-navigator', 'itemTag' => 'li', 'textTag' => 'span', 'currentClass' => 'current', 'prevClass' => 'prev', 'nextClass' => 'next')); ?>
            </div>
            <?php else: ?>
            <div class="no-content">
                <div class="no-content-icon"><i class="ri-inbox-line"></i></div>
                <h3 class="no-content-title">暂无内容</h3>
                <p class="no-content-desc">该分类下暂时没有文章</p>
                <a href="<?php $this->options->siteUrl(); ?>" class="btn-primary">返回首页</a>
            </div>
            <?php endif; ?>
        </div>
        
        <?php $this->need('sidebar.php'); ?>
    </div>
</div>

<?php $this->need('footer.php'); ?> 