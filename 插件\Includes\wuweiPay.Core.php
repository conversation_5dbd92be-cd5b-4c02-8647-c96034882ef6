<?php
/**
 * 吴畏支付核心功能
 * 
 * @package WuweiPay
 * <AUTHOR>
 * @link https://8ww.fun
 */
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 加载常量定义
require_once __DIR__ . '/wuweiPay.Const.php';

/**
 * 吴畏支付核心类
 */
class WuweiPayCore
{
    /**
     * 插件配置
     */
    private $options;
    
    /**
     * 数据库操作对象
     */
    private $db;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->options = Helper::options()->plugin('WuweiPay');
        $this->db = Typecho_Db::get();
    }
    
    /**
     * 创建订单
     * 
     * @param float $amount 金额
     * @param string $title 订单标题
     * @param string $type 订单类型
     * @param int $userId 用户ID
     * @param array $extra 额外数据
     * @return string 订单号
     */
    public function createOrder($amount, $title, $type = WUWEIPAY_TYPE_GENERAL, $userId = 0, $extra = [])
    {
        // 生成订单号
        $orderId = $this->generateOrderId();
        
        // 准备订单数据
        $orderData = [
            'order_id' => $orderId,
            'user_id' => $userId,
            'amount' => $amount,
            'title' => $title,
            'type' => $type,
            'payment_method' => '',
            'status' => WUWEIPAY_STATUS_UNPAID,
            'created_time' => time(),
            'paid_time' => 0,
            'extra' => json_encode($extra)
        ];
        
        // 插入订单数据
        $this->db->query($this->db->insert('table.wuweipay_orders')->rows($orderData));
        
        return $orderId;
    }
    
    /**
     * 更新订单支付方式
     * 
     * @param string $orderId 订单号
     * @param string $method 支付方式
     * @return bool 是否成功
     */
    public function updateOrderMethod($orderId, $method)
    {
        return $this->db->query($this->db->update('table.wuweipay_orders')
            ->rows(['payment_method' => $method])
            ->where('order_id = ?', $orderId)) > 0;
    }
    
    /**
     * 完成订单支付
     * 
     * @param string $orderId 订单号
     * @return bool 是否成功
     */
    public function completeOrder($orderId)
    {
        // 查询订单
        $order = $this->getOrder($orderId);
        if (!$order || $order['status'] == WUWEIPAY_STATUS_PAID) {
            return false;
        }
        
        // 更新订单状态
        $result = $this->db->query($this->db->update('table.wuweipay_orders')
            ->rows([
                'status' => WUWEIPAY_STATUS_PAID,
                'paid_time' => time()
            ])
            ->where('order_id = ?', $orderId)) > 0;
        
        // 处理订单完成后的操作
        if ($result) {
            $this->processOrderCompletion($order);
        }
        
        return $result;
    }
    
    /**
     * 处理订单完成后的操作
     * 
     * @param array $order 订单数据
     */
    private function processOrderCompletion($order)
    {
        // 根据订单类型执行不同操作
        switch ($order['type']) {
            case WUWEIPAY_TYPE_VIP:
                // 处理VIP会员订单
                $this->processVipOrder($order);
                break;
                
            case WUWEIPAY_TYPE_DONATION:
                // 处理打赏订单
                $this->processDonationOrder($order);
                break;
                
            case WUWEIPAY_TYPE_GENERAL:
            default:
                // 处理普通订单
                $this->processGeneralOrder($order);
                break;
        }
        
        // 触发订单完成事件
        $this->triggerOrderCompletedEvent($order);
    }
    
    /**
     * 处理VIP会员订单
     * 
     * @param array $order 订单数据
     */
    private function processVipOrder($order)
    {
        // 如果有用户ID，更新用户的VIP状态
        if ($order['user_id'] > 0) {
            // 这里实现VIP会员逻辑
        }
    }
    
    /**
     * 处理打赏订单
     * 
     * @param array $order 订单数据
     */
    private function processDonationOrder($order)
    {
        // 处理打赏逻辑
    }
    
    /**
     * 处理普通订单
     * 
     * @param array $order 订单数据
     */
    private function processGeneralOrder($order)
    {
        // 处理普通订单逻辑
    }
    
    /**
     * 触发订单完成事件
     * 
     * @param array $order 订单数据
     */
    private function triggerOrderCompletedEvent($order)
    {
        // 触发事件，让其他插件可以处理
    }
    
    /**
     * 获取订单信息
     * 
     * @param string $orderId 订单号
     * @return array|null 订单信息
     */
    public function getOrder($orderId)
    {
        return $this->db->fetchRow($this->db->select()
            ->from('table.wuweipay_orders')
            ->where('order_id = ?', $orderId));
    }
    
    /**
     * 检查订单状态
     * 
     * @param string $orderId 订单号
     * @return int 订单状态
     */
    public function checkOrderStatus($orderId)
    {
        $order = $this->getOrder($orderId);
        return $order ? $order['status'] : -1;
    }
    
    /**
     * 生成订单号
     * 
     * @return string 订单号
     */
    private function generateOrderId()
    {
        return WUWEIPAY_ORDER_PREFIX . date('YmdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 生成签名
     * 
     * @param array $params 参数
     * @return string 签名
     */
    public function generateSign($params)
    {
        // 排序
        ksort($params);
        
        // 组装签名字符串
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($key != 'sign' && $value !== '' && !is_null($value)) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        
        // 添加密钥
        $signStr .= 'key=' . $this->options->wuweipay_key;
        
        // MD5加密并转为大写
        return strtoupper(md5($signStr));
    }
    
    /**
     * 验证签名
     * 
     * @param array $params 参数
     * @param string $sign 签名
     * @return bool 是否验证通过
     */
    public function verifySign($params, $sign)
    {
        return $this->generateSign($params) === $sign;
    }
}
