<?php
/**
 * 会员付费页面
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 检查用户是否登录，未登录则跳转到登录页面
if (!$this->user->hasLogin()) {
    $this->response->redirect($this->options->loginUrl);
}

$this->need('header.php');
?>

<div class="main-container">
    <div class="page-header">
        <h1 class="page-title"><i class="ri-vip-crown-line"></i> 会员套餐</h1>
        <p class="page-desc">选择适合您的会员套餐，享受更多优质内容和功能权限。会员可查看站内所有VIP专享内容。</p>
    </div>
    
    <div class="membership-container">
        <div class="membership-cards">
            <!-- 月度会员 -->
            <div class="membership-plan" data-plan="monthly" data-price="<?php echo $this->options->memberMonthlyPrice; ?>">
                <div class="plan-header">
                    <div class="plan-icon"><i class="ri-calendar-line"></i></div>
                    <h3 class="plan-title">月度会员</h3>
                    <div class="plan-price">
                        <span class="price-currency">¥</span>
                        <span class="price-value"><?php echo $this->options->memberMonthlyPrice; ?></span>
                        <span class="price-period">/月</span>
                    </div>
                </div>
                <div class="plan-features">
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>解锁全站会员内容</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>每月更新资源抢先看</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>高清无水印资源下载</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>技术支持优先响应</span>
                    </div>
                    <div class="feature-item muted">
                        <i class="ri-close-line"></i>
                        <span>历史内容归档查阅</span>
                    </div>
                    <div class="feature-item muted">
                        <i class="ri-close-line"></i>
                        <span>专属会员活动邀请</span>
                    </div>
                </div>
                <div class="plan-action">
                    <button class="plan-btn">选择此套餐</button>
                </div>
            </div>
            
            <!-- 年度会员 -->
            <div class="membership-plan active" data-plan="yearly" data-price="<?php echo $this->options->memberYearlyPrice; ?>">
                <div class="plan-tag">推荐</div>
                <div class="plan-header">
                    <div class="plan-icon"><i class="ri-calendar-check-line"></i></div>
                    <h3 class="plan-title">年度会员</h3>
                    <div class="plan-price">
                        <span class="price-currency">¥</span>
                        <span class="price-value"><?php echo $this->options->memberYearlyPrice; ?></span>
                        <span class="price-period">/年</span>
                    </div>
                    <div class="price-discount">
                        较月付节省 <?php echo round((12 * floatval($this->options->memberMonthlyPrice) - floatval($this->options->memberYearlyPrice)) / (12 * floatval($this->options->memberMonthlyPrice)) * 100); ?>%
                    </div>
                </div>
                <div class="plan-features">
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>解锁全站会员内容</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>每月更新资源抢先看</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>高清无水印资源下载</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>技术支持优先响应</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>历史内容归档查阅</span>
                    </div>
                    <div class="feature-item muted">
                        <i class="ri-close-line"></i>
                        <span>专属会员活动邀请</span>
                    </div>
                </div>
                <div class="plan-action">
                    <button class="plan-btn">选择此套餐</button>
                </div>
            </div>
            
            <!-- 永久会员 -->
            <div class="membership-plan" data-plan="lifetime" data-price="<?php echo $this->options->memberLifetimePrice; ?>">
                <div class="plan-header">
                    <div class="plan-icon"><i class="ri-vip-crown-line"></i></div>
                    <h3 class="plan-title">永久会员</h3>
                    <div class="plan-price">
                        <span class="price-currency">¥</span>
                        <span class="price-value"><?php echo $this->options->memberLifetimePrice; ?></span>
                        <span class="price-period">/永久</span>
                    </div>
                </div>
                <div class="plan-features">
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>解锁全站会员内容</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>每月更新资源抢先看</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>高清无水印资源下载</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>技术支持优先响应</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>历史内容归档查阅</span>
                    </div>
                    <div class="feature-item">
                        <i class="ri-check-line"></i>
                        <span>专属会员活动邀请</span>
                    </div>
                </div>
                <div class="plan-action">
                    <button class="plan-btn">选择此套餐</button>
                </div>
            </div>
        </div>
        
        <!-- 支付表单 -->
        <div class="checkout-section">
            <div class="order-summary">
                <h3 class="summary-title">订单摘要</h3>
                <div class="summary-item">
                    <span class="item-label">套餐类型</span>
                    <span class="item-value">年度会员</span>
                </div>
                <div class="summary-item">
                    <span class="item-label">会员价格</span>
                    <span class="item-value">¥<span id="selected_price"><?php echo $this->options->memberYearlyPrice; ?></span></span>
                </div>
            </div>
            
            <div class="payment-methods">
                <h3 class="payment-title">支付方式</h3>
                <div class="method-options">
                    <div class="payment-method active" data-method="alipay">
                        <div class="method-icon"><i class="ri-alipay-line"></i></div>
                        <div class="method-info">
                            <div class="method-name">支付宝</div>
                            <div class="method-desc">中国领先的第三方支付平台</div>
                        </div>
                    </div>
                    <div class="payment-method" data-method="wechat">
                        <div class="method-icon"><i class="ri-wechat-pay-line"></i></div>
                        <div class="method-info">
                            <div class="method-name">微信支付</div>
                            <div class="method-desc">腾讯旗下便捷安全的支付工具</div>
                        </div>
                    </div>
                    <div class="payment-method" data-method="card">
                        <div class="method-icon"><i class="ri-bank-card-line"></i></div>
                        <div class="method-info">
                            <div class="method-name">银行卡支付</div>
                            <div class="method-desc">支持各大银行的借记卡/信用卡</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <form action="<?php $this->options->siteUrl(); ?>payment-process.html" method="post" class="checkout-form">
                <input type="hidden" name="member_plan" id="selected_plan" value="yearly">
                <input type="hidden" name="payment_method" id="selected_method" value="alipay">
                
                <div class="form-group">
                    <label for="email">确认邮箱</label>
                    <input type="email" id="email" name="email" value="<?php $this->user->mail(); ?>" readonly>
                    <small class="form-tip">确认账单接收邮箱</small>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn-checkout">立即支付</button>
                </div>
                
                <div class="checkout-notice">
                    <p>点击"立即支付"，即表示您同意我们的<a href="#">服务条款</a>和<a href="#">隐私政策</a>。</p>
                </div>
            </form>
        </div>
    </div>
    
    <div class="faq-section">
        <h2 class="section-title">常见问题</h2>
        <div class="faq-grid">
            <div class="faq-item">
                <h3 class="faq-question"><i class="ri-question-line"></i> 如何选择适合我的会员方案？</h3>
                <p class="faq-answer">如果您只是想短期体验我们的会员服务，建议选择月度会员；若准备长期使用，年度会员更经济实惠；而终身会员则为重度用户提供一次性买断的选择，无需担心续费问题。</p>
            </div>
            <div class="faq-item">
                <h3 class="faq-question"><i class="ri-question-line"></i> 会员费用支付后可以退款吗？</h3>
                <p class="faq-answer">根据我们的服务条款，会员费用一经支付成功，不支持退款。但如果您在使用过程中遇到任何问题，我们的客服团队将竭诚为您解决。</p>
            </div>
            <div class="faq-item">
                <h3 class="faq-question"><i class="ri-question-line"></i> 会员权限可以在多台设备上使用吗？</h3>
                <p class="faq-answer">是的，您的会员账号可以在多台设备上登录使用，但我们不建议多人共享同一个账号，这可能导致账号安全问题。</p>
            </div>
            <div class="faq-item">
                <h3 class="faq-question"><i class="ri-question-line"></i> 支付成功后多久会员权限生效？</h3>
                <p class="faq-answer">通常情况下，支付成功后会员权限将立即生效。如遇系统处理延迟，最长不会超过10分钟。若超时仍未生效，请联系客服处理。</p>
            </div>
        </div>
    </div>
</div>

<style>
    .membership-container {
        display: flex;
        flex-wrap: wrap;
        gap: 40px;
        margin-bottom: 50px;
    }
    
    .membership-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        flex: 1 1 600px;
    }
    
    .membership-plan {
        background-color: #191919;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        padding: 25px;
        display: flex;
        flex-direction: column;
        flex: 1 1 calc(33.333% - 20px);
        transition: all 0.3s;
        position: relative;
        border: 2px solid transparent;
    }
    
    .membership-plan.active {
        border-color: #FE2C55;
        background-color: rgba(254, 44, 85, 0.05);
        transform: scale(1.03);
    }
    
    .membership-plan:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
    }
    
    .plan-tag {
        position: absolute;
        top: -10px;
        right: 20px;
        background: linear-gradient(to right, #FE2C55, #8134AF);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .plan-header {
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .plan-icon {
        width: 60px;
        height: 60px;
        background-color: rgba(254, 44, 85, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
    }
    
    .plan-icon i {
        color: #FE2C55;
        font-size: 30px;
    }
    
    .plan-title {
        font-size: 20px;
        margin-bottom: 10px;
        color: #fff;
    }
    
    .plan-price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        margin-bottom: 5px;
    }
    
    .price-currency {
        font-size: 16px;
        color: #ccc;
        margin-right: 2px;
    }
    
    .price-value {
        font-size: 32px;
        font-weight: 600;
        color: #FE2C55;
    }
    
    .price-period {
        font-size: 14px;
        color: #ccc;
        margin-left: 2px;
    }
    
    .price-discount {
        font-size: 12px;
        color: #52c41a;
        margin-top: 5px;
    }
    
    .plan-features {
        flex-grow: 1;
        margin-bottom: 20px;
    }
    
    .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        color: #fff;
    }
    
    .feature-item i {
        margin-right: 10px;
        font-size: 16px;
        color: #52c41a;
    }
    
    .feature-item.muted {
        color: #aaa;
    }
    
    .feature-item.muted i {
        color: #aaa;
    }
    
    .plan-action {
        text-align: center;
    }
    
    .plan-btn {
        background-color: transparent;
        color: #FE2C55;
        border: 2px solid #FE2C55;
        padding: 10px 0;
        width: 100%;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .plan-btn:hover, .active .plan-btn {
        background-color: #FE2C55;
        color: white;
    }
    
    /* 结账区域样式 */
    .checkout-section {
        flex: 1 1 350px;
        background-color: #191919;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        padding: 25px;
    }
    
    .order-summary {
        margin-bottom: 25px;
        padding-bottom: 25px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .summary-title, .payment-title {
        font-size: 18px;
        margin-bottom: 15px;
        color: #fff;
    }
    
    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    
    .item-label {
        color: #ccc;
    }
    
    .item-value {
        font-weight: 600;
    }
    
    .payment-methods {
        margin-bottom: 25px;
    }
    
    .method-options {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .payment-method {
        display: flex;
        align-items: center;
        padding: 15px;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        border: 2px solid transparent;
    }
    
    .payment-method:hover {
        background-color: rgba(255, 255, 255, 0.08);
    }
    
    .payment-method.active {
        border-color: #FE2C55;
        background-color: rgba(254, 44, 85, 0.05);
    }
    
    .method-icon {
        width: 40px;
        height: 40px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    
    .method-icon i {
        font-size: 20px;
        color: #FE2C55;
    }
    
    .method-name {
        font-weight: 600;
        margin-bottom: 2px;
    }
    
    .method-desc {
        font-size: 12px;
        color: #aaa;
    }
    
    .checkout-form .form-group {
        margin-bottom: 20px;
    }
    
    .checkout-form label {
        display: block;
        margin-bottom: 8px;
        color: #ccc;
    }
    
    .checkout-form input {
        width: 100%;
        padding: 12px 15px;
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        color: #fff;
        font-size: 14px;
    }
    
    .checkout-form .form-tip {
        display: block;
        margin-top: 5px;
        color: #aaa;
        font-size: 12px;
    }
    
    .form-actions {
        margin-bottom: 20px;
    }
    
    .btn-checkout {
        background: linear-gradient(to right, #FE2C55, #8134AF);
        color: white;
        border: none;
        padding: 12px 0;
        width: 100%;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .btn-checkout:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(254, 44, 85, 0.3);
    }
    
    .checkout-notice {
        text-align: center;
        font-size: 12px;
        color: #aaa;
    }
    
    .checkout-notice a {
        color: #FE2C55;
        text-decoration: underline;
    }
    
    /* FAQ区域样式 */
    .faq-section {
        margin-top: 40px;
        margin-bottom: 40px;
    }
    
    .section-title {
        font-size: 24px;
        margin-bottom: 20px;
    }
    
    .faq-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 20px;
    }
    
    .faq-item {
        background-color: #191919;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }
    
    .faq-question {
        display: flex;
        align-items: center;
        font-size: 16px;
        margin-bottom: 10px;
        color: #fff;
    }
    
    .faq-question i {
        color: #FE2C55;
        margin-right: 10px;
        font-size: 18px;
    }
    
    .faq-answer {
        color: #ccc;
        font-size: 14px;
        line-height: 1.6;
    }
    
    @media (max-width: 768px) {
        .membership-cards {
            flex-direction: column;
        }
        
        .membership-plan {
            flex: 1 1 auto;
        }
        
        .faq-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 会员套餐选择
        const membershipPlans = document.querySelectorAll('.membership-plan');
        
        membershipPlans.forEach(function(plan) {
            plan.addEventListener('click', function() {
                // 移除所有选中状态
                membershipPlans.forEach(function(p) {
                    p.classList.remove('active');
                });
                
                // 添加当前选中状态
                this.classList.add('active');
                
                // 更新选择的套餐ID和价格
                const planId = this.getAttribute('data-plan');
                const planPrice = this.getAttribute('data-price');
                const planName = this.querySelector('.plan-title').textContent;
                
                const selectedPlanInput = document.querySelector('#selected_plan');
                const selectedPriceElem = document.querySelector('#selected_price');
                const summaryTypeElem = document.querySelector('.summary-item .item-value');
                
                if (selectedPlanInput) {
                    selectedPlanInput.value = planId;
                }
                
                if (selectedPriceElem) {
                    selectedPriceElem.textContent = planPrice;
                }
                
                if (summaryTypeElem) {
                    summaryTypeElem.textContent = planName;
                }
            });
        });
        
        // 支付方式选择
        const paymentMethods = document.querySelectorAll('.payment-method');
        
        paymentMethods.forEach(function(method) {
            method.addEventListener('click', function() {
                // 移除所有选中状态
                paymentMethods.forEach(function(m) {
                    m.classList.remove('active');
                });
                
                // 添加当前选中状态
                this.classList.add('active');
                
                // 更新选择的支付方式
                const payMethod = this.getAttribute('data-method');
                const selectedMethodInput = document.querySelector('#selected_method');
                
                if (selectedMethodInput) {
                    selectedMethodInput.value = payMethod;
                }
            });
        });
    });
</script>

<?php $this->need('footer.php'); ?> 