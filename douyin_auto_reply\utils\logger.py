#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志记录工具模块
"""

import logging
import os
from datetime import datetime
from pathlib import Path

class Logger:
    """日志记录类"""
    
    def __init__(self, name="DouYinAutoReply", log_dir="logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建日志文件名
        today = datetime.now().strftime("%Y%m%d")
        self.log_file = self.log_dir / f"{name}_{today}.log"
        
        # 配置日志
        self.setup_logger()
    
    def setup_logger(self):
        """配置日志器"""
        # 创建日志器
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
        
        # 创建文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message):
        """调试信息"""
        self.logger.debug(message)
    
    def info(self, message):
        """一般信息"""
        self.logger.info(message)
    
    def warning(self, message):
        """警告信息"""
        self.logger.warning(message)
    
    def error(self, message):
        """错误信息"""
        self.logger.error(message)
    
    def critical(self, message):
        """严重错误"""
        self.logger.critical(message)

# 创建全局日志器实例
default_logger = Logger()

# 导出便捷函数
def log_debug(message):
    default_logger.debug(message)

def log_info(message):
    default_logger.info(message)

def log_warning(message):
    default_logger.warning(message)

def log_error(message):
    default_logger.error(message)

def log_critical(message):
    default_logger.critical(message) 