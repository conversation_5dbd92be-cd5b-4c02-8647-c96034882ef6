# WW Style - 抖音风格的Typecho主题

WW Style是一款黑色背景配合红色强调色的简约大气主题，集成会员和付费功能，适合各类内容创作者使用。

## 主题特性

* 抖音风格：黑色背景配合红色强调色，简约大气
* 响应式设计：完美适配PC和移动端设备
* 会员系统：支持多种会员等级和特权设置
* 付费内容：支持文章付费查看
* 优化交互：流畅的动画效果和交互体验
* 高性能：优化的代码结构和加载性能

## 最新优化

* **增强动画效果**：添加了抖音风格特有的卡片悬浮、按钮脉冲效果和流畅切换动画
* **图片懒加载**：提升页面加载速度和性能
* **移动端优化**：添加折叠式导航菜单，提升移动端浏览体验
* **性能优化**：预连接、预加载关键资源，减少阻塞渲染时间
* **可访问性**：增加ARIA标签和键盘导航支持
* **页面加载指示器**：提供更好的用户反馈
* **消息提示组件**：美观的全局消息提示
* **图标加载备用方案**：确保在网络环境不佳时仍能正常显示
* **现代化的交互体验**：更流畅的滚动动画和过渡效果

## 依赖组件

* [Remixicon](https://remixicon.com/) - 图标字体
* [Typecho](http://typecho.org/) - 1.1及以上版本

## 安装方法

1. 下载主题压缩包
2. 解压后将`ww_style`文件夹上传到Typecho的`usr/themes/`目录下
3. 登录Typecho后台，进入"外观"菜单，启用"WW Style"主题
4. 根据需要进行相关设置

## 主题设置

主题设置页面可以进行以下配置：

* 轮播图设置：可以自定义首页轮播图的图片和链接
* 会员设置：配置会员价格和权限
* 付费内容：设置付费内容的价格和显示方式
* 社交账号：配置页脚显示的社交账号链接

## 使用技巧

### 设置文章缩略图

在文章编辑页面的自定义字段中添加`thumb`字段，值为缩略图的URL地址。

### 设置会员专享内容

在文章编辑页面的自定义字段中添加`isPremium`字段，值为`1`表示该文章为会员专享。

### 设置付费内容

在文章编辑页面的自定义字段中添加`price`字段，值为付费价格（数字），例如`9.9`。

## 性能优化建议

* 使用CDN加速静态资源加载
* 压缩并优化图片大小，推荐使用WebP格式
* 配置浏览器缓存，减少资源重复加载

## 使用问题解决

### 图标显示异常

默认使用了多重备份的图标字体加载方案。如果仍有问题，请检查：
1. 是否能正常访问CDN资源
2. 可以尝试将图标字体下载到本地使用

### 移动端显示问题

如遇移动端显示异常，请尝试：
1. 清除浏览器缓存
2. 确认您的服务器是否支持HTTPS（部分动画效果在非安全连接下可能受限）

## 更新日志

### v1.0.1 (2023-08-15)
- 增强了轮播图功能，添加触摸滑动支持
- 优化页面加载性能，添加懒加载功能
- 改进移动端导航体验，添加折叠菜单
- 添加页面加载指示器和消息提示组件
- 修复了图标字体加载失败的问题

### v1.0.0 (2023-07-01)
- 首次发布

## 许可证

本主题采用 MIT 许可证发布。

## 鸣谢

感谢以下开源项目的支持：
* [Typecho](http://typecho.org/)
* [Remixicon](https://remixicon.com/)

## 主题特性

- **抖音风格设计**：黑色背景、红色强调，现代时尚的视觉效果
- **会员系统**：支持月度、年度和永久会员
- **付费内容**：支持文章内容付费阅读
- **响应式设计**：完美适配手机、平板和桌面设备
- **文章浏览量统计**：自动统计和显示文章浏览量
- **文章点赞功能**：用户可以为喜欢的文章点赞
- **评论系统增强**：支持评论嵌套和回复通知
- **会员中心**：用户可以管理自己的会员状态和购买历史

## 安装方法

1. 下载主题文件
2. 将主题文件夹上传到 Typecho 的 `/usr/themes/` 目录下
3. 登录 Typecho 后台，进入"外观"，启用 "WW Style" 主题
4. 进入主题设置页面，配置主题选项

## 主题配置

### 基本设置

- **网站Logo**：上传您的网站Logo（推荐尺寸：200x50像素，透明背景）
- **首页轮播图**：上传最多5张首页轮播图（推荐尺寸：1920x600像素）
- **默认封面图**：上传文章默认封面图（推荐尺寸：800x400像素）
- **社交媒体链接**：设置微博、微信、QQ等社交媒体链接
- **底部信息**：自定义网站底部显示的信息

### 会员系统设置

- **开启会员系统**：选择是否启用会员系统功能
- **会员价格设置**：设置月度、年度和永久会员的价格
- **会员特权设置**：配置会员可以享受的特权
- **支付方式设置**：配置支付宝、微信支付等付款方式
- **付款通知邮箱**：设置接收付款通知的邮箱地址

## 会员系统使用方法

### 会员内容设置

在文章编辑页面，您可以使用以下短代码来设置会员可见内容：

```
[member]这里是会员专享内容[/member]
```

非会员用户将看到一个提示框，提示他们开通会员才能查看内容。

### 付费内容设置

在文章编辑页面，您可以使用以下短代码来设置付费可见内容：

```
[pay price="9.9"]这里是付费内容[/pay]
```

未购买的用户将看到一个付费提示框，可以选择支付查看内容。

### 会员管理

管理员可以在后台查看和管理所有会员信息，包括：

1. 查看会员列表
2. 修改会员等级
3. 延长或缩短会员期限
4. 查看付费内容购买记录
5. 处理退款请求

## 数据库说明

本主题使用Typecho自带的`table.fields`表存储会员相关信息，无需额外创建表格。会员信息存储方式如下：

- 会员状态：`member_status` 字段，值为 `valid` 表示有效会员
- 会员等级：`member_level` 字段，值为 `monthly`、`yearly` 或 `lifetime`
- 会员过期时间：`member_expires` 字段，存储过期的UNIX时间戳，或`forever`（永久会员）
- 购买记录：`user_purchased_用户ID` 字段，值为 `yes` 表示已购买

## 注意事项

1. 本主题需要Typecho 1.2.1及以上版本
2. PHP版本需要7.2或更高
3. 确保服务器支持Typecho所需的所有PHP扩展
4. 主题包含的支付接口仅供参考，实际使用时请自行对接您的支付接口
5. 首次使用时，请务必配置好支付通知URL和邮箱，以确保能收到付款通知

## 常见问题

**Q: 如何修改主题颜色？**  
A: 可以在主题设置页面调整主要颜色和强调色。

**Q: 会员系统是否支持自动续费？**  
A: 目前版本不支持自动续费，用户需要手动续费。

**Q: 如何处理付款但未自动开通会员的情况？**  
A: 管理员可以在后台手动为用户开通会员，使用`setUserMemberStatus`函数。

## 联系与支持

如有任何问题或建议，请通过以下方式联系我们：

- GitHub Issues: [提交问题](https://github.com/yourusername/ww-style/issues)
- 邮箱：<EMAIL> 