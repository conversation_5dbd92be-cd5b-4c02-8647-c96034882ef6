// 表情面板管理类
class EmojiPanel {
    constructor() {
        this.categories = {
            faces: {
                title: '表情',
                emojis: ['😀','😃','😄','😁','😆','😅','😂','🤣','😊','😇','🙂','🙃','😉','😌','😍','🥰','😘','😗','😙','😚','😋','😛','😝','😜','🤪','🤨','🧐','🤓','😎','🤩']
            },
            hearts: {
                title: '爱心',
                emojis: ['❤️','🧡','💛','💚','💙','💜','🖤','🤍','🤎','💔','❣️','💕','💞','💓','💗','💖','💘','💝','💟','♥️','♡','💌','💋','💯','💢','💥','💫','💦','💨','💤']
            },
            gestures: {
                title: '手势',
                emojis: ['👋','🤚','✋','🖐️','👌','🤌','🤏','✌️','🤞','🫰','🤟','🤘','🤙','👈','👉','👆','👇','☝️','👍','👎','✊','👊','🤛','🤜','👏','👐','🤲','🫶','👏']
            },
            animals: {
                title: '动物',
                emojis: ['🐶','🐱','🐭','🐹','🐰','🦊','🐻','🐼','🐨','🐯','🦁','🐮','🐷','🐸','🐵','🐔','🐧','🐦','🐤','🦆','🦅','🦉','🦇','🐺','🐗','🐴','🦄','🐝','🦋','🐞']
            },
            foods: {
                title: '美食',
                emojis: ['🍎','🍐','🍊','🍋','🍌','🍉','🍇','🍓','🍈','🍒','🍑','🍍','🥝','🍅','🥑','🥦','🥬','🥒','🌶','🌽','🥐','🥯','🍞','🥖','🥨','🧀','🥚','🍳','🥞','🍔']
            },
            weather: {
                title: '天气',
                emojis: ['☀️','🌤️','⛅️','🌥️','☁️','🌦️','🌧️','⛈️','🌩️','🌨️','❄️','☃️','⛄️','🌬️','💨','🌪️','🌫️','🌈','☔️','⚡️','❄️','🌟','⭐️','🌙','🌘','🌍','🌎','🌏','🪐','🌠']
            }
        };

        this.panel = document.getElementById('emoji-panel');
        this.button = document.getElementById('emoji-btn');
        this.init();
    }

    init() {
        if (!this.panel || !this.button) return;

        // 初始化表情分类
        this.initCategories();
        
        // 绑定按钮点击事件
        this.button.onclick = (e) => {
            e.stopPropagation();
            this.togglePanel();
        };

        // 点击外部关闭面板
        document.addEventListener('click', (e) => {
            if (!this.panel.contains(e.target) && e.target !== this.button) {
                this.hidePanel();
            }
        });
    }

    initCategories() {
        // 添加表情到对应分类
        Object.entries(this.categories).forEach(([category, data]) => {
            const container = document.getElementById(category);
            if (container) {
                data.emojis.forEach(emoji => {
                    const span = document.createElement('span');
                    span.className = 'emoji-item';
                    span.textContent = emoji;
                    span.onclick = () => this.insertEmoji(emoji);
                    container.appendChild(span);
                });
            }
        });

        // 切换分类事件
        document.querySelectorAll('.emoji-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const category = tab.dataset.category;
                this.switchCategory(category);
            });
        });
    }

    switchCategory(category) {
        // 更新标签激活状态
        document.querySelectorAll('.emoji-tab').forEach(t => {
            t.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');
        
        // 更新分类显示状态
        document.querySelectorAll('.emoji-category').forEach(c => {
            c.classList.remove('active');
        });
        document.getElementById(category).classList.add('active');
    }

    togglePanel() {
        const currentTextarea = document.getElementById('textarea-user');
        if (!currentTextarea) return;

        const textareaRect = currentTextarea.getBoundingClientRect();
        const btnRect = this.button.getBoundingClientRect();
        
        // 计算最佳位置
        const panelHeight = 320;
        let top = btnRect.top + window.scrollY - panelHeight - 10;
        
        if (top < window.scrollY + 10) {
            top = btnRect.bottom + window.scrollY + 10;
        }
        
        const left = textareaRect.left + (textareaRect.width - 360) / 2;
        
        this.panel.style.position = 'absolute';
        this.panel.style.top = `${top}px`;
        this.panel.style.left = `${Math.max(10, left)}px`;
        
        this.panel.style.display = this.panel.style.display === 'block' ? 'none' : 'block';
    }

    hidePanel() {
        if (this.panel) {
            this.panel.style.display = 'none';
        }
    }

    insertEmoji(emoji) {
        const textarea = document.getElementById('textarea-user');
        if (!textarea) return;

        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;
        
        textarea.value = text.substring(0, start) + emoji + text.substring(end);
        textarea.focus();
        textarea.selectionStart = textarea.selectionEnd = start + emoji.length;
        
        this.hidePanel();
    }
}

// 导出表情面板类
window.EmojiPanel = EmojiPanel; 