<?php ?><?php // /* *****  * @自助授权：https://sq.shilin.studio  * @自助下单：https://order.shilin.studio  * @Author: 诗林工作室  * @AuthorUri: https://shilin.studio  * @Date: 2025-03-30 20:49:54  * @LastEditTime: 2025-03-30 05:48:38  * Copyright (c) 2024 by Shilin Studio All Rights Reserved. */ - by 贝塔PHP加密|https://sg.bt58.vip ?><?php
if(!function_exists('sg_load')){$__v=phpversion();$__x=explode('.',$__v);$__v2=$__x[0].'.'.(int)$__x[1];$__u=strtolower(substr(php_uname(),0,3));$__ts=(@constant('PHP_ZTS') || @constant('ZEND_THREAD_SAFE')?'ts':'');$__f=$__f0='ixed.'.$__v2.$__ts.'.'.$__u;$__ff=$__ff0='ixed.'.$__v2.'.'.(int)$__x[2].$__ts.'.'.$__u;$__ed=@ini_get('extension_dir');$__e=$__e0=@realpath($__ed);$__dl=function_exists('dl') && function_exists('file_exists') && @ini_get('enable_dl') && !@ini_get('safe_mode');if($__dl && $__e && version_compare($__v,'5.2.5','<') && function_exists('getcwd') && function_exists('dirname')){$__d=$__d0=getcwd();if(@$__d[1]==':') {$__d=str_replace('\\','/',substr($__d,2));$__e=str_replace('\\','/',substr($__e,2));}$__e.=($__h=str_repeat('/..',substr_count($__e,'/')));$__f='/ixed/'.$__f0;$__ff='/ixed/'.$__ff0;while(!file_exists($__e.$__d.$__ff) && !file_exists($__e.$__d.$__f) && strlen($__d)>1){$__d=dirname($__d);}if(file_exists($__e.$__d.$__ff)) dl($__h.$__d.$__ff); else if(file_exists($__e.$__d.$__f)) dl($__h.$__d.$__f);}if(!function_exists('sg_load') && $__dl && $__e0){if(file_exists($__e0.'/'.$__ff0)) dl($__ff0); else if(file_exists($__e0.'/'.$__f0)) dl($__f0);}if(!function_exists('sg_load')){$__ixedurl='https://www.sourceguardian.com/loaders/download.php?php_v='.urlencode($__v).'&php_ts='.($__ts?'1':'0').'&php_is='.@constant('PHP_INT_SIZE').'&os_s='.urlencode(php_uname('s')).'&os_r='.urlencode(php_uname('r')).'&os_m='.urlencode(php_uname('m'));$__sapi=php_sapi_name();if(!$__e0) $__e0=$__ed;if(function_exists('php_ini_loaded_file')) $__ini=php_ini_loaded_file(); else $__ini='php.ini';if((substr($__sapi,0,3)=='cgi')||($__sapi=='cli')||($__sapi=='embed')){$__msg="\nPHP script '".__FILE__."' is protected by SourceGuardian and requires a SourceGuardian loader '".$__f0."' to be installed.\n\n1) Download the required loader '".$__f0."' from the SourceGuardian site: ".$__ixedurl."\n2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="\n3) Edit ".$__ini." and add 'extension=".$__f0."' directive";}}$__msg.="\n\n";}else{$__msg="<html><body>PHP script '".__FILE__."' is protected by <a href=\"https://www.sourceguardian.com/\">SourceGuardian</a> and requires a SourceGuardian loader '".$__f0."' to be installed.<br><br>1) <a href=\"".$__ixedurl."\" target=\"_blank\">Click here</a> to download the required '".$__f0."' loader from the SourceGuardian site<br>2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="<br>3) Edit ".$__ini." and add 'extension=".$__f0."' directive<br>4) Restart the web server";}}$__msg.="</body></html>";}die($__msg);exit();}}return sg_load('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');
