export const models = [
  {
    label: "安卓",
    value: "android",
    width: 1224,
    height: 2700,
  },
  {
    label: "苹果",
    value: "ios",
    width: 1125,
    height: 2436,
  },
];
// iPhoneX
// 分辨率: 1125x2436

// iPhoneXR
// 分辨率: 828x1792

// iPhoneXs Max
// 分辨率: 1242x2688

// iPhone11
// 分辨率: 828x1792

// iPhone11 Pro
// 分辨率: 1125x2436

// iPhone12
// 分辨率: 1170x2532

// iPhone12 Mini
// 分辨率: 1080x2340

// iPhone11 Pro Max
// 分辨率: 1242x2688

// iPhone13 Mini
// 分辨率: 1080x2340

// iPhone13
// 分辨率: 1170x2532

// iPhone13 Pro Max
// 分辨率: 1242x2688

// iPhone14 Pro
// 分辨率: 1179x2556

// iPhone14 Pro Max
// 分辨率: 1290x2796

// ---

// Mate40
// 分辨率: 1080x2376

// Mate40pro
// 分辨率: 1200x2640

// Nova8
// 分辨率: 1080x2340

// Mi10
// 分辨率: 1080x2340

// Mi11
// 分辨率: 1440x3200

export const networkTypes = [
  {
    label: "Wifi",
    value: "wifi",
  },
  {
    label: "3G",
    value: "3",
  },
  {
    label: "4G",
    value: "4",
  },
  {
    label: "5G",
    value: "5",
  },
];

export const wifiSignals = [
  {
    label: "1格",
    value: "1",
  },
  {
    label: "2格",
    value: "2",
  },
  {
    label: "3格",
    value: "3",
  },
];

export const phoneSignals = [
  {
    label: "1格",
    value: "1",
  },
  {
    label: "2格",
    value: "2",
  },
  {
    label: "3格",
    value: "3",
  },
  {
    label: "4格",
    value: "4",
  },
];

export const addTypes = [
  {
    label: "文本",
    value: "text",
  }, 
  {
    label: "图片",
    value: "image",
  }, 
  {
    label: "转账",
    value: "transferAccounts",
  }, 
  {
    label: "红包",
    value: "redEnvelope",
  }, 
  {
    label: "语音",
    value: "voice",
  }, 
  {
    label: "音视频",
    value: "avInvite",
  }, 
  {
    label: "名片",
    value: "businessCard",
  },
  {
    label: "拍一拍",
    value: "takeAPat",
  },
  {
    label: "时间",
    value: "time",
  },
  {
    label: "撤回",
    value: "revoke",
  },
  {
    label: "系统消息",
    value: "system",
  },
]

export const weeks = [
  {
    label: "周一",
    value: "周一",
  },
  {
    label: "周二",
    value: "周二",
  },
  {
    label: "周三",
    value: "周三",
  },
  {
    label: "周四",
    value: "周四",
  },
  {
    label: "周五",
    value: "周五",
  },
  {
    label: "周六",
    value: "周六",
  },
  {
    label: "周日",
    value: "周日",
  },
  {
    label: "昨天",
    value: "昨天",
  },
];

export const morningAfternoon = [
  {
    label: "上午",
    value: "上午",
  },
  {
    label: "下午",
    value: "下午",
  },
]

export const emojiList = [
  "微笑",
  "撇嘴",
  "色",
  "发呆",
  "得意",
  "流泪",
  "害羞",
  "闭嘴",
  "睡",
  "大哭",
  "尴尬",
  "发怒",
  "调皮",
  "呲牙",
  "惊讶",
  "难过",
  "囧",
  "抓狂",
  "吐",
  "偷笑",
  "愉快",
  "白眼",
  "傲慢",
  "困",
  "惊恐",
  "憨笑",
  "悠闲",
  "咒骂",
  "疑问",
  "嘘",
  "晕",
  "衰",
  "骷髅",
  "敲打",
  "再见",
  "擦汗",
  "抠鼻",
  "鼓掌",
  "坏笑",
  "右哼哼",
  "鄙视",
  "委屈",
  "快哭了",
  "阴险",
  "亲亲",
  "可怜",
  "笑脸",
  "生病",
  "脸红",
  "破涕为笑",
  "恐惧",
  "失望",
  "无语",
  "嘿哈",
  "捂脸",
  "奸笑",
  "机智",
  "皱眉",
  "耶",
  "吃瓜",
  "加油",
  "汗",
  "天啊",
  "Emm",
  "社会社会",
  "旺财",
  "好的",
  "打脸",
  "哇",
  "翻白眼",
  "666",
  "让我看看",
  "叹气",
  "苦涩",
  "裂开",
  "嘴唇",
  "爱心",
  "心碎",
  "拥抱",
  "强",
  "弱",
  "握手",
  "胜利",
  "抱拳",
  "勾引",
  "拳头",
  "OK",
  "合十",
  "啤酒",
  "咖啡",
  "蛋糕",
  "玫瑰",
  "凋谢",
  "菜刀",
  "炸弹",
  "便便",
  "月亮",
  "太阳",
  "庆祝",
  "礼物",
  "红包",
  "發",
  "福",
  "烟花",
  "猪头",
  "跳跳",
  "发抖",
  "转圈",
]

export const avInviteTypes = [
  {
    label: "音频邀请",
    value: "audio",
  },
  {
    label: "视频邀请",
    value: "video",
  },
]

export const avInviteStates = [
  {
    label: "通话完成",
    value: "success",
  },
  {
    label: "已取消",
    value: "cancel",
  },
  {
    label: "忙线中",
    value: "busy",
  },
  {
    label: "已拒绝",
    value: "reject",
  },
]

export const patRoles = [
  {
    label: "对方",
    value: "other",
  },
  {
    label: "自己",
    value: "own",
  },
]

export const ynEnums = [
  {
    label: "是",
    value: true,
  },
  {
    label: "否",
    value: false,
  },
]