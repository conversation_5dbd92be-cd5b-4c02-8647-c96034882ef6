<?php
/**
 * 吴畏支付处理类
 * 
 * @package WuweiPay
 * <AUTHOR>
 * @link https://8ww.fun
 */
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 加载核心文件
require_once __DIR__ . '/wuweiPay.Core.php';

/**
 * 吴畏支付处理类
 */
class WuweiPayment
{
    /**
     * 核心对象
     */
    private $core;
    
    /**
     * 插件配置
     */
    private $options;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->core = new WuweiPayCore();
        $this->options = Helper::options()->plugin('WuweiPay');
    }
    
    /**
     * 创建支付
     * 
     * @param float $amount 金额
     * @param string $title 订单标题
     * @param string $type 订单类型
     * @param int $userId 用户ID
     * @param array $extra 额外数据
     * @return array 支付信息
     */
    public function createPayment($amount, $title, $type = WUWEIPAY_TYPE_GENERAL, $userId = 0, $extra = [])
    {
        // 创建订单
        $orderId = $this->core->createOrder($amount, $title, $type, $userId, $extra);
        
        // 返回支付信息
        return [
            'order_id' => $orderId,
            'amount' => $amount,
            'title' => $title,
            'type' => $type,
            'user_id' => $userId,
            'methods' => $this->getEnabledPaymentMethods(),
            'time' => time()
        ];
    }
    
    /**
     * 获取启用的支付方式
     * 
     * @return array 支付方式列表
     */
    public function getEnabledPaymentMethods()
    {
        $methods = [];
        $enabledMethods = $this->options->payment_methods;
        
        if (in_array('alipay', $enabledMethods)) {
            $methods['alipay'] = [
                'name' => '支付宝',
                'icon' => 'alipay'
            ];
        }
        
        if (in_array('wechat', $enabledMethods)) {
            $methods['wechat'] = [
                'name' => '微信支付',
                'icon' => 'wechat'
            ];
        }
        
        return $methods;
    }
    
    /**
     * 发起支付
     * 
     * @param string $orderId 订单号
     * @param string $method 支付方式
     * @return array 支付结果
     */
    public function doPay($orderId, $method)
    {
        // 获取订单信息
        $order = $this->core->getOrder($orderId);
        if (!$order) {
            return [
                'code' => WUWEIPAY_ERROR_ORDER,
                'msg' => '订单不存在'
            ];
        }
        
        // 检查订单状态
        if ($order['status'] == WUWEIPAY_STATUS_PAID) {
            return [
                'code' => WUWEIPAY_ERROR_ORDER,
                'msg' => '订单已支付'
            ];
        }
        
        // 更新订单支付方式
        $this->core->updateOrderMethod($orderId, $method);
        
        // 准备支付参数
        $params = [
            'app_id' => $this->options->wuweipay_appid,
            'out_trade_no' => $orderId,
            'total_amount' => $order['amount'],
            'subject' => $order['title'],
            'body' => $order['title'],
            'notify_url' => $this->getNotifyUrl(),
            'return_url' => $this->getReturnUrl(),
            'pay_type' => $method,
            'timestamp' => time()
        ];
        
        // 生成签名
        $params['sign'] = $this->core->generateSign($params);
        
        // 请求支付接口
        $result = $this->requestPay($params);
        
        // 处理支付结果
        if (isset($result['code']) && $result['code'] == 0) {
            return [
                'code' => WUWEIPAY_ERROR_NONE,
                'order_id' => $orderId,
                'pay_url' => $result['pay_url'],
                'qr_code' => isset($result['qr_code']) ? $result['qr_code'] : '',
                'qrpay' => isset($result['qr_code']),
                'webIn' => true,
                'client' => $this->isMobile() ? 'mobile' : 'pc',
                'title1' => '请选择支付方式',
                'title2' => '正在获取支付信息...',
                'title3' => '请扫码支付',
                'close' => '关闭'
            ];
        } else {
            return [
                'code' => WUWEIPAY_ERROR_PAYMENT,
                'msg' => isset($result['msg']) ? $result['msg'] : '支付请求失败'
            ];
        }
    }
    
    /**
     * 请求支付接口
     * 
     * @param array $params 支付参数
     * @return array 接口返回结果
     */
    private function requestPay($params)
    {
        $url = $this->options->wuweipay_gateway;
        
        // 发起HTTP请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        curl_close($ch);
        
        // 解析返回结果
        $result = json_decode($response, true);
        if (!$result) {
            return [
                'code' => WUWEIPAY_ERROR_UNKNOWN,
                'msg' => '解析支付接口返回数据失败'
            ];
        }
        
        return $result;
    }
    
    /**
     * 处理支付通知
     */
    public function handleNotify()
    {
        // 获取通知数据
        $data = $_POST;
        
        // 验证签名
        if (!isset($data['sign']) || !$this->core->verifySign($data, $data['sign'])) {
            echo 'sign_error';
            return;
        }
        
        // 验证订单号
        $orderId = isset($data['out_trade_no']) ? $data['out_trade_no'] : '';
        $order = $this->core->getOrder($orderId);
        if (!$order) {
            echo 'order_not_exist';
            return;
        }
        
        // 验证金额
        if ($order['amount'] != $data['total_amount']) {
            echo 'amount_error';
            return;
        }
        
        // 更新订单状态
        if ($this->core->completeOrder($orderId)) {
            echo 'success';
        } else {
            echo 'fail';
        }
    }
    
    /**
     * 处理支付返回
     */
    public function handleReturn()
    {
        // 获取返回数据
        $data = $_GET;
        
        // 验证签名
        if (!isset($data['sign']) || !$this->core->verifySign($data, $data['sign'])) {
            $this->redirect('/', '支付验证失败');
            return;
        }
        
        // 验证订单号
        $orderId = isset($data['out_trade_no']) ? $data['out_trade_no'] : '';
        $order = $this->core->getOrder($orderId);
        if (!$order) {
            $this->redirect('/', '订单不存在');
            return;
        }
        
        // 根据订单类型跳转到不同页面
        switch ($order['type']) {
            case WUWEIPAY_TYPE_VIP:
                $this->redirect('/user', '支付成功，正在跳转到用户中心...');
                break;
                
            default:
                $this->redirect('/', '支付成功，正在跳转到首页...');
                break;
        }
    }
    
    /**
     * 检查订单状态
     */
    public function checkOrderStatus()
    {
        // 获取订单号
        $orderId = isset($_POST['orderNum']) ? $_POST['orderNum'] : '';
        if (empty($orderId)) {
            $this->outputJson(['status' => 'error', 'msg' => '订单号不能为空']);
            return;
        }
        
        // 检查订单状态
        $status = $this->core->checkOrderStatus($orderId);
        if ($status == WUWEIPAY_STATUS_PAID) {
            $order = $this->core->getOrder($orderId);
            $this->outputJson([
                'status' => 'paid',
                'type' => $order['type']
            ]);
        } else {
            $this->outputJson(['status' => 'unpaid']);
        }
    }
    
    /**
     * 输出JSON数据
     * 
     * @param array $data 数据
     */
    private function outputJson($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * 页面跳转
     * 
     * @param string $url 跳转地址
     * @param string $message 提示信息
     */
    private function redirect($url, $message = '')
    {
        $html = '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>支付结果</title>';
        $html .= '<meta http-equiv="refresh" content="3;url=' . $url . '">';
        $html .= '<style>body{font-family:Arial,sans-serif;text-align:center;padding-top:50px;}h1{color:#32a852;}</style>';
        $html .= '</head><body><h1>支付结果</h1><p>' . $message . '</p></body></html>';
        echo $html;
        exit;
    }
    
    /**
     * 获取通知URL
     * 
     * @return string 通知URL
     */
    private function getNotifyUrl()
    {
        return Helper::options()->siteUrl . 'wuweipay/notify';
    }
    
    /**
     * 获取返回URL
     * 
     * @return string 返回URL
     */
    private function getReturnUrl()
    {
        return Helper::options()->siteUrl . 'wuweipay/return';
    }
    
    /**
     * 判断是否为移动设备
     * 
     * @return bool 是否为移动设备
     */
    private function isMobile()
    {
        $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        return preg_match('/(android|iphone|ipad|ipod|blackberry|windows phone)/i', $userAgent);
    }
}
