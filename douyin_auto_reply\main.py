#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音聊天自动回复工具
主程序入口文件
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_modules = [
        'pyautogui',
        'cv2',
        'pytesseract', 
        'PIL',
        'numpy',
        'loguru'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        messagebox.showerror(
            "依赖检查失败", 
            f"缺少以下依赖包：\n{', '.join(missing_modules)}\n\n请运行: pip install -r requirements.txt"
        )
        return False
    return True

def main():
    """主函数"""
    print("="*50)
    print("抖音聊天自动回复工具 v1.0")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 导入GUI界面
        from ui.main_window import MainWindow
        
        # 创建主窗口
        root = tk.Tk()
        app = MainWindow(root)
        
        print("✅ 程序启动成功！")
        print("📖 GUI界面已打开，请在界面中进行操作")
        
        # 运行主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("🔧 请确保所有依赖都已正确安装")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main() 