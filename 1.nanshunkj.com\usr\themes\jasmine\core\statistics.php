<?php
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

class SiteStatistics {
    // 获取网站运行天数
    public static function getBuildTime() {
        $options = Helper::options();
        $startDate = isset($options->startDate) ? $options->startDate : '2024-01-01';
        $buildDate = strtotime($startDate);
        $now = time();
        $days = floor(($now - $buildDate) / 86400);
        return $days;
    }

    // 获取文章总数
    public static function getPostCount() {
        $db = Typecho_Db::get();
        $posts = $db->fetchRow($db->select(array('COUNT(cid)' => 'num'))
                             ->from('table.contents')
                             ->where('type = ?', 'post')
                             ->where('status = ?', 'publish'));
        return $posts['num'];
    }

    // 获取访问量
    public static function getVisitorCount() {
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();
        
        try {
            // 创建访问统计表（如果不存在）
            $db->query("CREATE TABLE IF NOT EXISTS `{$prefix}visitors` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `ip` varchar(64) DEFAULT NULL,
                `visit_time` datetime DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
            
            // 记录访问
            $ip = $_SERVER['REMOTE_ADDR'];
            $time = date('Y-m-d H:i:s');
            $db->query($db->insert('table.visitors')->rows(array(
                'ip' => $ip,
                'visit_time' => $time
            )));
            
            // 获取总访问量
            $visitors = $db->fetchRow($db->select(array('COUNT(DISTINCT ip)' => 'num'))
                                    ->from('table.visitors'));
            return $visitors['num'];
        } catch (Exception $e) {
            return 0; // 发生错误时返回0
        }
    }

    // 显示统计信息
    public static function showStats() {
        echo '<div class="site-stats">';
        echo '<p>';
        echo '<span>网站已运行 ' . self::getBuildTime() . ' 天</span> | ';
        echo '<span>共有文章 ' . self::getPostCount() . ' 篇</span> | ';
        echo '<span>累计访问 ' . self::getVisitorCount() . ' 人次</span>';
        echo '</p>';
        echo '</div>';
    }

    // 获取CSS样式
    public static function getStyles() {
        return <<<EOT
<style>
.site-stats {
    padding: 10px 0;
    text-align: center;
    color: #666;
    font-size: 14px;
}
.site-stats span {
    margin: 0 10px;
}
</style>
EOT;
    }
} 