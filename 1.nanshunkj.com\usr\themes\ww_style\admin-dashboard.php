<?php
/**
 * 管理员仪表盘页面
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 检查用户是否登录且是管理员，否则跳转到登录页面
if (!$this->user->hasLogin() || !$this->user->pass('administrator', true)) {
    $this->response->redirect($this->options->loginUrl);
}

// 获取统计数据
$summary = generateOrderSummary();
$activeMembers = getActiveMembers(5);

$this->need('header.php');
?>

<div class="main-container">
    <div class="page-header">
        <h1 class="page-title"><i class="ri-dashboard-line"></i> 管理仪表盘</h1>
        <p class="page-desc">查看网站数据统计和会员信息概览</p>
    </div>
    
    <div class="admin-card">
        <div class="admin-tabs">
            <a href="<?php $this->options->siteUrl(); ?>admin-dashboard.php" class="tab-item active">
                <i class="ri-home-4-line"></i> 仪表盘
            </a>
            <a href="<?php $this->options->siteUrl(); ?>admin-member.php" class="tab-item">
                <i class="ri-team-line"></i> 会员管理
            </a>
            <a href="<?php $this->options->siteUrl(); ?>admin-dashboard.php?section=orders" class="tab-item">
                <i class="ri-shopping-cart-line"></i> 订单管理
            </a>
            <a href="<?php $this->options->siteUrl(); ?>admin-dashboard.php?section=settings" class="tab-item">
                <i class="ri-settings-line"></i> 系统设置
            </a>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-icon"><i class="ri-user-star-line"></i></div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo $summary['totalMembers']; ?></div>
                    <div class="stat-label">会员总数</div>
                </div>
                <div class="stat-trend up">
                    <i class="ri-arrow-up-s-line"></i> <?php echo $summary['newMembers']; ?> 新增
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="ri-money-cny-circle-line"></i></div>
                <div class="stat-info">
                    <div class="stat-value">¥<?php echo number_format($summary['totalIncome'], 2); ?></div>
                    <div class="stat-label">总收入</div>
                </div>
                <div class="stat-trend up">
                    <i class="ri-arrow-up-s-line"></i> ¥<?php echo number_format($summary['todayIncome'], 2); ?> 今日
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="ri-shopping-cart-line"></i></div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo $summary['totalOrders']; ?></div>
                    <div class="stat-label">订单总数</div>
                </div>
                <div class="stat-trend <?php echo $summary['conversionRate'] > 50 ? 'up' : 'down'; ?>">
                    <?php echo $summary['conversionRate']; ?>% 支付率
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="ri-vip-crown-line"></i></div>
                <div class="stat-info">
                    <div class="stat-value"><?php echo $summary['lifetimeMembers']; ?></div>
                    <div class="stat-label">永久会员</div>
                </div>
                <div class="stat-trend">
                    <?php echo $summary['monthlyMembers']; ?> 月付 / <?php echo $summary['yearlyMembers']; ?> 年付
                </div>
            </div>
        </div>
        
        <div class="dashboard-sections">
            <!-- 图表区域 -->
            <div class="dashboard-section chart-section">
                <div class="section-header">
                    <h3 class="section-title">收入趋势</h3>
                    <div class="section-actions">
                        <button class="btn-filter active" data-period="7">7天</button>
                        <button class="btn-filter" data-period="30">30天</button>
                        <button class="btn-filter" data-period="90">90天</button>
                    </div>
                </div>
                
                <div class="chart-container">
                    <!-- 这里放置图表，实际项目中应使用ECharts或其他图表库 -->
                    <div class="placeholder-chart">
                        <div class="chart-bars">
                            <?php for ($i = 0; $i < 7; $i++): ?>
                            <div class="chart-bar" style="height: <?php echo rand(20, 100); ?>%;">
                                <div class="bar-tooltip">¥<?php echo rand(10, 1000); ?></div>
                            </div>
                            <?php endfor; ?>
                        </div>
                        <div class="chart-x-axis">
                            <?php 
                            for ($i = 6; $i >= 0; $i--) {
                                echo '<div class="axis-label">' . date('m/d', strtotime('-' . $i . ' days')) . '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 会员分布 -->
            <div class="dashboard-section member-section">
                <div class="section-header">
                    <h3 class="section-title">会员分布</h3>
                </div>
                
                <div class="member-distribution">
                    <div class="donut-chart">
                        <!-- 这里放置环形图，实际项目中应使用图表库 -->
                        <div class="donut-placeholder">
                            <div class="donut-segment monthly" style="--percentage: <?php echo $summary['totalMembers'] > 0 ? ($summary['monthlyMembers'] / $summary['totalMembers']) * 100 : 0; ?>%;"></div>
                            <div class="donut-segment yearly" style="--percentage: <?php echo $summary['totalMembers'] > 0 ? ($summary['yearlyMembers'] / $summary['totalMembers']) * 100 : 0; ?>%;"></div>
                            <div class="donut-segment lifetime" style="--percentage: <?php echo $summary['totalMembers'] > 0 ? ($summary['lifetimeMembers'] / $summary['totalMembers']) * 100 : 0; ?>%;"></div>
                            <div class="donut-label"><?php echo $summary['totalMembers']; ?></div>
                        </div>
                    </div>
                    
                    <div class="distribution-legend">
                        <div class="legend-item">
                            <div class="legend-color monthly"></div>
                            <div class="legend-text">
                                <div class="legend-label">月度会员</div>
                                <div class="legend-value"><?php echo $summary['monthlyMembers']; ?> (<?php echo $summary['totalMembers'] > 0 ? round(($summary['monthlyMembers'] / $summary['totalMembers']) * 100) : 0; ?>%)</div>
                            </div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color yearly"></div>
                            <div class="legend-text">
                                <div class="legend-label">年度会员</div>
                                <div class="legend-value"><?php echo $summary['yearlyMembers']; ?> (<?php echo $summary['totalMembers'] > 0 ? round(($summary['yearlyMembers'] / $summary['totalMembers']) * 100) : 0; ?>%)</div>
                            </div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color lifetime"></div>
                            <div class="legend-text">
                                <div class="legend-label">永久会员</div>
                                <div class="legend-value"><?php echo $summary['lifetimeMembers']; ?> (<?php echo $summary['totalMembers'] > 0 ? round(($summary['lifetimeMembers'] / $summary['totalMembers']) * 100) : 0; ?>%)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="dashboard-sections">
            <!-- 近期订单 -->
            <div class="dashboard-section orders-section">
                <div class="section-header">
                    <h3 class="section-title">近期订单</h3>
                    <div class="section-actions">
                        <a href="<?php $this->options->siteUrl(); ?>admin-dashboard.php?section=orders" class="btn-text">查看全部</a>
                    </div>
                </div>
                
                <div class="recent-orders">
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>订单ID</th>
                                    <th>用户</th>
                                    <th>商品</th>
                                    <th>金额</th>
                                    <th>日期</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $recentOrders = getAllOrders('', '', 1, 5)['orders'];
                                if (!empty($recentOrders)):
                                    foreach ($recentOrders as $order):
                                ?>
                                <tr>
                                    <td><?php echo $order['id']; ?></td>
                                    <td><?php echo $order['username']; ?></td>
                                    <td><?php echo $order['title']; ?></td>
                                    <td>¥<?php echo $order['amount']; ?></td>
                                    <td><?php echo date('Y-m-d H:i', $order['created']); ?></td>
                                    <td>
                                        <?php if ($order['status'] === 'paid'): ?>
                                        <span class="status success">已支付</span>
                                        <?php elseif ($order['status'] === 'pending'): ?>
                                        <span class="status warning">待支付</span>
                                        <?php else: ?>
                                        <span class="status error">失败</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php 
                                    endforeach;
                                else:
                                ?>
                                <tr>
                                    <td colspan="6" class="empty-data">暂无订单数据</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 活跃会员 -->
            <div class="dashboard-section members-section">
                <div class="section-header">
                    <h3 class="section-title">活跃会员</h3>
                    <div class="section-actions">
                        <a href="<?php $this->options->siteUrl(); ?>admin-member.php" class="btn-text">管理会员</a>
                    </div>
                </div>
                
                <div class="active-members">
                    <?php if (!empty($activeMembers)): ?>
                        <?php foreach ($activeMembers as $member): ?>
                        <div class="member-card">
                            <img src="<?php echo getUserAvatar($member['mail'], 50); ?>" alt="<?php echo $member['screenName']; ?>" class="member-avatar">
                            <div class="member-info">
                                <div class="member-name"><?php echo $member['screenName']; ?></div>
                                <div class="member-meta">
                                    <span class="member-level"><?php echo $member['level']; ?></span>
                                    <span class="member-expires"><?php echo $member['expires']; ?>到期</span>
                                </div>
                            </div>
                            <div class="member-actions">
                                <a href="<?php $this->options->siteUrl(); ?>admin-member.php?edit=<?php echo $member['uid']; ?>" class="btn-icon">
                                    <i class="ri-edit-line"></i>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-data">暂无活跃会员</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* 仪表盘样式 */
    .stats-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background-color: #222;
        border-radius: 12px;
        padding: 20px;
        flex: 1;
        min-width: 220px;
        display: flex;
        align-items: center;
        position: relative;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background-color: #191919;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 15px;
        font-size: 22px;
        color: #FE2C55;
    }
    
    .stat-info {
        flex: 1;
    }
    
    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #aaa;
        font-size: 14px;
    }
    
    .stat-trend {
        position: absolute;
        right: 20px;
        bottom: 20px;
        font-size: 12px;
        color: #aaa;
        display: flex;
        align-items: center;
    }
    
    .stat-trend.up {
        color: #10B981;
    }
    
    .stat-trend.down {
        color: #EF4444;
    }
    
    .stat-trend i {
        margin-right: 3px;
        font-size: 16px;
    }
    
    .dashboard-sections {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .dashboard-section {
        background-color: #222;
        border-radius: 12px;
        padding: 20px;
        flex: 1;
        min-width: 350px;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        margin: 0;
    }
    
    .section-actions {
        display: flex;
        gap: 10px;
    }
    
    .btn-filter {
        background-color: #333;
        color: #aaa;
        border: none;
        border-radius: 5px;
        padding: 5px 10px;
        font-size: 12px;
        cursor: pointer;
    }
    
    .btn-filter.active {
        background-color: #FE2C55;
        color: #fff;
    }
    
    .btn-text {
        color: #FE2C55;
        font-size: 14px;
        text-decoration: none;
        display: flex;
        align-items: center;
    }
    
    .btn-text:hover {
        text-decoration: underline;
    }
    
    /* 图表样式 */
    .chart-container {
        height: 250px;
    }
    
    .placeholder-chart {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .chart-bars {
        flex: 1;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        padding-bottom: 10px;
    }
    
    .chart-bar {
        width: 25px;
        background: linear-gradient(to top, #FE2C55, #8134AF);
        border-radius: 3px 3px 0 0;
        position: relative;
    }
    
    .bar-tooltip {
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #191919;
        color: #fff;
        padding: 3px 6px;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.2s;
    }
    
    .chart-bar:hover .bar-tooltip {
        opacity: 1;
    }
    
    .chart-x-axis {
        height: 30px;
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #333;
    }
    
    .axis-label {
        font-size: 12px;
        color: #888;
        text-align: center;
        padding-top: 5px;
        width: 25px;
    }
    
    /* 会员分布环形图 */
    .member-distribution {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .donut-chart {
        width: 180px;
        height: 180px;
    }
    
    .donut-placeholder {
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 50%;
        background-color: #333;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .donut-segment {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
        transform-origin: center left;
    }
    
    .donut-segment.monthly {
        background-color: #FE2C55;
        transform: rotate(0deg);
        clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
    }
    
    .donut-segment.yearly {
        background-color: #7F5AF0;
        transform: rotate(calc(3.6deg * var(--percentage)));
        clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
    }
    
    .donut-segment.lifetime {
        background-color: #4CC9F0;
        transform: rotate(calc(3.6deg * (var(--percentage) + var(--percentage))));
        clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
    }
    
    .donut-label {
        position: absolute;
        width: 120px;
        height: 120px;
        background-color: #222;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #fff;
    }
    
    .distribution-legend {
        flex: 1;
        margin-left: 30px;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .legend-color {
        width: 14px;
        height: 14px;
        border-radius: 3px;
        margin-right: 10px;
    }
    
    .legend-color.monthly {
        background-color: #FE2C55;
    }
    
    .legend-color.yearly {
        background-color: #7F5AF0;
    }
    
    .legend-color.lifetime {
        background-color: #4CC9F0;
    }
    
    .legend-text {
        display: flex;
        flex-direction: column;
    }
    
    .legend-label {
        font-size: 14px;
        color: #ccc;
    }
    
    .legend-value {
        font-weight: 500;
    }
    
    /* 近期订单 */
    .table-responsive {
        overflow-x: auto;
    }
    
    .admin-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .admin-table th {
        padding: 12px;
        text-align: left;
        color: #888;
        font-weight: 500;
        border-bottom: 1px solid #333;
    }
    
    .admin-table td {
        padding: 12px;
        border-bottom: 1px solid #333;
    }
    
    .admin-table tr:last-child td {
        border-bottom: none;
    }
    
    .status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 20px;
        font-size: 12px;
        background-color: #333;
    }
    
    .status.success {
        background-color: rgba(16, 185, 129, 0.2);
        color: #10B981;
    }
    
    .status.warning {
        background-color: rgba(245, 158, 11, 0.2);
        color: #F59E0B;
    }
    
    .status.error {
        background-color: rgba(239, 68, 68, 0.2);
        color: #EF4444;
    }
    
    .empty-data {
        text-align: center;
        padding: 20px;
        color: #888;
    }
    
    /* 活跃会员 */
    .active-members {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .member-card {
        display: flex;
        align-items: center;
        background-color: #191919;
        border-radius: 10px;
        padding: 12px;
    }
    
    .member-avatar {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        object-fit: cover;
        margin-right: 15px;
    }
    
    .member-info {
        flex: 1;
    }
    
    .member-name {
        font-weight: 500;
        margin-bottom: 5px;
    }
    
    .member-meta {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 12px;
    }
    
    .member-level {
        background-color: #FE2C55;
        color: #fff;
        padding: 2px 6px;
        border-radius: 10px;
    }
    
    .member-expires {
        color: #aaa;
    }
    
    .member-actions {
        display: flex;
        gap: 5px;
    }
    
    .btn-icon {
        width: 32px;
        height: 32px;
        border-radius: 16px;
        background-color: #333;
        color: #ccc;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.2s;
    }
    
    .btn-icon:hover {
        background-color: #FE2C55;
        color: #fff;
    }
    
    /* 响应式调整 */
    @media (max-width: 1200px) {
        .stats-cards {
            gap: 15px;
        }
        
        .stat-card {
            min-width: 200px;
        }
        
        .dashboard-section {
            min-width: 300px;
        }
    }
    
    @media (max-width: 992px) {
        .stats-cards {
            flex-wrap: wrap;
        }
        
        .stat-card {
            flex: 1 1 calc(50% - 15px);
        }
        
        .dashboard-section {
            flex: 1 1 100%;
        }
    }
    
    @media (max-width: 768px) {
        .stat-card {
            flex: 1 1 100%;
        }
        
        .member-distribution {
            flex-direction: column;
        }
        
        .distribution-legend {
            margin-left: 0;
            margin-top: 20px;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 图表日期筛选
    const filterBtns = document.querySelectorAll('.btn-filter');
    
    filterBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            // 移除所有按钮的选中状态
            filterBtns.forEach(function(b) {
                b.classList.remove('active');
            });
            
            // 添加当前按钮的选中状态
            this.classList.add('active');
            
            // 这里应该加载不同时间段的数据，实际项目中通过AJAX请求获取
            // 在这个简化版本中只做样式变化
        });
    });
    
    // 添加图表柱状图动画效果
    const chartBars = document.querySelectorAll('.chart-bar');
    
    chartBars.forEach(function(bar) {
        const height = bar.style.height;
        bar.style.height = '0%';
        
        setTimeout(function() {
            bar.style.transition = 'height 1s ease-out';
            bar.style.height = height;
        }, 100);
    });
});
</script>

<?php $this->need('footer.php'); ?> 