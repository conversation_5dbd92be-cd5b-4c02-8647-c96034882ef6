document.addEventListener('DOMContentLoaded', function() {
    const announcement = document.querySelector('.announcement-slide');
    const closeBtn = document.querySelector('.announcement-close');
    const content = document.querySelector('.announcement-content');
    
    // 如果公告内容为空，不显示公告
    if (!content.textContent.trim()) {
        return;
    }
    
    // 检查本地存储中的关闭状态
    const isHidden = localStorage.getItem('announcement_hidden');
    const lastShownDate = localStorage.getItem('announcement_date');
    const today = new Date().toDateString();
    
    // 如果今天没有显示过，且没有被手动关闭，则显示公告
    if (!isHidden || lastShownDate !== today) {
        setTimeout(() => {
            announcement.classList.add('active');
            localStorage.setItem('announcement_date', today);
        }, 1000);
    }
    
    // 点击关闭按钮
    closeBtn.addEventListener('click', function() {
        announcement.classList.remove('active');
        localStorage.setItem('announcement_hidden', 'true');
    });
}); 