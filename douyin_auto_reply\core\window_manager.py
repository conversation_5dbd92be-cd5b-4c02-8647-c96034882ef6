#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口管理模块
用于检测和操作抖音聊天窗口
"""

import time
import subprocess
import sys

try:
    import pyautogui
    import pygetwindow as gw
except ImportError:
    print("警告: pyautogui 或 pygetwindow 未安装，将使用基础功能")
    pyautogui = None
    gw = None

class WindowManager:
    """窗口管理类"""
    
    def __init__(self):
        self.douyin_window = None
        self.window_titles = [
            "抖音聊天",
            "抖音", 
            "Douyin",
            "抖音聊天 - 抖音",
        ]
        
        # 安全设置
        if pyautogui:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.5
    
    def find_douyin_window(self):
        """查找抖音聊天窗口"""
        if not gw:
            print("❌ pygetwindow 未安装，无法检测窗口")
            return False
            
        try:
            # 获取所有窗口
            windows = gw.getAllWindows()
            
            for window in windows:
                for title in self.window_titles:
                    if title in window.title:
                        self.douyin_window = window
                        print(f"✅ 找到抖音窗口: {window.title}")
                        return True
            
            print("❌ 未找到抖音聊天窗口")
            print("💡 请确保抖音聊天程序已启动")
            return False
            
        except Exception as e:
            print(f"❌ 查找窗口时出错: {e}")
            return False
    
    def activate_window(self):
        """激活抖音窗口"""
        if not self.douyin_window:
            return False
            
        try:
            self.douyin_window.activate()
            time.sleep(0.5)
            return True
        except Exception as e:
            print(f"❌ 激活窗口失败: {e}")
            return False
    
    def get_window_info(self):
        """获取窗口信息"""
        if not self.douyin_window:
            return None
            
        try:
            return {
                'title': self.douyin_window.title,
                'left': self.douyin_window.left,
                'top': self.douyin_window.top,
                'width': self.douyin_window.width,
                'height': self.douyin_window.height,
                'is_maximized': self.douyin_window.isMaximized,
                'is_minimized': self.douyin_window.isMinimized
            }
        except Exception as e:
            print(f"❌ 获取窗口信息失败: {e}")
            return None
    
    def is_window_active(self):
        """检查窗口是否处于活动状态"""
        if not self.douyin_window:
            return False
            
        try:
            return self.douyin_window == gw.getActiveWindow()
        except:
            return False
    
    def take_screenshot(self):
        """截取窗口截图"""
        if not pyautogui or not self.douyin_window:
            return None
            
        try:
            # 激活窗口
            self.activate_window()
            
            # 获取窗口区域
            left = self.douyin_window.left
            top = self.douyin_window.top
            width = self.douyin_window.width
            height = self.douyin_window.height
            
            # 截图
            screenshot = pyautogui.screenshot(region=(left, top, width, height))
            return screenshot
            
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    def click_position(self, x, y, relative=True):
        """点击指定位置"""
        if not pyautogui:
            print("❌ pyautogui 未安装，无法点击")
            return False
            
        try:
            if relative and self.douyin_window:
                # 相对窗口位置
                actual_x = self.douyin_window.left + x
                actual_y = self.douyin_window.top + y
            else:
                # 绝对位置
                actual_x = x
                actual_y = y
            
            pyautogui.click(actual_x, actual_y)
            return True
            
        except Exception as e:
            print(f"❌ 点击失败: {e}")
            return False
    
    def send_text(self, text):
        """发送文本到当前焦点"""
        if not pyautogui:
            print("❌ pyautogui 未安装，无法发送文本")
            return False
            
        try:
            pyautogui.typewrite(text)
            time.sleep(0.2)
            pyautogui.press('enter')
            return True
            
        except Exception as e:
            print(f"❌ 发送文本失败: {e}")
            return False 