var Markdown="object"==typeof exports&&"function"==typeof require?exports:{};(function(){function A(e){return e}function t(e){return!1}function q(){}function W(){}q.prototype={chain:function(e,n){var r=this[e];if(!r)throw new Error("unknown hook "+e);this[e]=r===A?n:function(e){var t=Array.prototype.slice.call(arguments,0);return t[0]=r.apply(null,t),n.apply(null,t)}},set:function(e,t){if(!this[e])throw new Error("unknown hook "+e);this[e]=t},addNoop:function(e){this[e]=A},addFalse:function(e){this[e]=t}},Markdown.HookCollection=q,W.prototype={set:function(e,t){this["s_"+e]=t},get:function(e){return this["s_"+e]}},Markdown.Converter=function(e){var c,l,s,n,r=this.hooks=new q;r.addNoop("plainLinkText"),r.add<PERSON><PERSON>("preConversion"),r.addNoop("postNormalization"),r.add<PERSON>oop("preBlockGamut"),r.addNoop("postBlockGamut"),r.addNoop("preSpanGamut"),r.addNoop("postSpanGamut"),r.addNoop("postConversion");var t,a,o,i,d,u=A,f=A;(e=e||{}).nonAsciiLetters&&(t=/[Q\u00aa\u00b5\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0370-\u0374\u0376-\u0377\u037a-\u037d\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u048a-\u0523\u0531-\u0556\u0559\u0561-\u0587\u05d0-\u05ea\u05f0-\u05f2\u0621-\u064a\u0660-\u0669\u066e-\u066f\u0671-\u06d3\u06d5\u06e5-\u06e6\u06ee-\u06fc\u06ff\u0710\u0712-\u072f\u074d-\u07a5\u07b1\u07c0-\u07ea\u07f4-\u07f5\u07fa\u0904-\u0939\u093d\u0950\u0958-\u0961\u0966-\u096f\u0971-\u0972\u097b-\u097f\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bd\u09ce\u09dc-\u09dd\u09df-\u09e1\u09e6-\u09f1\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a59-\u0a5c\u0a5e\u0a66-\u0a6f\u0a72-\u0a74\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abd\u0ad0\u0ae0-\u0ae1\u0ae6-\u0aef\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3d\u0b5c-\u0b5d\u0b5f-\u0b61\u0b66-\u0b6f\u0b71\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bd0\u0be6-\u0bef\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c33\u0c35-\u0c39\u0c3d\u0c58-\u0c59\u0c60-\u0c61\u0c66-\u0c6f\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbd\u0cde\u0ce0-\u0ce1\u0ce6-\u0cef\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d28\u0d2a-\u0d39\u0d3d\u0d60-\u0d61\u0d66-\u0d6f\u0d7a-\u0d7f\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0e01-\u0e30\u0e32-\u0e33\u0e40-\u0e46\u0e50-\u0e59\u0e81-\u0e82\u0e84\u0e87-\u0e88\u0e8a\u0e8d\u0e94-\u0e97\u0e99-\u0e9f\u0ea1-\u0ea3\u0ea5\u0ea7\u0eaa-\u0eab\u0ead-\u0eb0\u0eb2-\u0eb3\u0ebd\u0ec0-\u0ec4\u0ec6\u0ed0-\u0ed9\u0edc-\u0edd\u0f00\u0f20-\u0f29\u0f40-\u0f47\u0f49-\u0f6c\u0f88-\u0f8b\u1000-\u102a\u103f-\u1049\u1050-\u1055\u105a-\u105d\u1061\u1065-\u1066\u106e-\u1070\u1075-\u1081\u108e\u1090-\u1099\u10a0-\u10c5\u10d0-\u10fa\u10fc\u1100-\u1159\u115f-\u11a2\u11a8-\u11f9\u1200-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u1380-\u138f\u13a0-\u13f4\u1401-\u166c\u166f-\u1676\u1681-\u169a\u16a0-\u16ea\u1700-\u170c\u170e-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176c\u176e-\u1770\u1780-\u17b3\u17d7\u17dc\u17e0-\u17e9\u1810-\u1819\u1820-\u1877\u1880-\u18a8\u18aa\u1900-\u191c\u1946-\u196d\u1970-\u1974\u1980-\u19a9\u19c1-\u19c7\u19d0-\u19d9\u1a00-\u1a16\u1b05-\u1b33\u1b45-\u1b4b\u1b50-\u1b59\u1b83-\u1ba0\u1bae-\u1bb9\u1c00-\u1c23\u1c40-\u1c49\u1c4d-\u1c7d\u1d00-\u1dbf\u1e00-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u203f-\u2040\u2054\u2071\u207f\u2090-\u2094\u2102\u2107\u210a-\u2113\u2115\u2119-\u211d\u2124\u2126\u2128\u212a-\u212d\u212f-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2183-\u2184\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2c6f\u2c71-\u2c7d\u2c80-\u2ce4\u2d00-\u2d25\u2d30-\u2d65\u2d6f\u2d80-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2e2f\u3005-\u3006\u3031-\u3035\u303b-\u303c\u3041-\u3096\u309d-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312d\u3131-\u318e\u31a0-\u31b7\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fc3\ua000-\ua48c\ua500-\ua60c\ua610-\ua62b\ua640-\ua65f\ua662-\ua66e\ua67f-\ua697\ua717-\ua71f\ua722-\ua788\ua78b-\ua78c\ua7fb-\ua801\ua803-\ua805\ua807-\ua80a\ua80c-\ua822\ua840-\ua873\ua882-\ua8b3\ua8d0-\ua8d9\ua900-\ua925\ua930-\ua946\uaa00-\uaa28\uaa40-\uaa42\uaa44-\uaa4b\uaa50-\uaa59\uac00-\ud7a3\uf900-\ufa2d\ufa30-\ufa6a\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d\ufb1f-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe33-\ufe34\ufe4d-\ufe4f\ufe70-\ufe74\ufe76-\ufefc\uff10-\uff19\uff21-\uff3a\uff3f\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]/g,a="Q".charCodeAt(0),o="A".charCodeAt(0),i="Z".charCodeAt(0),d="a".charCodeAt(0)-i-1,u=function(e){return e.replace(t,function(e){for(var t,n=e.charCodeAt(0),r="";0<n;)a<=(t=n%51+o)&&t++,i<t&&(t+=d),r=String.fromCharCode(t)+r,n=n/51|0;return"Q"+r+"Q"})},f=function(e){return e.replace(/Q([A-PR-Za-z]{1,3})Q/g,function(e,t){for(var n,r=0,u=0;u<t.length;u++)n=t.charCodeAt(u),i<n&&(n-=d),a<n&&n--,r=51*r+(n-=o);return String.fromCharCode(r)})});var p=e.asteriskIntraWordEmphasis?function(e){return-1===e.indexOf("*")&&-1===e.indexOf("_")?e:(e=(e=(e=u(e)).replace(/(?=[^\r][*_]|[*_])(^|(?=\W__|(?!\*)[\W_]\*\*|\w\*\*\w)[^\r])(\*\*|__)(?!\2)(?=\S)((?:|[^\r]*?(?!\2)[^\r])(?=\S_|\w|\S\*\*(?:[\W_]|$)).)(?=__(?:\W|$)|\*\*(?:[^*]|$))\2/g,"$1<strong>$3</strong>")).replace(/(?=[^\r][*_]|[*_])(^|(?=\W_|(?!\*)(?:[\W_]\*|\D\*(?=\w)\D))[^\r])(\*|_)(?!\2\2\2)(?=\S)((?:(?!\2)[^\r])*?(?=[^\s_]_|(?=\w)\D\*\D|[^\s*]\*(?:[\W_]|$)).)(?=_(?:\W|$)|\*(?:[^*]|$))\2/g,"$1<em>$3</em>"),f(e))}:function(e){return-1===e.indexOf("*")&&-1===e.indexOf("_")?e:(e=(e=(e=u(e)).replace(/(^|[\W_])(?:(?!\1)|(?=^))(\*|_)\2(?=\S)([^\r]*?\S)\2\2(?!\2)(?=[\W_]|$)/g,"$1<strong>$3</strong>")).replace(/(^|[\W_])(?:(?!\1)|(?=^))(\*|_)(?=\S)((?:(?!\2)[^\r])*?\S)\2(?!\2)(?=[\W_]|$)/g,"$1<em>$3</em>"),f(e))};function g(e){return e=(e=(e=(e=(e=e.replace(/^(<(p|div|h[1-6]|blockquote|pre|table|dl|ol|ul|script|noscript|form|fieldset|iframe|math|ins|del)\b[^\r]*?\n<\/\2>[ \t]*(?=\n+))/gm,m)).replace(/^(<(p|div|h[1-6]|blockquote|pre|table|dl|ol|ul|script|noscript|form|fieldset|iframe|math)\b[^\r]*?.*<\/\2>[ \t]*(?=\n+)\n)/gm,m)).replace(/\n[ ]{0,3}((<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,m)).replace(/\n\n[ ]{0,3}(<!(--(?:|(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>[ \t]*(?=\n{2,}))/g,m)).replace(/(?:\n\n)([ ]{0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,m)}function h(e){return e=e.replace(/(^\n+|\n+$)/g,""),"\n\n~K"+(s.push(e)-1)+"K\n\n"}function m(e,t){return h(t)}this.makeHtml=function(e){if(c)throw new Error("Recursive call to converter.makeHtml");return c=new W,l=new W,s=[],n=0,e=(e=I(e="\n\n"+(e=(e=(e=(e=(e=r.preConversion(e)).replace(/~/g,"~T")).replace(/\$/g,"~D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n"))+"\n\n")).replace(/^[ \t]+$/gm,""),e=g(e=r.postNormalization(e)),e=v(e=e.replace(/^[ ]{0,3}\[([^\[\]]+)\]:[ \t]*\n?[ \t]*<?(\S+?)>?(?=\s|$)[ \t]*\n?[ \t]*((\n*)["(](.+?)[")][ \t]*)?(?:\n+)/gm,function(e,t,n,r,u,a){return t=t.toLowerCase(),c.set(t,$(n)),u?r:(a&&l.set(t,a.replace(/"/g,"&quot;")),"")})),e=(e=(e=e.replace(/~E(\d+)E/g,function(e,t){t=parseInt(t);return String.fromCharCode(t)})).replace(/~D/g,"$$")).replace(/~T/g,"~"),e=r.postConversion(e),s=l=c=null,e};var b=function(e){return v(e)};function v(e,t){e=r.preBlockGamut(e,b);var n="<hr />\n";return e=C(e=(e=(e=(e=e.replace(/^(.+)[ \t]*\n=+[ \t]*\n+/gm,function(e,t){return"<h1>"+w(t)+"</h1>\n\n"}).replace(/^(.+)[ \t]*\n-+[ \t]*\n+/gm,function(e,t){return"<h2>"+w(t)+"</h2>\n\n"}).replace(/^(\#{1,6})[ \t]*(.+?)[ \t]*\#*\n+/gm,function(e,t,n){t=t.length;return"<h"+t+">"+w(n)+"</h"+t+">\n\n"})).replace(/^[ ]{0,2}([ ]?\*[ ]?){3,}[ \t]*$/gm,n)).replace(/^[ ]{0,2}([ ]?-[ ]?){3,}[ \t]*$/gm,n)).replace(/^[ ]{0,2}([ ]?_[ ]?){3,}[ \t]*$/gm,n)),n=e,e=n=(n=(n+="~0").replace(/(?:\n\n|^\n?)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=~0))/g,function(e,t,n){return"\n\n"+(t="<pre><code>"+(t=(t=(t=I(t=E(H(t)))).replace(/^\n+/g,"")).replace(/\n+$/g,""))+"\n</code></pre>")+"\n\n"+n})).replace(/~0/,""),e=e.replace(/((^[ \t]*>[ \t]?.+\n(.+\n)*\n*)+)/gm,function(e,t){return h("<blockquote>\n"+(t=(t=(t=v(t=(t=(t=t.replace(/^[ \t]*>[ \t]?/gm,"~0")).replace(/~0/g,"")).replace(/^[ \t]+$/gm,""))).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,function(e,t){return t.replace(/^  /gm,"~0").replace(/~0/g,"")}))+"\n</blockquote>")}),e=function(e,t){for(var n=(e=(e=e.replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),r=[],u=/~K(\d+)K/,a=n.length,o=0;o<a;o++){var i=n[o];u.test(i)?r.push(i):/\S/.test(i)&&(i=(i=w(i)).replace(/^([ \t]*)/g,"<p>"),i+="</p>",r.push(i))}if(!t){a=r.length;for(o=0;o<a;o++)for(var c=!0;c;)c=!1,r[o]=r[o].replace(/~K(\d+)K/g,function(e,t){return c=!0,s[t]})}return r.join("\n\n")}(e=g(e=r.postBlockGamut(e,b)),t)}function w(e){var t;return e=r.preSpanGamut(e),e=e.replace(/(^|[^\\`])(`+)(?!`)([^\r]*?[^`])\2(?!`)/gm,function(e,t,n,r,u){return t+"<code>"+(r=(r=E(r=(r=r.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""))).replace(/:\/\//g,"~P"))+"</code>"}),e=e.replace(/(<[a-z\/!$]("[^"]*"|'[^']*'|[^'">])*>|<!(--(?:|(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>)/gi,function(e){return B(e.replace(/(.)<\/?code>(?=.)/g,"$1`"),"!"==e.charAt(1)?"\\`*_/":"\\`*_")}),e=e.replace(/\\(\\)/g,N).replace(/\\([`*_{}\[\]()>#+-.!])/g,N),e=-1===(t=e).indexOf("![")?t:t=(t=t.replace(/(!\[(.*?)\][ ]?(?:\n[ ]*)?\[(.*?)\])()()()()/g,T)).replace(/(!\[(.*?)\]\s?\([ \t]*()<?(\S+?)>?[ \t]*((['"])(.*?)\6[ \t]*)?\))/g,T),e=$(e=(e=function(e){e=e.replace(L,_);return e=e.replace(/<((https?|ftp):[^'">\s]+)>/gi,function(e,t){return'<a href="'+R(t)+'">'+r.plainLinkText(t)+"</a>"})}(e=-1===(t=e).indexOf("[")?t:t=(t=(t=t.replace(/(\[((?:\[[^\]]*\]|[^\[\]])*)\][ ]?(?:\n[ ]*)?\[(.*?)\])()()()()/g,k)).replace(/(\[((?:\[[^\]]*\]|[^\[\]])*)\]\([ \t]*()<?((?:\([^)]*\)|[^()\s])*?)>?[ \t]*((['"])(.*?)\6[ \t]*)?\))/g,k)).replace(/(\[([^\[\]]+)\])()()()()()/g,k))).replace(/~P/g,"://")),e=(e=p(e)).replace(/  +\n/g," <br>\n"),e=r.postSpanGamut(e)}function k(e,t,n,r,u,a,o,i){null==i&&(i="");n=n.replace(/:\/\//g,"~P"),r=r.toLowerCase();if(""==u)if(u="#"+(r=""==r?n.toLowerCase().replace(/ ?\n/g," "):r),null!=c.get(r))u=c.get(r),null!=l.get(r)&&(i=l.get(r));else{if(!(-1<t.search(/\(\s*\)$/m)))return t;u=""}u='<a href="'+(u=R(u))+'"';return""!=i&&(u+=' title="'+(i=B(i=x(i),"*_"))+'"'),u+=">"+n+"</a>"}function x(e){return e.replace(/>/g,"&gt;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}function T(e,t,n,r,u,a,o,i){r=r.toLowerCase(),i=i||"";if(""==u){if(u="#"+(r=""==r?n.toLowerCase().replace(/ ?\n/g," "):r),null==c.get(r))return t;u=c.get(r),null!=l.get(r)&&(i=l.get(r))}n=B(x(n),"*_[]()"),n='<img src="'+(u=B(u,"*_"))+'" alt="'+n+'"';return n+=' title="'+(i=B(i=x(i),"*_"))+'"',n+=" />"}function C(e,a){e+="~0";var t=/^(([ ]{0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(~0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm;return e=(e=n?e.replace(t,function(e,t,n){var r,u=t,t=-1<n.search(/[*+-]/g)?"ul":"ol";"ol"==t&&(r=parseInt(n,10));n=y(u,t,a),u="<"+t;return r&&1!==r&&(u+=' start="'+r+'"'),n=u+">"+(n=n.replace(/\s+$/,""))+"</"+t+">\n"}):e.replace(t=/(\n\n|^\n?)(([ ]{0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(~0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/g,function(e,t,n,r){var u,a=t,o=n,t=-1<r.search(/[*+-]/g)?"ul":"ol",n="<"+t;return(u="ol"==t?parseInt(r,10):u)&&1!==u&&(n+=' start="'+u+'"'),a+n+">\n"+y(o,t)+"</"+t+">\n"})).replace(/~0/,"")}var S={ol:"\\d+[.]",ul:"[*+-]"};function y(e,t,u){n++,e=e.replace(/\n{2,}$/,"\n"),e+="~0";var t=S[t],t=new RegExp("(^[ \\t]*)("+t+")[ \\t]+([^\\r]+?(\\n+))(?=(~0|\\1("+t+")[ \\t]+))","gm"),a=!1;return e=(e=e.replace(t,function(e,t,n,r){t=/\n\n$/.test(r);return t||-1<r.search(/\n{2,}/)||a?r=v(H(r),!0):(r=(r=C(H(r),!0)).replace(/\n$/,""),u||(r=w(r))),a=t,"<li>"+r+"</li>\n"})).replace(/~0/g,""),n--,e}function E(e){return e=B(e=(e=(e=e.replace(/&/g,"&amp;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),"*_{}[]\\",!1)}function $(e){return e=(e=e.replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?!]|~D)/gi,"&lt;")}var e="[-A-Z0-9+&@#/%=~_|[\\])]",L=new RegExp('(="|<)?\\b(https?|ftp)(://[-A-Z0-9+&@#/%?=~_|[\\]()!:,.;]*'+e+")(?=$|\\W)","gi"),F=new RegExp(e,"i");function _(e,t,n,r){if(t)return e;if(")"!==r.charAt(r.length-1))return"<"+n+r+">";for(var u=r.match(/[()]/g),a=0,o=0;o<u.length;o++)"("===u[o]?a<=0?a=1:a++:a--;var i,c="";return a<0&&(i=new RegExp("\\){1,"+-a+"}$"),r=r.replace(i,function(e){return c=e,""})),c&&(i=r.charAt(r.length-1),F.test(i)||(c=i+c,r=r.substr(0,r.length-1))),"<"+n+r+">"+c}function H(e){return e=(e=e.replace(/^(\t|[ ]{1,4})/gm,"~0")).replace(/~0/g,"")}function I(e){if(!/\t/.test(e))return e;var n,r=["    ","   ","  "," "],u=0;return e.replace(/[\n\t]/g,function(e,t){return"\n"===e?(u=t+1,e):(n=(t-u)%4,u=t+1,r[n])})}function R(e){return e=B(e=x(e),"*_:()[]")}function B(e,t,n){t="(["+t.replace(/([\[\]\\])/g,"\\$1")+"])";n&&(t="\\\\"+t);t=new RegExp(t,"g");return e=e.replace(t,N)}function N(e,t){return"~E"+t.charCodeAt(0)+"E"}}})(),function(){var b={},v={},w={},k=window.document,d=window.RegExp,x=window.navigator,f=72,T={isIE:/msie/.test(x.userAgent.toLowerCase()),isIE_5or6:/msie 6/.test(x.userAgent.toLowerCase())||/msie 5/.test(x.userAgent.toLowerCase()),isOpera:/opera/.test(x.userAgent.toLowerCase())},n={bold:"Strong <strong> Ctrl+B",boldexample:"strong text",italic:"Emphasis <em> Ctrl+I",italicexample:"emphasized text",link:"Hyperlink <a> Ctrl+L",linkdescription:"enter link description here",linkdialog:'<p><b>Insert Hyperlink</b></p><p>http://example.com/ "optional title"</p>',linkname:null,quote:"Blockquote <blockquote> Ctrl+Q",quoteexample:"Blockquote",code:"Code Sample <pre><code> Ctrl+K",codeexample:"enter code here",image:"Image <img> Ctrl+G",imagedescription:"enter image description here",imagedialog:"<p><b>Insert Image</b></p><p>http://example.com/images/diagram.jpg \"optional title\"<br>Need <a href='http://www.google.com/search?q=free+image+hosting' target='_blank'>free image hosting?</a></p>",imagename:null,olist:"Numbered List <ol> Ctrl+O",ulist:"Bulleted List <ul> Ctrl+U",litem:"List item",heading:"Heading <h1>/<h2> Ctrl+H",headingexample:"Heading",more:"More contents \x3c!--more--\x3e Ctrl+M",fullscreen:"FullScreen Ctrl+J",exitFullscreen:"Exit FullScreen Ctrl+E",fullscreenUnsupport:"Sorry, the browser dont support fullscreen api",hr:"Horizontal Rule <hr> Ctrl+R",undo:"Undo - Ctrl+Z",redo:"Redo - Ctrl+Y",redomac:"Redo - Ctrl+Shift+Z",ok:"OK",cancel:"Cancel",help:"Markdown Editing Help"};function t(){}function p(e){this.buttonBar=k.getElementById("wmd-button-bar"+e),this.preview=k.getElementById("wmd-preview"+e),this.input=k.getElementById("text")}function g(t,n){var r,u,a,o=this,i=[],c=0,l="none",s=function(e,t){l!=e&&(l=e,t||f()),T.isIE&&"moving"==l?a=null:u=setTimeout(d,1)},d=function(e){a=new C(n,e),u=void 0};this.setCommandMode=function(){l="command",f(),u=setTimeout(d,0)},this.canUndo=function(){return 1<c},this.canRedo=function(){return!!i[c+1]},this.undo=function(){o.canUndo()&&(r?(r.restore(),r=null):(i[c]=new C(n),i[--c].restore(),t&&t())),l="none",n.input.focus(),d()},this.redo=function(){o.canRedo()&&(i[++c].restore(),t&&t()),l="none",n.input.focus(),d()};var f=function(){var e=a||new C(n);if(!e)return!1;"moving"!=l?(r&&(i[c-1].text!=r.text&&(i[c++]=r),r=null),i[c++]=e,i[c+1]=null,t&&t()):r=r||e},p=function(e){var t=!1;if((e.ctrlKey||e.metaKey)&&!e.altKey){var n=e.charCode||e.keyCode;switch(String.fromCharCode(n).toLowerCase()){case"y":o.redo(),t=!0;break;case"z":e.shiftKey?o.redo():o.undo(),t=!0}}t&&(e.preventDefault&&e.preventDefault(),window.event&&(window.event.returnValue=!1))},g=function(e){e.ctrlKey||e.metaKey||(33<=(e=e.keyCode)&&e<=40||63232<=e&&e<=63235?s("moving"):8==e||46==e||127==e?s("deleting"):13==e?s("newlines"):27==e?s("escape"):(e<16||20<e)&&91!=e&&s("typing"))};!function(){b.addEvent(n.input,"keypress",function(e){!e.ctrlKey&&!e.metaKey||e.altKey||89!=e.keyCode&&90!=e.keyCode||e.preventDefault()});function e(){(T.isIE||a&&a.text!=n.input.value)&&null==u&&(l="paste",f(),d())}b.addEvent(n.input,"keydown",p),b.addEvent(n.input,"keydown",g),b.addEvent(n.input,"mousedown",function(){s("moving")}),n.input.onpaste=e,n.input.ondrop=e}(),d(!0),f()}function C(a,e){var o=this,i=a.input;this.init=function(){b.isVisible(i)&&(!e&&k.activeElement&&k.activeElement!==i||(this.setInputAreaSelectionStartEnd(),this.scrollTop=i.scrollTop,(!this.text&&i.selectionStart||0===i.selectionStart)&&(this.text=i.value)))},this.setInputAreaSelection=function(){var e;b.isVisible(i)&&(void 0===i.selectionStart||T.isOpera?k.selection&&(k.activeElement&&k.activeElement!==i||(i.focus(),(e=i.createTextRange()).moveStart("character",-i.value.length),e.moveEnd("character",-i.value.length),e.moveEnd("character",o.end),e.moveStart("character",o.start),e.select())):(i.focus(),i.selectionStart=o.start,i.selectionEnd=o.end,i.scrollTop=o.scrollTop))},this.setInputAreaSelectionStartEnd=function(){if(a.ieCachedRange||!i.selectionStart&&0!==i.selectionStart){if(k.selection){o.text=b.fixEolChars(i.value);var e=a.ieCachedRange||k.selection.createRange(),t=b.fixEolChars(e.text),n=""+t+"";e.text=n;var r=b.fixEolChars(i.value);e.moveStart("character",-n.length),e.text=t,o.start=r.indexOf(""),o.end=r.lastIndexOf("")-"".length;var u=o.text.length-b.fixEolChars(i.value).length;if(u){for(e.moveStart("character",-t.length);u--;)t+="\n",o.end+=1;e.text=t}a.ieCachedRange&&(o.scrollTop=a.ieCachedScrollTop),a.ieCachedRange=null,this.setInputAreaSelection()}}else o.start=i.selectionStart,o.end=i.selectionEnd},this.restore=function(){null!=o.text&&o.text!=i.value&&(i.value=o.text),this.setInputAreaSelection(),i.scrollTop=o.scrollTop},this.getChunks=function(){var e=new t;return e.before=b.fixEolChars(o.text.substring(0,o.start)),e.startTag="",e.selection=b.fixEolChars(o.text.substring(o.start,o.end)),e.endTag="",e.after=b.fixEolChars(o.text.substring(o.end)),e.scrollTop=o.scrollTop,e},this.setChunks=function(e){e.before=e.before+e.startTag,e.after=e.endTag+e.after,this.start=e.before.length,this.end=e.before.length+e.selection.length,this.text=e.before+e.selection+e.after,this.scrollTop=e.scrollTop},this.init()}function h(r,u,a){function o(){var e=0;return window.innerHeight?e=window.pageYOffset:k.documentElement&&k.documentElement.scrollTop?e=k.documentElement.scrollTop:k.body&&(e=k.body.scrollTop),e}function t(){var e,t,n;u.preview&&((t=u.input.value)&&t==c||(c=t,e=(new Date).getTime(),t=r.makeHtml(t),n=(new Date).getTime(),i=n-e,m(t)))}function n(){e&&(clearTimeout(e),e=void 0),e=setTimeout(t,3e3<i?3e3:i)}var e,i,c,l=function(e){return e.scrollHeight<=e.clientHeight?1:e.scrollTop/(e.scrollHeight-e.clientHeight)};this.refresh=function(e){e?(c="",t()):n()},this.processingTime=function(){return i};var s,d,f,p=!0,g=function(e){var t=u.preview,n=t.parentNode,r=t.nextSibling;n.removeChild(t),t.innerHTML=e,r?n.insertBefore(t,r):n.appendChild(t)},h=function(e){u.preview.innerHTML=e},m=function(e){var t,n=v.getTop(u.input)-o();u.preview&&(function(t){if(s)return s(t);try{h(t),s=h}catch(e){(s=g)(t)}}(e),a()),u.preview&&(u.preview.scrollTop=(u.preview.scrollHeight-u.preview.clientHeight)*l(u.preview)),p?p=!1:(t=v.getTop(u.input)-o(),T.isIE?setTimeout(function(){window.scrollBy(0,t-n)},0):window.scrollBy(0,t-n))};d=u.input,f=n,b.addEvent(d,"input",f),d.onpaste=f,d.ondrop=f,b.addEvent(d,"keypress",f),b.addEvent(d,"keydown",f),t(),u.preview&&(u.preview.scrollTop=0)}function m(c,u,r,a,o,n,l,s,d){var i=u.input,f={};!function(){var e=u.buttonBar,o=document.createElement("ul");o.id="wmd-button-row"+c,o.className="wmd-button-row",o=e.appendChild(o);var i=0,t=function(e,t,n,r){var u=document.createElement("li");u.className="wmd-button",u.style.left=i+"px",i+=25;var a=document.createElement("span");return u.id=e+c,u.appendChild(a),u.title=t,u.XShift=n,r&&(u.textOp=r),g(u,!0),o.appendChild(u),u},n=function(e){var t=document.createElement("li");t.className="wmd-spacer wmd-spacer"+e,t.id="wmd-spacer"+e+c,o.appendChild(t),i+=25};f.bold=t("wmd-bold-button",d("bold"),"0px",h("doBold")),f.italic=t("wmd-italic-button",d("italic"),"-20px",h("doItalic")),n(1),f.link=t("wmd-link-button",d("link"),"-40px",h(function(e,t){return this.doLinkOrImage(e,t,!1)})),f.quote=t("wmd-quote-button",d("quote"),"-60px",h("doBlockquote")),f.code=t("wmd-code-button",d("code"),"-80px",h("doCode")),f.image=t("wmd-image-button",d("image"),"-100px",h(function(e,t){return this.doLinkOrImage(e,t,!0)})),n(2),f.olist=t("wmd-olist-button",d("olist"),"-120px",h(function(e,t){this.doList(e,t,!0)})),f.ulist=t("wmd-ulist-button",d("ulist"),"-140px",h(function(e,t){this.doList(e,t,!1)})),f.heading=t("wmd-heading-button",d("heading"),"-160px",h("doHeading")),f.hr=t("wmd-hr-button",d("hr"),"-180px",h("doHorizontalRule")),f.more=t("wmd-more-button",d("more"),"-280px",h("doMore")),n(3),f.undo=t("wmd-undo-button",d("undo"),"-200px",null),f.undo.execute=function(e){e&&e.undo()};e=/win/.test(x.platform.toLowerCase())?d("redo"):d("redomac");f.redo=t("wmd-redo-button",e,"-220px",null),f.redo.execute=function(e){e&&e.redo()},n(4),f.fullscreen=t("wmd-fullscreen-button",d("fullscreen"),"-240px",null),f.fullscreen.execute=function(){l.doFullScreen(f,!0)},f.exitFullscreen=t("wmd-exit-fullscreen-button",d("exitFullscreen"),"-260px",null),f.exitFullscreen.style.display="none",f.exitFullscreen.execute=function(){l.doFullScreen(f,!1)},r.makeButton(f,t,h,w),s&&(n=document.createElement("li"),t=document.createElement("span"),n.appendChild(t),n.className="wmd-button wmd-help-button",n.id="wmd-help-button"+c,n.XShift="-300px",n.isHelp=!0,n.style.right="0px",n.title=d("help"),n.onclick=s.handler,g(n,!0),o.appendChild(n),f.help=n);m()}();var e="keydown";function p(e){if(i.focus(),e.textOp){a&&a.setCommandMode();var t=new C(u);if(!t)return;function n(){i.focus(),r&&t.setChunks(r),t.restore(),o.refresh()}var r=t.getChunks();e.textOp(r,n)||n()}e.execute&&e.execute(a)}function g(e,t){t?(T.isIE&&(e.onmousedown=function(){k.activeElement&&k.activeElement!==u.input||(u.ieCachedRange=document.selection.createRange(),u.ieCachedScrollTop=u.input.scrollTop)}),e.isHelp||(e.onclick=function(){return this.onmouseout&&this.onmouseout(),p(this),!1})):e.onmouseover=e.onmouseout=e.onclick=function(){}}function h(e){var t;return"string"==typeof e&&(e=n[t=e]),function(){e.apply(n,arguments),t&&r.commandExecuted(t)}}function m(){a&&(g(f.undo,a.canUndo()),g(f.redo,a.canRedo()))}T.isOpera&&(e="keypress"),b.addEvent(i,e,function(e){if(!e.ctrlKey&&!e.metaKey||e.altKey||e.shiftKey)9==e.keyCode&&window.fullScreenEntered&&((t={}).textOp=h("doTab"),p(t),e.preventDefault&&e.preventDefault(),window.event&&(window.event.returnValue=!1));else{var t=e.charCode||e.keyCode;switch(String.fromCharCode(t).toLowerCase()){case"b":p(f.bold);break;case"i":p(f.italic);break;case"l":p(f.link);break;case"q":p(f.quote);break;case"k":p(f.code);break;case"g":p(f.image);break;case"o":p(f.olist);break;case"u":p(f.ulist);break;case"m":p(f.more);break;case"j":p(f.fullscreen);break;case"e":p(f.exitFullscreen);break;case"h":p(f.heading);break;case"r":p(f.hr);break;case"y":p(f.redo);break;case"z":e.shiftKey?p(f.redo):p(f.undo);break;default:return}e.preventDefault&&e.preventDefault(),window.event&&(window.event.returnValue=!1)}}),b.addEvent(i,"keyup",function(e){!e.shiftKey||e.ctrlKey||e.metaKey||13===(e.charCode||e.keyCode)&&((e={}).textOp=h("doAutoindent"),p(e))}),T.isIE&&b.addEvent(i,"keydown",function(e){if(27===e.keyCode)return!1}),this.setUndoRedoButtonStates=m}function S(e,t){this.hooks=e,this.getString=t}Markdown.Editor=function(u,a,o){(o="function"==typeof(o=o||{}).handler?{helpButton:o}:o).strings=o.strings||{},o.helpButton&&(o.strings.help=o.strings.help||o.helpButton.title);function i(e){var t=o.strings[e]||n[e];return"imagename"!=e&&"linkname"!=e||(o.strings[e]=null),t}a=a||"";var c=this.hooks=new Markdown.HookCollection;c.addNoop("onPreviewRefresh"),c.addNoop("postBlockquoteCreation"),c.addFalse("insertImageDialog"),c.addFalse("insertLinkDialog"),c.addNoop("makeButton"),c.addNoop("commandExecuted"),c.addNoop("enterFullScreen"),c.addNoop("enterFakeFullScreen"),c.addNoop("exitFullScreen"),this.getConverter=function(){return u};var l,s=this;this.run=function(){var e,t,n,r;l||(l=new p(a),e=new S(c,i),t=new h(u,l,function(){c.onPreviewRefresh()}),/\?noundo/.test(k.location.href)||(n=new g(function(){t.refresh(),r&&r.setUndoRedoButtonStates()},l),this.textOperation=function(e){n.setCommandMode(),e(),s.refreshPreview()}),fullScreenManager=new y(c,i),(r=new m(a,l,c,n,t,e,fullScreenManager,o.helpButton,i)).setUndoRedoButtonStates(),(s.refreshPreview=function(){t.refresh(!0)})())}},t.prototype.findTags=function(e,t){var n,r=this;e&&(n=b.extendRegExp(e,"","$"),this.before=this.before.replace(n,function(e){return r.startTag=r.startTag+e,""}),n=b.extendRegExp(e,"^",""),this.selection=this.selection.replace(n,function(e){return r.startTag=r.startTag+e,""})),t&&(n=b.extendRegExp(t,"","$"),this.selection=this.selection.replace(n,function(e){return r.endTag=e+r.endTag,""}),n=b.extendRegExp(t,"^",""),this.after=this.after.replace(n,function(e){return r.endTag=e+r.endTag,""}))},t.prototype.trimWhitespace=function(e){var t,n,r=this;e?t=n="":(t=function(e){return r.before+=e,""},n=function(e){return r.after=e+r.after,""}),this.selection=this.selection.replace(/^(\s*)/,t).replace(/(\s*)$/,n)},t.prototype.skipLines=function(e,t,n){var r,u;if(void 0===e&&(e=1),void 0===t&&(t=1),e++,t++,navigator.userAgent.match(/Chrome/)&&"X".match(/()./),this.selection=this.selection.replace(/(^\n*)/,""),this.startTag=this.startTag+d.$1,this.selection=this.selection.replace(/(\n*$)/,""),this.endTag=this.endTag+d.$1,this.startTag=this.startTag.replace(/(^\n*)/,""),this.before=this.before+d.$1,this.endTag=this.endTag.replace(/(\n*$)/,""),this.after=this.after+d.$1,this.before){for(r=u="";e--;)r+="\\n?",u+="\n";this.before=this.before.replace(new d((r=n?"\\n*":r)+"$",""),u)}if(this.after){for(r=u="";t--;)r+="\\n?",u+="\n";this.after=this.after.replace(new d(r=n?"\\n*":r,""),u)}},b.isVisible=function(e){return window.getComputedStyle?"none"!==window.getComputedStyle(e,null).getPropertyValue("display"):e.currentStyle?"none"!==e.currentStyle.display:void 0},b.addEvent=function(e,t,n){e.attachEvent?e.attachEvent("on"+t,n):e.addEventListener(t,n,!1)},b.removeEvent=function(e,t,n){e.detachEvent?e.detachEvent("on"+t,n):e.removeEventListener(t,n,!1)},b.fixEolChars=function(e){return e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")},b.extendRegExp=function(e,t,n){null==t&&(t=""),null==n&&(n="");var r,e=e.toString();return e=(e=e.replace(/\/([gim]*)$/,function(e,t){return r=t,""})).replace(/(^\/|\/$)/g,""),new d(e=t+e+n,r)},v.getTop=function(e,t){var n=e.offsetTop;if(!t)for(;e=e.offsetParent;)n+=e.offsetTop;return n},v.getHeight=function(e){return e.offsetHeight||e.scrollHeight},v.getWidth=function(e){return e.offsetWidth||e.scrollWidth},v.getPageSize=function(){var e,t,n,r=self.innerHeight&&self.scrollMaxY?(e=k.body.scrollWidth,self.innerHeight+self.scrollMaxY):k.body.scrollHeight>k.body.offsetHeight?(e=k.body.scrollWidth,k.body.scrollHeight):(e=k.body.offsetWidth,k.body.offsetHeight);return self.innerHeight?(t=self.innerWidth,n=self.innerHeight):k.documentElement&&k.documentElement.clientHeight?(t=k.documentElement.clientWidth,n=k.documentElement.clientHeight):k.body&&(t=k.body.clientWidth,n=k.body.clientHeight),[Math.max(e,t),Math.max(r,n),t,n]},w.createBackground=function(){var e=k.createElement("div"),t=e.style;e.className="wmd-prompt-background",t.position="absolute",t.top="0",t.zIndex="1000",T.isIE?t.filter="alpha(opacity=50)":t.opacity="0.5";var n=v.getPageSize();return t.height=n[1]+"px",T.isIE?(t.left=k.documentElement.scrollLeft,t.width=k.documentElement.clientWidth):(t.left="0",t.width="100%"),k.body.appendChild(e),e},w.dialog=function(r,t,u,a){var o,i=function(e){27===(e.charCode||e.keyCode)&&c(!0)},c=function(e){return b.removeEvent(k.body,"keydown",i),o.parentNode.removeChild(o),t(e),!1};setTimeout(function(){!function(){(o=k.createElement("div")).className="wmd-prompt-dialog",o.setAttribute("role","dialog");var e=k.createElement("div"),t=k.createElement("form");t.style;t.onsubmit=function(){return c(!1)},o.appendChild(t),t.appendChild(e),"function"==typeof r?r.call(this,e):e.innerHTML=r;var n=k.createElement("button");n.type="button",n.className="btn btn-s primary",n.onclick=function(){return c(!1)},n.innerHTML=u;e=k.createElement("button");e.type="button",e.className="btn btn-s",e.onclick=function(){return c(!0)},e.innerHTML=a,t.appendChild(n),t.appendChild(e),b.addEvent(k.body,"keydown",i),k.body.appendChild(o)}()},0)},w.prompt=function(r,u,n,a,o){var i,c;void 0===u&&(u="");var l=function(e){27===(e.charCode||e.keyCode)&&s(!0)},s=function(e){b.removeEvent(k.body,"keydown",l);var t=c.value;return e?t=null:(t=t.replace(/^http:\/\/(https?|ftp):\/\//,"$1://"),/^(?:https?|ftp):\/\//.test(t)||/^[_a-z0-9-]+:/i.test(t)||(t="http://"+t)),i.parentNode.removeChild(i),n(t),!1};setTimeout(function(){!function(){(i=k.createElement("div")).className="wmd-prompt-dialog",i.setAttribute("role","dialog");var e=k.createElement("div");e.innerHTML=r,i.appendChild(e);var t=k.createElement("form");t.style;t.onsubmit=function(){return s(!1)},i.appendChild(t),(c=k.createElement("input")).type="text",c.value=u,t.appendChild(c);var n=k.createElement("button");n.type="button",n.className="btn btn-s primary",n.onclick=function(){return s(!1)},n.innerHTML=a;e=k.createElement("button");e.type="button",e.className="btn btn-s",e.onclick=function(){return s(!0)},e.innerHTML=o,t.appendChild(n),t.appendChild(e),b.addEvent(k.body,"keydown",l),k.body.appendChild(i)}();var e,t=u.length;void 0!==c.selectionStart?(c.selectionStart=0,c.selectionEnd=t):c.createTextRange&&((e=c.createTextRange()).collapse(!1),e.moveStart("character",-t),e.moveEnd("character",t),e.select()),c.focus()},0)};var e=S.prototype;function y(e,t){this.fullScreenBind=!1,this.hooks=e,this.getString=t,this.isFakeFullScreen=!1}function u(){return document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen||document.msIsFullScreen}e.prefixes="(?:\\s{4,}|\\s*>|\\s*-\\s+|\\s*\\d+\\.|=|\\+|-|_|\\*|#|\\s*\\[[^\n]]+\\]:)",e.unwrap=function(e){var t=new d("([^\\n])\\n(?!(\\n|"+this.prefixes+"))","g");e.selection=e.selection.replace(t,"$1 $2")},e.wrap=function(e,t){this.unwrap(e);var t=new d("(.{1,"+t+"})( +|$\\n?)","gm"),n=this;e.selection=e.selection.replace(t,function(e,t){return new d("^"+n.prefixes,"").test(e)?e:t+"\n"}),e.selection=e.selection.replace(/\s+$/,"")},e.doBold=function(e,t){return this.doBorI(e,t,2,this.getString("boldexample"))},e.doItalic=function(e,t){return this.doBorI(e,t,1,this.getString("italicexample"))},e.doBorI=function(e,t,n,r){e.trimWhitespace(),e.selection=e.selection.replace(/\n{2,}/g,"\n");var u=/(\**$)/.exec(e.before)[0],a=/(^\**)/.exec(e.after)[0],u=Math.min(u.length,a.length);n<=u&&(2!=u||1!=n)?(e.before=e.before.replace(d("[*]{"+n+"}$",""),""),e.after=e.after.replace(d("^[*]{"+n+"}",""),"")):!e.selection&&a?(e.after=e.after.replace(/^([*_]*)/,""),e.before=e.before.replace(/(\s?)$/,""),u=d.$1,e.before=e.before+a+u):(e.selection||a||(e.selection=r),e.before=e.before+(n=n<=1?"*":"**"),e.after=n+e.after)},e.stripLinkDefs=function(e,a){return e=e.replace(/^[ ]{0,3}\[(\d+)\]:[ \t]*\n?[ \t]*<?(\S+?)>?[ \t]*\n?[ \t]*(?:(\n*)["(](.+?)[")][ \t]*)?(?:\n+|$)/gm,function(e,t,n,r,u){return a[t]=e.replace(/\s*$/,""),r?(a[t]=e.replace(/["(](.+?)[")]$/,""),r+u):""})},e.addLinkDef=function(e,t){var o=0,i={};e.before=this.stripLinkDefs(e.before,i),e.selection=this.stripLinkDefs(e.selection,i),e.after=this.stripLinkDefs(e.after,i);var n="",c=/(\[)((?:\[[^\]]*\]|[^\[\]])*)(\][ ]?(?:\n[ ]*)?\[)(\d+)(\])/g,l=function(e){o++,e=e.replace(/^[ ]{0,3}\[(\d+)\]:/,"  ["+o+"]:"),n+="\n"+e},s=function(e,t,n,r,u,a){return n=n.replace(c,s),i[u]?(l(i[u]),t+n+r+o+a):e};e.before=e.before.replace(c,s),t?l(t):e.selection=e.selection.replace(c,s);t=o;return e.after=e.after.replace(c,s),e.after&&(e.after=e.after.replace(/\n*$/,"")),e.after||(e.selection=e.selection.replace(/\n*$/,"")),e.after+="\n\n"+n,t},e.doLinkOrImage=function(n,r,u){if(n.trimWhitespace(),n.findTags(/\s*!?\[/,/\][ ]?(?:\n[ ]*)?(\[.*?\])?/),1<n.endTag.length&&0<n.startTag.length)n.startTag=n.startTag.replace(/!?\[/,""),n.endTag="",this.addLinkDef(n,null);else{if(n.selection=n.startTag+n.selection+n.endTag,n.startTag=n.endTag="",!/\n\n/.test(n.selection)){function e(e){var t;o.parentNode.removeChild(o),null!==e&&(n.selection=(" "+n.selection).replace(/([^\\](?:\\\\)*)(?=[[\]])/g,"$1\\").substr(1),e=" [999]: "+e.replace(/^\s*(.*?)(?:\s+"(.+)")?\s*$/,function(e,t,n){var r=!1;return t=t.replace(/%(?:[\da-fA-F]{2})|\?|\+|[^\w\d-./[\]]/g,function(e){if(3===e.length&&"%"==e.charAt(0))return e.toUpperCase();switch(e){case"?":return r=!0,"?";case"+":if(r)return"%20"}return encodeURI(e)}),(n=n&&(n=n.trim?n.trim():n.replace(/^\s*/,"").replace(/\s*$/,"")).replace(/"/g,"quot;").replace(/\(/g,"&#40;").replace(/\)/g,"&#41;").replace(/</g,"&lt;").replace(/>/g,"&gt;"))?t+' "'+n+'"':t}),e=a.addLinkDef(n,e),n.startTag=u?"![":"[",n.endTag="]["+e+"]",n.selection||(u?(t=a.getString("imagename"),n.selection=t||a.getString("imagedescription")):(t=a.getString("linkname"),n.selection=t||a.getString("linkdescription")))),r(),a.hooks.commandExecuted(u?"doImage":"doLink")}var a=this,o=w.createBackground();return u?this.hooks.insertImageDialog(e)||w.prompt(this.getString("imagedialog"),"http://",e,this.getString("ok"),this.getString("cancel")):this.hooks.insertLinkDialog(e)||w.prompt(this.getString("linkdialog"),"http://",e,this.getString("ok"),this.getString("cancel")),!0}this.addLinkDef(n,null)}},e.doAutoindent=function(t,e){var n=this,r=!1;t.before=t.before.replace(/(\n|^)[ ]{0,3}([*+-]|\d+[.])[ \t]*\n$/,"\n\n"),t.before=t.before.replace(/(\n|^)[ ]{0,3}>[ \t]*\n$/,"\n\n"),t.before=t.before.replace(/(\n|^)[ \t]+\n$/,"\n\n"),t.selection||/^[ \t]*(?:\n|$)/.test(t.after)||(t.after=t.after.replace(/^[^\n]*/,function(e){return t.selection=e,""}),r=!0),/(\n|^)[ ]{0,3}([*+-]|\d+[.])[ \t]+.*\n$/.test(t.before)&&n.doList&&n.doList(t),/(\n|^)[ ]{0,3}>[ \t]+.*\n$/.test(t.before)&&n.doBlockquote&&n.doBlockquote(t),/(\n|^)(\t|[ ]{4,}).*\n$/.test(t.before)&&n.doCode&&n.doCode(t),r&&(t.after=t.selection+t.after,t.selection="")},e.doBlockquote=function(u,e){u.selection=u.selection.replace(/^(\n*)([^\r]+?)(\n*)$/,function(e,t,n,r){return u.before+=t,u.after=r+u.after,n}),u.before=u.before.replace(/(>[ \t]*)$/,function(e,t){return u.selection=t+u.selection,""}),u.selection=u.selection.replace(/^(\s|>)+$/,""),u.selection=u.selection||this.getString("quoteexample");var t="",n="";if(u.before){for(var r=u.before.replace(/\n$/,"").split("\n"),a=!1,o=0;o<r.length;o++){var i=!1,c=r[o],a=a&&0<c.length;/^>/.test(c)?(i=!0,!a&&1<c.length&&(a=!0)):i=!!/^[ \t]*$/.test(c)||a,i?t+=c+"\n":(n+=t+c,t="\n")}/(^|\n)>/.test(t)||(n+=t,t="")}u.startTag=t,u.before=n,u.after&&(u.after=u.after.replace(/^\n?/,"\n")),u.after=u.after.replace(/^(((\n|^)(\n[ \t]*)*>(.+\n)*.*)+(\n[ \t]*)*)/,function(e){return u.endTag=e,""});function l(e){var n=e?"> ":"";u.startTag&&(u.startTag=u.startTag.replace(/\n((>|\s)*)\n$/,function(e,t){return"\n"+t.replace(/^[ ]{0,3}>?[ \t]*$/gm,n)+"\n"})),u.endTag&&(u.endTag=u.endTag.replace(/^\n((>|\s)*)\n/,function(e,t){return"\n"+t.replace(/^[ ]{0,3}>?[ \t]*$/gm,n)+"\n"}))}/^(?![ ]{0,3}>)/m.test(u.selection)?(this.wrap(u,f-2),u.selection=u.selection.replace(/^/gm,"> "),l(!0),u.skipLines()):(u.selection=u.selection.replace(/^[ ]{0,3}> ?/gm,""),this.unwrap(u),l(!1),!/^(\n|^)[ ]{0,3}>/.test(u.selection)&&u.startTag&&(u.startTag=u.startTag.replace(/\n{0,2}$/,"\n\n")),!/(\n|^)[ ]{0,3}>.*$/.test(u.selection)&&u.endTag&&(u.endTag=u.endTag.replace(/^\n{0,2}/,"\n\n"))),u.selection=this.hooks.postBlockquoteCreation(u.selection),/\n/.test(u.selection)||(u.selection=u.selection.replace(/^(> *)/,function(e,t){return u.startTag+=t,""}))},e.doCode=function(t,e){var n,r=/\S[ ]*$/.test(t.before);!/^[ ]*\S/.test(t.after)&&!r||/\n/.test(t.selection)?(t.before=t.before.replace(/[ ]{4}$/,function(e){return t.selection=e+t.selection,""}),r=n=1,/(\n|^)(\t|[ ]{4,}).*\n$/.test(t.before)&&(n=0),/^\n(\t|[ ]{4,})/.test(t.after)&&(r=0),t.skipLines(n,r),t.selection?/^[ ]{0,3}\S/m.test(t.selection)?/\n/.test(t.selection)?t.selection=t.selection.replace(/^/gm,"    "):t.before+="    ":t.selection=t.selection.replace(/^(?:[ ]{4}|[ ]{0,3}\t)/gm,""):(t.startTag="    ",t.selection=this.getString("codeexample"))):(t.trimWhitespace(),t.findTags(/`/,/`/),t.startTag||t.endTag?t.endTag&&!t.startTag?(t.before+=t.endTag,t.endTag=""):t.startTag=t.endTag="":(t.startTag=t.endTag="`",t.selection||(t.selection=this.getString("codeexample"))))},e.doList=function(e,t,n){function r(e){return void 0===n&&(n=/^\s*\d/.test(e)),e=e.replace(/^[ ]{0,3}([*+-]|\d+[.])\s/gm,function(e){return i()})}var u=/^\n*(([ ]{0,3}([*+-]|\d+[.])[ \t]+.*)(\n.+|\n{2,}([*+-].*|\d+[.])[ \t]+.*|\n{2,}[ \t]+\S.*)*)\n*/,a="-",o=1,i=function(){var e;return n?(e=" "+o+". ",o++):e=" "+a+" ",e};if(e.findTags(/(\n|^)*[ ]{0,3}([*+-]|\d+[.])\s+/,null),!e.before||/\n$/.test(e.before)||/^\n/.test(e.startTag)||(e.before+=e.startTag,e.startTag=""),e.startTag){var c=/\d+[.]/.test(e.startTag);if(e.startTag="",e.selection=e.selection.replace(/\n[ ]{4}/g,"\n"),this.unwrap(e),e.skipLines(),c&&(e.after=e.after.replace(u,r)),n==c)return}var l=1;e.before=e.before.replace(/(\n|^)(([ ]{0,3}([*+-]|\d+[.])[ \t]+.*)(\n.+|\n{2,}([*+-].*|\d+[.])[ \t]+.*|\n{2,}[ \t]+\S.*)*)\n*$/,function(e){return/^\s*([*+-])/.test(e)&&(a=d.$1),l=/[^\n]\n\n[^\n]/.test(e)?1:0,r(e)}),e.selection||(e.selection=this.getString("litem"));var c=i(),s=1;e.after=e.after.replace(u,function(e){return s=/[^\n]\n\n[^\n]/.test(e)?1:0,r(e)}),e.trimWhitespace(!0),e.skipLines(l,s,!0);c=(e.startTag=c).replace(/./g," ");this.wrap(e,f-c.length),e.selection=e.selection.replace(/\n/g,"\n"+c),this.hooks.commandExecuted("doList")},e.doHeading=function(e,t){if(e.selection=e.selection.replace(/\s+/g," "),e.selection=e.selection.replace(/(^\s+|\s+$)/g,""),!e.selection)return e.startTag="## ",e.selection=this.getString("headingexample"),void(e.endTag=" ##");var n=0;e.findTags(/#+[ ]*/,/[ ]*#+/),/#+/.test(e.startTag)&&(n=d.lastMatch.length),e.startTag=e.endTag="",e.findTags(null,/\s?(-+|=+)/),/=+/.test(e.endTag)&&(n=1),/-+/.test(e.endTag)&&(n=2),e.startTag=e.endTag="",e.skipLines(1,1);n=0==n?2:n-1;if(0<n){var r=2<=n?"-":"=",u=e.selection.length;for(f<u&&(u=f),e.endTag="\n";u--;)e.endTag+=r}},e.doHorizontalRule=function(e,t){e.startTag="----------\n",e.selection="",e.skipLines(2,1,!0)},e.doMore=function(e,t){e.startTag="\x3c!--more--\x3e\n\n",e.selection="",e.skipLines(2,0,!0)},e.doTab=function(e,t){e.startTag="    ",e.selection=""},y.prototype.doFullScreen=function(e,t){var n=function(){var e,t={fullScreenChange:["onfullscreenchange","onwebkitfullscreenchange","onmozfullscreenchange","onmsfullscreenchange"],requestFullscreen:["requestFullscreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullScreen"],cancelFullscreen:["cancelFullscreen","exitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msCancelFullScreen"]},n={};for(e in t){for(var r=t[e].length,u=!1,a=0;a<r;a++){var o=t[e][a];if(void 0!==document[o]||void 0!==document.body[o]){n[e]=o,u=!0;break}}if(!u)return!1}return n}(),r=this;if(!n)return alert(r.getString("fullscreenUnsupport")),!1;this.fullScreenBind||(b.addEvent(document,n.fullScreenChange.substring(2),function(){u()?(e.fullscreen.style.display="none",e.exitFullscreen.style.display="",r.hooks.enterFullScreen()):(e.fullscreen.style.display="",e.exitFullscreen.style.display="none",r.hooks.exitFullScreen())}),this.fullScreenBind=!0),t?(r.isFakeFullScreen?(document.body[n.requestFullscreen]("webkitRequestFullScreen"==n.requestFullscreen?Element.ALLOW_KEYBOARD_INPUT:null),r.isFakeFullScreen=!1):u()||(e.exitFullscreen.style.display="",r.hooks.enterFakeFullScreen(),r.isFakeFullScreen=!0),window.fullScreenEntered=!0):(r.isFakeFullScreen?(e.exitFullscreen.style.display="none",r.hooks.exitFullScreen()):u()&&document[n.cancelFullscreen](),r.isFakeFullScreen=!1,window.fullScreenEntered=!1)}}();