<?php if (!defined("__TYPECHO_ROOT_DIR__")) {
  exit();
} ?>

<div class="sidebar__right__inner flex flex-col px-3 md:px-5">
    <style>
    /* 基础模块样式优化 */
    .sidebar__right__inner > div {
        padding: 1.25rem 0;
        border-bottom: 1px solid rgb(245, 245, 244);
        position: relative;
        width: 100%;
        max-width: 100%;
        overflow: hidden; /* 防止内容溢出 */
    }
    
    /* 响应式布局调整 */
    @media (max-width: 768px) {
        .sidebar__right__inner {
            padding: 0 1rem;
        }
        
        .tag-cloud {
            max-height: none; /* 移动端不限制标签云高度 */
        }
    }
    
    /* 优化标签云响应式表现 */
    .tag-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 0 4px;
        width: 100%;
    }
    
    .tag-item {
        flex: 0 1 auto; /* 允许标签自动缩放 */
        max-width: 100%; /* 防止标签超出容器 */
    }
    
    /* 间距控制 */
    .sidebar__right__inner > div::before,
    .sidebar__right__inner > div::after {
        content: '';
        display: block;
        height: 10px;
    }
    
    .sidebar__right__inner > div::before {
        margin-top: -10px;
    }
    
    .sidebar__right__inner > div::after {
        margin-bottom: -10px;
    }
    
    /* 暗色模式 */
    .dark .sidebar__right__inner > div {
        border-bottom: 1px solid rgb(82, 82, 82);
    }
    
    /* 标签云样式统一 */
    .tag-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 0 8px;
        max-height: calc(5 * (1.875rem + 8px)); /* 5行的最大高度：每行高度+间距 */
        overflow: hidden;
    }
    
    .tag-item {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        height: 1.875rem;
        font-size: 0.875rem;
        color: rgb(75, 85, 99);
        background-color: rgb(243, 244, 246);
        border-radius: 9999px;
        transition: all 0.3s ease;
    }
    
    .dark .tag-item {
        color: rgb(156, 163, 175);
        background-color: rgb(31, 41, 55);
    }
    
    .tag-text {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
    
    /* 统一的悬停效果 - 修改这部分 */
    .tag-item:hover,
    .sidebar__right__inner a:hover,
    .sidebar__right__inner li:hover > a {
        background-color: #333333 !important; /* 改为黑色 */
        color: white !important;
        transform: translateY(-1px);
        transition: all 0.3s ease;
    }
    
    /* 添加链接的基础样式 */
    .sidebar__right__inner a {
        transition: all 0.3s ease;
    }
    
    /* 暗色模式下的悬停效果 */
    .dark .tag-item:hover,
    .dark .sidebar__right__inner a:hover,
    .dark .sidebar__right__inner li:hover > a {
        background-color: #1a1a1a !important;
        color: white !important;
    }
    
    /* 特殊链接的悬停效果（如图标链接） */
    .sidebar__right__inner .group:hover iconify-icon {
        color: white !important;
    }
    
    /* 统一的链接基础样式 */
    .sidebar__right__inner a {
        transition: all 0.3s ease;
        border-radius: 9999px; /* 统一圆角 */
        padding: 4px 12px; /* 统一内边距 */
    }
    
    /* 热门文章链接特殊样式 */
    .sidebar__right__inner .popular-article {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 12px;
        border-radius: 9999px;
        width: 100%;
    }
    
    /* 统一的悬停效果 */
    .tag-item:hover,
    .sidebar__right__inner a:hover,
    .sidebar__right__inner li:hover > a,
    .sidebar__right__inner .popular-article:hover {
        background-color: #333333 !important;
        color: white !important;
        transform: translateY(-1px);
        transition: all 0.3s ease;
    }
    
    /* 悬停时图标颜色统一为白色 */
    .sidebar__right__inner a:hover iconify-icon,
    .sidebar__right__inner .popular-article:hover iconify-icon,
    .sidebar__right__inner .popular-article:hover span {
        color: white !important;
    }
    
    /* 暗色模式下的悬停效果 */
    .dark .tag-item:hover,
    .dark .sidebar__right__inner a:hover,
    .dark .sidebar__right__inner li:hover > a,
    .dark .sidebar__right__inner .popular-article:hover {
        background-color: #1a1a1a !important;
        color: white !important;
    }
    
    /* 文章目录样式 */
    #article-toc {
        display: none; /* 默认隐藏，通过JS控制显示 */
    }
    
    .toc-content {
        padding: 0 8px;
        margin-top: 10px;
    }

    .toc-content a {
        display: block;
        padding: 5px 12px;
        color: rgb(75, 85, 99);
        text-decoration: none;
        border-radius: 9999px;
        transition: all 0.3s ease;
        margin: 4px 0;
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .toc-content a:hover {
        background-color: #333333 !important;
        color: white !important;
        transform: translateY(-1px);
    }

    .dark .toc-content a {
        color: rgb(156, 163, 175);
    }

    .dark .toc-content a:hover {
        background-color: #1a1a1a !important;
        color: white !important;
    }

    .toc-h3 { padding-left: 28px !important; }
    .toc-h4 { padding-left: 44px !important; }
    </style>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "Author", true)): ?>
    <div class="flex flex-col gap-y-5">
        <div class="flex gap-x-3">
            <div class="shrink-0">
                <img src="<?php echo getAvatarByMail($this->author->mail, true); ?>"
                     loading="lazy"
                     alt="<?php $this->author->screenName(); ?>" 
                     class="w-[50px] h-[50px] rounded-full object-cover"
                >
            </div>
            <div class="flex flex-col justify-between">
                <p class="jasmine-primary-color font-medium">
                    <?php $this->author->screenName(); ?>
                </p>
                <p class="line-clamp-2 text-sm text-gray-500 dark:text-gray-400">
                    <?php $this->options->authorRecommend(); ?>
                </p>
            </div>
        </div>
        <?php if ($authorTag = $this->options->authorTag): ?>
            <ul class="flex flex-wrap gap-x-2 gap-y-2">
                <?php foreach (explode(",", $authorTag) as $tag): ?>
                    <li class="bg-stone-200 rounded py-1 px-2 text-sm dark:bg-black dark:text-neutral-400">
                        <?php echo $tag; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <div class="flex flex-col justify-start gap-x-3 gap-y-4" id="article-toc">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:list" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                文章目录
            </h2>
        </div>
        <div class="toc-content"></div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 只在文章页面执行
        if (!document.querySelector('.markdown-body')) return;
        
        const article = document.querySelector('.markdown-body');
        const toc = document.querySelector('.toc-content');
        const headings = article.querySelectorAll('h2, h3, h4');
        
        if (headings.length === 0) return;
        
        // 显示目录容器
        document.getElementById('article-toc').style.display = 'flex';
        
        // 生成目录
        headings.forEach((heading, index) => {
            const id = `heading-${index}`;
            heading.id = id;
            const link = document.createElement('a');
            link.href = `#${id}`;
            link.textContent = heading.textContent;
            link.className = `toc-${heading.tagName.toLowerCase()}`;
            toc.appendChild(link);
        });
    });
    </script>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "PopularArticles", true)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:flame" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                热门文章
            </h2>
        </div>
        <?php 
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();
        
        // 修改查询以获取完整的文章信息
        $posts = $db->fetchAll($db->select()
            ->from('table.contents')
            ->where('type = ?', 'post')
            ->where('status = ?', 'publish')
            ->order('views', Typecho_Db::SORT_DESC)
            ->limit(5)
        );
        
        if (!empty($posts)): ?>
            <div class="flex flex-col gap-y-2 px-2">
                <?php 
                foreach ($posts as $post): 
                    // 创建文章对象以获取正确的永久链接
                    $post = Typecho_Widget::widget('Widget_Abstract_Contents')->push($post);
                ?>
                    <a href="<?php echo $post['permalink']; ?>" 
                       class="popular-article">
                        <span class="flex-1 line-clamp-1 text-sm dark:text-gray-400 text-neutral-500">
                            <?php echo subString($post['title'], 20); ?>
                        </span>
                        <span class="text-gray-400">
                            <iconify-icon icon="tabler:link" class="text-lg"></iconify-icon>
                        </span>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-gray-500 dark:text-gray-400">暂无热门文章</div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "PopularCategories", false)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:category" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                热门分类
            </h2>
        </div>
        <ul class="flex flex-wrap gap-y-2 px-2">
            <?php $this->widget("Widget_Metas_Category_List", "ignoreZeroCount=1&limit=15")->to($categories); ?>
            <?php if ($categories->have()): ?>
                <?php while ($categories->next()): ?>
                    <li>
                        <a href="<?php $categories->permalink(); ?>"
                           title="<?php $categories->name(); ?>"
                           class="dark:text-gray-400 text-sm rounded-full px-3 py-1">
                           <?php $categories->name(); ?>
                        </a>
                    </li>
                <?php endwhile; ?>
            <?php endif; ?>
        </ul>
    </div>
    <?php endif; ?>
    
    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "LatestComments", true)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:message-circle-2" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                最新评论
            </h2>
        </div>
        <div class="flex flex-col gap-y-2 px-2" id="recent-comments">
            <?php 
            $recentComments = getRecentComments(5);
            if (!empty($recentComments)): 
                foreach ($recentComments as $comment): 
            ?>
                <div class="flex items-center gap-x-1">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100 w-[80px] text-right truncate">
                        <?php echo mb_substr($comment['author'], 0, 10, 'UTF-8'); ?>
                    </div>
                    <span class="text-gray-400 dark:text-gray-500">：</span>
                    <div class="text-sm text-gray-500 dark:text-gray-400 line-clamp-1 flex-1">
                        <?php echo $comment['text']; ?>
                    </div>
                </div>
            <?php 
                endforeach;
            else: 
            ?>
                <div class="text-gray-500 dark:text-gray-400">暂无评论</div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "PopularTags", true)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:tags" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                热门标签
            </h2>
        </div>
        <div class="tag-cloud">
            <?php 
            $this->widget("Widget_Metas_Tag_Cloud", "ignoreZeroCount=1&limit=30")->to($tags);
            $tagCount = 0;
            $maxLength = mb_strlen("技术教程", 'UTF-8'); // 用作标准长度
            
            if ($tags->have()):
                while ($tags->next()):
                    // 只显示不超过标准长度的标签
                    if (mb_strlen($tags->name, 'UTF-8') <= $maxLength):
                        $tagCount++;
            ?>
                <a href="<?php $tags->permalink(); ?>" 
                   class="tag-item"
                   title="<?php echo $tags->name; ?>">
                    <span class="tag-text"># <?php echo $tags->name; ?></span>
                </a>
            <?php 
                    endif;
                endwhile;
            endif;
            ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "About", true)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:user" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                关于站长
            </h2>
        </div>
        <ul class="flex flex-col gap-y-3 px-2 dark:text-gray-400">
            <?php if ($this->options->wx): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:brand-wechat" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->wx(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->qq): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:brand-qq" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->qq(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->location): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:map-pin" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->location(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->email): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:mail" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->email(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->career): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:briefcase" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->career(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->github): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:brand-github" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->github(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->link): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:link" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->link(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->cc): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:badge-cc" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->cc(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->icpCode): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:id-badge-2" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><a href="https://beian.miit.gov.cn/" target="_blank"><?php $this->options->icpCode(); ?></a></span>
                </li>
            <?php endif; ?>
        </ul>
    </div>
    <?php endif; ?>

</div>
