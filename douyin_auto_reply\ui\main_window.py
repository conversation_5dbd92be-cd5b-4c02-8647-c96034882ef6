#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.window_manager import WindowManager
    from core.message_detector import MessageDetector
    from core.auto_replier import AutoReplier
    from config.settings import settings
except ImportError as e:
    print(f"导入模块失败: {e}")
    WindowManager = None
    MessageDetector = None
    AutoReplier = None
    settings = None

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_widgets()
        self.setup_layout()
        
        # 核心组件
        self.window_manager = None
        self.message_detector = None
        self.auto_replier = None
        self.monitoring_thread = None
        self.is_monitoring = False
        
        # 初始化组件
        self.init_components()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("抖音聊天自动回复工具 v1.0 (MVP)")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 设置窗口图标 (可选)
        try:
            self.root.iconbitmap(default='icon.ico')
        except:
            pass
    
    def setup_variables(self):
        """设置变量"""
        self.status_var = tk.StringVar(value="未启动")
        self.douyin_status_var = tk.StringVar(value="未检测到")
        self.message_count_var = tk.StringVar(value="0")
        self.reply_count_var = tk.StringVar(value="0")
    
    def setup_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # 标题
        title_label = ttk.Label(main_frame, text="抖音聊天自动回复工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 状态信息框架
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # 状态标签
        ttk.Label(status_frame, text="运行状态:").grid(row=0, column=0, sticky="w")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                foreground="red", font=("Arial", 10, "bold"))
        status_label.grid(row=0, column=1, sticky="w", padx=(10, 0))
        
        ttk.Label(status_frame, text="抖音聊天:").grid(row=1, column=0, sticky="w")
        douyin_label = ttk.Label(status_frame, textvariable=self.douyin_status_var,
                                foreground="orange", font=("Arial", 10, "bold"))
        douyin_label.grid(row=1, column=1, sticky="w", padx=(10, 0))
        
        # 统计信息
        ttk.Label(status_frame, text="检测到消息:").grid(row=2, column=0, sticky="w")
        ttk.Label(status_frame, textvariable=self.message_count_var).grid(row=2, column=1, sticky="w", padx=(10, 0))
        
        ttk.Label(status_frame, text="自动回复:").grid(row=3, column=0, sticky="w")
        ttk.Label(status_frame, textvariable=self.reply_count_var).grid(row=3, column=1, sticky="w", padx=(10, 0))
        
        # 控制按钮框架
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # 控制按钮
        self.start_button = ttk.Button(control_frame, text="启动监控", 
                                      command=self.start_monitoring, style="Accent.TButton")
        self.start_button.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_button = ttk.Button(control_frame, text="停止监控", 
                                     command=self.stop_monitoring, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(5, 5))
        
        self.test_button = ttk.Button(control_frame, text="测试连接", 
                                     command=self.test_connection)
        self.test_button.grid(row=0, column=2, padx=(5, 0))
        
        # 日志显示框架
        log_frame = ttk.LabelFrame(main_frame, text="实时日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky="nsew", pady=(0, 10))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, width=60, height=15, 
                                                 font=("Consolas", 9))
        self.log_text.grid(row=0, column=0, sticky="nsew")
        
        # 清空日志按钮
        clear_button = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_button.grid(row=1, column=0, sticky="e", pady=(5, 0))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def setup_layout(self):
        """设置布局"""
        pass  # 在setup_widgets中已完成
    
    def init_components(self):
        """初始化核心组件"""
        try:
            if WindowManager:
                self.window_manager = WindowManager()
                self.log("✅ 窗口管理器初始化成功")
            else:
                self.log("❌ 窗口管理器初始化失败: 缺少依赖")
            
            if AutoReplier:
                self.auto_replier = AutoReplier(settings)
                self.log("✅ 自动回复器初始化成功")
            else:
                self.log("❌ 自动回复器初始化失败: 缺少依赖")
                
        except Exception as e:
            self.log(f"❌ 组件初始化失败: {e}")
    
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = int(self.log_text.index(tk.END).split('.')[0])
        if lines > 1000:
            self.log_text.delete(1.0, "100.0")
    
    def test_connection(self):
        """测试抖音聊天连接"""
        self.log("🔍 正在检测抖音聊天窗口...")
        
        if not self.window_manager:
            self.log("❌ 窗口管理器未初始化")
            messagebox.showerror("错误", "窗口管理器未初始化")
            return
        
        try:
            if self.window_manager.find_douyin_window():
                window_info = self.window_manager.get_window_info()
                if window_info:
                    self.douyin_status_var.set("已连接")
                    self.log(f"✅ 抖音窗口连接成功: {window_info['title']}")
                    self.log(f"   窗口大小: {window_info['width']}x{window_info['height']}")
                    messagebox.showinfo("成功", "抖音聊天窗口检测成功！")
                else:
                    self.douyin_status_var.set("连接异常")
                    self.log("⚠️ 窗口信息获取失败")
            else:
                self.douyin_status_var.set("未检测到")
                self.log("❌ 未找到抖音聊天窗口")
                messagebox.showwarning("警告", "未找到抖音聊天窗口\n请确保抖音聊天程序已启动")
                
        except Exception as e:
            self.log(f"❌ 测试连接失败: {e}")
            messagebox.showerror("错误", f"测试连接失败: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.log("⚠️ 监控已在运行中")
            return
        
        # 检查连接
        if not self.window_manager or not self.window_manager.find_douyin_window():
            messagebox.showerror("错误", "请先测试连接，确保抖音聊天窗口可用")
            return
        
        # 初始化消息检测器
        if MessageDetector and self.window_manager:
            self.message_detector = MessageDetector(self.window_manager)
        else:
            self.log("❌ 无法初始化消息检测器")
            messagebox.showerror("错误", "无法初始化消息检测器")
            return
        
        # 启动监控线程
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        # 更新界面
        self.status_var.set("运行中")
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        
        self.log("🚀 开始自动监控和回复")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
        # 更新界面
        self.status_var.set("已停止")
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        
        self.log("⏹️ 停止监控")
    
    def monitoring_loop(self):
        """监控循环"""
        self.log("🔄 监控循环启动")
        
        try:
            while self.is_monitoring:
                # 检测新消息
                if self.message_detector and self.message_detector.has_new_message():
                    # 获取模拟消息内容
                    message_content = self.message_detector.get_mock_message_content()
                    self.log(f"📥 检测到新消息: {message_content}")
                    
                    # 更新消息计数
                    stats = self.message_detector.get_statistics()
                    self.message_count_var.set(str(stats['message_count']))
                    
                    # 自动回复
                    if self.auto_replier:
                        success = self.auto_replier.send_reply(message_content, self.window_manager)
                        if success:
                            reply_stats = self.auto_replier.get_statistics()
                            self.reply_count_var.set(str(reply_stats['total_replies']))
                
                # 等待间隔
                import time
                time.sleep(2)
                
        except Exception as e:
            self.log(f"❌ 监控循环出错: {e}")
            self.is_monitoring = False
            
        self.log("🔄 监控循环结束")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("📝 日志已清空")
    
    def on_closing(self):
        """窗口关闭事件"""
        if self.is_monitoring:
            if messagebox.askokcancel("退出", "监控正在运行，确定要退出吗？"):
                self.stop_monitoring()
                self.root.destroy()
        else:
            self.root.destroy()

# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    app = MainWindow(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop() 