<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>

<aside class="sidebar">
    <!-- 会员模块 -->
    <div class="widget member-widget">
        <h3 class="member-title">加入会员</h3>
        <p class="member-desc">$9/月起，解锁全站所有付费内容</p>
        <a href="<?php $this->options->adminUrl('register.php'); ?>" class="join-btn">立即加入</a>
    </div>

    <!-- 热门推荐 -->
    <div class="widget">
        <h3 class="widget-title">热门推荐</h3>
        <div class="trending-list">
            <?php
            // 获取热门文章（根据浏览量排序）
            $db = Typecho_Db::get();
            $prefix = $db->getPrefix();

            // 获取文章的浏览量信息
            $sql = $db->select()->from('table.contents')
                ->where('type = ?', 'post')
                ->where('status = ?', 'publish')
                ->order('created', Typecho_Db::SORT_DESC)
                ->limit(10);

            $posts = $db->fetchAll($sql);
            $hotPosts = array();

            // 获取每篇文章的浏览量
            foreach ($posts as $post) {
                $viewCount = 0;
                $viewFields = $db->fetchRow($db->select('str_value')->from('table.fields')
                    ->where('cid = ?', $post['cid'])
                    ->where('name = ?', 'views'));

                if ($viewFields) {
                    $viewCount = intval($viewFields['str_value']);
                }

                $post['views'] = $viewCount;
                $hotPosts[] = $post;
            }

            // 根据浏览量排序
            usort($hotPosts, function($a, $b) {
                return $b['views'] - $a['views'];
            });

            // 取前3篇
            $hotPosts = array_slice($hotPosts, 0, 3);

            if (!empty($hotPosts)):
                foreach ($hotPosts as $key => $post):
                    $cid = $post['cid'];
                    // 构建文章对象
                    $widget = Typecho_Widget::widget('Widget_Archive@hotpost_' . $cid, 'pageSize=1&type=post', 'cid=' . $cid);
            ?>
            <div class="trending-item">
                <div class="trending-img">
                    <?php
                    $thumb = '';
                    $thumbField = $db->fetchRow($db->select('str_value')->from('table.fields')
                        ->where('cid = ?', $cid)
                        ->where('name = ?', 'thumb'));

                    if ($thumbField && !empty($thumbField['str_value'])) {
                        $thumb = $thumbField['str_value'];
                    }

                    if (!empty($thumb)):
                    ?>
                    <img src="<?php echo $thumb; ?>" alt="<?php echo $widget->title; ?>">
                    <?php else: ?>
                    <img src="<?php $this->options->themeUrl('assets/img/default.jpg'); ?>" alt="<?php echo $widget->title; ?>">
                    <?php endif; ?>
                </div>
                <div class="trending-content">
                    <h4 class="trending-title"><?php echo $widget->title; ?></h4>
                    <div class="trending-meta">
                        <div class="stat-item">
                            <i class="ri-eye-line"></i>
                            <span><?php echo $post['views']; ?></span>
                        </div>
                    </div>
                </div>
            </div>
            <?php
                endforeach;
            else:
            ?>
            <div class="no-posts">暂无热门文章</div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 标签云 -->
    <div class="widget">
        <h3 class="widget-title">热门标签</h3>
        <div class="tag-cloud">
            <?php $this->widget('Widget_Metas_Tag_Cloud', 'limit=20&ignoreZeroCount=1&sort=count&desc=1')->to($tags); ?>
            <?php if($tags->have()): ?>
            <?php while ($tags->next()): ?>
            <a href="<?php $tags->permalink(); ?>" class="tag"><?php $tags->name(); ?></a>
            <?php endwhile; ?>
            <?php else: ?>
            <div class="no-tags">暂无标签</div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 最新文章 -->
    <div class="widget">
        <h3 class="widget-title">最新文章</h3>
        <div class="latest-list">
            <?php $this->widget('Widget_Contents_Post_Recent', 'pageSize=4')->to($recent); ?>
            <?php if ($recent->have()): ?>
            <?php while($recent->next()): ?>
            <div class="latest-item">
                <div class="latest-time">
                    <i class="ri-time-line"></i>
                    <span><?php $recent->date('Y-m-d'); ?></span>
                </div>
                <div class="latest-title">
                    <a href="<?php $recent->permalink(); ?>"><?php $recent->title(); ?></a>
                </div>
            </div>
            <?php endwhile; ?>
            <?php else: ?>
            <div class="no-posts">暂无最新文章</div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 本周热门 -->
    <div class="widget">
        <h3 class="widget-title">本周热门</h3>
        <div class="weekly-list">
            <?php
            // 获取本周热门文章
            $db = Typecho_Db::get();
            $prefix = $db->getPrefix();

            // 获取一周内的文章
            $weekAgo = time() - 7 * 24 * 60 * 60;
            $sql = $db->select()->from('table.contents')
                ->where('type = ?', 'post')
                ->where('status = ?', 'publish')
                ->where('created > ?', $weekAgo)
                ->order('created', Typecho_Db::SORT_DESC)
                ->limit(10);

            $posts = $db->fetchAll($sql);
            $weeklyPosts = array();

            // 获取每篇文章的浏览量
            foreach ($posts as $post) {
                $viewCount = 0;
                $viewFields = $db->fetchRow($db->select('str_value')->from('table.fields')
                    ->where('cid = ?', $post['cid'])
                    ->where('name = ?', 'views'));

                if ($viewFields) {
                    $viewCount = intval($viewFields['str_value']);
                }

                $post['views'] = $viewCount;
                $weeklyPosts[] = $post;
            }

            // 根据浏览量排序
            usort($weeklyPosts, function($a, $b) {
                return $b['views'] - $a['views'];
            });

            // 取前5篇
            $weeklyPosts = array_slice($weeklyPosts, 0, 5);

            if (!empty($weeklyPosts)):
                foreach ($weeklyPosts as $key => $post):
                    $cid = $post['cid'];
                    // 构建文章对象
                    $widget = Typecho_Widget::widget('Widget_Archive@weeklypost_' . $cid, 'pageSize=1&type=post', 'cid=' . $cid);
            ?>
            <div class="weekly-item">
                <div class="weekly-rank">
                    <span><?php echo $key + 1; ?></span>
                </div>
                <div class="weekly-content">
                    <div class="weekly-title">
                        <a href="<?php echo $widget->permalink; ?>"><?php echo $widget->title; ?></a>
                    </div>
                    <div class="weekly-meta">
                        <span><?php echo $post['views']; ?>阅读</span>
                    </div>
                </div>
            </div>
            <?php
                endforeach;
            else:
            ?>
            <div class="no-posts">暂无本周热门文章</div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 加入微信群 -->
    <div class="widget">
        <h3 class="widget-title">加入微信群</h3>
        <div class="qrcode-container">
            <img src="<?php $this->options->themeUrl('assets/img/qrcode.jpg'); ?>" alt="微信群二维码" class="qrcode-img">
        </div>
    </div>

    <!-- 主题购买 -->
    <div class="widget">
        <h3 class="widget-title">主题购买</h3>
        <div class="theme-buy">
            <div class="theme-info">
                <p>Typecho主题WW Style 1.0</p>
                <p>一款简约而不简单的主题</p>
                <a href="#" class="buy-btn">立即购买</a>
            </div>
        </div>
    </div>
</aside>