<?php
/**
 * 会员管理后台页面
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 检查用户是否登录且是管理员，否则跳转到登录页面
if (!$this->user->hasLogin() || !$this->user->pass('administrator', true)) {
    $this->response->redirect($this->options->loginUrl);
}

$this->need('header.php');

// 处理操作请求
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    $uid = isset($_POST['uid']) ? intval($_POST['uid']) : 0;
    
    if ($action === 'update_member' && $uid > 0) {
        $level = isset($_POST['level']) ? $_POST['level'] : '';
        $duration = isset($_POST['duration']) ? intval($_POST['duration']) : 0;
        
        if ($level && in_array($level, ['monthly', 'yearly', 'lifetime'])) {
            setUserMemberStatus($uid, $level, $duration);
            echo '<div class="message success">会员信息已更新</div>';
        }
    } elseif ($action === 'delete_member' && $uid > 0) {
        // 删除会员状态
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();
        
        // 删除会员相关字段
        $db->query($db->delete('table.fields')
            ->where('name = ?', 'member_status')
            ->where('cid = ?', $uid));
        $db->query($db->delete('table.fields')
            ->where('name = ?', 'member_level')
            ->where('cid = ?', $uid));
        $db->query($db->delete('table.fields')
            ->where('name = ?', 'member_expires')
            ->where('cid = ?', $uid));
            
        echo '<div class="message success">会员已删除</div>';
    }
}

// 获取当前页码
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$pageSize = 10;
$offset = ($page - 1) * $pageSize;

// 获取会员列表
$db = Typecho_Db::get();
$prefix = $db->getPrefix();

// 查询所有用户
$userQuery = $db->select('uid, name, mail, screenName, created')
    ->from('table.users')
    ->order('uid', Typecho_Db::SORT_DESC)
    ->limit($pageSize)
    ->offset($offset);
$users = $db->fetchAll($userQuery);

// 获取总用户数
$totalUsers = $db->fetchObject($db->select(array('COUNT(uid)' => 'num'))->from('table.users'))->num;
$totalPages = ceil($totalUsers / $pageSize);
?>

<div class="main-container">
    <div class="page-header">
        <h1 class="page-title"><i class="ri-user-settings-line"></i> 会员管理</h1>
        <p class="page-desc">管理网站会员信息，包括会员状态、等级和有效期</p>
    </div>
    
    <div class="admin-card">
        <div class="admin-tabs">
            <a href="<?php $this->options->siteUrl(); ?>admin-member.php" class="tab-item active">
                <i class="ri-team-line"></i> 会员列表
            </a>
            <a href="<?php $this->options->siteUrl(); ?>admin-member.php?section=purchases" class="tab-item">
                <i class="ri-shopping-cart-line"></i> 购买记录
            </a>
            <a href="<?php $this->options->siteUrl(); ?>admin-member.php?section=settings" class="tab-item">
                <i class="ri-settings-line"></i> 会员设置
            </a>
        </div>
        
        <?php $section = isset($_GET['section']) ? $_GET['section'] : 'members'; ?>
        
        <?php if ($section === 'members' || $section === ''): ?>
        <!-- 会员列表 -->
        <div class="admin-section">
            <div class="section-header">
                <h3 class="section-title">会员列表</h3>
                <div class="section-actions">
                    <form class="search-form" method="get">
                        <input type="text" name="keyword" placeholder="搜索用户名/邮箱..." value="<?php echo isset($_GET['keyword']) ? htmlspecialchars($_GET['keyword']) : ''; ?>">
                        <button type="submit" class="search-btn"><i class="ri-search-line"></i></button>
                    </form>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>用户名</th>
                            <th>显示名称</th>
                            <th>邮箱</th>
                            <th>注册时间</th>
                            <th>会员状态</th>
                            <th>会员等级</th>
                            <th>到期时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo $user['uid']; ?></td>
                            <td><?php echo $user['name']; ?></td>
                            <td><?php echo $user['screenName']; ?></td>
                            <td><?php echo $user['mail']; ?></td>
                            <td><?php echo date('Y-m-d', $user['created']); ?></td>
                            <td>
                                <?php if (getUserMemberStatus($user['uid'])): ?>
                                <span class="status success">有效</span>
                                <?php else: ?>
                                <span class="status">普通用户</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php 
                                $level = getUserMemberStatus($user['uid']) ? getMemberLevel($user['uid']) : '无';
                                echo $level;
                                ?>
                            </td>
                            <td>
                                <?php 
                                $expires = getUserMemberStatus($user['uid']) ? getMemberExpires($user['uid']) : '-';
                                echo $expires;
                                ?>
                            </td>
                            <td>
                                <button class="btn-action edit-member" data-uid="<?php echo $user['uid']; ?>" data-username="<?php echo $user['name']; ?>">
                                    <i class="ri-edit-line"></i>
                                </button>
                                <?php if (getUserMemberStatus($user['uid'])): ?>
                                <button class="btn-action delete-member" data-uid="<?php echo $user['uid']; ?>" data-username="<?php echo $user['name']; ?>">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页导航 -->
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>" class="page-btn prev">
                    <i class="ri-arrow-left-s-line"></i> 上一页
                </a>
                <?php endif; ?>
                
                <div class="page-info">第 <?php echo $page; ?> 页，共 <?php echo $totalPages; ?> 页</div>
                
                <?php if ($page < $totalPages): ?>
                <a href="?page=<?php echo $page + 1; ?>" class="page-btn next">
                    下一页 <i class="ri-arrow-right-s-line"></i>
                </a>
                <?php endif; ?>
            </div>
        </div>
        
        <?php elseif ($section === 'purchases'): ?>
        <!-- 购买记录 -->
        <div class="admin-section">
            <div class="section-header">
                <h3 class="section-title">购买记录</h3>
                <div class="section-actions">
                    <form class="search-form" method="get">
                        <input type="hidden" name="section" value="purchases">
                        <input type="text" name="keyword" placeholder="搜索订单号/用户..." value="<?php echo isset($_GET['keyword']) ? htmlspecialchars($_GET['keyword']) : ''; ?>">
                        <button type="submit" class="search-btn"><i class="ri-search-line"></i></button>
                    </form>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>订单ID</th>
                            <th>用户ID</th>
                            <th>用户名</th>
                            <th>商品类型</th>
                            <th>商品名称</th>
                            <th>支付金额</th>
                            <th>支付方式</th>
                            <th>支付时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // 这里获取购买记录，实际实现时需要创建订单表
                        // 以下是示例数据
                        $purchases = [
                            [
                                'id' => 'ORD20230601001',
                                'uid' => 1,
                                'username' => 'admin',
                                'type' => 'member',
                                'name' => '年度会员',
                                'amount' => '299.00',
                                'method' => '支付宝',
                                'time' => time() - 86400,
                                'status' => 'success'
                            ],
                            [
                                'id' => 'ORD20230602001',
                                'uid' => 2,
                                'username' => 'user1',
                                'type' => 'member',
                                'name' => '月度会员',
                                'amount' => '29.99',
                                'method' => '微信支付',
                                'time' => time() - 43200,
                                'status' => 'success'
                            ]
                        ];
                        
                        foreach ($purchases as $purchase):
                        ?>
                        <tr>
                            <td><?php echo $purchase['id']; ?></td>
                            <td><?php echo $purchase['uid']; ?></td>
                            <td><?php echo $purchase['username']; ?></td>
                            <td><?php echo $purchase['type']; ?></td>
                            <td><?php echo $purchase['name']; ?></td>
                            <td>¥<?php echo $purchase['amount']; ?></td>
                            <td><?php echo $purchase['method']; ?></td>
                            <td><?php echo date('Y-m-d H:i:s', $purchase['time']); ?></td>
                            <td>
                                <?php if ($purchase['status'] === 'success'): ?>
                                <span class="status success">成功</span>
                                <?php elseif ($purchase['status'] === 'pending'): ?>
                                <span class="status warning">处理中</span>
                                <?php else: ?>
                                <span class="status error">失败</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页导航 -->
            <div class="pagination">
                <a href="?section=purchases&page=1" class="page-btn prev">
                    <i class="ri-arrow-left-s-line"></i> 上一页
                </a>
                
                <div class="page-info">第 1 页，共 1 页</div>
                
                <a href="?section=purchases&page=1" class="page-btn next disabled">
                    下一页 <i class="ri-arrow-right-s-line"></i>
                </a>
            </div>
        </div>
        
        <?php elseif ($section === 'settings'): ?>
        <!-- 会员设置 -->
        <div class="admin-section">
            <div class="section-header">
                <h3 class="section-title">会员设置</h3>
            </div>
            
            <form class="admin-form" method="post" action="<?php $this->options->adminUrl(); ?>options-theme.php">
                <div class="form-section">
                    <h4 class="form-subtitle">会员价格设置</h4>
                    
                    <div class="form-group">
                        <label for="memberMonthlyPrice">月度会员价格</label>
                        <input type="text" id="memberMonthlyPrice" name="memberMonthlyPrice" value="<?php echo $this->options->memberMonthlyPrice; ?>" required>
                        <div class="form-tip">设置月度会员价格，单位：元</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="memberYearlyPrice">年度会员价格</label>
                        <input type="text" id="memberYearlyPrice" name="memberYearlyPrice" value="<?php echo $this->options->memberYearlyPrice; ?>" required>
                        <div class="form-tip">设置年度会员价格，单位：元</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="memberLifetimePrice">永久会员价格</label>
                        <input type="text" id="memberLifetimePrice" name="memberLifetimePrice" value="<?php echo $this->options->memberLifetimePrice; ?>" required>
                        <div class="form-tip">设置永久会员价格，单位：元</div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-subtitle">支付设置</h4>
                    
                    <div class="form-group">
                        <label for="alipayAppId">支付宝AppID</label>
                        <input type="text" id="alipayAppId" name="alipayAppId" value="<?php echo $this->options->alipayAppId; ?>">
                        <div class="form-tip">填写您的支付宝应用ID</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="alipayPrivateKey">支付宝私钥</label>
                        <textarea id="alipayPrivateKey" name="alipayPrivateKey" rows="3"><?php echo $this->options->alipayPrivateKey; ?></textarea>
                        <div class="form-tip">填写您的支付宝应用私钥</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="wechatAppId">微信支付AppID</label>
                        <input type="text" id="wechatAppId" name="wechatAppId" value="<?php echo $this->options->wechatAppId; ?>">
                        <div class="form-tip">填写您的微信支付应用ID</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="wechatMchId">微信支付商户号</label>
                        <input type="text" id="wechatMchId" name="wechatMchId" value="<?php echo $this->options->wechatMchId; ?>">
                        <div class="form-tip">填写您的微信支付商户号</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="wechatKey">微信支付密钥</label>
                        <input type="text" id="wechatKey" name="wechatKey" value="<?php echo $this->options->wechatKey; ?>">
                        <div class="form-tip">填写您的微信支付API密钥</div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-subtitle">通知设置</h4>
                    
                    <div class="form-group">
                        <label for="notifyEmail">付款通知邮箱</label>
                        <input type="email" id="notifyEmail" name="notifyEmail" value="<?php echo $this->options->notifyEmail; ?>">
                        <div class="form-tip">设置接收付款通知的邮箱地址</div>
                    </div>
                    
                    <div class="form-group checkbox">
                        <input type="checkbox" id="enableEmailNotify" name="enableEmailNotify" value="1" <?php echo $this->options->enableEmailNotify ? 'checked' : ''; ?>>
                        <label for="enableEmailNotify">启用邮件通知</label>
                        <div class="form-tip">开启后，系统将通过邮件通知用户付款情况</div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn-primary">保存设置</button>
                </div>
            </form>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- 编辑会员对话框 -->
<div class="modal" id="editMemberModal">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">编辑会员信息</h3>
            <button class="modal-close"><i class="ri-close-line"></i></button>
        </div>
        <div class="modal-body">
            <form id="editMemberForm" method="post">
                <input type="hidden" name="action" value="update_member">
                <input type="hidden" name="uid" id="edit_uid" value="">
                
                <div class="form-group">
                    <label for="edit_username">用户名</label>
                    <input type="text" id="edit_username" readonly>
                </div>
                
                <div class="form-group">
                    <label for="level">会员等级</label>
                    <select id="level" name="level" required>
                        <option value="">请选择会员等级</option>
                        <option value="monthly">月度会员</option>
                        <option value="yearly">年度会员</option>
                        <option value="lifetime">永久会员</option>
                    </select>
                </div>
                
                <div class="form-group duration-group">
                    <label for="duration">会员有效期</label>
                    <div class="duration-input">
                        <input type="number" id="duration" name="duration" min="1" value="1">
                        <select id="duration_unit" name="duration_unit">
                            <option value="days">天</option>
                            <option value="months">月</option>
                            <option value="years">年</option>
                        </select>
                    </div>
                    <div class="form-tip">选择永久会员时，此项无效</div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn-primary">保存修改</button>
                    <button type="button" class="btn-secondary modal-cancel">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除会员对话框 -->
<div class="modal" id="deleteMemberModal">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">删除会员</h3>
            <button class="modal-close"><i class="ri-close-line"></i></button>
        </div>
        <div class="modal-body">
            <form id="deleteMemberForm" method="post">
                <input type="hidden" name="action" value="delete_member">
                <input type="hidden" name="uid" id="delete_uid" value="">
                
                <div class="confirm-message">
                    确定要删除用户 <span id="delete_username"></span> 的会员资格吗？此操作不可撤销。
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn-danger">确认删除</button>
                    <button type="button" class="btn-secondary modal-cancel">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化编辑会员对话框
    const editBtns = document.querySelectorAll('.edit-member');
    const editModal = document.getElementById('editMemberModal');
    const editForm = document.getElementById('editMemberForm');
    const levelSelect = document.getElementById('level');
    const durationGroup = document.querySelector('.duration-group');
    
    editBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const uid = this.getAttribute('data-uid');
            const username = this.getAttribute('data-username');
            
            document.getElementById('edit_uid').value = uid;
            document.getElementById('edit_username').value = username;
            
            // 显示对话框
            editModal.classList.add('active');
        });
    });
    
    // 根据会员等级显示/隐藏有效期选项
    if (levelSelect) {
        levelSelect.addEventListener('change', function() {
            if (this.value === 'lifetime') {
                durationGroup.style.display = 'none';
            } else {
                durationGroup.style.display = 'block';
            }
        });
    }
    
    // 初始化删除会员对话框
    const deleteBtns = document.querySelectorAll('.delete-member');
    const deleteModal = document.getElementById('deleteMemberModal');
    
    deleteBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const uid = this.getAttribute('data-uid');
            const username = this.getAttribute('data-username');
            
            document.getElementById('delete_uid').value = uid;
            document.getElementById('delete_username').textContent = username;
            
            // 显示对话框
            deleteModal.classList.add('active');
        });
    });
    
    // 关闭所有对话框
    const closeBtns = document.querySelectorAll('.modal-close, .modal-cancel');
    closeBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
            }
        });
    });
});
</script>

<?php $this->need('footer.php'); ?> 