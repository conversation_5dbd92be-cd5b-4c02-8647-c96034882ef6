<?php
/**
 * 支付处理页面
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 引入订单系统
require_once 'orders.php';

// 检查用户是否登录，未登录则跳转到登录页面
if (!$this->user->hasLogin()) {
    $this->response->redirect($this->options->loginUrl);
}

// 获取支付信息
$member_plan = isset($_POST['member_plan']) ? $_POST['member_plan'] : 'yearly';
$payment_method = isset($_POST['payment_method']) ? $_POST['payment_method'] : 'alipay';

// 根据不同的套餐类型设置价格和名称
switch ($member_plan) {
    case 'monthly':
        $plan_name = '月度会员';
        $plan_price = $this->options->memberMonthlyPrice;
        $plan_duration = '1个月';
        break;
    case 'yearly':
        $plan_name = '年度会员';
        $plan_price = $this->options->memberYearlyPrice;
        $plan_duration = '1年';
        break;
    case 'lifetime':
        $plan_name = '永久会员';
        $plan_price = $this->options->memberLifetimePrice;
        $plan_duration = '永久';
        break;
    default:
        $plan_name = '年度会员';
        $plan_price = $this->options->memberYearlyPrice;
        $plan_duration = '1年';
}

// 支付方式的中文名
$payment_method_name = '';
switch ($payment_method) {
    case 'alipay':
        $payment_method_name = '支付宝';
        $payment_icon = 'ri-alipay-line';
        $qrcode_img = 'https://via.placeholder.com/200x200.png?text=支付宝二维码';
        break;
    case 'wechat':
        $payment_method_name = '微信支付';
        $payment_icon = 'ri-wechat-pay-line';
        $qrcode_img = 'https://via.placeholder.com/200x200.png?text=微信支付二维码';
        break;
    case 'card':
        $payment_method_name = '银行卡支付';
        $payment_icon = 'ri-bank-card-line';
        $qrcode_img = 'https://via.placeholder.com/200x200.png?text=银行卡二维码';
        break;
    default:
        $payment_method_name = '支付宝';
        $payment_icon = 'ri-alipay-line';
        $qrcode_img = 'https://via.placeholder.com/200x200.png?text=支付宝二维码';
}

// 创建订单
$order_data = [
    'uid' => $this->user->uid,
    'type' => 'member',
    'title' => $plan_name,
    'amount' => $plan_price,
    'payment_method' => $payment_method
];

$order_id = createOrder($order_data);

// 如果是购买内容，处理内容购买订单
if (isset($_GET['cid']) && isset($_GET['price'])) {
    $cid = intval($_GET['cid']);
    $price = floatval($_GET['price']);
    
    // 获取内容标题
    $db = Typecho_Db::get();
    $content = $db->fetchRow($db->select('title')->from('table.contents')->where('cid = ?', $cid));
    
    if ($content) {
        $order_data = [
            'uid' => $this->user->uid,
            'type' => 'content',
            'item_id' => $cid,
            'title' => '付费内容: ' . $content['title'],
            'amount' => $price,
            'payment_method' => $payment_method
        ];
        
        $order_id = createOrder($order_data);
    }
}

// 处理支付回调
if (isset($_POST['payment_callback']) && isset($_POST['order_id'])) {
    $callback_order_id = $_POST['order_id'];
    $transaction_id = isset($_POST['transaction_id']) ? $_POST['transaction_id'] : 'TX' . time() . rand(1000, 9999);
    
    // 处理支付回调
    if (handlePaymentCallback($callback_order_id, $transaction_id)) {
        // 返回JSON响应
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => '支付成功',
            'redirect' => $this->options->siteUrl . 'member.html'
        ]);
        exit;
    } else {
        // 返回JSON响应
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => '支付处理失败，请稍后再试'
        ]);
        exit;
    }
}

// 模拟支付成功（仅用于演示，实际环境中应删除）
if (isset($_GET['demo_pay_success']) && isset($_GET['order_id'])) {
    $demo_order_id = $_GET['order_id'];
    $demo_transaction_id = 'DEMO' . time() . rand(1000, 9999);
    
    // 处理支付回调
    handlePaymentCallback($demo_order_id, $demo_transaction_id);
    
    // 跳转到成功页面
    $this->response->redirect($this->options->siteUrl . 'payment-process.php?payment_success=1&order_id=' . $demo_order_id);
    exit;
}

$payment_success = isset($_GET['payment_success']) && $_GET['payment_success'] == 1;
$success_order_id = isset($_GET['order_id']) ? $_GET['order_id'] : '';

$this->need('header.php');
?>

<div class="main-container">
    <div class="payment-container">
        <div class="payment-header">
            <h1 class="payment-title">订单支付</h1>
            <p class="payment-desc">请按照以下步骤完成支付，成功后您的会员权限将立即生效。</p>
        </div>
        
        <!-- 支付步骤导航 -->
        <div class="payment-steps">
            <div class="step active">
                <div class="step-number active">1</div>
                <div class="step-name">确认订单</div>
            </div>
            <div class="step active">
                <div class="step-number active">2</div>
                <div class="step-name">选择支付方式</div>
            </div>
            <div class="step <?php echo $payment_success ? 'active' : ''; ?>">
                <div class="step-number <?php echo $payment_success ? 'active' : ''; ?>">3</div>
                <div class="step-name">完成支付</div>
            </div>
        </div>
        
        <?php if (!$payment_success): ?>
        <!-- 支付信息确认 -->
        <div class="payment-info">
            <div class="info-item">
                <div class="info-label">订单号</div>
                <div class="info-value"><?php echo $order_id; ?></div>
            </div>
            <div class="info-item">
                <div class="info-label">商品名称</div>
                <div class="info-value"><?php echo $plan_name; ?> (<?php echo $plan_duration; ?>)</div>
            </div>
            <div class="info-item">
                <div class="info-label">支付金额</div>
                <div class="info-value price">¥<?php echo $plan_price; ?></div>
            </div>
            <div class="info-item">
                <div class="info-label">支付方式</div>
                <div class="info-value method"><i class="<?php echo $payment_icon; ?>"></i> <?php echo $payment_method_name; ?></div>
            </div>
        </div>
        
        <!-- 支付二维码区域 -->
        <div class="qrcode-section">
            <div class="qrcode-container">
                <img src="<?php echo $qrcode_img; ?>" alt="支付二维码" class="qrcode-img">
                <div class="qrcode-tip">
                    <i class="ri-smartphone-line"></i> 请使用<?php echo $payment_method_name; ?>扫码支付
                </div>
            </div>
            <div class="payment-detail">
                <div class="detail-title">支付说明</div>
                <div class="detail-steps">
                    <div class="detail-step">
                        <div class="step-icon"><i class="ri-smartphone-line"></i></div>
                        <div class="step-text">
                            <div class="step-title">第1步</div>
                            <div class="step-desc">打开<?php echo $payment_method_name; ?>APP</div>
                        </div>
                    </div>
                    <div class="detail-step">
                        <div class="step-icon"><i class="ri-scan-2-line"></i></div>
                        <div class="step-text">
                            <div class="step-title">第2步</div>
                            <div class="step-desc">扫描左侧二维码</div>
                        </div>
                    </div>
                    <div class="detail-step">
                        <div class="step-icon"><i class="ri-check-double-line"></i></div>
                        <div class="step-text">
                            <div class="step-title">第3步</div>
                            <div class="step-desc">确认金额并付款</div>
                        </div>
                    </div>
                </div>
                <div class="payment-amount">
                    <div class="amount-label">支付金额</div>
                    <div class="amount-value">¥<?php echo $plan_price; ?></div>
                </div>
                <div class="payment-actions">
                    <button class="btn-check" data-order-id="<?php echo $order_id; ?>">我已支付</button>
                    <a href="<?php $this->options->siteUrl(); ?>payment.html" class="btn-cancel">取消支付</a>
                    <!-- 模拟支付成功（仅用于演示，实际环境中应删除） -->
                    <a href="?demo_pay_success=1&order_id=<?php echo $order_id; ?>" class="btn-demo-pay">模拟支付成功(测试用)</a>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- 支付结果 -->
        <div class="payment-result">
            <div class="result-icon success"><i class="ri-check-line"></i></div>
            <h2 class="result-title">支付成功</h2>
            <p class="result-desc">恭喜您，会员已开通成功！您现在可以享受所有会员特权。</p>
            <div class="result-info">
                <div class="info-item">
                    <div class="info-label">订单号</div>
                    <div class="info-value"><?php echo $success_order_id; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">支付时间</div>
                    <div class="info-value"><?php echo date('Y-m-d H:i:s'); ?></div>
                </div>
            </div>
            <div class="result-actions">
                <a href="<?php $this->options->siteUrl(); ?>member.html" class="btn-primary">进入会员中心</a>
                <a href="<?php $this->options->siteUrl(); ?>" class="btn-secondary">返回首页</a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 检查支付按钮
    const checkPayBtn = document.querySelector('.btn-check');
    
    if (checkPayBtn) {
        checkPayBtn.addEventListener('click', function() {
            const orderId = this.getAttribute('data-order-id');
            
            // 显示加载状态
            this.innerHTML = '<i class="ri-loader-2-line spin"></i> 检查中...';
            this.disabled = true;
            
            // 发送AJAX请求检查支付状态
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo $this->options->siteUrl; ?>payment-process.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.success) {
                                // 支付成功，跳转
                                window.location.href = response.redirect || '<?php echo $this->options->siteUrl; ?>member.html';
                            } else {
                                // 显示错误信息
                                alert(response.message || '支付验证失败，请稍后再试');
                                checkPayBtn.innerHTML = '我已支付';
                                checkPayBtn.disabled = false;
                            }
                        } catch (e) {
                            alert('系统错误，请稍后再试');
                            checkPayBtn.innerHTML = '我已支付';
                            checkPayBtn.disabled = false;
                        }
                    } else {
                        alert('网络错误，请稍后再试');
                        checkPayBtn.innerHTML = '我已支付';
                        checkPayBtn.disabled = false;
                    }
                }
            };
            xhr.send('payment_callback=1&order_id=' + orderId);
        });
    }
    
    // 自动检查支付状态
    function checkPaymentStatus(orderId) {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '<?php echo $this->options->siteUrl; ?>payment-process.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // 支付成功，跳转
                        window.location.href = response.redirect || '<?php echo $this->options->siteUrl; ?>member.html';
                    }
                } catch (e) {
                    // 忽略错误，继续轮询
                }
            }
        };
        xhr.send('payment_callback=1&order_id=' + orderId + '&auto_check=1');
    }
    
    // 定时检查支付状态（每5秒检查一次）
    <?php if (!$payment_success && isset($order_id) && !empty($order_id)): ?>
    let checkInterval = setInterval(function() {
        checkPaymentStatus('<?php echo $order_id; ?>');
    }, 5000);
    
    // 30分钟后停止检查
    setTimeout(function() {
        clearInterval(checkInterval);
    }, 30 * 60 * 1000);
    <?php endif; ?>
});
</script>

<style>
    /* 支付页面样式 */
    .payment-container {
        background-color: #191919;
        border-radius: 16px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        padding: 30px;
        margin: 30px 0;
    }
    
    .payment-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .payment-title {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #fff;
    }
    
    .payment-desc {
        color: #aaa;
        font-size: 16px;
        max-width: 600px;
        margin: 0 auto;
    }
    
    /* 支付步骤 */
    .payment-steps {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }
    
    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 20px;
        position: relative;
    }
    
    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 16px;
        right: -25px;
        width: 30px;
        height: 2px;
        background-color: #333;
        z-index: 1;
    }
    
    .step.active:not(:last-child)::after {
        background-color: #FE2C55;
    }
    
    .step-number {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #333;
        color: #888;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 600;
        margin-bottom: 8px;
        z-index: 2;
    }
    
    .step-number.active {
        background-color: #FE2C55;
        color: #fff;
    }
    
    .step-name {
        font-size: 14px;
        color: #888;
    }
    
    .step.active .step-name {
        color: #fff;
    }
    
    /* 支付信息 */
    .payment-info {
        background-color: #222;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #333;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        color: #888;
    }
    
    .info-value {
        color: #fff;
        font-weight: 500;
    }
    
    .info-value.price {
        color: #FE2C55;
        font-weight: 600;
        font-size: 20px;
    }
    
    .info-value.method {
        display: flex;
        align-items: center;
    }
    
    .info-value.method i {
        margin-right: 5px;
        font-size: 18px;
    }
    
    /* 二维码区域 */
    .qrcode-section {
        display: flex;
        background-color: #222;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .qrcode-container {
        flex: 1;
        padding: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #333;
    }
    
    .qrcode-img {
        max-width: 200px;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    
    .qrcode-tip {
        color: #aaa;
        display: flex;
        align-items: center;
    }
    
    .qrcode-tip i {
        margin-right: 5px;
    }
    
    .payment-detail {
        flex: 1;
        padding: 30px;
    }
    
    .detail-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 20px;
    }
    
    .detail-steps {
        margin-bottom: 30px;
    }
    
    .detail-step {
        display: flex;
        margin-bottom: 15px;
    }
    
    .step-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #333;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 15px;
    }
    
    .step-icon i {
        color: #FE2C55;
        font-size: 20px;
    }
    
    .step-title {
        color: #fff;
        font-weight: 500;
        margin-bottom: 5px;
    }
    
    .step-desc {
        color: #aaa;
    }
    
    .payment-amount {
        background-color: #191919;
        border-radius: 8px;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .amount-label {
        color: #888;
    }
    
    .amount-value {
        color: #FE2C55;
        font-size: 24px;
        font-weight: 600;
    }
    
    .payment-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .btn-check {
        background-color: #FE2C55;
        color: #fff;
        border: none;
        border-radius: 30px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        flex: 1;
    }
    
    .btn-check:hover {
        background-color: #e01b41;
    }
    
    .btn-cancel {
        background-color: transparent;
        color: #888;
        border: 1px solid #333;
        border-radius: 30px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        flex: 1;
        text-align: center;
    }
    
    .btn-cancel:hover {
        color: #fff;
        border-color: #444;
    }
    
    .btn-demo-pay {
        background-color: #333;
        color: #aaa;
        border: none;
        border-radius: 30px;
        padding: 12px 25px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        flex: 1;
        text-align: center;
    }
    
    .btn-demo-pay:hover {
        background-color: #444;
        color: #fff;
    }
    
    /* 支付结果 */
    .payment-result {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 40px 0;
    }
    
    .result-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .result-icon.success {
        background-color: rgba(52, 211, 153, 0.2);
        color: #34D399;
    }
    
    .result-icon.success i {
        font-size: 40px;
    }
    
    .result-icon.error {
        background-color: rgba(239, 68, 68, 0.2);
        color: #EF4444;
    }
    
    .result-title {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 10px;
    }
    
    .result-desc {
        color: #aaa;
        margin-bottom: 30px;
        max-width: 600px;
    }
    
    .result-info {
        background-color: #222;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        width: 100%;
        max-width: 500px;
    }
    
    .result-actions {
        display: flex;
        gap: 15px;
    }
    
    .btn-primary {
        background-color: #FE2C55;
        color: #fff;
        border: none;
        border-radius: 30px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .btn-primary:hover {
        background-color: #e01b41;
    }
    
    .btn-secondary {
        background-color: #333;
        color: #fff;
        border: none;
        border-radius: 30px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .btn-secondary:hover {
        background-color: #444;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .spin {
        animation: spin 1s linear infinite;
        display: inline-block;
    }
</style>

<?php $this->need('footer.php'); ?> 