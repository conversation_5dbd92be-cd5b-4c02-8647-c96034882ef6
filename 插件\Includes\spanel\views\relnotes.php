<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly. ?>

<h3>v2.2.8</h3>
<ul>
  <li>Fixed: Free vs Pro version load priority conflict.</li>
</ul>

<h3>v2.2.7</h3>
<ul>
  <li>Added: Number field "min", "max", "step" options.</li>
  <li>Updated: Google Web Fonts array added new fonts.</li>
  <li>Updated: JS libraries (codemirror, leaflet, etc).</li>
  <li>Improved: Group field "custom title and prefix" option (samples added).</li>
  <li>Improved: Some JS and CSS coding.</li>
</ul>

<h3>v2.2.6</h3>
<ul>
  <li>Added: WordPress 5.9 compatible.</li>
  <li>Added: New field "DateTime" both date and time option.</li>
  <li>Improved: Some JS and CSS coding.</li>
</ul>

<h3>v2.2.5</h3>
<ul>
  <li>Added: Dependency new "contains" condition feature.</li>
  <li>Fixed: Admin bar menu show in for non-admin capabilities.</li>
  <li>Updated: Google Web Fonts array added 300+ new fonts.</li>
  <li>Updated: "Font Awesome 5 Free" package and added new icons.</li>
  <li>Updated: JS libraries (codemirror, leaflet, etc).</li>
  <li>Improved: "Shortcode" and "Icon" modal popups responsive css.</li>
  <li>Improved: Usage anywhere framework fields.</li>
</ul>

<h3>v2.2.4</h3>
<ul>
  <li>Added: WordPress 5.8 compatible.</li>
  <li>Fixed: Widget Framework after WP 5.8 block-based widgets issue.</li>
  <li>Fixed: Shortcode Framework "Elementor" php8 error throw issue.</li>
  <li>Updated: Google Web Fonts array.</li>
  <li>Updated: "Font Awesome 5 Free" package and added new icons.</li>
  <li>Updated: JS libraries (codemirror, leaflet, etc).</li>
  <li>Improved: Some js and css coding.</li>
</ul>

<h3>v2.2.3</h3>
<ul>
  <li>Added: Upload field show "preview" option.</li>
  <li>Added: "description" option for all framework create sections array.</li>
  <li>Fixed: Option Framework admin sub-menu link click issue on Mobile.</li>
</ul>

<h3>v2.2.2</h3>
<ul>
  <li>Added: WordPress 5.7 compatible.</li>
  <li>Fixed: Spinner field unit issue in repeater fields.</li>
  <li>Fixed: Media field svg preview issue.</li>
  <li>Updated: Google Web Fonts array.</li>
  <li>Updated: "Font Awesome 5 Free" package and added new icons.</li>
  <li>Updated: JS libraries (codemirror, leaflet, etc).</li>
  <li>Improved: Mobile device styles.</li>
  <li>Improved: Multiple instances for avoid potential conflicts.</li>
  <li>Improved: Callback field for class method calls.</li>
</ul>

<h3>v2.2.1</h3>
<ul>
  <li>Added: Metabox sections can be show by specific post_type option.</li>
  <li>Added: Inline tabs style for Metabox and Options Frameworks.</li>
  <li>Added: Nav menu "locations" select field options.</li>
  <li>Fixed: Minor bug fixes and improvements.</li>
  <li>Improved: Path-finder function for fix issue missing styles.</li>
</ul>

<h3>v2.2.0</h3>
<ul>
  <li>Added: Output feature for Taxonomy Framework fields.</li>
  <li>Fixed: Option Framework first section "restore" issue.</li>
  <li>Fixed: Link field open dialog issue.</li>
  <li>Updated: Google Web Fonts array.</li>
  <li>Updated: JS libraries (codemirror, leaflet, etc).</li>
  <li>Updated: "Font Awesome 5 Free" package and added new icons.</li>
  <li>Improved: Typography field enqueue methods.</li>
</ul>

<h3>v2.1.9</h3>
<ul>
  <li>Added: New field "link" using wplink.</li>
  <li>Added: Image field "inline" list style.</li>
  <li>Improved: Data sanitization and escaping.</li>
  <li>Improved: RTL styles.</li>
</ul>

<h3>v2.1.8</h3>
<ul>
  <li>Added: Localized 20+ languages support.</li>
  <li>Improved: Group/Repeater nested usage feature.</li>
  <li>Improved: Font Awesome 4 package usage.</li>
  <li>Updated: "Font Awesome 5 Free" package and added new icons.</li>
  <li>Updated: Google Web Fonts array.</li>
  <li>Fixed: Shortcode Framework usage inside plugin issue.</li>
  <li>Fixed: Spinner and Slider field fractional numbers step validation issue.</li>
</ul>

<h3>v2.1.7</h3>
<ul>
  <li>Fixed: Sortable field append new fields after saved issue.</li>
  <li>Fixed: Customizer dependency issue after v2.1.6 update.</li>
  <li>Fixed: Typography field "Normal 400" style embed issue.</li>
  <li>Updated: Assets library (shilin.css to style.css, shilin.js to main.js).</li>
  <li>Improved: Sanitize/Validate callback method.</li>
  <li>Improved: Global dependency feature.</li>
  <li>Improved: Usage anywhere framework fields.</li>
</ul>

<h3>v2.1.6</h3>
<ul>
  <li>Added: Nav Menu Option Framework (bonus).</li>
  <li>Added: Section "class" paramter for Admin, Metabox Option Frameworks.</li>
  <li>Added: Fields with output css feature can be used in Accordion, Tabbed and Fieldset fields.</li>
  <li>Added: Visible dependency instead of hiding.</li>
  <li>Fixed: Background field gradient transparent issue.</li>
  <li>Improved: Admin Option Framework tab anchors (#tab-1,2 etc.) with the actual tab names.</li>
  <li>Improved: Data sanitization and escaping.</li>
  <li>Improved: All of framework scripts and styles loads only when needed.</li>
  <li>Improved: Output css feature and documentation.</li>
  <li>Improved: Network menus and options integration.</li>
  <li>Improved: Google web fonts load method.</li>
  <li>Improved: Some js and css coding.</li>
</ul>

<h3>v2.1.5</h3>
<ul>
  <li>Added: Sub menu item title argument for Admin Option Framework.</li>
  <li>Added: A parameter "sanitize => false" for allow to script/iframe or any html code.</li>
  <li>Fixed: Reset section issue for Admin Option Framework.</li>
  <li>Fixed: A few bugs fixed caused by sanitize have been.</li>
  <li>Fixed: Output CSS sanitize issue.</li>
  <li>Updated: "Font Awesome 5 Free" package and added new icons.</li>
  <li>Improved: Data sanitization and escaping.</li>
</ul>

<h3>v2.1.4</h3>
<ul>
  <li>Added: "Font Awesome 5 Free" package for "Icon" field ( v4 is still can be used ).</li>
  <li>Added: "Button Set" field custom options types like "Select" field categories, pages etc.</li>
  <li>Added: New field "callback". This field allows to make custom html output with a function.</li>
  <li>Added: Data sanitization and escaping for all framework attributes/data.</li>
  <li>Fixed: Backup field and Ajax-save stripslashes issue.</li>
  <li>Improved: Form warning message on reset button.</li>
  <li>Improved: Map search field autocomplete (caching same requests in typing).</li>
</ul>

<h3>v2.1.3</h3>
<ul>
  <li>Added: New field "map".</li>
  <li>Added: Quick save (ctrl+s,command+s) for Admin Option Framework.</li>
  <li>Added: Form warning when changed any option for Admin Option Framework.</li>
  <li>Fixed: Output CSS twice times issue.</li>
  <li>Updated: Google Web Fonts array.</li>
  <li>Updated: Customizer Framework samples.</li>
  <li>Improved: Background field for auto hide/show background attributes.</li>
</ul>

<h3>v2.1.2</h3>
<ul>
  <li>Added: WP 5.3 "Kirk" UI compatibility.</li>
  <li>Added: Comment Metabox Framework (bonus).</li>
  <li>Added: New field "number".</li>
  <li>Fixed: Media field dependency issue.</li>
  <li>Fixed: Button set field default selected issue.</li>
  <li>Fixed: Select field empty message issue.</li>
  <li>Fixed: Multiple chosen select issue in Shortcode Framework.</li>
  <li>Fixed: Checkbox field confict with Repeater and Group field issue.</li>
  <li>Fixed: Image Select and Multiple Checkbox fields refresh issue in Customize Framework.</li>
</ul>

<h3>v2.1.1</h3>
<ul>
  <li>Added: Chosen select AJAX search option.</li>
  <li>Added: Chosen select sortable option.</li>
  <li>Added: Chosen select keep options order.</li>
  <li>Added: Users select field options.</li>
  <li>Fixed: Single shortcode insert issue.</li>
  <li>Fixed: Taxonomy Framework multiple save issue.</li>
  <li>Fixed: Widgets Framework chosen select save issue.</li>
  <li>Fixed: Chosen multiple select refresh issue in Customizer.</li>
  <li>Fixed: Minor bug fixes and improvements.</li>
</ul>

<h3>v2.1.0</h3>
<ul>
  <li>Added: SSL protocol control for library files.</li>
  <li>Added: SCSS style files to main files again.</li>
  <li>Fixed: Default value issue in Taxonomy Framework.</li>
  <li>Fixed: Customize option refresh/save issue in existing customizer section.</li>
</ul>

<h3>v2.0.9</h3>
<ul>
  <li>Added: Framework fields support for predefined customizer sections.</li>
  <li>Fixed: Metabox save php notices when using "serialize" and "unserialize" in same time.</li>
  <li>Fixed: Multiple instances save data issue.</li>
  <li>Fixed: Spacing field all_icon parameter.</li>
  <li>Fixed: Admin options show in customizer parameter "show_in_customizer".</li>
  <li>Improved: Media field specific type of uploads.</li>
  <li>Improved: Spacing and dimensions fields unit parameter.</li>
  <li>Improved: "empty_message" parameter for checkbox, radio and select fields.</li>
</ul>

<h3>v2.0.8</h3>
<ul>
  <li>Added: Framework wrapper classname param as "class" for override css styles.</li>
  <li>Added: Checkbox and Radio field group option like Select field group.</li>
  <li>Added: Attachment post type metabox support.</li>
  <li>Fixed: Auto-filled issue for framework search input.</li>
  <li>Fixed: Spacing field refresh issue in Customizer.</li>
  <li>Fixed: Radio field checked issue in category id select.</li>
  <li>Fixed: Metabox single option save issue.</li>
  <li>Fixed: Metabox wp editor loaded issue.</li>
  <li>Improved: Dependency hide/show for a text field after the value is written.</li>
  <li>Improved: Dependency parameter for multiple conditions.</li>
</ul>

<h3>v2.0.7</h3>
<ul>
  <li>Added: Profile Option Framework (bonus).</li>
  <li>Added: Media field fallback function for easy migration from v1.x to v2.x.</li>
  <li>Fixed: A minor PHP notice in the Shortcode Generate Framework.</li>
  <li>Improved: Link Color field output for given array elements.</li>
  <li>Improved: Elementor integration for Shortcode Generate Framework.</li>
  <li>Improved: Backup field UI fallback in the Customize Framework.</li>
  <li>Improved: Global dependency controls.</li>
</ul>

<h3>v2.0.6</h3>
<ul>
  <li>Added: Default values from a external array as optional for all frameworks.</li>
  <li>Added: Widget Title sync support in Widgets Framework.</li>
  <li>Added: WP Roles select field options.</li>
  <li>Fixed: Initialization issue inside after_theme_setup action.</li>
  <li>Fixed: Backup export button issue.</li>
  <li>Fixed: WP Media uploaded item selected issue.</li>
  <li>Improved: Reset and Import UI messages in the Customize Framework.</li>
  <li>Improved: Translation .POT file.</li>
  <li>Improved: Global dependency controls.</li>
</ul>

<h3>v2.0.5</h3>
<ul>
  <li>Added: Dependency controls for among separate sections fields.</li>
  <li>Added: Border field new border style properties.</li>
  <li>Added: Taxonomy Framework section title param.</li>
  <li>Fixed: Metabox section title issue.</li>
  <li>Fixed: Typography refresh issue in Customizer.</li>
  <li>Fixed: Group and Repeater fields without title parameter.</li>
  <li>Fixed: Color field default issue in Background, Typography, Border fields.</li>
  <li>Fixed: RevSlider CodeMirror conflict.</li>
  <li>Fixed: Shortcode Generate Framework Group/Repeater nested issue.</li>
</ul>

<h3>v2.0.4</h3>
<ul>
  <li>Added: Dark and Light themes.</li>
  <li>Added: New params to change on/off texts for Switcher field.</li>
  <li>Added: Shortcode generate framework support for Elementor text editor.</li>
  <li>Fixed: Sortable and Sorter fields ordering save issue in Customizer.</li>
  <li>Fixed: Radio, Button Set and Image Select fields issue in Group field.</li>
  <li>Fixed: Color picker default/clear button issue in Customizer.</li>
  <li>Improved: RTL style of framework.</li>
  <li>Improved: Media and Upload fields remove buttons.</li>
  <li>Improved: Framework style css.</li>
  <li>Changed: Backup field data type "json" instead of "serialize".</li>
</ul>

<h3>v2.0.3</h3>
<ul>
  <li>Added: Widget Options Framework (bonus).</li>
  <li>Added: Nested Group support.</li>
  <li>Added: Nested Repeater support.</li>
  <li>Added: Spanish Translation po/mo.</li>
  <li>Added: Date range "from" and "to" for Date field.</li>
  <li>Added: New param "empty_message" if options not provided for Select, Checkbox, Radio.</li>
  <li>Fixed: Metabox framework php notices in 404 page etc.</li>
  <li>Fixed: WP Editor field save issue.</li>
  <li>Improved: Validate email function.</li>
  <li>Improved: Group field arguments.</li>
  <li>Improved: Font-Awesome library.</li>
  <li>Improved: Hide to "welcome" page automatically if not used as plugin.</li>
  <li>Improved: Confirm alert box messages translations.</li>
</ul>

<h3>v2.0.2</h3>
<ul>
  <li>Added: Page Templates "default" option for spesific metabox hide/show.</li>
  <li>Added: Post Formats "default" option for spesific metabox hide/show.</li>
  <li>Added: Only allow numeric inputs for Spacing, Dimensions, Border, Slider, Spinner, Typography etc.</li>
  <li>Added: ChosenJS custom options support and improved width and css style.</li>
  <li>Fixed: Taxonomy framework jquery triggers. It was not working again after saving. It fixed now.</li>
  <li>Fixed: Code Editor style issue for used inside Group/Repeater.</li>
  <li>Fixed: Sortable field sortby issue.</li>
  <li>Fixed: Options panel show in customizer option.</li>
  <li>Fixed: Media field URL show/hide option issue.</li>
  <li>Improved: Typography, Color, Spinner, Date, Chosen, Slider fields for used inside Group/Repeater.</li>
  <li>Improved: All fields javascript triggers for more performance.</li>
  <li>Improved: Customizer Framework field dependency feature.</li>
  <li>Improved: Customizer Framework field styles.</li>
</ul>

<h3>v2.0.1</h3>
<ul>
  <li>Added: WP Editor AJAX support for Group Field, Repeater Field and Everywhere.</li>
  <li>Added: Custom palette colors option for Color Picker.</li>
  <li>Added: Override files feature again.</li>
  <li>Added: Validate URL function callback.</li>
  <li>Fixed: Group field save issue.</li>
  <li>Fixed: Multiple given post formats metabox hide/show issue.</li>
  <li>Fixed: Minor bugs.</li>
</ul>

<h3>v2.0.0</h3>
<ul>
  <li>Premium version release.</li>
</ul>

<h3>v1.0.0</h3>
<ul>
  <li>Initial release.</li>
</ul>

