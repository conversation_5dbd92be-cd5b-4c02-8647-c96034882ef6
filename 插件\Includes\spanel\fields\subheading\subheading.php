<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.
/**
 *
 * Field: subheading
 *
 * <AUTHOR> Studio
 * @Uri https://shilin.studio
 *
 */
if ( ! class_exists( 'Shilin_Field_subheading' ) ) {
  class Shilin_Field_subheading extends Shilin_Fields {

    public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
      parent::__construct( $field, $value, $unique, $where, $parent );
    }

    public function render() {

      echo ( ! empty( $this->field['content'] ) ) ? $this->field['content'] : '';

    }

  }
}
