<?php ?><?php // /* *****  * @自助授权：https://sq.shilin.studio  * @自助下单：https://order.shilin.studio  * @Author: 诗林工作室  * @AuthorUri: https://shilin.studio  * @Date: 2025-03-30 20:49:54  * @LastEditTime: 2025-03-30 05:48:38  * Copyright (c) 2024 by Shilin Studio All Rights Reserved. */ - by 贝塔PHP加密|https://sg.bt58.vip ?><?php
if(!function_exists('sg_load')){$__v=phpversion();$__x=explode('.',$__v);$__v2=$__x[0].'.'.(int)$__x[1];$__u=strtolower(substr(php_uname(),0,3));$__ts=(@constant('PHP_ZTS') || @constant('ZEND_THREAD_SAFE')?'ts':'');$__f=$__f0='ixed.'.$__v2.$__ts.'.'.$__u;$__ff=$__ff0='ixed.'.$__v2.'.'.(int)$__x[2].$__ts.'.'.$__u;$__ed=@ini_get('extension_dir');$__e=$__e0=@realpath($__ed);$__dl=function_exists('dl') && function_exists('file_exists') && @ini_get('enable_dl') && !@ini_get('safe_mode');if($__dl && $__e && version_compare($__v,'5.2.5','<') && function_exists('getcwd') && function_exists('dirname')){$__d=$__d0=getcwd();if(@$__d[1]==':') {$__d=str_replace('\\','/',substr($__d,2));$__e=str_replace('\\','/',substr($__e,2));}$__e.=($__h=str_repeat('/..',substr_count($__e,'/')));$__f='/ixed/'.$__f0;$__ff='/ixed/'.$__ff0;while(!file_exists($__e.$__d.$__ff) && !file_exists($__e.$__d.$__f) && strlen($__d)>1){$__d=dirname($__d);}if(file_exists($__e.$__d.$__ff)) dl($__h.$__d.$__ff); else if(file_exists($__e.$__d.$__f)) dl($__h.$__d.$__f);}if(!function_exists('sg_load') && $__dl && $__e0){if(file_exists($__e0.'/'.$__ff0)) dl($__ff0); else if(file_exists($__e0.'/'.$__f0)) dl($__f0);}if(!function_exists('sg_load')){$__ixedurl='https://www.sourceguardian.com/loaders/download.php?php_v='.urlencode($__v).'&php_ts='.($__ts?'1':'0').'&php_is='.@constant('PHP_INT_SIZE').'&os_s='.urlencode(php_uname('s')).'&os_r='.urlencode(php_uname('r')).'&os_m='.urlencode(php_uname('m'));$__sapi=php_sapi_name();if(!$__e0) $__e0=$__ed;if(function_exists('php_ini_loaded_file')) $__ini=php_ini_loaded_file(); else $__ini='php.ini';if((substr($__sapi,0,3)=='cgi')||($__sapi=='cli')||($__sapi=='embed')){$__msg="\nPHP script '".__FILE__."' is protected by SourceGuardian and requires a SourceGuardian loader '".$__f0."' to be installed.\n\n1) Download the required loader '".$__f0."' from the SourceGuardian site: ".$__ixedurl."\n2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="\n3) Edit ".$__ini." and add 'extension=".$__f0."' directive";}}$__msg.="\n\n";}else{$__msg="<html><body>PHP script '".__FILE__."' is protected by <a href=\"https://www.sourceguardian.com/\">SourceGuardian</a> and requires a SourceGuardian loader '".$__f0."' to be installed.<br><br>1) <a href=\"".$__ixedurl."\" target=\"_blank\">Click here</a> to download the required '".$__f0."' loader from the SourceGuardian site<br>2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="<br>3) Edit ".$__ini." and add 'extension=".$__f0."' directive<br>4) Restart the web server";}}$__msg.="</body></html>";}die($__msg);exit();}}return sg_load('84188AC199EA7FA5AAQAAAAhAAAABKAAAACABAAAAAAAAAD/ctNM/bRMYhMHJtjdsuuanPkgAoK7OAGdBIWCRzyWpeiGzEgJH61IapuqN2p+cnlCbILXkSX9FZYInYn4dMZJ6rqfJU6oipnNXQmmymoimwG9uzec4gqBRSuhySE3IcnBVFREc8i2clZnCbkASA9YPlhYNgVye6wLb0vzF+q3rSsTQnXQKijfkn5cjdPDWyNOsfSFoA7yKSc82UvRgtCuEkoAAADIBQAAztdiKtsLNUUMJ3EGNkVIlQgxdKdkaHzPoWm50sGR1Y9+fZXWMNpNNWT88G2/y98K5gLo09l9rYA6eMRatjHcZLNF9l0ZbU/1UVa9FUtG+cyfey9J3969vp0x1XREg7y5qrYqSu3vcq7Rr55Q/bA0fBC1PqOpFfGzpjxNwkYkfJPJA0w6uHIXqsS9Ukn1LlWrY0Sq2hCdOIcF7XCJN7uPA7jC6v4HtT7YsXKFDsdPeeo/HhRex+gmUAnjZulRjvBjZl5LXSm8GQDmdLktgyMMsMHFqzEb8lFP4FMhePsIVp9tMVm8+sS8SeYpx5QwUfqvSu+LaiQhTlGvUEzj9jrtp4m+bDSk/jISs//qsIov4hogQ1OtdPtFYvpWQIr0wIXO8/LiR70Wewpmlp+31wPXF5d8//ptNrnRN4NM14Kb/+0vhFa+z1oSZkrggKFjr01YRZtS/BXg4YBj8bveYIyE+5jGoKaUjqWcKjKNJhgNzbaXoAl/Sq9tZ7vp3YyV4KCF49RAAB3BqS3gXnmn7xICVWPhlz0XXCw60RzjAQkfh0NTzER5e3OPRzHMPPOFz1ub3z5dbitNH2AuJo4Q4/yEeTUZVrUrXPB1LRKDrd6xcLps5zi6v4lSxn0JLnB9e4PyYp/TtAqXTEOTBph5kg24fXRmLEo55Q9LPuuhUV0DCmZcFAGlT3Wt1o/0VAvblL5oM8qjZRudAeGNBlsBMYXwo7PJ8KhGAsRXjSFv7Y1hLAaJOepYeo7wTmByst2OZ2IMHqRiRUjO5+VYlWbsrOFob/YFkJY5SUdVLXnZVVWc1zOQA4IuCFtfU922O0XrJUguqXcinDc2FfEwan4cOFNXf2qMwXnjpv5nRHLbc48nmawEIV+dV+hgtqBSsf/sCJVEDXVZ2jLUncnzudmT9XZT/EcYtEB6uHYrW48kuMZ58zHfnR9m0R4kVS/NJx8lTM3kQ3zQrL0necMBPc97ti1FSimRl67gyCB0tv/fNGnglb6KBCmJo/cmm0XnNWD+AWQORFIeILhOgiUhE6a9fFeQme3kDy9G8dlhS0aTW1sCgipRcY1NI2Xea7nnB5hWvScs/DIvYZ3CdRO5Xc9e2u0YAg49DqNwLPBDiYvJcLYSzMWZJOe6gXBMoX1+NvoxBViYLwEUb49fbrwc0pZVRFeEhZTRQdMNyL5Z5D9bL4eR6SHMewYnwRgjln+EyxT8jzjEnQ3zcfmKEvWKoXMESx9zAfvADt4PRpwVickyfup2p7ow6LyE12p5vh47wAlR/p+AxSBTe96QcZqYDMKih20QnWgMQszR4ugmTM1D7Ll1dZsblJ0g9Nu4hMoKwzcMt7hcbLAVcIGnDVNjfnT5zSEWT3IdyvIyAUHV4tUOBKhNF9+Y63YGIMNiFbwa9bkxeO5X2nbpJWhaIFuNVRU3RjAqhf2iVNY6vMqaBDJrXoo/ajkPCCgCMcHUULNF974CfauEsSMm0Pl2YnHzUbt+7vrtgvd+xGmLH+KVaYJUow+X+wQq4gpxM4Aoik4LzS5yj5QK0kMB+nuYLYfmUs9RoIMcm2xob1x1214Bzwt0HtdIyYkQ+z4345PW437e0OzNyR8Ki3+UjPLsM3529J/qkJTdDwiRrdDvI+akflzLBSEcCcvlwwgIMv1ftcaYL0/YMTf78vprj8WFLFHBy3OIyeUD8Bo6SJF/BkSFZnyveymV17ESs/yxcMD8dC6BXEd6RD+2HdVKEKyylOJjb9oJcHS9qJc6NiBEkkuoJj6Bz9HLJKpEXkMnDuUyfzrziczp94NRzNkmzTSeWDPItmsopFVjxT+FZjbMyKhTAULEsx2pG5yqZEePYYH7a20oDdrrjeBSMmg7JciRmcY8n1E191XkO6Q4GwQ4CoStyKETbctty2KQtkCHXF6uQfZ1g5QpYwx+MgKbBR/gzjN93fYjHrh7BxMpdUNqbCqgti7pt2wewjv5KdWdw3UAmQgAAADYBQAAciEO4/Nfa4ycM+BPRaFapBdjbUtlKFi/o1U6FEmw85eUhWqmb0XWkvcRNQI/MgKBRfv5XB7xQlqFNWfJcUlZZ899U8WUikHo+60seSi93IVv8egNhT8umVbuvw0iIiUVauKRRwkaMzKqHY4uP1kXExS8lYZy9G+uybMU6JJG34CIXXU5Ys+dzqkEIY46vxjuypPwPZh6PB6pxJmCdT3SLZ1c7eao0K64dC8XQ5PEL4BnFq+hXEC2fLNdRhrDd13VJTKb1GADaRxCSHadyMfcsofkD7Iq0eVaaaCL3u2GDJZCin5M9xtyyZ4zeturVVxCy0E2fUXQ0+m3zliK3TypnOSvAkvlCOuqe1hEPvs2RPMTQmjpTv6cWKNdW995m3AjkA6WLAfS9guNhmUiPpz6au2f5UBx1jnCI+4TPLPKvKGrdZO8Q1PRbFdwHbkgPTFR6crYqd3IxHqbeehwNV7iVavIdu89sH4hkgLcamOsqwy1D+BVUVCzZBcX6U+kkq4xPeNkggfY+g20DK724LdmTeMR8IfriOGJsqDKR1JvVWacfq4ljphiNiKF+VyUXHBn8jwcp8nHN0oINGUrEYizr6hL7vX4XFicuGNMRvIGPgYVbF4wvB15mLQqtI+1D9ApeOgZK9DTx61uqbVLDHaB0jh621x2XPOiAhADlJZOiWwCZDOk20FgWv3+VqyMIyWQzt/v4M8T1ARIA12HhSeqByj8fc8RzsEva9cjld70i2gWFt4QuEl3ETD9gFN0+2vdwrQZXuPE/IOHT3lYNp1UnsxhBV6eKfch5hy/TcLSzCAYkz3ufibX6qsO2IXblX4KoKMdiil64nY79Xz2upQiueONiXbtA3OWOD/RROV5YtR3R9ru1r5D1W69jV49KbWkFS2qyEFRaM94bbBdTg6+z49RGqoX5RBdK/M37Ch1Cc5dgZN/fjYUDF4bKeUCPraV64AfitcGAbw7IRhsx4iKl28oZH0vdDrdjaBqk5pihFnRgvJw+yA+BEIlm0uVi/xoROSLHZVcWa7nlefKTU2fyDI+EgVdwA6iQDXWHIRXiW5iIEwvO5EvoQhj9sXhKpDtgK5S5qvGbFDLb77ko/XK2IzFrb7yx01okH65AW+/ROMs5Xs+rXJVPqCsyMl/lQSh3IdZhNNPz5K7BBbyuKTyVl9VT1wjJYkri2+Ui7f9pySK9OgPKYAESmPclCYMxA9lrBZyrt5FngSLgcZoRqM1kupeKwqTKsPsMqj9soCSMPCP1mV8euwC5g/1zR8kFfUj0U+RS7SO06sPflAHJVVGjyKbDvjw8KogK0BPeLD6HCcjdrffUI88eURlhWdImYXCptGIJmK510ImhVgd0ePypTFGqio5i7tQXWagpT5TvZw0qNCnsm3dEL/jm33oxQFcVivcDQCHDaqvdvlHE1oNV5nEWDk8jfB/qycQBG6Bx3q1oXyhjspJ6vsyMXfxAy0ktb2tsBoLaRxnh80pJk8H580U46BckB9yxc28GyHhhiyZdJlhRnjLCte9opFRjbZYnwBX2s3SDiy0MjOv3jVJwDg+QfRr9MMy8tpO9DDbr2HKMYBz8O4f25wN4xZyGMObaUW+kmpkSoFlt5Dsmo2FrlBgKC+ukDjS60FemUUD0I8m33ZY/q5/Ohj7bVN2VlUjkk4a3QwVx+gIPct5w5uV71Y8DvZaQzvs7kSn2PaOJo/ibN5giyLyrZ+dqCgI8BVzdcgZlzGCv/jJIE/6vGwZP/jL+mBBzJjJz38vZEWeS9oWt4exYtjOl57ZBhV4pUCLqRemk6wDlF7Xahbuvyd5iZmGg087qjHkhBeKpr179nhIh858FDEzGnCSlV6n7HxJpwXJM62TP0T2Vi5Y1WkRYJMzwSlAcr+dbXMUCvXaiho3sJlxHaOmEci6kfL992rbazSqN3LpzlDEgqe0JGP623IK6kA+hZMQJpEvsoOVCmbzPwFQPSAt9F7USPvj/v9mIoZANSAAh2hRAAAAMAUAAAoYxDnf/5DHLTSgJETEAz+Nuz/2VSCtlj/A/hovBbjQWgr/hbUGE/oqDjUCvaQJ9I6IfMAJq794CkJxq+HtHWOuALHtzJDLAE1KCnHvuq762f5FBV8RHol/3GhCMve1YxCGjiV2wfTigAQz6LkLLM2WZoUv1T6uXHJYdsuZ6KdkDUoX46m5gbco1Sk0Mhw67LFgw+WtS9R492j1zlWgj40uWOXWT1C+OI77jCVX65hyToORd1Ncdol+0khBV0wnmYGFXmyOd9bLTNDPZjD0msj/K/OQkOLC5+xZTO/e4U/vFJi7fk0Nk3U5K5waeiZ+gPnFZDQRx7k0WAEms9wo+iKfGz+D+PL1woGwgsMr2yCJhlrJAeiwCnlBR0evdJ+rtgiYva+aA2vRVyg3JPxO+UtnQTryAHMIyg5qGGhOn1xZZuF4EEDe9Y+yo1ohVKEZuyeOJ7K8WStipY1mjbM9AAcquaL5C+5vMV5Aa56VQaQxYlJ3nut8ypspC5h+I348tc6XjOHZBNxnzcK93Q13tjvz6Id91yUPEOB60k+5ixao2O3YQKf9r2WdicMOkmoTgBi/gCOhtLQByGvHD1sml+1/LCM/TKXjQOCWi0VhhJ+1TZbb04MJfgozdiUJNLr56miFVTfheKHJK8HSee9bwty+I+I+tP953LiYf8uODJq/OEaNV3fSLSrRdiiEfAzJuR8eI8yWht6aMchLBdiQcUiSACrgrQC4wk5jNsQsZIn+80NWkRsmSNEKoBEmLlNvqx3g864SAdaoke3nqrtaLM0+69lr8dpOc0o1egKo7bEKx7kaYU8FmqVcvVbeQVTDWSXX1ftxyUThkQZFRLXQ/6blDr+9YDZ4mx7Zd/lccQzJDtqUStsinT4sRfvaDVq9jgwF4meialRumxdhrnmdHKtQfn4yXU3oFR1nveJ7C3dB/jBNvBdQjzm9LzKr4vp30Fr484U/IU3uETbSfi8SD2wit97c085DUbPa0sQ9kilvBiPWbere+dgWDMTL821hnj7S7Ee51Vm2wriKHd/Xw0VtfJ9SYDgnxXNzVX8TqTj5U7hV4Wr4pcPrfSDd/AhCZQP/1lmf7T/TCVR7AXaTp8SCjx7Z8V/KiF6M89L79WnAwb7p+fPw2lq8AXWPf9WRzq1PfThPi2hF3A914kWD8nNtC9zumZ3tH6c4v0TCn98eaPR++m/kEb34wWkrLz8rbUzoVnWZQaaaM4vCgaKWSn1I3McnSBFDmt7up/CKg3tF4vLFSnbMHy8DdlNwqThuBlt6h9RPdjP9LD4yGY6+6eWb5ceVTkMLHH6dEj7+dec/7qQ0IxsFbHjFcwpuZVoApgsgRt6WPLWM0g/Ibm6uVGlaqoGKJNO+tuUEP5wLccTkbtg6UL8LxUHxfhpd0lBY0wBCIxvFOxSFBk3IbKfp538D1gqzFMA981TOM0wvhH83OieYEyLgOMCWB4L9K966OMipcOO6sE/qAKss1NedsgK/B976SyRsriPtgdx0ze9laoFY7kaQASxXua/63dWeKwf6xoCjiaflQxVA3SEDH6060OWxN38x1ObwXbSCf8swlg4E1oh0OMNIS2xvZukxaecPBeuDXe9uTJM5aDmabVcTVBmTCAr0MA/Jrw6vY8SAae5L24HTz5DUp6VGzlOkHv66TuMfi9TJ1VSTFuTve0tKTebIdy/G/dhDaeoIlNIYnZD2Sl/jrov06IQ0UOydh4iCKV3laZDBXXuWhhw+eRXPoJRPR50l3z1elytHfkqOUgAAACgFAAC1ZeaoQFoY+pVuEsjcYfnVvT5tNHAr9pxBV2mIm8GBZvPf9E63Um28A1GirfFZxC6aX/YPtGaPCS6ylNgJQeZ1preHGbmmd9MomKTYeOge9d7L83N+zXuk38cVR4XH2E7gMX0W5lgUPNsl0lIjSFmO8c0PsnDIuC815tDooe9LQ4tZfVtIGSRYdFUIVxE1PT83ZFpV3LTnxtMa2XM8Rx/dKzl3w1sH2sQIFfzBmGT0Nvyiz4B8GcWJqh1wjlloXdKSbi4/LVe4yTIqhsd8G7fUj5umDPvcxyhUWRdchOm2KJQyOl5x2wq75lM4hTcp6gGTEwfFrIyHO3TC/JA+Q77yvrX5BDH2cxuHbWUhYoRDRDgVNfEytmUb2cZ/pai7iymj7MYMqQnuxlU5S6+BYsWJL57r3eASF3Y9Gj6BHnnKnW8iH9nxdcHerP8rPfV55Nn7gAbH1TCiDUF9KXUt8G5LKzEWy1yGRxcd375Rf4Qf71nyn8vnto82DQZ2aFi9GZ/8In5LY2a1tAgc5BAUYpsR3baGAll4SipnIxx8mdJdaOV69MM+FHwr7MvFAo23JAOozUdiddLPrEZ59HWDO66ZnVl6wLw+HSuBiGdpanA1v7dusbQZ/8FcBgo7obLtThaaQvxjjaFTrWzuI6jR5qpz99iHB6dCnqHIgirt1012yeOjolvH07jXpAfRlqkX39CVRnX0n1h14QfYk31/bSdXfALQmUo/b4aR34D2rj0INhQyeYCWyMwpVszG9RiNdZ3iJ1FArlmCSJhE1Sx1sQSWNxX53QUM7ZWyhLQsJWrBFCJtJTGCIt2654SGKDwd64lcBUMci7hrxoHqeJgUAt/Cz/ZijGRNi2x+5IhE82xc1vrY9tHFvqC2DwW6gsKxw0xTm2fR86/4VmmhVhWWn7caFY+eT8wBPIpOS7cGg5lZj7yVwg3fAxdpBNiBuvSL9EAqoVRE7SX3zavu2DveLnRfZTl4LDJl3p57M4yAiVlvYvKRnteN3z/cL9dhNq45xybQITjrdMIzpVuoIfD2oWcEWImqgFpzk0ZATXfjedJfBKPoYrGItq5xt6Df1HU1o8HSqRUJWowcq4vFq1EZbjkUEkhR6jwbBNdb/2y2RxsKnynCHBEY6dTvIQkWYIz5KDb64CksKn73cjA+f88Sq0cmHW89BMt8ySaOnc/6qChm40Nz4vNErgmRHghGbJXVM7HHv+SqXfq0SwBoPIebeLKqX9w0lznkVUWU/Dc08q14eIoC32OUkhlcRmMaRyTJN3gcD4ujs2hCkBCTl6u3LB/58t4bQkdYNJECjw8POClCjq8Q27ElkHGxd6hfCk3yoZfSmKvNz42FYsUi8HytOafE5XXF2bqPY96m2mD8mVMP82OpPRGF1Hg1O4qHmML2iwXxfCy9nVyk9DhbW9p8X2TPdFNTt8EENIizzlw3ldXv/3/aWRUuYCNqPeZjsuuwXRvIW2l0tGonJMUFxBECuCCHZ7Ctg6K1iN/b3VjvPYuTuSOXAAJVYzl2NY3SoiWEDfhjxpNdqqcIaUAeCrceXSp+ivtHvXhlHU8WcKGS9sV2DLoEi4SEJ/4gnUbo+wyAlH2eonlUmZnXrarx/YH9ecD6zgIAa2QEU3SsYWJ6ewfQ5c6lNdCWKKsF2i2CozkmEzLYAbgygECV/w1bo1RrltC6tcE8Yjy8EcMyWzqaga/8jdsYsjimALw8ip9lnuA44V1gW/CMFm8lnoXAsh3vwCczo+P3US35J1QAAAAA');
