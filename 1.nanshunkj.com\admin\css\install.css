h1 { text-align: center; }

details summary { cursor: pointer; }

@keyframes fadein { from { opacity: 0; }
  to { opacity: 1; } }

.fresh .keep-word { display: none; }

.keep .fresh-word { display: none; }

form > .message { display: none; padding: 20px; border-radius: 5px; }

.message textarea { width: 100%; height: 200px; resize: none; margin: 10px 0; }

.message.fade { display: block; animation: fadein .5s linear; }

.message *:last-child { margin-bottom: 0; }

.message p { margin-top: 10px; }

.message p button { margin-left: 5px; }

.message p button:first-child { margin-left: 0; }
