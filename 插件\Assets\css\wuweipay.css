/**
 * 吴畏支付样式
 * 
 * @package WuweiPay
 * <AUTHOR>
 * @link https://8ww.fun
 */

/* 支付按钮 */
.wuweiPayBtn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #1e9fff;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
    font-size: 14px;
    margin: 10px 0;
}

.wuweiPayBtn:hover {
    background-color: #0d8aee;
}

/* 支付弹窗 */
.wuweiPayPopupLayer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: none;
}

.wuweiPayModal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 360px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

/* 支付弹窗头部 */
.wuweiPayHeader {
    position: relative;
    padding: 15px;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.wuweiPayTitle, .wuweiPayBack {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.wuweiPayBack {
    cursor: pointer;
}

.wuweiPayBack:before {
    content: "← ";
}

.wuweiPayClose {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 20px;
    height: 20px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.wuweiClose {
    display: inline-block;
    width: 20px;
    height: 20px;
    position: relative;
}

.wuweiClose:before, .wuweiClose:after {
    content: "";
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: #999;
    top: 9px;
    left: 0;
}

.wuweiClose:before {
    transform: rotate(45deg);
}

.wuweiClose:after {
    transform: rotate(-45deg);
}

/* 支付弹窗内容 */
.wuweiPayContent {
    padding: 20px;
}

/* 支付方式选项 */
.wuweiPaymentOptions {
    display: flex;
    justify-content: space-around;
}

.wuweiPayOption {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    width: 120px;
}

.wuweiPayOption:hover {
    border-color: #1e9fff;
    box-shadow: 0 0 10px rgba(30, 159, 255, 0.2);
}

.wuweiAlipay, .wuweiWechat {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-bottom: 10px;
}

.wuweiAlipay {
    background-image: url('../img/alipay.png');
}

.wuweiWechat {
    background-image: url('../img/wechat.png');
}

.payName {
    font-size: 14px;
    color: #666;
}

/* 加载动画 */
.wuweiGoogleLoad {
    width: 100px;
    height: 100px;
    margin: 20px auto;
    position: relative;
}

.wuweiGoogleLoad .shape {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.wuweiGoogleLoad .shape1 {
    background-color: #4285F4;
    top: 0;
    left: 40px;
    animation: bounce 1s infinite;
}

.wuweiGoogleLoad .shape2 {
    background-color: #34A853;
    top: 40px;
    right: 0;
    animation: bounce 1s infinite 0.2s;
}

.wuweiGoogleLoad .shape3 {
    background-color: #FBBC05;
    bottom: 0;
    left: 40px;
    animation: bounce 1s infinite 0.4s;
}

.wuweiGoogleLoad .shape4 {
    background-color: #EA4335;
    top: 40px;
    left: 0;
    animation: bounce 1s infinite 0.6s;
}

@keyframes bounce {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.5);
    }
}

/* 二维码 */
.wuweiPayQrcode {
    text-align: center;
    padding: 10px;
}

.wuweiQrcode {
    display: inline-block;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.wuweiQrcode img {
    width: 200px;
    height: 200px;
    display: block;
    margin: 0 auto;
}

.wuweiQrcode p {
    margin: 10px 0 0;
    color: #666;
    font-size: 14px;
}

.wuweiQrcode p:last-child {
    font-weight: bold;
    color: #f60;
    font-size: 16px;
}

/* 支付成功遮罩 */
.wuweiOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
}

.wuweiOverlay .content {
    text-align: center;
    color: #34A853;
    font-size: 16px;
    font-weight: bold;
}

.wuweiPaySuccess {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-image: url('../img/success.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-bottom: 10px;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .wuweiPayModal {
        width: 90%;
    }
    
    .wuweiPaymentOptions {
        flex-direction: column;
        align-items: center;
    }
    
    .wuweiPayOption {
        margin-bottom: 10px;
        width: 80%;
    }
    
    .wuweiQrcode img {
        width: 180px;
        height: 180px;
    }
}
