import{_ as i,o as t,c as s,F as l,l as p,a as o,m as n,t as m,Q as d}from"./index-50feddeb.js";import{e as u}from"./emojiBase64-09fa9685.js";const f={class:"emoji_wrap",style:{"margin-top":"10px"}},g=["title","onClick"],k=["src"],h={__name:"Emoji",setup(v,{emit:_}){const c=a=>{_("add",a)};return(a,x)=>(t(),s("div",f,[(t(!0),s(l,null,p(n(d),(e,r)=>(t(),s("a",{key:r,title:e,onClick:y=>c(e)},[o("img",{src:"data:image/png;base64,"+n(u)[e],alt:""},null,8,k),o("p",null,m(e),1)],8,g))),128))]))}},B=i(h,[["__scopeId","data-v-7733f39f"]]);export{B as default};
