<?php if (!defined("__TYPECHO_ROOT_DIR__")) {
  exit();
} ?>

<head>

    <meta charset="UTF-8"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge">


    <?php if ($this->is("index")): ?>
        <meta property="og:type" content="blog"/>
        <meta property="og:url" content="<?php $this->options->siteUrl(); ?>"/>
        <meta property="og:title" content="<?php $this->options->title(); ?>"/>
        <meta property="og:author" content="<?php $this->author->name(); ?>"/>
        <meta name="keywords" content="<?php $this->keywords(); ?>">
        <meta name="description" content="<?php $this->options->description(); ?>">
    <?php endif; ?>
    <?php if ($this->is("post") || $this->is("page") || $this->is("attachment")): ?>
        <meta property="og:url" content="<?php $this->permalink(); ?>"/>
        <meta property="og:title" content="<?php $this->title(); ?> - <?php $this->options->title(); ?>"/>
        <meta property="og:author" content="<?php $this->author(); ?>"/>
        <meta property="og:type" content="article"/>
        <meta property="article:published_time" content="<?php $this->date("c"); ?>"/>
        <meta property="article:published_first"
              content="<?php $this->options->title(); ?>, <?php $this->permalink(); ?>"/>
        <meta name="keywords" content="<?php
        $k = $this->fields->keyword;
        if (empty($k)) {
          echo $this->keywords();
        } else {
          echo $k;
        }
        ?>">
        <meta name="description" content="<?php
        $d = $this->fields->description;
        if (empty($d) || !$this->is("single")) {
          if ($this->getDescription()) {
            echo $this->getDescription();
          }
        } else {
          echo $d;
        }
        ?>"/>
    <?php endif; ?>
    <title><?php
    $this->archiveTitle(
      [
        "category" => _t("分类 %s 下的文章"),
        "search" => _t("包含关键字 %s 的文章"),
        "tag" => _t("标签 %s 下的文章"),
        "author" => _t("%s 发布的文章"),
      ],
      "",
      " - "
    );
    $this->options->title();
    ?></title>
    <?php $this->header("description=&generator=&pingback=&template=&xmlrpc=&wlw=&commentReply=&keywords="); ?>
    <link rel="dns-prefetch" href="https://npm.elemecdn.com" />
    <link rel="stylesheet" href="<?php $this->options->themeUrl('assets/css/announcement.css'); ?>">
    <style>
      <?php if (getOptions()->themeColor == "1"): ?>
        :root {
          --primary-bg: #a6c4c2;
          --link-color: #346193;
          --link-hover-color: #1e3a5e;
        }
        <?php elseif (getOptions()->themeColor == "2"): ?>
          :root{
            --primary-bg: #feae51;
            --link-color: #346193;
            --link-hover-color: #1e3a5e;
          }
          <?php elseif (getOptions()->themeColor == "3"): ?>
          :root{
            --primary-bg: #a2c6e1;
            --link-color: #346193;
            --link-hover-color: #1e3a5e;
          }
          <?php elseif (getOptions()->themeColor == "4"): ?>
          :root{
            --primary-bg: rgb(239 68 68);
            --link-color: #346193;
            --link-hover-color: #1e3a5e;
          }
      <?php else: ?>
        :root {
          --primary-bg: #000;
          --link-color: #346193;
          --link-hover-color: #1e3a5e;
        }
      <?php endif; ?>

      /* 基础文本样式 */
      body {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
          text-rendering: optimizeLegibility;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
      }

      /* 文章卡片基础样式 */
      .post-item {
          position: relative !important;
          border: 1px solid rgba(0,0,0,0.06) !important;
          border-radius: 12px !important;
          background: #fff !important;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
          height: 120px !important;
          display: flex !important;
          align-items: center !important;
          overflow: visible !important;
      }

      /* 文章卡片内容容器 */
      .post-item > div {
          padding: 1rem !important;
          width: 100% !important;
          display: flex !important;
          align-items: center !important;
          gap: 1rem !important;
      }

      /* 深色模式 */
      .dark .post-item {
          background: #161829 !important;
          border-color: rgba(255,255,255,0.1) !important;
      }
    </style>
    <link type="text/css" rel="stylesheet" href="<?php $this->options->themeUrl("assets/dist/style.css?v=" . getThemeVersion()); ?>"/>
    <link rel="shoucut icon" href="<?php echo getOptionValueOrDefault("icon", $this->options->siteUrl . "favicon.ico"); ?>">
    <script async src="https://npm.elemecdn.com/iconify-icon@1.0.7/dist/iconify-icon.min.js"></script>
    <script src="<?php $this->options->themeUrl("/assets/dist/jasmine.iife.js?v=" . getThemeVersion()); ?>"></script>
    <script src="<?php $this->options->themeUrl('assets/js/announcement.js'); ?>"></script>
    <script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?bf5631f78d96ceabd7aaa0d5c1b9c869";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
</head>

<?php
// 在 header 中设置全局的登录注册链接
$loginUrl = $this->options->loginUrl;
$registerUrl = $this->options->registerUrl;

if (isset($_SERVER['REQUEST_URI'])) {
    $referer = Helper::options()->siteUrl . ltrim($_SERVER['REQUEST_URI'], '/');
    $loginUrl .= '?referer=' . urlencode($referer);
    $registerUrl .= '?referer=' . urlencode($referer);
}

// 将URL保存到全局变量中，供模板使用
$this->loginUrl = $loginUrl;
$this->registerUrl = $registerUrl;
?>

<div class="announcement-slide">
    <div class="announcement-content">
        <?php $this->options->announcement(); ?>
    </div>
    <div class="announcement-close">×</div>
</div>