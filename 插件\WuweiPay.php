<?php
/**
 * 吴畏支付(WuweiPay) - Typecho博客支付整合插件，支持吴畏支付接口
 *
 * @package WuweiPay
 * <AUTHOR>
 * @version 1.0.0
 * @link https://8ww.fun
 */
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

class WuweiPay_Plugin implements Typecho_Plugin_Interface
{
    /**
     * 激活插件方法,如果激活失败,直接抛出异常
     *
     * @access public
     * @return void
     * @throws Typecho_Plugin_Exception
     */
    public static function activate()
    {
        // 创建数据表
        self::createTables();

        // 添加路由
        Helper::addRoute('wuweipay_notify', '/wuweipay/notify', 'WuweiPay_Plugin', 'notify');
        Helper::addRoute('wuweipay_return', '/wuweipay/return', 'WuweiPay_Plugin', 'returnUrl');
        Helper::addRoute('wuweipay_check', '/wuweipay/check', '<PERSON><PERSON>Pay_Plugin', 'checkOrder');
        Helper::addRoute('wuweipay_ajax', '/wuweipay/ajax', 'WuweiPay_Plugin', 'ajaxHandler');

        // 添加面板入口
        Helper::addPanel(1, 'WuweiPay/manage-payments.php', '吴畏支付', '管理支付记录', 'administrator');

        // 添加样式和脚本
        Helper::addScript('wuweipay.js', 'WuweiPay/Assets/js/wuweipay.all.js');
        Helper::addStyleSheet('wuweipay.css', 'WuweiPay/Assets/css/wuweipay.css');

        return _t('插件已经激活，请前往设置页面配置吴畏支付信息');
    }

    /**
     * 禁用插件方法,如果禁用失败,直接抛出异常
     *
     * @static
     * @access public
     * @return void
     * @throws Typecho_Plugin_Exception
     */
    public static function deactivate()
    {
        // 删除路由
        Helper::removeRoute('wuweipay_notify');
        Helper::removeRoute('wuweipay_return');
        Helper::removeRoute('wuweipay_check');
        Helper::removeRoute('wuweipay_ajax');

        // 删除面板入口
        Helper::removePanel(1, 'WuweiPay/manage-payments.php');

        // 删除样式和脚本
        Helper::removeScript('wuweipay.js');
        Helper::removeStyleSheet('wuweipay.css');

        return _t('插件已被禁用');
    }

    /**
     * 获取插件配置面板
     *
     * @access public
     * @param Typecho_Widget_Helper_Form $form 配置面板
     * @return void
     */
    public static function config(Typecho_Widget_Helper_Form $form)
    {
        // 支付设置
        $wuweipay_appid = new Typecho_Widget_Helper_Form_Element_Text(
            'wuweipay_appid',
            NULL,
            '',
            _t('吴畏支付AppID'),
            _t('请输入吴畏支付平台申请的AppID')
        );
        $form->addInput($wuweipay_appid);

        $wuweipay_key = new Typecho_Widget_Helper_Form_Element_Text(
            'wuweipay_key',
            NULL,
            '',
            _t('吴畏支付密钥'),
            _t('请输入吴畏支付平台申请的密钥')
        );
        $form->addInput($wuweipay_key);

        $wuweipay_gateway = new Typecho_Widget_Helper_Form_Element_Text(
            'wuweipay_gateway',
            NULL,
            'https://8ww.fun/api/pay',
            _t('吴畏支付网关'),
            _t('吴畏支付接口网关地址，默认为https://8ww.fun/api/pay')
        );
        $form->addInput($wuweipay_gateway);

        // 支付方式设置
        $payment_methods = new Typecho_Widget_Helper_Form_Element_Checkbox(
            'payment_methods',
            array(
                'alipay' => _t('支付宝'),
                'wechat' => _t('微信支付'),
            ),
            array('alipay', 'wechat'),
            _t('启用的支付方式'),
            _t('选择要启用的支付方式')
        );
        $form->addInput($payment_methods);
    }

    /**
     * 个人用户的配置面板
     *
     * @access public
     * @param Typecho_Widget_Helper_Form $form
     * @return void
     */
    public static function personalConfig(Typecho_Widget_Helper_Form $form)
    {
    }

    /**
     * 创建数据表
     *
     * @access private
     * @return void
     */
    private static function createTables()
    {
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();

        // 创建订单表
        if (!$db->fetchRow($db->query("SHOW TABLES LIKE '{$prefix}wuweipay_orders'"))) {
            $db->query("CREATE TABLE `{$prefix}wuweipay_orders` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `order_id` varchar(64) NOT NULL COMMENT '订单号',
                `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
                `amount` decimal(10,2) NOT NULL COMMENT '金额',
                `title` varchar(255) NOT NULL COMMENT '订单标题',
                `type` varchar(32) NOT NULL COMMENT '订单类型',
                `payment_method` varchar(32) NOT NULL COMMENT '支付方式',
                `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0未支付，1已支付',
                `created_time` int(11) NOT NULL COMMENT '创建时间',
                `paid_time` int(11) DEFAULT NULL COMMENT '支付时间',
                `extra` text COMMENT '额外数据',
                PRIMARY KEY (`id`),
                UNIQUE KEY `order_id` (`order_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='吴畏支付订单表';");
        }
    }

    /**
     * 支付通知处理
     */
    public static function notify()
    {
        // 处理支付通知
        require_once 'Includes/wuweiPay.Payment.php';
        $payment = new WuweiPayment();
        $payment->handleNotify();
    }

    /**
     * 支付返回处理
     */
    public static function returnUrl()
    {
        // 处理支付返回
        require_once 'Includes/wuweiPay.Payment.php';
        $payment = new WuweiPayment();
        $payment->handleReturn();
    }

    /**
     * 检查订单状态
     */
    public static function checkOrder()
    {
        // 检查订单状态
        require_once 'Includes/wuweiPay.Payment.php';
        $payment = new WuweiPayment();
        $payment->checkOrderStatus();
    }

    /**
     * Ajax处理
     */
    public static function ajaxHandler()
    {
        $action = isset($_POST['action']) ? $_POST['action'] : '';

        require_once 'Includes/wuweiPay.Payment.php';
        $payment = new WuweiPayment();

        switch ($action) {
            case 'wuwei_buy_page':
                // 获取购买页面信息
                $postId = isset($_POST['postID']) ? intval($_POST['postID']) : 0;
                $payType = isset($_POST['payType']) ? $_POST['payType'] : 'general';

                // 获取文章信息
                $db = Typecho_Db::get();
                $post = $db->fetchRow($db->select()->from('table.contents')->where('cid = ?', $postId));

                $title = $post ? $post['title'] : '支付订单';
                $amount = 0;

                // 根据支付类型设置金额
                switch ($payType) {
                    case 'vip':
                        $amount = 99.00; // VIP会员价格
                        $title = 'VIP会员';
                        break;
                    case 'donation':
                        $amount = 10.00; // 默认打赏金额
                        $title = '打赏: ' . $title;
                        break;
                    default:
                        $amount = 5.00; // 默认商品价格
                        $title = '购买: ' . $title;
                        break;
                }

                // 创建支付
                $payInfo = $payment->createPayment($amount, $title, $payType, Typecho_Widget::widget('Widget_User')->uid);

                // 添加界面文本
                $payInfo['title1'] = '请选择支付方式';
                $payInfo['title2'] = '正在获取支付信息...';
                $payInfo['title3'] = '请扫码支付';
                $payInfo['close'] = '关闭';

                header('Content-Type: application/json');
                echo json_encode($payInfo);
                exit;

            case 'wuwei_pay_page':
                // 发起支付
                $orderId = isset($_POST['postID']) ? $_POST['postID'] : '';
                $payMethod = isset($_POST['payMethod']) ? $_POST['payMethod'] : '';

                $result = $payment->doPay($orderId, $payMethod);

                header('Content-Type: application/json');
                echo json_encode($result);
                exit;

            case 'wuwei_status_page':
                // 检查订单状态
                $payment->checkOrderStatus();
                exit;
        }

        header('Content-Type: application/json');
        echo json_encode(['code' => 1, 'msg' => '未知操作']);
        exit;
    }

    /**
     * 显示支付按钮
     *
     * @param float $amount 金额
     * @param string $title 标题
     * @param string $type 类型
     * @param array $attributes 额外属性
     * @return string 按钮HTML
     */
    public static function payButton($amount = 0, $title = '', $type = 'general', $attributes = [])
    {
        // 检查插件是否已配置
        $options = Helper::options()->plugin('WuweiPay');
        if (empty($options->wuweipay_appid) || empty($options->wuweipay_key)) {
            return '<div class="wuweiPayError">支付功能未配置</div>';
        }

        // 生成订单ID
        $orderId = uniqid('WP');

        // 设置默认值
        if (empty($title)) {
            $title = '支付订单';
        }

        if ($amount <= 0) {
            switch ($type) {
                case 'vip':
                    $amount = 99.00;
                    break;
                case 'donation':
                    $amount = 10.00;
                    break;
                default:
                    $amount = 5.00;
                    break;
            }
        }

        // 构建按钮属性
        $attrs = '';
        foreach ($attributes as $key => $value) {
            $attrs .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
        }

        // 构建按钮HTML
        $html = '<button class="wuweiPayBtn" data-id="' . $orderId . '" data-type="' . $type . '"' . $attrs . '>';

        switch ($type) {
            case 'vip':
                $html .= '开通VIP会员 ￥' . number_format($amount, 2);
                break;
            case 'donation':
                $html .= '打赏 ￥' . number_format($amount, 2);
                break;
            default:
                $html .= '立即购买 ￥' . number_format($amount, 2);
                break;
        }

        $html .= '</button>';

        // 添加JavaScript变量
        $html .= '<script>var wuwei = {ajaxurl: "' . Helper::options()->siteUrl . 'wuweipay/ajax"};</script>';

        return $html;
    }
}
