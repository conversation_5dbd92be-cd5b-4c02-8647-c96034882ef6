<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 375 812">
  <defs>
    <style>
      .cls-1 {
        clip-path: url(#clip-Registration_Screen);
      }

      .cls-2, .cls-4 {
        fill: #fff;
      }

      .cls-3 {
        fill: url(#linear-gradient);
      }

      .cls-4 {
        font-size: 11px;
        letter-spacing: 0.2em;
      }

      .cls-12, .cls-4 {
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
      }

      .cls-5, .cls-7 {
        fill: none;
        stroke-width: 3px;
      }

      .cls-5 {
        stroke: #7b52ab;
      }

      .cls-10, .cls-6, .cls-8 {
        fill: #222;
        font-family: Montserrat-Regular, Montserrat;
      }

      .cls-12, .cls-6, .cls-8 {
        font-size: 18px;
      }

      .cls-7 {
        stroke: #e5e5e5;
      }

      .cls-8 {
        opacity: 0.5;
      }

      .cls-9 {
        fill: #e5e5e5;
        opacity: 0.3;
      }

      .cls-10 {
        font-size: 14px;
        opacity: 0.7;
      }

      .cls-11 {
        fill: url(#linear-gradient-2);
      }

      .cls-12, .cls-13 {
        fill: #522b83;
      }

      .cls-14 {
        filter: url(#Rectangle_1);
      }
    </style>
    <linearGradient id="linear-gradient" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#7b52ab"/>
      <stop offset="1" stop-color="#522b83"/>
    </linearGradient>
    <filter id="Rectangle_1" x="31" y="486" width="313" height="68" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#522b83" flood-opacity="0.349"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f1e7fc"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <clipPath id="clip-Registration_Screen">
      <rect width="375" height="812"/>
    </clipPath>
  </defs>
  <g id="Registration_Screen" data-name="Registration Screen" class="cls-1">
    <rect class="cls-2" width="375" height="812"/>
    <rect id="Screen_Background" data-name="Screen Background" class="cls-2" width="375" height="812"/>
    <g id="Create_Account_Button" data-name="Create Account Button" transform="translate(-67 -240)">
      <g class="cls-14" transform="matrix(1, 0, 0, 1, 67, 240)">
        <rect id="Rectangle_1-2" data-name="Rectangle 1" class="cls-3" width="295" height="50" rx="25" transform="translate(40 492)"/>
      </g>
      <text id="CREATE_ACCOUNT" data-name="CREATE ACCOUNT" class="cls-4" transform="translate(255 762)"><tspan x="-67.078" y="0">CREATE ACCOUNT</tspan></text>
    </g>
    <g id="Name_Activ" data-name="Name Activ" transform="translate(-0.5 -49)">
      <line id="Line" class="cls-5" x2="295" transform="translate(40.5 310.5)"/>
      <text id="Stanislav" class="cls-6" transform="translate(60 269)"><tspan x="88.067" y="17">Stanislav</tspan></text>
    </g>
    <g id="E-mail_Input" data-name="E-mail Input" transform="translate(-0.5 -49)">
      <line id="Line-2" data-name="Line" class="cls-7" x2="295" transform="translate(40.5 400.5)"/>
      <text id="E-mail" class="cls-8" transform="translate(60 359)"><tspan x="99.02" y="17">E-mail</tspan></text>
    </g>
    <g id="Phone_Input" data-name="Phone Input" transform="translate(-0.5 -49)">
      <line id="Line-3" data-name="Line" class="cls-7" x2="295" transform="translate(40.5 500.5)"/>
      <text id="Phone" class="cls-8" transform="translate(60 459)"><tspan x="98.237" y="17">Phone</tspan></text>
    </g>
    <g id="Note" transform="translate(-1 180)">
      <path id="Union_1" data-name="Union 1" class="cls-9" d="M-395,91.7a10,10,0,0,1-10-10v-62a10,10,0,0,1,10-10h17.481V0l9.7,9.7H-120a10,10,0,0,1,10,10v62a10,10,0,0,1-10,10Z" transform="translate(446 500.303)"/>
      <text id="All_fields_are_required._Enter_valid_information." data-name="All fields are required. Enter valid information." class="cls-10" transform="translate(77 528)"><tspan x="13.585" y="14">All fields are required. Enter </tspan><tspan x="49.376" y="40">valid information.</tspan></text>
    </g>
    <g id="Header">
      <path id="Background_Color" data-name="Background Color" class="cls-11" d="M0,0H375V70H0Z"/>
      <text id="Registration" class="cls-12" transform="translate(60 24)"><tspan x="70.967" y="17">Registration</tspan></text>
      <path id="Left_Arrow" data-name="Left Arrow" class="cls-13" d="M22.429,12.286H7.343l3.63-3.63a1.49,1.49,0,0,0,.456-1.084A1.59,1.59,0,0,0,9.857,6a1.482,1.482,0,0,0-1.084.456L2.52,12.708A1.506,1.506,0,0,0,2,13.857a1.425,1.425,0,0,0,.508,1.137l6.265,6.265a1.482,1.482,0,0,0,1.084.456,1.589,1.589,0,0,0,1.571-1.571,1.49,1.49,0,0,0-.456-1.084l-3.63-3.63H22.429a1.571,1.571,0,0,0,0-3.143Z" transform="translate(38 21)"/>
    </g>
  </g>
</svg>
