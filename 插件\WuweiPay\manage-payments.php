<?php
include 'common.php';
include 'header.php';
include 'menu.php';

// 检查权限
$user = Typecho_Widget::widget('Widget_User');
if (!$user->pass('administrator')) {
    throw new Typecho_Widget_Exception(_t('您没有权限访问此页面'), 403);
}

// 获取数据库对象
$db = Typecho_Db::get();
$prefix = $db->getPrefix();

// 处理分页
$pageSize = 20;
$currentPage = isset($_GET['page']) ? intval($_GET['page']) : 1;
if ($currentPage < 1) {
    $currentPage = 1;
}
$offset = ($currentPage - 1) * $pageSize;

// 获取订单总数
$totalOrders = $db->fetchObject($db->select(array('COUNT(id)' => 'num'))
    ->from('table.wuweipay_orders'))->num;

// 计算总页数
$totalPages = ceil($totalOrders / $pageSize);

// 获取订单列表
$orders = $db->fetchAll($db->select()
    ->from('table.wuweipay_orders')
    ->order('id', Typecho_Db::SORT_DESC)
    ->page($currentPage, $pageSize));
?>

<div class="main">
    <div class="body container">
        <div class="typecho-page-title">
            <h2><?php _e('吴畏支付 - 订单管理'); ?></h2>
        </div>
        <div class="row typecho-page-main" role="main">
            <div class="col-mb-12 typecho-list">
                <div class="typecho-list-operate clearfix">
                    <form method="get">
                        <div class="operate">
                            <label><i class="sr-only"><?php _e('全选'); ?></i><input type="checkbox" class="typecho-table-select-all" /></label>
                            <div class="btn-group btn-drop">
                                <button class="btn dropdown-toggle btn-s" type="button"><i class="sr-only"><?php _e('操作'); ?></i><?php _e('选中项'); ?> <i class="i-caret-down"></i></button>
                                <ul class="dropdown-menu">
                                    <li><a href="<?php $options->index('/action/wuweipay?do=delete'); ?>"><?php _e('删除'); ?></a></li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>

                <form method="post" name="manage_orders" class="operate-form">
                    <div class="typecho-table-wrap">
                        <table class="typecho-list-table">
                            <colgroup>
                                <col width="20"/>
                                <col width="60"/>
                                <col width="150"/>
                                <col width="80"/>
                                <col width="80"/>
                                <col width="150"/>
                                <col width="80"/>
                                <col width="150"/>
                                <col width="150"/>
                            </colgroup>
                            <thead>
                                <tr>
                                    <th> </th>
                                    <th><?php _e('ID'); ?></th>
                                    <th><?php _e('订单号'); ?></th>
                                    <th><?php _e('金额'); ?></th>
                                    <th><?php _e('用户ID'); ?></th>
                                    <th><?php _e('订单标题'); ?></th>
                                    <th><?php _e('状态'); ?></th>
                                    <th><?php _e('创建时间'); ?></th>
                                    <th><?php _e('支付时间'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($orders)): ?>
                                <?php foreach ($orders as $order): ?>
                                <tr id="order-<?php echo $order['id']; ?>">
                                    <td><input type="checkbox" value="<?php echo $order['id']; ?>" name="id[]"/></td>
                                    <td><?php echo $order['id']; ?></td>
                                    <td><?php echo $order['order_id']; ?></td>
                                    <td><?php echo sprintf('￥%.2f', $order['amount']); ?></td>
                                    <td><?php echo $order['user_id'] ? $order['user_id'] : '-'; ?></td>
                                    <td><?php echo $order['title']; ?></td>
                                    <td>
                                        <?php if ($order['status'] == 1): ?>
                                        <span class="text-success"><?php _e('已支付'); ?></span>
                                        <?php else: ?>
                                        <span class="text-warning"><?php _e('未支付'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i:s', $order['created_time']); ?></td>
                                    <td>
                                        <?php if ($order['paid_time']): ?>
                                        <?php echo date('Y-m-d H:i:s', $order['paid_time']); ?>
                                        <?php else: ?>
                                        -
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="9"><h6 class="typecho-list-table-title"><?php _e('没有任何订单'); ?></h6></td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </form>

                <?php if ($totalPages > 1): ?>
                <div class="typecho-pager">
                    <div class="typecho-pager-content">
                        <ul>
                            <?php if ($currentPage > 1): ?>
                            <li class="prev"><a href="?page=<?php echo $currentPage - 1; ?>"><?php _e('上一页'); ?></a></li>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <?php if ($i == $currentPage): ?>
                            <li class="current"><a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a></li>
                            <?php else: ?>
                            <li><a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a></li>
                            <?php endif; ?>
                            <?php endfor; ?>
                            
                            <?php if ($currentPage < $totalPages): ?>
                            <li class="next"><a href="?page=<?php echo $currentPage + 1; ?>"><?php _e('下一页'); ?></a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
include 'footer.php';
?>
