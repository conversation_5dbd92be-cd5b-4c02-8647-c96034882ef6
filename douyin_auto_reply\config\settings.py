#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import json
import configparser
from pathlib import Path

class Settings:
    """配置管理类"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent
        self.config_file = self.config_dir / "app_config.ini"
        self.replies_file = self.config_dir / "replies.json"
        
        # 默认配置
        self.default_config = {
            'app': {
                'window_title': '抖音聊天自动回复工具',
                'check_interval': '2',  # 检查新消息间隔(秒)
                'auto_start': 'false',
                'debug_mode': 'true'
            },
            'douyin': {
                'window_name': '抖音聊天',
                'message_area_x': '0',
                'message_area_y': '0', 
                'message_area_width': '800',
                'message_area_height': '600'
            },
            'ocr': {
                'tesseract_path': '',
                'language': 'chi_sim+eng',
                'confidence_threshold': '60'
            },
            'reply': {
                'enable_auto_reply': 'true',
                'reply_delay_min': '1',  # 最小回复延迟(秒)
                'reply_delay_max': '3',  # 最大回复延迟(秒)
                'max_replies_per_hour': '50'
            }
        }
        
        # 加载配置
        self.load_config()
        self.load_replies()
    
    def load_config(self):
        """加载配置文件"""
        self.config = configparser.ConfigParser()
        
        if self.config_file.exists():
            self.config.read(self.config_file, encoding='utf-8')
        else:
            # 创建默认配置
            self.config.read_dict(self.default_config)
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def load_replies(self):
        """加载回复模板"""
        if self.replies_file.exists():
            with open(self.replies_file, 'r', encoding='utf-8') as f:
                self.replies = json.load(f)
        else:
            # 创建默认回复模板
            self.replies = {
                "keywords": {
                    "你好": ["你好！很高兴收到您的消息", "嗨！有什么可以帮助您的吗？"],
                    "咨询": ["感谢您的咨询，请详细说明您的需求", "我来为您详细解答"],
                    "价格": ["关于价格问题，请稍等，我马上为您查询"],
                    "购买": ["感谢您的购买意向，请告诉我具体需求"],
                    "再见": ["再见！期待下次为您服务", "拜拜！有问题随时联系我"]
                },
                "default_replies": [
                    "收到您的消息了，稍后回复您",
                    "您好，我已经看到您的消息了",
                    "感谢您的留言，正在处理中"
                ],
                "blacklist_keywords": [
                    "广告", "推广", "加群", "刷单"
                ]
            }
            self.save_replies()
    
    def save_replies(self):
        """保存回复模板"""
        with open(self.replies_file, 'w', encoding='utf-8') as f:
            json.dump(self.replies, f, ensure_ascii=False, indent=2)
    
    def get(self, section, option, fallback=None):
        """获取配置值"""
        return self.config.get(section, option, fallback=fallback)
    
    def getint(self, section, option, fallback=0):
        """获取整数配置值"""
        return self.config.getint(section, option, fallback=fallback)
    
    def getboolean(self, section, option, fallback=False):
        """获取布尔配置值"""
        return self.config.getboolean(section, option, fallback=fallback)
    
    def set(self, section, option, value):
        """设置配置值"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, option, str(value))
        self.save_config()

# 全局配置实例
settings = Settings() 