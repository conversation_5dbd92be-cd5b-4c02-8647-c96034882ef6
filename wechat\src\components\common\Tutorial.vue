<template>
  <div>
    如何使用微信对话生成器？
    - 设置状态栏
    在页面左侧菜单选择“状态栏”，可以根据不同的需求设置自定义的手机状态栏，包括手机的时间、手机网络和信号、手机电量、充电状态等
    - 设置型号
    在页面左侧菜单选择“型号”，微信对话生成器提供了安卓(华为、小米)、苹果(iPhoneX - iPhone14)等多种手机型号选择，根据需要选择相应的手机型号
    - 设置角色
    在页面左侧菜单选择“角色”，生成器提供了角色管理功能，您可以创建、编辑、删除角色，选择角色进行聊天对话
    - 对话聊天
    在页面左侧菜单选择“对话”，然后在“选择发送用户”区域选择要发送对话的用户，选择好用户后就可以发送聊天内容了。微信对话生成器提供了多种对话内容，包括文本表情、图片视频、微信转账、微信红包、语音消息、系统消息、系统时间等
    发送文本表情对话
    在对话类型处选择文本对话，然后在文本框输入你的对话内容，也可以点击表情插入表情，然后点击发送按钮，即可发送文本表情对话
    发送图片对话
    在对话类型处选择图片对话，然后选择图片上传，点击发送即可发送图片对话内容
    发送转账对话
    在对话类型处选择转账对话，输入转账金额和转账备注，点击发送即可发送转账对话内容
    发送红包对话
    在对话类型处选择红包对话，输入红包金额和红包备注，点击发送即可发送红包对话内容
    发送语音对话
    在对话类型处选择语音对话，然后输入语音时间，设置已读或未读，点击发送即可
    发送系统消息
    系统消息是微信对话中重要的组成部分，如加好友、打招呼、被删除拉黑等，在对话类型处选择系统消息对话，然后输入消息内容，例如：以上是打招呼内容，然后点击发送
    发送系统时间
    系统时间可以让人清楚消息发送的时间，在对话类型处选择系统时间对话，然后输入选择日期时间，点击发送即可发送系统时间
    - 预览对话内容效果
    在操作区域的右侧，既可实时的看到您添加的对话聊天内容，把鼠标移动到聊天内容区域，然后混动滚轮，既可滚翻查看聊天内容。您也可以拉动右侧的滑杆，往上是放大效果图，往下是缩小效果图，点击下面适应屏幕按钮可自动适应屏幕高度。
    - 生成对话截图和保存
    鼠标移动到右下角“相机”按钮，然后点击等待片刻即可生成聊天截图，然后点击右下角下载按钮，即可下载生成的图片
  </div>
</template>

<script>
export default {

}
</script>

<style>

</style>