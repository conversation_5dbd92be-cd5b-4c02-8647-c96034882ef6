<?php
/*
** 诗林Wordpress主题/插件开发框架
** <AUTHOR>
** @Uri https://shilin.studio
*/

 if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly.
/**
 *
 * Field: content
 *
 * <AUTHOR> Studio
 * @Uri https://shilin.studio
 *
 */
if ( ! class_exists( 'Shilin_Field_content' ) ) {
  class Shilin_Field_content extends Shilin_Fields {

    public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
      parent::__construct( $field, $value, $unique, $where, $parent );
    }

    public function render() {

      if ( ! empty( $this->field['content'] ) ) {

        echo $this->field['content'];

      }

    }

  }
}
