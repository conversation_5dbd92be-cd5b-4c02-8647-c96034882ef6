<?php
/**
 * 登录/注册页面
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 如果用户已登录，跳转到会员中心
if ($this->user->hasLogin()) {
    $this->response->redirect($this->options->siteUrl . 'member.html');
}

$rememberEmail = Typecho_Cookie::get('__typecho_remember_mail');
$rememberName = Typecho_Cookie::get('__typecho_remember_name');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录/注册 - <?php $this->options->title(); ?></title>
    <!-- 引入样式表和字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <?php $this->header(); ?>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #0e0e0e;
            color: #fff;
            overflow-x: hidden;
            line-height: 1.6;
            background-image: 
                radial-gradient(circle at 15% 50%, rgba(254, 44, 85, 0.1) 0%, transparent 25%),
                radial-gradient(circle at 85% 30%, rgba(129, 52, 175, 0.1) 0%, transparent 25%);
            min-height: 100vh;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 顶部导航栏 */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 5%;
            transition: all 0.3s ease;
        }
        
        .header.scrolled {
            height: 50px;
            background-color: rgba(0, 0, 0, 0.95);
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(to right, #FE2C55, #8134AF);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: 1px;
        }
        
        /* 主要内容区 */
        .main-container {
            margin-top: 80px;
            padding: 0 5%;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 80px);
        }
        
        /* 登录/注册容器 */
        .auth-container {
            background-color: rgba(25, 25, 25, 0.9);
            border-radius: 16px;
            width: 100%;
            max-width: 900px;
            display: flex;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
            margin: 40px 0;
        }
        
        /* 左侧信息区 */
        .auth-info {
            flex: 1;
            background: linear-gradient(135deg, #FE2C55 0%, #8134AF 100%);
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .auth-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8Y3JlYXRpdmUlMjBibGFja3xlbnwwfHwwfHw%3D&ixlib=rb-1.2.1&w=1000&q=80');
            background-size: cover;
            background-position: center;
            opacity: 0.2;
            z-index: 0;
        }
        
        .info-content {
            position: relative;
            z-index: 1;
        }
        
        .info-logo {
            font-size: 32px;
            margin-bottom: 20px;
            font-weight: bold;
            color: white;
        }
        
        .info-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 15px;
            color: white;
        }
        
        .info-description {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .info-features {
            margin-bottom: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: white;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .feature-icon i {
            font-size: 14px;
            color: white;
        }
        
        .back-home {
            display: inline-flex;
            align-items: center;
            color: white;
            font-weight: 500;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 10px 20px;
            border-radius: 30px;
            transition: all 0.3s;
        }
        
        .back-home i {
            margin-right: 8px;
        }
        
        .back-home:hover {
            background-color: rgba(0, 0, 0, 0.5);
            transform: translateY(-2px);
        }
        
        /* 右侧表单区 */
        .auth-form {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .form-tabs {
            display: flex;
            margin-bottom: 30px;
        }
        
        .form-tab {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            font-size: 16px;
            color: #aaa;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .form-tab.active {
            color: #FE2C55;
            border-bottom-color: #FE2C55;
        }
        
        .form-content {
            display: none;
        }
        
        .form-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #ccc;
            font-size: 14px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: #fff;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #FE2C55;
            box-shadow: 0 0 0 2px rgba(254, 44, 85, 0.2);
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            color: #ccc;
            font-size: 14px;
        }
        
        .remember-me input {
            margin-right: 8px;
        }
        
        .forget-password {
            color: #ccc;
            font-size: 14px;
            transition: color 0.3s;
        }
        
        .forget-password:hover {
            color: #FE2C55;
        }
        
        .form-submit {
            background: linear-gradient(to right, #FE2C55, #8134AF);
            color: white;
            border: none;
            padding: 12px 0;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .form-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(254, 44, 85, 0.3);
        }
        
        .social-login {
            text-align: center;
            margin-top: 20px;
        }
        
        .social-title {
            position: relative;
            margin-bottom: 20px;
            color: #ccc;
            font-size: 14px;
        }
        
        .social-title::before, .social-title::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30%;
            height: 1px;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .social-title::before {
            left: 0;
        }
        
        .social-title::after {
            right: 0;
        }
        
        .social-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .social-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            transition: all 0.3s;
        }
        
        .social-button.weibo {
            background-color: #E6162D;
        }
        
        .social-button.wechat {
            background-color: #09BB07;
        }
        
        .social-button.qq {
            background-color: #12B7F5;
        }
        
        .social-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
        }
        
        .error-msg {
            color: #ff4d4f;
            margin-bottom: 15px;
            font-size: 14px;
            display: none;
        }
        
        .error-msg.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .auth-container {
                flex-direction: column;
                max-width: 500px;
            }
            
            .auth-info {
                padding: 30px;
            }
            
            .info-features {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <a href="<?php $this->options->siteUrl(); ?>" class="logo"><?php $this->options->title(); ?></a>
    </header>
    
    <!-- 主要内容区 -->
    <div class="main-container">
        <div class="auth-container">
            <!-- 左侧信息区 -->
            <div class="auth-info">
                <div class="info-content">
                    <div class="info-logo"><?php $this->options->title(); ?></div>
                    <h1 class="info-title">欢迎加入我们</h1>
                    <p class="info-description">加入会员，享受更多精彩内容和专属服务。高质量的内容，尽在掌握。</p>
                    
                    <div class="info-features">
                        <div class="feature-item">
                            <div class="feature-icon"><i class="ri-check-line"></i></div>
                            <span>获取会员专享内容</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"><i class="ri-check-line"></i></div>
                            <span>高清无水印资源下载</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"><i class="ri-check-line"></i></div>
                            <span>优先获取最新更新</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"><i class="ri-check-line"></i></div>
                            <span>7×24小时技术支持</span>
                        </div>
                    </div>
                    
                    <a href="<?php $this->options->siteUrl(); ?>" class="back-home">
                        <i class="ri-home-line"></i> 返回首页
                    </a>
                </div>
            </div>
            
            <!-- 右侧表单区 -->
            <div class="auth-form">
                <div class="form-tabs">
                    <div class="form-tab active" data-tab="login">会员登录</div>
                    <div class="form-tab" data-tab="register">注册账号</div>
                </div>
                
                <div class="error-msg" id="error-msg"></div>
                
                <!-- 登录表单 -->
                <form class="form-content active" id="login-form" method="post" action="<?php $this->options->loginAction(); ?>">
                    <input type="hidden" name="referer" value="<?php echo htmlspecialchars($this->request->get('referer')); ?>">
                    
                    <div class="form-group">
                        <label for="name">用户名</label>
                        <input type="text" id="name" name="name" placeholder="请输入您的用户名" value="<?php echo $rememberName; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" placeholder="请输入您的密码" required>
                    </div>
                    
                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" name="remember" value="1" checked> 记住我
                        </label>
                        <a href="<?php $this->options->siteUrl(); ?>forgot.html" class="forget-password">忘记密码？</a>
                    </div>
                    
                    <button type="submit" class="form-submit">登 录</button>
                </form>
                
                <!-- 注册表单 -->
                <form class="form-content" id="register-form" method="post" action="<?php $this->options->registerAction(); ?>">
                    <div class="form-group">
                        <label for="register-name">用户名</label>
                        <input type="text" id="register-name" name="name" placeholder="请设置您的用户名（字母、数字、下划线）" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="register-mail">电子邮箱</label>
                        <input type="email" id="register-mail" name="mail" placeholder="请输入您的电子邮箱" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="register-password">密码</label>
                        <input type="password" id="register-password" name="password" placeholder="请设置您的密码（至少6位）" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm-password">确认密码</label>
                        <input type="password" id="confirm-password" name="confirm" placeholder="请再次输入您的密码" required>
                    </div>
                    
                    <button type="submit" class="form-submit">注 册</button>
                </form>
                
                <div class="social-login">
                    <div class="social-title">使用社交账号登录</div>
                    <div class="social-buttons">
                        <a href="#" class="social-button weibo"><i class="ri-weibo-fill"></i></a>
                        <a href="#" class="social-button wechat"><i class="ri-wechat-fill"></i></a>
                        <a href="#" class="social-button qq"><i class="ri-qq-fill"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 表单切换
            const tabs = document.querySelectorAll('.form-tab');
            const forms = document.querySelectorAll('.form-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const target = tab.getAttribute('data-tab');
                    
                    // 移除所有active类
                    tabs.forEach(t => t.classList.remove('active'));
                    forms.forEach(f => f.classList.remove('active'));
                    
                    // 添加active类到当前选中的tab和form
                    tab.classList.add('active');
                    document.getElementById(target + '-form').classList.add('active');
                });
            });
            
            // 滚动效果
            window.addEventListener('scroll', function() {
                const header = document.querySelector('.header');
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
            
            // 注册表单验证
            const registerForm = document.getElementById('register-form');
            
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    const password = document.getElementById('register-password').value;
                    const confirm = document.getElementById('confirm-password').value;
                    const errorMsg = document.getElementById('error-msg');
                    
                    if (password !== confirm) {
                        e.preventDefault();
                        errorMsg.innerText = '两次输入的密码不一致';
                        errorMsg.classList.add('active');
                    } else if (password.length < 6) {
                        e.preventDefault();
                        errorMsg.innerText = '密码长度至少为6位';
                        errorMsg.classList.add('active');
                    } else {
                        errorMsg.classList.remove('active');
                    }
                });
            }
            
            // 检查URL参数中是否有错误信息
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');
            
            if (error) {
                const errorMsg = document.getElementById('error-msg');
                errorMsg.innerText = decodeURIComponent(error);
                errorMsg.classList.add('active');
            }
        });
    </script>
    
    <?php $this->footer(); ?>
</body>
</html> 