/**
 * WW Style 主题会员中心JavaScript
 * 
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

(function() {
    "use strict";
    
    // DOM完全加载后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 导航菜单高亮
        highlightMenu();
        
        // 初始化个人资料保存
        initProfileSave();
        
        // 初始化密码修改
        initPasswordChange();
        
        // 初始化收藏移除
        initFavoriteRemove();
        
        // 初始化通知设置
        initNotificationSettings();
    });
    
    /**
     * 导航菜单高亮
     */
    function highlightMenu() {
        const currentSection = getQueryParam('section') || 'profile';
        const menuItems = document.querySelectorAll('.menu-item');
        
        menuItems.forEach(function(item) {
            const section = item.getAttribute('data-section');
            if (section === currentSection) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    /**
     * 初始化个人资料保存功能
     */
    function initProfileSave() {
        const saveProfileBtn = document.querySelector('.profile-form .btn-save');
        
        if (saveProfileBtn) {
            saveProfileBtn.addEventListener('click', function() {
                const nickname = document.getElementById('nickname').value;
                const bio = document.getElementById('bio').value;
                
                // 简单验证
                if (!nickname.trim()) {
                    showMessage('显示名称不能为空', 'error');
                    return;
                }
                
                // 显示加载状态
                saveProfileBtn.disabled = true;
                saveProfileBtn.innerHTML = '<i class="ri-loader-2-line spin"></i> 保存中...';
                
                // 发送AJAX请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', typechoConfig.siteUrl + 'index.php/action/profile-edit', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        saveProfileBtn.disabled = false;
                        saveProfileBtn.innerHTML = '保存修改';
                        
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.success) {
                                    showMessage('个人资料已更新', 'success');
                                } else {
                                    showMessage(response.message || '更新失败，请稍后再试', 'error');
                                }
                            } catch (e) {
                                showMessage('系统错误，请稍后再试', 'error');
                            }
                        } else {
                            showMessage('网络错误，请稍后再试', 'error');
                        }
                    }
                };
                xhr.send('nickname=' + encodeURIComponent(nickname) + '&bio=' + encodeURIComponent(bio));
            });
        }
    }
    
    /**
     * 初始化密码修改功能
     */
    function initPasswordChange() {
        const changePasswordBtn = document.querySelector('.password-form .btn-save');
        
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', function() {
                const oldPassword = document.getElementById('old_password').value;
                const newPassword = document.getElementById('new_password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                
                // 简单验证
                if (!oldPassword) {
                    showMessage('请输入旧密码', 'error');
                    return;
                }
                
                if (!newPassword) {
                    showMessage('请输入新密码', 'error');
                    return;
                }
                
                if (newPassword.length < 6) {
                    showMessage('新密码长度不能小于6位', 'error');
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    showMessage('两次输入的密码不一致', 'error');
                    return;
                }
                
                // 显示加载状态
                changePasswordBtn.disabled = true;
                changePasswordBtn.innerHTML = '<i class="ri-loader-2-line spin"></i> 处理中...';
                
                // 发送AJAX请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', typechoConfig.siteUrl + 'index.php/action/password-edit', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        changePasswordBtn.disabled = false;
                        changePasswordBtn.innerHTML = '修改密码';
                        
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.success) {
                                    showMessage('密码修改成功', 'success');
                                    // 清空表单
                                    document.getElementById('old_password').value = '';
                                    document.getElementById('new_password').value = '';
                                    document.getElementById('confirm_password').value = '';
                                } else {
                                    showMessage(response.message || '密码修改失败，请稍后再试', 'error');
                                }
                            } catch (e) {
                                showMessage('系统错误，请稍后再试', 'error');
                            }
                        } else {
                            showMessage('网络错误，请稍后再试', 'error');
                        }
                    }
                };
                xhr.send('old_password=' + encodeURIComponent(oldPassword) + '&new_password=' + encodeURIComponent(newPassword));
            });
        }
    }
    
    /**
     * 初始化收藏移除功能
     */
    function initFavoriteRemove() {
        const removeFavoriteButtons = document.querySelectorAll('.favorite-actions .remove-action');
        
        removeFavoriteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const cid = this.getAttribute('data-cid');
                const favoriteItem = this.closest('.favorite-item');
                
                if (!cid || !favoriteItem) {
                    return;
                }
                
                if (confirm('确定要取消收藏这篇文章吗？')) {
                    // 显示加载状态
                    button.innerHTML = '<i class="ri-loader-2-line spin"></i>';
                    button.disabled = true;
                    
                    // 发送AJAX请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', typechoConfig.siteUrl + 'index.php/action/favorite?cid=' + cid + '&unfavorite=1', true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4 && xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.success) {
                                    // 移除该收藏项
                                    favoriteItem.style.height = '0';
                                    favoriteItem.style.opacity = '0';
                                    favoriteItem.style.marginBottom = '0';
                                    setTimeout(function() {
                                        favoriteItem.remove();
                                        
                                        // 检查是否没有收藏了
                                        const favoritesList = document.querySelector('.favorites-list');
                                        if (favoritesList && !favoritesList.querySelector('.favorite-item')) {
                                            // 显示空收藏提示
                                            favoritesList.innerHTML = `
                                                <div class="empty-message">
                                                    <div class="empty-icon"><i class="ri-heart-line"></i></div>
                                                    <div class="empty-text">您还没有收藏任何内容</div>
                                                    <a href="${typechoConfig.siteUrl}" class="btn-primary">浏览内容</a>
                                                </div>
                                            `;
                                        }
                                    }, 300);
                                } else {
                                    showMessage(response.message || '操作失败，请稍后再试', 'error');
                                    button.innerHTML = '<i class="ri-delete-bin-line"></i> 取消收藏';
                                    button.disabled = false;
                                }
                            } catch (e) {
                                showMessage('系统错误，请稍后再试', 'error');
                                button.innerHTML = '<i class="ri-delete-bin-line"></i> 取消收藏';
                                button.disabled = false;
                            }
                        } else if (xhr.readyState === 4) {
                            showMessage('网络错误，请稍后再试', 'error');
                            button.innerHTML = '<i class="ri-delete-bin-line"></i> 取消收藏';
                            button.disabled = false;
                        }
                    };
                    xhr.send();
                }
            });
        });
    }
    
    /**
     * 初始化通知设置
     */
    function initNotificationSettings() {
        const notificationSettings = document.querySelectorAll('.notification-form input[type="checkbox"]');
        const saveSettingsBtn = document.querySelector('.notification-form .btn-save');
        
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', function() {
                // 收集设置
                const settings = {};
                notificationSettings.forEach(function(checkbox) {
                    settings[checkbox.name] = checkbox.checked ? 1 : 0;
                });
                
                // 显示加载状态
                saveSettingsBtn.disabled = true;
                saveSettingsBtn.innerHTML = '<i class="ri-loader-2-line spin"></i> 保存中...';
                
                // 发送AJAX请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', typechoConfig.siteUrl + 'index.php/action/settings-save', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        saveSettingsBtn.disabled = false;
                        saveSettingsBtn.innerHTML = '保存设置';
                        
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.success) {
                                    showMessage('设置已保存', 'success');
                                } else {
                                    showMessage(response.message || '保存失败，请稍后再试', 'error');
                                }
                            } catch (e) {
                                showMessage('系统错误，请稍后再试', 'error');
                            }
                        } else {
                            showMessage('网络错误，请稍后再试', 'error');
                        }
                    }
                };
                xhr.send(JSON.stringify(settings));
            });
        }
    }
    
    /**
     * 获取URL参数
     * @param {string} name 参数名
     * @returns {string|null} 参数值
     */
    function getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }
    
    /**
     * 显示消息提示
     * @param {string} message 消息内容
     * @param {string} type 消息类型（success/error）
     */
    function showMessage(message, type = 'success') {
        // 检查是否已存在消息容器
        let messageContainer = document.querySelector('.message-container');
        
        if (!messageContainer) {
            // 创建消息容器
            messageContainer = document.createElement('div');
            messageContainer.className = 'message-container';
            document.body.appendChild(messageContainer);
        }
        
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = 'message ' + type;
        messageElement.innerHTML = '<i class="ri-' + (type === 'success' ? 'check-line' : 'close-circle-line') + '"></i> ' + message;
        
        messageContainer.appendChild(messageElement);
        
        // 显示消息
        setTimeout(function() {
            messageElement.classList.add('show');
        }, 10);
        
        // 自动移除
        setTimeout(function() {
            messageElement.classList.remove('show');
            setTimeout(function() {
                messageElement.remove();
            }, 300);
        }, 3000);
    }
})(); 