-- 修复 system_settings 表缺失字段
-- 执行此脚本来添加缺失的 updated_by 和 updated_at 字段

-- 1. 添加 updated_by 字段（引用用户ID）
ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES auth.users(id);

-- 2. 添加 updated_at 字段（更新时间）
ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT now();

-- 3. 为现有记录设置默认值
UPDATE system_settings 
SET updated_at = now() 
WHERE updated_at IS NULL;

-- 4. 创建自动更新 updated_at 的触发器
CREATE OR REPLACE FUNCTION update_system_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. 创建触发器（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'update_system_settings_updated_at_trigger'
    ) THEN
        CREATE TRIGGER update_system_settings_updated_at_trigger
            BEFORE UPDATE ON system_settings
            FOR EACH ROW
            EXECUTE FUNCTION update_system_settings_updated_at();
    END IF;
END $$;

-- 6. 验证表结构
\d system_settings; 