# TeePay基础版 #
***** 为了区分开来，基础版名称为TeePay，专业版名称为TePay。  
***** 有少部分人反馈启用插件错误，请自己认真去检查一下：  
***** 1、插件名称更改为TeePay没有？  
***** 2、PHP 和 MySql 数据库的版本有没有问题？   
***** 3、自己配置是否有问题？   
***** 4、下载的版本是否有问题，目前最新免费版本1.5.4，本人亲测全流程都很流畅。   
***** 5、简单修复一点，最近快站二维码不稳定，更换了，见[《TeePay 生成二维码接口故障解决方法》](https://pangsuan.com/p/teepay-qrcode-fixed.html)。  
 

# 安全提示 #
此版本存在安全问题，不去修复了，因为有人用此版本存在的问题去恶意给人刷单，性质非常恶劣，甚至将使用者的服务器弄成了502错误，故提醒还想使用此版本的，参考实现逻辑，自己重新写一个。   
具体搞破坏的人就不公布了，请大家参考这里：https://www.shinenet.cn/archives/117.html  
还有几个其它的问题，也在这里说一下：https://pangsuan.com/p/teepay-bugs.html  


## Typecho 个人支付宝、微信收款插件 ##  

插件下载地址：https://github.com/mhcyong/TeePay.git  
插件介绍：https://pangsuan.com/p/teepay.html  
演示地址：https://pangsuan.com/p/show-how.html  
推荐个人微信收款平台：https://payjs.cn/ref/zgpnbd    

## TePay 专业版购买方式 ##
1：官网：https://pangsuan.com/p/tepass.html  
2：QQ：744272645（胖蒜网）  
3、微信：744272645（不常在线）  


## 效果演示 ##
1、仰泳の猪：https://zbqbk.cn/index.php/archives/128.html  
2、梁晓斌：https://www.liangxiaobin.com/child/99/  
3、猫先生的个人博客：https://www.mr-mao.cn/archives/gen2-thinksystem-server-install-2008r2.html  
4、憶の年：https://www.529i.com/archives/801.html   
5、kali博客：https://blog.bbskali.cn/index.php/archives/1504/  
6、木稚隐语： https://wbessy.com/Pay.html   
7、太阳源码： https://www.sunym.top/archives/5/     


## TePay 专业版（[TePay-Pro](https://store.pangsuan.com/p/tepay-pro.html)）更新记录 ##
2020-02-06：TePay-Pro 4.2.3 修改手机端微信付款方式，在微信内打开可直接付款，浏览器打开弹出二维码，可截屏到微信相册扫码付款。  
2020-02-01：按计划发布TePay-Pro 4.2.1，具体介绍请到官网。  
2020-01-18：调整付费记录页面到插件里面，减少用户更换主题的操作，同时增加了订单管理的页；付费记录页面地址为：http://your_domain/tepay/fees；订单管理页面地址为：http://your_domain/tepay/order；该功能目前只在TePay-Pro-4.0以后版本，2月1日后购买专业版用户都可以使用。   
2020-01-12：启用插件时创建一个付费记录的页面，方便用户查看自己的付费情况，访问地址格式为：http://your_domain/tepay.html，地址可以在独立页面修改，创建的时候会在当前主题生成一个page_tepay.php文件，换主题的时候记得复制过去。  
2020-01-07：发现iPad上不能唤醒支付宝app付款，更改iPad上的付款方式都为扫码付款。  
2019-12-17: 付款成功后付费阅读区域增加显示付款方式。  
2019-12-12：针对专业版独立页面也增加了收款设置，故在数据表中增加一个字段来区分，同时更新版本号为TePay-Pro-3.1.0更新此版本的老用户要注意一下。  
2019-12-11：移动端的个人收款之前因为一些原因只保留了支付宝唤醒APP付款，现在还是把微信二维码收款也加上吧。  
2019-12-10：听取用户的意见，给 [独立页面也增加了收款功能](https://pangsuan.com/p/tepay-for-page.html) ，好的建议我都会考虑的，专业版用户放心吧~~  
2019-12-07：完善管理后台更新文章付费，删除付费文章时的提醒通知。  
2019-12-02: 为了方便删除不需要的付费设置，在编辑文章页面和付费文章管理页面都增加了删除付费设置的操作。  
2019-11-29: 管理文章付费时，如果设置为免费，同时又不设置价格，就干脆从付费表中去掉此条记录（适用TePay-Pro-3.0.0以后的版本）。  
2019-11-25：因支付宝当面付不播报，故采用Server酱来推送支付成功消息到微信；为了保护我和专业版用户的利益，对少部分核心代码进行加密，不会影响二次修改，开发等等操作。  
2019-11-24：更新TePay-Pro到3.0.0版本，去掉了一些不需要的功能，也听取了已购买专业版用户的意见，增加一些功能，比如订单标题可以单独修改。  
2019-11-21：在订单表中增加文章标题的字段，方便直接从数据表查看，也减轻了数据查询的压力。  


## 注意事项 ##
1、支付宝支付注意是应用私钥，支付宝公钥。  
2、使用这个插件网站必须只根目录，二级目录无效，如果一定要在二级目录请自行修改引用文件路劲。  
3、只适用个人支付宝（需签约当面付收单，免费）和个人微信（需在 [payjs.cn](https://payjs.cn/ref/zgpnbd) 开通个人微商户，费用300元，找我拿优惠码可以便宜100元，QQ：744272645）。  
4、因违法被封，或服务器在国外不能收到支付宝回调通知的无法使用。


## TeePay 基础版修改记录 ##
2020-01-22：优化了一点点小细节，另外亲测可正常使用。  
2019-11-05：更改插件名称为TeePay，此插件不再进行大的功能变更，如有需要请了解专业版 [TePay-Pro](https://store.pangsuan.com/p/tepay-pro.html) 。     
2019-10-16: 将PC与手机端分开，手机端只保留支付宝支付，因为可以直接跳转到支付宝付款；微信在手机端没有找到好的方式，故先去掉。  
2019-10-15: 去掉付费内容里面Parsedown.php及相关代码，减少因主题问题带来的冲突。  
2019-09-27: 调整主站域名为 https://pangsuan.com ，微商户也改名为“胖蒜网”。  
2019-07-23：修改菜单位置为顶级菜单，增加一个付费记录页面。
