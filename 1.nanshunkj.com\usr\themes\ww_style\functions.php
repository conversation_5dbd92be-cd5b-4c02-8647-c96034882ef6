<?php
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// 引入订单系统
require_once 'orders.php';

/**
 * WW Style 主题函数
 *
 * @package WW Style
 * <AUTHOR> Style
 * @version 1.0.0
 */

/**
 * 主题初始化
 */
function themeInit($archive) {
    // 确保订单表存在
    createOrdersTable();

    // 注册文章自定义字段
    if ($archive->is('single')) {
        $archive->content = createContent($archive);
    }

    // 增加浏览量
    if ($archive->is('single') && !$archive->is('page')) {
        viewCounter($archive);
    }

    // 检查会员是否过期
    if (Typecho_Widget::widget('Widget_User')->hasLogin()) {
        checkMemberExpired(Typecho_Widget::widget('Widget_User')->uid);
    }
}

/**
 * 创建文章内容，处理自定义内容格式
 *
 * @param object $archive 文章对象
 * @return string 处理后的内容
 */
function createContent($archive) {
    $content = $archive->content;

    // 处理代码高亮
    $content = preg_replace('/<pre><code(.*?)>/i', '<pre class="line-numbers"><code$1>', $content);

    // 处理图片懒加载
    $content = preg_replace('/<img(.*?)src="(.*?)"(.*?)>/i', '<img$1data-src="$2" class="lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="$3>', $content);

    // 自定义解析内容，例如短代码
    $content = parseShortcode($content, $archive->cid);

    // 增加浏览量
    if ($archive->is('single') && !$archive->is('page')) {
        viewCounter($archive);
    }

    return $content;
}

/**
 * 解析短代码
 *
 * @param string $content 文章内容
 * @param int $cid 文章ID
 * @return string 解析后的内容
 */
function parseShortcode($content, $cid = 0) {
    // 解析会员内容短代码 [member]...[/member]
    $pattern = '/\[member\](.*?)\[\/member\]/s';
    if (preg_match_all($pattern, $content, $matches)) {
        foreach ($matches[0] as $key => $match) {
            if (getUserMemberStatus(Typecho_Widget::widget('Widget_User')->uid)) {
                // 会员可见内容
                $replace = '<div class="member-content-box">' . $matches[1][$key] . '</div>';
            } else {
                // 非会员提示
                $replace = '<div class="member-content-locked">
                    <div class="locked-icon"><i class="ri-lock-line"></i></div>
                    <div class="locked-title">会员专享内容</div>
                    <div class="locked-desc">开通会员即可查看</div>
                    <div class="locked-btn">
                        <a href="' . Helper::options()->siteUrl . 'index.php/payment.html" class="btn-primary">立即开通</a>
                    </div>
                </div>';
            }
            $content = str_replace($match, $replace, $content);
        }
    }

    // 解析付费内容短代码 [pay price="9.9"]...[/pay]
    $pattern = '/\[pay price="(.*?)"\](.*?)\[\/pay\]/s';
    if (preg_match_all($pattern, $content, $matches)) {
        foreach ($matches[0] as $key => $match) {
            $price = $matches[1][$key];
            $contentToPay = $matches[2][$key];

            // 检查用户是否已购买
            if (checkUserPurchased($cid, Typecho_Widget::widget('Widget_User')->uid)) {
                // 已购买，显示内容
                $replace = '<div class="pay-content-unlocked">' . $contentToPay . '</div>';
            } else {
                // 未购买，显示购买提示
                $replace = '<div class="pay-content-locked">
                    <div class="locked-icon"><i class="ri-shopping-cart-line"></i></div>
                    <div class="locked-title">付费内容</div>
                    <div class="locked-desc">付费即可查看完整内容</div>
                    <div class="locked-price">¥' . $price . '</div>
                    <div class="locked-btn">
                        <a href="' . Helper::options()->siteUrl . 'index.php/payment-process.html?cid=' . $cid . '&price=' . $price . '" class="btn-primary">立即购买</a>
                    </div>
                </div>';
            }
            $content = str_replace($match, $replace, $content);
        }
    }

    // 解析高亮内容短代码 [highlight]...[/highlight]
    $pattern = '/\[highlight\](.*?)\[\/highlight\]/s';
    if (preg_match_all($pattern, $content, $matches)) {
        foreach ($matches[0] as $key => $match) {
            $highlightContent = $matches[1][$key];
            $replace = '<div class="highlight-content">' . $highlightContent . '</div>';
            $content = str_replace($match, $replace, $content);
        }
    }

    // 解析提示框短代码 [tip type="info|success|warning|error" title="标题"]...[/tip]
    $pattern = '/\[tip type="(.*?)" title="(.*?)"\](.*?)\[\/tip\]/s';
    if (preg_match_all($pattern, $content, $matches)) {
        foreach ($matches[0] as $key => $match) {
            $tipType = $matches[1][$key];
            $tipTitle = $matches[2][$key];
            $tipContent = $matches[3][$key];

            $iconClass = 'ri-information-line';
            switch ($tipType) {
                case 'success':
                    $iconClass = 'ri-checkbox-circle-line';
                    break;
                case 'warning':
                    $iconClass = 'ri-error-warning-line';
                    break;
                case 'error':
                    $iconClass = 'ri-close-circle-line';
                    break;
            }

            $replace = '<div class="tip-box tip-' . $tipType . '">
                <div class="tip-icon"><i class="' . $iconClass . '"></i></div>
                <div class="tip-content">
                    <div class="tip-title">' . $tipTitle . '</div>
                    <div class="tip-body">' . $tipContent . '</div>
                </div>
            </div>';

            $content = str_replace($match, $replace, $content);
        }
    }

    return $content;
}

/**
 * 检查用户是否已购买内容
 *
 * @param int $cid 文章ID
 * @param int $uid 用户ID
 * @return bool 是否已购买
 */
function checkUserPurchased($cid, $uid) {
    // 如果用户未登录，直接返回false
    if (empty($uid)) {
        return false;
    }

    // 从数据库查询用户是否已购买内容
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 查询购买记录
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'user_purchased_' . $uid));

    if ($row) {
        return $row['str_value'] == 'yes';
    }

    return false;
}

/**
 * 后台设置菜单
 */
function themeConfig($form) {
    // 主题Logo
    $logoUrl = new Typecho_Widget_Helper_Form_Element_Text('logoUrl', NULL, NULL, _t('站点Logo地址'), _t('在这里填入一个图片URL地址, 以在网站标题前加上一个LOGO'));
    $form->addInput($logoUrl);



    // 会员价格设置
    $memberMonthlyPrice = new Typecho_Widget_Helper_Form_Element_Text('memberMonthlyPrice', NULL, '29.99', _t('月度会员价格'), _t('设置月度会员价格'));
    $form->addInput($memberMonthlyPrice);

    $memberYearlyPrice = new Typecho_Widget_Helper_Form_Element_Text('memberYearlyPrice', NULL, '299.00', _t('年度会员价格'), _t('设置年度会员价格'));
    $form->addInput($memberYearlyPrice);

    $memberLifetimePrice = new Typecho_Widget_Helper_Form_Element_Text('memberLifetimePrice', NULL, '799.00', _t('永久会员价格'), _t('设置永久会员价格'));
    $form->addInput($memberLifetimePrice);

    // 社交媒体链接
    $socialWeibo = new Typecho_Widget_Helper_Form_Element_Text('socialWeibo', NULL, NULL, _t('微博链接'), _t('输入您的微博主页链接'));
    $form->addInput($socialWeibo);

    $socialWechat = new Typecho_Widget_Helper_Form_Element_Text('socialWechat', NULL, NULL, _t('微信二维码'), _t('输入您的微信二维码图片链接'));
    $form->addInput($socialWechat);

    $socialQQ = new Typecho_Widget_Helper_Form_Element_Text('socialQQ', NULL, NULL, _t('QQ链接'), _t('输入您的QQ链接'));
    $form->addInput($socialQQ);

    $socialGithub = new Typecho_Widget_Helper_Form_Element_Text('socialGithub', NULL, NULL, _t('GitHub链接'), _t('输入您的GitHub主页链接'));
    $form->addInput($socialGithub);

    // 底部信息
    $footerInfo = new Typecho_Widget_Helper_Form_Element_Textarea('footerInfo', NULL, NULL, _t('底部信息'), _t('在这里填入需要显示在底部的内容，支持HTML代码'));
    $form->addInput($footerInfo);
}

/**
 * 会员系统相关函数
 */

/**
 * 获取用户头像
 *
 * @param string $mail 用户邮箱
 * @param int $size 头像尺寸
 * @return string 头像URL
 */
function getUserAvatar($mail, $size = 40) {
    $gravatarUrl = 'https://gravatar.loli.net/avatar/';
    $hash = md5(strtolower(trim($mail)));
    return $gravatarUrl . $hash . '?s=' . $size . '&d=mm&r=g';
}

/**
 * 获取用户简介
 *
 * @param int $uid 用户ID
 * @return string 用户简介
 */
function getUserBio($uid) {
    // 从数据库获取用户简介（需要在数据库添加相应字段）
    // 这里简单返回默认文本
    return '这个人很懒，什么都没有留下。';
}

/**
 * 获取用户发布的文章数量
 *
 * @param int $uid 用户ID
 * @return int 文章数量
 */
function getUserPostCount($uid) {
    $db = Typecho_Db::get();
    $count = $db->fetchObject($db->select(array('COUNT(cid)' => 'num'))
        ->from('table.contents')
        ->where('table.contents.type = ?', 'post')
        ->where('table.contents.status = ?', 'publish')
        ->where('table.contents.authorId = ?', $uid))->num;
    return $count;
}

/**
 * 获取用户粉丝数
 *
 * @param int $uid 用户ID
 * @return int 粉丝数
 */
function getUserFollowers($uid) {
    // 这里应该查询数据库中的粉丝关系表
    // 由于没有实际的粉丝系统，这里返回一个随机数作为示例
    return mt_rand(0, 999);
}

/**
 * 获取用户关注数
 *
 * @param int $uid 用户ID
 * @return int 关注数
 */
function getUserFollowing($uid) {
    // 这里应该查询数据库中的关注关系表
    // 由于没有实际的关注系统，这里返回一个随机数作为示例
    return mt_rand(0, 99);
}

/**
 * 获取用户文章总浏览量
 *
 * @param int $uid 用户ID
 * @return int 总浏览量
 */
function getUserViews($uid) {
    $db = Typecho_Db::get();
    $rows = $db->fetchAll($db->select('table.contents.cid')
        ->from('table.contents')
        ->where('table.contents.type = ?', 'post')
        ->where('table.contents.status = ?', 'publish')
        ->where('table.contents.authorId = ?', $uid));

    $views = 0;
    foreach ($rows as $row) {
        $views += getPostViews($row['cid']);
    }

    return $views;
}

/**
 * 判断用户是否是会员
 *
 * @param int $uid 用户ID
 * @return bool 是否是会员
 */
function getUserMemberStatus($uid) {
    // 如果用户未登录，直接返回false
    if (empty($uid)) {
        return false;
    }

    // 从数据库查询用户会员状态
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 查询用户元数据中的会员状态
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $uid, 'member_status'));

    if ($row) {
        return $row['str_value'] == 'valid' ? true : false;
    }

    return false;
}

/**
 * 获取会员等级
 *
 * @param int $uid 用户ID
 * @return string 会员等级
 */
function getMemberLevel($uid) {
    // 如果用户未登录或不是会员，返回空
    if (empty($uid) || !getUserMemberStatus($uid)) {
        return '';
    }

    // 从数据库查询会员等级
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 查询用户元数据中的会员等级
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $uid, 'member_level'));

    if ($row) {
        return $row['str_value'];
    }

    return '未知会员';
}

/**
 * 获取会员过期时间
 *
 * @param int $uid 用户ID
 * @return string 过期时间
 */
function getMemberExpires($uid) {
    // 如果用户未登录或不是会员，返回空
    if (empty($uid) || !getUserMemberStatus($uid)) {
        return '';
    }

    // 从数据库查询会员过期时间
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 查询用户元数据中的会员过期时间
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $uid, 'member_expires'));

    if ($row) {
        $expires = $row['str_value'];
        // 如果是永久会员
        if ($expires == 'forever') {
            return '永久有效';
        } else {
            return date('Y-m-d', $expires);
        }
    }

    return '未设置';
}

/**
 * 判断是否有权限访问会员内容
 *
 * @param object $post 文章对象
 * @return bool 是否有权限
 */
function isMemberAccess($post) {
    // 如果不是会员专享内容，直接返回true
    if (!$post->fields->isPremium) {
        return true;
    }

    // 如果用户未登录，返回false
    if (!Typecho_Widget::widget('Widget_User')->hasLogin()) {
        return false;
    }

    // 判断用户是否是会员
    return getUserMemberStatus(Typecho_Widget::widget('Widget_User')->uid);
}

/**
 * 获取文章浏览量
 *
 * @param object|int $post 文章对象或ID
 * @return int 浏览量
 */
function getPostViews($post) {
    $cid = is_object($post) ? $post->cid : $post;

    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 尝试从字段表获取浏览量
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'views'));

    if ($row) {
        return intval($row['str_value']);
    } else {
        return 0;
    }
}

/**
 * 更新文章浏览量
 *
 * @param object $archive 文章对象
 */
function viewCounter($archive) {
    $cid = $archive->cid;
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 查询是否存在浏览量字段
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'views'));

    if ($row) {
        // 更新浏览量
        $db->query($db->update('table.fields')
            ->rows(array('str_value' => (int)$row['str_value'] + 1))
            ->where('cid = ? AND name = ?', $cid, 'views'));
    } else {
        // 创建浏览量字段
        $db->query($db->insert('table.fields')
            ->rows(array(
                'cid'   => $cid,
                'name'  => 'views',
                'type'  => 'str',
                'str_value' => 1
            )));
    }
}

/**
 * 获取文章点赞数
 *
 * @param object|int $post 文章对象或ID
 * @return int 点赞数
 */
function getLikes($post) {
    $cid = is_object($post) ? $post->cid : $post;

    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 尝试从字段表获取点赞数
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'likes'));

    if ($row) {
        return intval($row['str_value']);
    } else {
        return 0;
    }
}

/**
 * 添加用户购买记录
 *
 * @param int $cid 文章ID
 * @param int $uid 用户ID
 * @param float $price 购买价格
 * @return bool 添加是否成功
 */
function addUserPurchaseRecord($cid, $uid, $price) {
    // 如果用户未登录，直接返回false
    if (empty($uid)) {
        return false;
    }

    // 添加购买记录
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 检查是否已有记录
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'user_purchased_' . $uid));

    if ($row) {
        // 更新记录
        return $db->query($db->update('table.fields')
            ->rows(array('str_value' => 'yes'))
            ->where('cid = ? AND name = ?', $cid, 'user_purchased_' . $uid)) > 0;
    } else {
        // 创建记录
        return $db->query($db->insert('table.fields')
            ->rows(array(
                'cid'   => $cid,
                'name'  => 'user_purchased_' . $uid,
                'type'  => 'str',
                'str_value' => 'yes'
            ))) > 0;
    }
}

/**
 * 设置用户会员状态
 *
 * @param int $uid 用户ID
 * @param string $level 会员等级 (monthly, yearly, lifetime)
 * @param int $duration 会员时长（秒）
 * @return bool 设置是否成功
 */
function setUserMemberStatus($uid, $level, $duration = 0) {
    // 如果用户未登录，直接返回false
    if (empty($uid)) {
        return false;
    }

    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 设置会员状态
    $statusRow = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $uid, 'member_status'));

    if ($statusRow) {
        // 更新会员状态
        $db->query($db->update('table.fields')
            ->rows(array('str_value' => 'valid'))
            ->where('cid = ? AND name = ?', $uid, 'member_status'));
    } else {
        // 创建会员状态
        $db->query($db->insert('table.fields')
            ->rows(array(
                'cid'   => $uid,
                'name'  => 'member_status',
                'type'  => 'str',
                'str_value' => 'valid'
            )));
    }

    // 设置会员等级
    $levelRow = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $uid, 'member_level'));

    if ($levelRow) {
        // 更新会员等级
        $db->query($db->update('table.fields')
            ->rows(array('str_value' => $level))
            ->where('cid = ? AND name = ?', $uid, 'member_level'));
    } else {
        // 创建会员等级
        $db->query($db->insert('table.fields')
            ->rows(array(
                'cid'   => $uid,
                'name'  => 'member_level',
                'type'  => 'str',
                'str_value' => $level
            )));
    }

    // 设置会员过期时间
    $expiresValue = ($level == 'lifetime') ? 'forever' : (time() + $duration);
    $expiresRow = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $uid, 'member_expires'));

    if ($expiresRow) {
        // 更新过期时间
        $db->query($db->update('table.fields')
            ->rows(array('str_value' => $expiresValue))
            ->where('cid = ? AND name = ?', $uid, 'member_expires'));
    } else {
        // 创建过期时间
        $db->query($db->insert('table.fields')
            ->rows(array(
                'cid'   => $uid,
                'name'  => 'member_expires',
                'type'  => 'str',
                'str_value' => $expiresValue
            )));
    }

    return true;
}

/**
 * 文章点赞功能的AJAX处理
 */
function ajaxLikePost() {
    // 检查是否为POST请求
    if ($_SERVER['REQUEST_METHOD'] != 'POST') {
        header('HTTP/1.1 405 Method Not Allowed');
        exit;
    }

    // 获取文章ID
    $cid = isset($_GET['cid']) ? intval($_GET['cid']) : 0;

    // 检查文章ID是否有效
    if ($cid <= 0) {
        echo json_encode(array(
            'success' => false,
            'message' => '无效的文章ID'
        ));
        exit;
    }

    // 检查用户是否已登录
    $user = Typecho_Widget::widget('Widget_User');
    if (!$user->hasLogin()) {
        echo json_encode(array(
            'success' => false,
            'message' => '请先登录后再点赞'
        ));
        exit;
    }

    // 获取用户ID
    $uid = $user->uid;

    // 检查用户是否已经点赞过该文章
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'liked_user_' . $uid));

    if ($row && $row['str_value'] == 'yes') {
        echo json_encode(array(
            'success' => false,
            'message' => '您已经点赞过该文章了'
        ));
        exit;
    }

    // 更新点赞记录
    $likesRow = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'likes'));

    $likes = 1;
    if ($likesRow) {
        $likes = intval($likesRow['str_value']) + 1;

        $db->query($db->update('table.fields')
            ->rows(array('str_value' => $likes))
            ->where('cid = ? AND name = ?', $cid, 'likes'));
    } else {
        $db->query($db->insert('table.fields')
            ->rows(array(
                'cid'   => $cid,
                'name'  => 'likes',
                'type'  => 'str',
                'str_value' => $likes
            )));
    }

    // 记录用户已点赞
    $userLikedRow = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'liked_user_' . $uid));

    if ($userLikedRow) {
        $db->query($db->update('table.fields')
            ->rows(array('str_value' => 'yes'))
            ->where('cid = ? AND name = ?', $cid, 'liked_user_' . $uid));
    } else {
        $db->query($db->insert('table.fields')
            ->rows(array(
                'cid'   => $cid,
                'name'  => 'liked_user_' . $uid,
                'type'  => 'str',
                'str_value' => 'yes'
            )));
    }

    // 返回成功结果
    echo json_encode(array(
        'success' => true,
        'count'   => $likes,
        'message' => '点赞成功'
    ));
    exit;
}

/**
 * 检查用户是否已经点赞过文章
 *
 * @param int $cid 文章ID
 * @param int $uid 用户ID
 * @return bool 是否已点赞
 */
function checkUserLiked($cid, $uid) {
    // 如果用户未登录，直接返回false
    if (empty($uid)) {
        return false;
    }

    // 从数据库查询用户是否已点赞
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 查询点赞记录
    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $cid, 'liked_user_' . $uid));

    if ($row) {
        return $row['str_value'] == 'yes';
    }

    return false;
}

/**
 * 会员过期检查
 *
 * @param int $uid 用户ID
 * @return bool 是否已过期
 */
function checkMemberExpired($uid) {
    // 如果用户未登录，直接返回true（视为已过期）
    if (empty($uid)) {
        return true;
    }

    // 检查用户是否是会员
    if (!getUserMemberStatus($uid)) {
        return true; // 不是会员，视为已过期
    }

    // 获取会员等级
    $level = getMemberLevel($uid);

    // 如果是永久会员，永不过期
    if ($level == 'lifetime') {
        return false;
    }

    // 从数据库获取过期时间
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    $row = $db->fetchRow($db->select('str_value')->from('table.fields')
        ->where('cid = ? AND name = ?', $uid, 'member_expires'));

    if ($row) {
        $expires = $row['str_value'];

        // 如果是永久有效
        if ($expires == 'forever') {
            return false;
        }

        // 检查是否已过期
        return time() > intval($expires);
    }

    return true; // 没有过期记录，视为已过期
}

/**
 * 会员过期处理
 *
 * @param int $uid 用户ID
 */
function handleMemberExpired($uid) {
    // 如果用户未登录，直接返回
    if (empty($uid)) {
        return;
    }

    // 检查是否已过期
    if (checkMemberExpired($uid)) {
        // 已过期，更新会员状态
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();

        // 更新会员状态为失效
        $db->query($db->update('table.fields')
            ->rows(array('str_value' => 'expired'))
            ->where('cid = ? AND name = ?', $uid, 'member_status'));
    }
}

/**
 * 获取会员统计信息
 *
 * @return array 会员统计信息
 */
function getMemberStats() {
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();

    // 获取总会员数
    $totalMembers = $db->fetchObject($db->select(array('COUNT(DISTINCT cid)' => 'num'))
        ->from('table.fields')
        ->where('name = ?', 'member_status')
        ->where('str_value = ?', 'valid'))->num;

    // 获取月度会员数
    $monthlyMembers = $db->fetchObject($db->select(array('COUNT(DISTINCT f1.cid)' => 'num'))
        ->from('table.fields AS f1')
        ->join('table.fields AS f2', 'f1.cid = f2.cid')
        ->where('f1.name = ?', 'member_status')
        ->where('f1.str_value = ?', 'valid')
        ->where('f2.name = ?', 'member_level')
        ->where('f2.str_value = ?', 'monthly'))->num;

    // 获取年度会员数
    $yearlyMembers = $db->fetchObject($db->select(array('COUNT(DISTINCT f1.cid)' => 'num'))
        ->from('table.fields AS f1')
        ->join('table.fields AS f2', 'f1.cid = f2.cid')
        ->where('f1.name = ?', 'member_status')
        ->where('f1.str_value = ?', 'valid')
        ->where('f2.name = ?', 'member_level')
        ->where('f2.str_value = ?', 'yearly'))->num;

    // 获取永久会员数
    $lifetimeMembers = $db->fetchObject($db->select(array('COUNT(DISTINCT f1.cid)' => 'num'))
        ->from('table.fields AS f1')
        ->join('table.fields AS f2', 'f1.cid = f2.cid')
        ->where('f1.name = ?', 'member_status')
        ->where('f1.str_value = ?', 'valid')
        ->where('f2.name = ?', 'member_level')
        ->where('f2.str_value = ?', 'lifetime'))->num;

    // 获取一周内新增会员数
    $oneWeekAgo = time() - 7 * 24 * 60 * 60;
    $newMembers = $db->fetchObject($db->select(array('COUNT(DISTINCT cid)' => 'num'))
        ->from('table.fields')
        ->where('name = ?', 'member_status')
        ->where('str_value = ?', 'valid')
        ->where('int_value > ?', $oneWeekAgo))->num;

    return [
        'totalMembers' => $totalMembers,
        'monthlyMembers' => $monthlyMembers,
        'yearlyMembers' => $yearlyMembers,
        'lifetimeMembers' => $lifetimeMembers,
        'newMembers' => $newMembers
    ];
}

/**
 * 生成订单摘要数据（用于管理员仪表盘）
 *
 * @return array 订单摘要数据
 */
function generateOrderSummary() {
    // 获取订单统计信息
    $orderStats = getOrdersStats();

    // 获取会员统计信息
    $memberStats = getMemberStats();

    // 合并数据
    $summary = array_merge($orderStats, $memberStats);

    // 添加一些计算数据
    $summary['conversionRate'] = $summary['totalOrders'] > 0 ? round(($summary['paidOrders'] / $summary['totalOrders']) * 100, 2) : 0;

    return $summary;
}

/**
 * 获取活跃会员列表（最近登录的会员）
 *
 * @param int $limit 返回数量
 * @return array 活跃会员列表
 */
function getActiveMembers($limit = 10) {
    $db = Typecho_Db::get();

    // 查询有效会员
    $members = $db->fetchAll($db->select('f.cid AS uid', 'u.name', 'u.screenName', 'u.mail', 'u.activated')
        ->from('table.fields AS f')
        ->join('table.users AS u', 'f.cid = u.uid')
        ->where('f.name = ?', 'member_status')
        ->where('f.str_value = ?', 'valid')
        ->order('u.activated', Typecho_Db::SORT_DESC)
        ->limit($limit));

    // 添加会员等级和到期时间信息
    foreach ($members as &$member) {
        $member['level'] = getMemberLevel($member['uid']);
        $member['expires'] = getMemberExpires($member['uid']);
    }

    return $members;
}

/**
 * 根据用户UID获取用户订单
 *
 * @param int $uid 用户ID
 * @param int $limit 返回数量
 * @return array 用户订单列表
 */
function getUserOrdersList($uid, $limit = 5) {
    // 获取用户订单
    $result = getUserOrders($uid, '', 1, $limit);

    return $result['orders'];
}

/**
 * 获取用户收藏的文章
 *
 * @param int $uid 用户ID
 * @param int $limit 返回数量
 * @return array 收藏文章列表
 */
function getUserFavorites($uid, $limit = 10) {
    $db = Typecho_Db::get();

    // 查询用户收藏的文章
    $favorites = $db->fetchAll($db->select('f.str_value AS cid', 'c.title', 'c.created', 'c.authorId')
        ->from('table.fields AS f')
        ->join('table.contents AS c', 'f.str_value = c.cid')
        ->where('f.name = ?', 'favorite')
        ->where('f.cid = ?', $uid)
        ->where('c.type = ?', 'post')
        ->where('c.status = ?', 'publish')
        ->order('f.int_value', Typecho_Db::SORT_DESC)
        ->limit($limit));

    // 添加文章链接和浏览量信息
    foreach ($favorites as &$favorite) {
        $favorite['permalink'] = Typecho_Router::url('post', ['cid' => $favorite['cid']], Typecho_Common::url('', Typecho_Widget::widget('Widget_Options')->siteUrl));
        $favorite['views'] = getPostViews($favorite['cid']);
    }

    return $favorites;
}

/**
 * 添加用户收藏
 *
 * @param int $uid 用户ID
 * @param int $cid 文章ID
 * @return bool 操作结果
 */
function addUserFavorite($uid, $cid) {
    $db = Typecho_Db::get();

    // 检查是否已收藏
    $favorite = $db->fetchRow($db->select('cid')
        ->from('table.fields')
        ->where('cid = ?', $uid)
        ->where('name = ?', 'favorite')
        ->where('str_value = ?', $cid));

    if ($favorite) {
        return false; // 已经收藏过
    }

    // 添加收藏
    try {
        $db->query($db->insert('table.fields')->rows([
            'cid' => $uid,
            'name' => 'favorite',
            'type' => 'str',
            'str_value' => $cid,
            'int_value' => time()
        ]));
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 删除用户收藏
 *
 * @param int $uid 用户ID
 * @param int $cid 文章ID
 * @return bool 操作结果
 */
function removeUserFavorite($uid, $cid) {
    $db = Typecho_Db::get();

    // 删除收藏
    try {
        $db->query($db->delete('table.fields')
            ->where('cid = ?', $uid)
            ->where('name = ?', 'favorite')
            ->where('str_value = ?', $cid));
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * AJAX处理收藏操作
 */
function ajaxFavoritePost() {
    // 检查用户是否登录
    if (!Typecho_Widget::widget('Widget_User')->hasLogin()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录后再收藏'
        ]);
        exit;
    }

    $uid = Typecho_Widget::widget('Widget_User')->uid;
    $cid = isset($_POST['cid']) ? intval($_POST['cid']) : 0;
    $action = isset($_POST['action']) ? $_POST['action'] : 'add';

    if (!$cid) {
        echo json_encode([
            'success' => false,
            'message' => '参数错误'
        ]);
        exit;
    }

    if ($action === 'add') {
        // 添加收藏
        $result = addUserFavorite($uid, $cid);
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => '收藏成功'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '已收藏过或操作失败'
            ]);
        }
    } else {
        // 删除收藏
        $result = removeUserFavorite($uid, $cid);
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => '取消收藏成功'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '操作失败'
            ]);
        }
    }

    exit;
}