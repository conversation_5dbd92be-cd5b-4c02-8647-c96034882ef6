@charset "UTF-8";
/* vim: set et sw=2 ts=2 sts=2 fdm=marker ff=unix fenc=utf8 */
/** Typecho 后台样式 <AUTHOR> Team @since   2008-09-26 @update  2013-11-02 @link    http://www.typecho.org/ @version 0.9 */
/** Typecho 全局样式 */
html { height: 100%; }

body { font-family: 'Helvetica Neue', Helvetica, Arial, -apple-system, system-ui, sans-serif; background: #F6F6F3; color: #444; font-size: 87.5%; line-height: 1.5; }

a { color: #467B96; text-decoration: none; }

a:hover { color: #499BC3; text-decoration: underline; }

code, pre, .mono { font-family: 'SF Mono', Menlo, Monaco, Consolas, 'Courier New', -apple-system, system-ui, monospace; }

.p { margin: 1em 0; }

.body-100 { height: 100%; }

a.balloon-button { display: inline-block; padding: 0 6px; min-width: 12px; height: 18px; line-height: 18px; background: #D8E7EE; font-size: .85714em; text-align: center; text-decoration: none; /** 修正ie中文不对齐 */ zoom: 1; border-radius: 30px; white-space: nowrap; }

a.button:hover, a.balloon-button:hover { background-color: #A5CADC; color: #FFF; text-decoration: none; }

/** Forms */
input[type=text], input[type=password], input[type=email], textarea { background: #FFF; border: 1px solid #D9D9D6; padding: 7px; border-radius: 2px; box-sizing: border-box; }

input[type=text]:disabled, input[type=text]:read-only, input[type=password]:disabled, input[type=password]:read-only, input[type=email]:disabled, input[type=email]:read-only, textarea:disabled, textarea:read-only { background: #F3F3F3; }

textarea { resize: vertical; line-height: 1.5; }

input[type="radio"], input[type="checkbox"] { margin-right: 3px; }

input[type="radio"], input[type="checkbox"], input[type="radio"] + label, input[type="checkbox"] + label { vertical-align: middle; }

input.text-s, textarea.text-s { padding: 5px; }

input.text-l, textarea.text-l { padding: 10px; font-size: 1.14286em; }

.w-10 { width: 10%; }

.w-20 { width: 20%; }

.w-30 { width: 30%; }

.w-40 { width: 40%; }

.w-50 { width: 50%; }

.w-60 { width: 60%; }

.w-70 { width: 70%; }

.w-80 { width: 80%; }

.w-90 { width: 90%; }

.w-100 { width: 100%; }

select { border: 1px solid #CCC; height: 28px; }

/** Buttons */
.btn, #ui-datepicker-div .ui-datepicker-current, #ui-datepicker-div .ui-datepicker-close { border: none; background-color: #E9E9E6; cursor: pointer; border-radius: 2px; display: inline-block; padding: 0 12px; height: 32px; color: #666; vertical-align: middle; zoom: 1; }

.btn:hover, #ui-datepicker-div .ui-datepicker-current:hover, #ui-datepicker-div .ui-datepicker-close:hover { transition-duration: .4s; background-color: #dbdbd6; }

.btn:active, #ui-datepicker-div .ui-datepicker-current:active, #ui-datepicker-div .ui-datepicker-close:active, .btn.active, #ui-datepicker-div .active.ui-datepicker-current, #ui-datepicker-div .active.ui-datepicker-close { background-color: #d6d6d0; }

.btn:disabled, #ui-datepicker-div .ui-datepicker-current:disabled, #ui-datepicker-div .ui-datepicker-close:disabled { background-color: #f7f7f6; cursor: default; }

.btn:disabled, #ui-datepicker-div .ui-datepicker-current:disabled, #ui-datepicker-div .ui-datepicker-close:disabled { color: #999; }

.btn-xs, #ui-datepicker-div .ui-datepicker-current, #ui-datepicker-div .ui-datepicker-close { padding: 0 10px; height: 25px; font-size: 13px; }

.btn-s { height: 28px; }

.btn-l { height: 40px; font-size: 1.14286em; font-weight: bold; }

.primary { border: none; background-color: #467B96; cursor: pointer; border-radius: 2px; color: #FFF; }

.primary:hover { transition-duration: .4s; background-color: #3c6a81; }

.primary:active, .primary.active { background-color: #39647a; }

.primary:disabled { background-color: #508cab; cursor: default; }

.btn-group { display: inline-block; }

.btn-warn { border: none; background-color: #B94A48; cursor: pointer; border-radius: 2px; color: #FFF; }

.btn-warn:hover { transition-duration: .4s; background-color: #a4403f; }

.btn-warn:active, .btn-warn.active { background-color: #9c3e3c; }

.btn-warn:disabled { background-color: #c1605e; cursor: default; }

.btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active, .btn-link.active { background-color: transparent; }

/* 下拉菜单 */
.btn-drop { position: relative; }

.dropdown-toggle { padding-right: 8px; }

.dropdown-menu { list-style: none; position: absolute; z-index: 2; left: 0; margin: 0; padding: 0; border: 1px solid #D9D9D6; background: #FFF; text-align: left; min-width: 108px; display: none; }

.dropdown-menu li { white-space: nowrap; }

.dropdown-menu li.multiline { padding: 5px 12px 12px; }

.dropdown-menu a { display: block; padding: 5px 12px; color: #666; }

.dropdown-menu a:hover { background: #F6F6F3; text-decoration: none !important; }

/** 提示信息框 */
.message { padding: 8px 10px; border-radius: 2px; }

.message a { font-weight: bold; text-decoration: underline; }

.error { background: #FBE3E4; color: #8A1F11; }

.error a { color: #8A1F11; }

.notice { background: #FFF6BF; color: #8A6D3B; }

.notice a { color: #8A6D3B; }

.success { background: #E6EFC2; color: #264409; }

.success a { color: #264409; }

.balloon { display: inline-block; padding: 0 4px; min-width: 10px; height: 14px; line-height: 14px; background: #B9B9B6; vertical-align: text-top; text-align: center; font-size: 12px; color: #FFF; border-radius: 20px; }

/** 后台分页 */
.typecho-pager { list-style: none; float: right; margin: 0; padding: 0; line-height: 1; text-align: center; zoom: 1; }

.typecho-pager li { display: inline-block; margin: 0 3px; height: 28px; line-height: 28px; }

.typecho-pager a { display: block; padding: 0 10px; border-radius: 2px; }

.typecho-pager a:hover { text-decoration: none; background: #E9E9E6; }

.typecho-pager li.current a { background: #E9E9E6; color: #444; }

/** 后台头部导航 */
.typecho-head-nav { padding: 0 10px; background: #292D33; position: relative; }

.typecho-head-nav a, .typecho-head-nav button.menu-bar { padding: 0 20px; height: 36px; line-height: 36px; color: #BBB; }

.typecho-head-nav a:focus, .typecho-head-nav a:hover, .typecho-head-nav button.menu-bar:focus, .typecho-head-nav button.menu-bar:hover { color: #FFF; text-decoration: none; }

.typecho-head-nav button.menu-bar { display: none; }

.typecho-head-nav #typecho-nav-list { float: left; }

.typecho-head-nav #typecho-nav-list > ul { list-style: none; margin: 0; padding: 0; position: relative; float: left; }

.typecho-head-nav #typecho-nav-list > ul:first-child { border-left: 1px solid #383D45; }

.typecho-head-nav #typecho-nav-list > ul .parent a { display: inline-block; border-right: 1px solid #383D45; background: #292D33; }

.typecho-head-nav #typecho-nav-list > ul .child { position: absolute; list-style: none; top: 36px; display: none; margin: 0; padding: 0; min-width: 160px; max-width: 240px; background: #202328; z-index: 250; }

.typecho-head-nav #typecho-nav-list > ul .child li.return { display: none; }

.typecho-head-nav #typecho-nav-list > ul .child li a { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: block; }

.typecho-head-nav #typecho-nav-list > ul .child li a:hover, .typecho-head-nav #typecho-nav-list > ul .child li a:focus { background: #292D33; }

.typecho-head-nav #typecho-nav-list > ul .child li.focus a { color: #6DA1BB; font-weight: bold; }

.typecho-head-nav #typecho-nav-list > ul .parent a:hover, .typecho-head-nav #typecho-nav-list > ul.focus .parent a, .typecho-head-nav #typecho-nav-list > ul.root:hover .parent a { background: #202328; }

.typecho-head-nav #typecho-nav-list > ul.focus .parent a { font-weight: bold; }

.typecho-head-nav #typecho-nav-list > ul.root:hover .child, .typecho-head-nav #typecho-nav-list > ul.root.expanded .child { display: block; }

.typecho-head-nav .operate { float: right; }

.typecho-head-nav .operate a { display: inline-block; margin-left: -1px; border: 1px solid #383D45; border-width: 0 1px; }

.typecho-head-nav .operate a:hover { background-color: #202328; }

@media (max-width: 575px) { @keyframes out { from { left: 0%; }
    to { left: -100%; } }
  @keyframes in { from { left: -100%; }
    to { left: 0%; } }
  .typecho-head-nav { padding: 0; position: fixed; bottom: 0; width: 100%; z-index: 10; }
  .typecho-head-nav #typecho-nav-list { display: none; }
  .typecho-head-nav .operate a:last-child { border-right-width: 0; }
  .typecho-head-nav button.menu-bar { display: inline-block; border: none; background: #292D33; border-right: 1px solid #383D45; }
  .typecho-head-nav button.menu-bar.focus { color: #FFF; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list { display: block; float: none; position: absolute; bottom: 36px; width: 100%; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list > ul { float: none; border-bottom: 1px solid #383D45; position: static; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list > ul:first-child { border-left: none; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list > ul .parent a { display: block; border: none; background: #202328; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list > ul .child { position: absolute; bottom: 0; left: 100%; top: auto; z-index: 20; width: 100%; max-width: 100%; min-width: auto; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list > ul .child li { border-bottom: 1px solid #383D45; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list > ul .child li.return { display: block; text-align: center; font-size: 12px; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list > ul .child li.return a { color: #777; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list.expanded { animation: out .15s ease-out forwards; }
  .typecho-head-nav button.menu-bar.focus + #typecho-nav-list.noexpanded { animation: in .15s ease-out forwards; } }

/** 注脚 */
.typecho-foot { padding: 4em 0 3em; color: #999; line-height: 1.8; text-align: center; }

.typecho-foot .copyright p { margin: 10px 0 0; }

.typecho-foot .resource { color: #CCC; }

.typecho-foot .resource a { margin: 0 3px; color: #999; }

/** 顶部消息样式 by 70 */
.popup { display: none; position: absolute; top: 0; left: 0; margin: 0; padding: 8px 0; border: none; width: 100%; z-index: 10; text-align: center; border-radius: 0; }

.popup ul { list-style: none; margin: 0; padding: 0; text-align: center; }

.popup ul li { display: inline-block; margin-right: 10px; }

/** logo 的样式 */
/** 载入状态 */
.loading { padding-left: 20px !important; background: transparent url(../img/ajax-loader.gif) no-repeat left center; }

/** 典型配置选项 */
.typecho-option { list-style: none; margin: 1em 0; padding: 0; }

.typecho-option-submit li { border-bottom: none; }

.typecho-option label.typecho-label { display: block; margin-bottom: .5em; font-weight: bold; }

.typecho-option label.required:after { content: " *"; color: #B94A48; }

.typecho-option span { margin-right: 15px; }

.typecho-option .description { margin: .5em 0 0; color: #999; font-size: .92857em; }

.typecho-option input.file { width: 100%; margin: .7em 0; }

.front-archive { padding-left: 1.5em; }

.profile-avatar { width: 220px; height: 220px; border-radius: 10px; }

/** 增加配置面板内部的错误样式 by 70 */
/** 欢迎界面 */
#typecho-welcome { margin: 1em 0; padding: 1em 2em; background-color: #E9E9E6; }

.welcome-board { color: #999; font-size: 1.15em; }

.welcome-board em { color: #444; font-size: 2em; font-style: normal; font-family: Georgia, serif; }

#start-link { margin-bottom: 25px; padding: 0 0 35px; border-bottom: 1px solid #ECECEC; }

#start-link li { float: left; margin-right: 1.5em; }

#start-link .balloon { margin-top: 2px; }

.latest-link li { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

.latest-link span { display: inline-block; margin-right: 4px; padding-right: 8px; border-right: 1px solid #ECECEC; width: 37px; text-align: right; color: #999; }

.update-check { font-size: 14px; }

/** 登录框 */
.typecho-login-wrap { display: table; margin: 0 auto; height: 100%; }

.typecho-login { display: table-cell; padding: 30px 0 100px; width: 280px; text-align: center; vertical-align: middle; }

.typecho-login h1 { margin: 0 0 1em; }

.typecho-login .more-link { margin-top: 2em; color: #CCC; }

.typecho-login .more-link a { margin: 0 3px; }

/** 标题 */
.typecho-page-title h2 { margin: 25px 0 10px; font-size: 1.28571em; }

.typecho-page-title h2 a { margin-left: 10px; padding: 3px 8px; background: #E9E9E6; font-size: .8em; border-radius: 2px; }

.typecho-page-title h2 a:hover { text-decoration: none; }

/** 后台页面主体 */
/** 主页主体 */
.typecho-dashboard ul { list-style: none; padding: 0; }

.typecho-dashboard li { margin-bottom: 5px; }

/** 标签页 */
.typecho-option-tabs { list-style: none; margin: 1em 0 0; padding: 0; font-size: 13px; text-align: center; }

.typecho-option-tabs.fix-tabs { margin-bottom: 1em; }

.typecho-option-tabs a { display: block; margin-right: -1px; border: 1px solid #D9D9D6; padding: 0 15px; height: 26px; line-height: 26px; color: #666; box-sizing: border-box; }

.typecho-option-tabs a:hover { background-color: #E9E9E6; color: #666; text-decoration: none; }

.typecho-option-tabs li { float: left; }

.typecho-option-tabs li:first-child a { border-radius: 2px 0 0 2px; }

.typecho-option-tabs li:last-child a { border-radius: 0 2px 2px 0; }

.typecho-option-tabs.right { float: right; }

.typecho-option-tabs li.current a, .typecho-option-tabs li.active a { background-color: #E9E9E6; }

/** 表格列表页 */
/** 列表页选项 */
.typecho-list-operate { margin: 1em 0; }

.typecho-list-operate input, .typecho-list-operate button, .typecho-list-operate select { vertical-align: bottom; }

.typecho-list-operate input[type="checkbox"] { vertical-align: text-top; }

@media (min-width: 576px) { .typecho-list-operate .operate { float: left; }
  .typecho-list-operate .search { float: right; } }

.typecho-list-operate span.operate-delete, a.operate-delete, .typecho-list-operate span.operate-button-delete, a.operate-button-delete { color: #B94A48; }

a.operate-edit { color: #007700; }

a.operate-reply { color: #545c30; }

.typecho-list-operate a:hover { text-decoration: none; }

/** 列表表格 */
/** 增加表格标题 by 70 */
.typecho-list-table-title { margin: 1em 0; color: #999; text-align: center; }

.typecho-table-wrap { padding: 30px; background: #FFF; }

.typecho-list-table { width: 100%; border-collapse: collapse; table-layout: fixed; }

.typecho-list-table.deactivate { color: #999; }

.typecho-list-table .right { text-align: right; }

.typecho-list-table th { padding: 0 10px 10px; border-bottom: 2px solid #F0F0EC; text-align: left; }

.typecho-list-table td { padding: 10px; border-top: 1px solid #F0F0EC; word-break: break-all; }

.typecho-list-table td pre { overflow: auto; }

.typecho-list-table .status { margin-left: 5px; color: #999; font-size: .92857em; font-style: normal; }

.typecho-list-table tbody tr:hover td { background-color: #F6F6F3; }

.typecho-list-table tbody tr.checked td { background-color: #FFF9E8; }

.typecho-list-table tr td .hidden-by-mouse { opacity: 0; }

.typecho-list-table tr:hover td .hidden-by-mouse { opacity: 1; }

.warning { color: #B94A48; }

/** 评论管理 */
.comment-reply-content { position: relative; margin: 1em 0; padding: 0 1em; border: 1px solid transparent; background-color: #F0F0EC; }

.comment-reply-content:after { position: absolute; right: 1em; border: 8px solid #F0F0EC; border-color: #F0F0EC #F0F0EC transparent transparent; content: " "; }

.comment-meta span, .comment-date { font-size: .92857em; color: #999; }

.comment-action a, .comment-action span { margin-right: 4px; }

.comment-edit label { display: block; }

.comment-content img { max-width: 100%; }

/** 评论回复 */
#typecho-respond { padding: 10px; display: none; }

/** 模板列表 */
.typecho-theme-list img { margin: 1em 0; max-width: 100%; max-height: 240px; }

.typecho-theme-list cite { font-style: normal; color: #999; }

.typecho-theme-list tbody tr.current td { background-color: #FFF9E8; }

/** 后台配置项 */
.typecho-page-main .typecho-option input.text { width: 100%; }

.typecho-page-main .typecho-option input.num { width: 40px; }

.typecho-page-main .typecho-option textarea { width: 100%; height: 100px; }

.typecho-page-main .typecho-option .multiline { display: block; margin: .3em 0; }

.typecho-page-main .typecho-option .multiline.hidden { display: none; }

/** 编辑模板 */
.typecho-select-theme { height: 25px; line-height: 25px; margin: 15px 0px; }

.typecho-select-theme h5 { color: #E47E00; font-weight: bold; float: left; font-size: 14px; width: 120px; margin-right: 10px; }

.typecho-select-theme select { width: 150px; }

/** 编辑模板（编辑详情） */
.typecho-edit-theme ul { list-style: none; margin: 0; padding: 0; }

.typecho-edit-theme li { padding: 3px 10px; }

.typecho-edit-theme .current { background-color: #E6E6E3; }

.typecho-edit-theme .current a { color: #444; }

.typecho-edit-theme textarea { font-size: .92857em; line-height: 1.2; height: 500px; }

/** 编写页面 */
.typecho-post-area .edit-draft-notice { color: #999; font-size: .92857em; }

.typecho-post-area .edit-draft-notice a { color: #B94A48; }

.typecho-post-area .typecho-label { display: block; margin: 1em 0 -0.5em; font-weight: bold; }

.typecho-post-area #auto-save-message { display: block; margin-top: 0.5em; color: #999; font-size: .92857em; }

.typecho-post-area .submit .right button { margin-left: 5px; }

.typecho-post-area .right { float: right; }

.typecho-post-area .left { float: left; }

.typecho-post-area .out-date { border: 1px solid #D3DBB3; padding: 3px; background: #fff; }

.typecho-post-area input.title { font-size: 1.17em; font-weight: bold; }

.typecho-post-area .url-slug { margin-top: -0.5em; color: #AAA; font-size: .92857em; word-break: break-word; }

.typecho-post-area #slug { padding: 2px; border: none; background: #FFFBCC; color: #666; }

.typecho-post-area #text { resize: none; }

#advance-panel { display: none; }

#custom-field { margin: 1em 0; padding: 10px 15px; background: #FFF; }

#custom-field.fold table, #custom-field.fold .description { display: none; }

#custom-field .description { margin-top: 10px; text-align: right; }

#custom-field .description button { float: left; }

#custom-field p.description { text-align: left; }

#custom-field .typecho-label { margin: 0; }

#custom-field .typecho-label a { display: block; color: #444; }

#custom-field .typecho-label a:hover { color: #467B96; text-decoration: none; }

#custom-field table { margin-top: 10px; }

#custom-field td { padding: 10px 5px; font-size: .92857em; border-bottom: 1px solid #F0F0EC; vertical-align: top; }

#custom-field td label { font-size: 1em; font-weight: normal; }

#custom-field select { height: 27px; }

.typecho-post-area .is-draft { background: #FFF1A8; }

.typecho-post-option .description { margin-top: -0.5em; color: #999; font-size: .92857em; }

.category-option ul { list-style: none; border: 1px solid #D9D9D6; padding: 6px 12px; max-height: 240px; overflow: auto; background-color: #FFF; border-radius: 2px; }

.category-option li { margin: 3px 0; }

.visibility-option ul, .allow-option ul { list-style: none; padding: 0; }

/** 标签列表 */
.typecho-page-main ul.tag-list { list-style: none; margin: 0; padding: 20px; background-color: #FFF; }

.typecho-page-main ul.tag-list li { display: inline-block; margin: 0 0 5px 0; padding: 5px 5px 5px 10px; cursor: pointer; }

.typecho-page-main ul.tag-list li:hover { background-color: #E9E9E6; }

.typecho-page-main ul.tag-list li input { display: none; }

.typecho-page-main ul.tag-list li.checked { background-color: #FFFBCC; }

.typecho-page-main ul.tag-list li.size-5 { font-size: 1em; }

.typecho-page-main ul.tag-list li.size-10 { font-size: 1.2em; }

.typecho-page-main ul.tag-list li.size-20 { font-size: 1.4em; }

.typecho-page-main ul.tag-list li.size-30 { font-size: 1.6em; }

.typecho-page-main ul.tag-list li.size-0 { font-size: 1.8em; }

.typecho-page-main .tag-edit-link { visibility: hidden; }

.typecho-page-main li:hover .tag-edit-link { visibility: visible; }

.typecho-attachment-photo { border: 1px solid #E6E6E3; max-width: 100%; }

/* Upload */
#upload-panel { border: 1px dashed #D9D9D6; background-color: #FFF; color: #999; font-size: .92857em; }

#upload-panel.drag { background-color: #FFFBCC; }

.upload-area { padding: 15px; text-align: center; }

#file-list { list-style: none; margin: 0 10px; padding: 0; max-height: 450px; overflow: auto; word-break: break-all; }

#file-list li, #file-list .insert { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }

#file-list li { padding: 8px 0; border-top: 1px dashed #D9D9D6; }

#file-list .insert { display: block; max-width: 100%; }

#file-list .file { margin-left: 5px; }

#file-list .info { text-transform: uppercase; }

#btn-fullscreen-upload { visibility: hidden; }

/** 附件管理 */
.edit-media button { margin-right: 6px; }

/* 拖动调整 textarea 大小 */
.resize { display: block; margin: 2px auto 0; padding: 2px 0; border: 1px solid #D9D9D6; border-width: 1px 0; width: 60px; cursor: row-resize; }

.resize i { display: block; height: 1px; background-color: #D9D9D6; }

/* 拖动排序 */
.tDnD_whileDrag { background-color: #FFFBCC; }

@media (max-width: 575px) { .typecho-list-operate .search { margin-top: 10px; }
  .typecho-table-wrap { padding: 10px; margin: 0 -10px; }
  .typecho-option-submit button[type="submit"] { width: 100%; }
  .profile-avatar { width: 110px; height: 110px; } }

/** 导入扩展样式 */
/** icons */
.icons-sprite, .icons-icon-delete, .icons-icon-edit, .icons-icon-exlink, .icons-icon-upload-active, .icons-icon-upload, .icons-mime-application, .icons-mime-archive, .icons-mime-audio, .icons-mime-html, .icons-mime-image, .icons-mime-office, .icons-mime-script, .icons-mime-text, .icons-mime-unknow, .icons-mime-video, .i-edit, .i-delete, .i-upload, .i-upload-active, .i-exlink, .mime-office, .mime-text, .mime-image, .mime-html, .mime-archive, .mime-application, .mime-audio, .mime-script, .mime-video, .mime-unknow { background-image: url("../img/icons.png?_=01c3ae1"); background-repeat: no-repeat; }

.icons-icon-delete { background-position: 0 0; width: 16px; height: 16px; }

.icons-icon-edit { background-position: 0 -16px; width: 16px; height: 16px; }

.icons-icon-exlink { background-position: 0 -32px; width: 16px; height: 16px; }

.icons-icon-upload-active { background-position: 0 -208px; width: 24px; height: 24px; }

.icons-icon-upload { background-position: 0 -232px; width: 24px; height: 24px; }

.icons-icon-upload:active, .icons-icon-upload.icon-upload-active { background-position: 0 -208px; }

.icons-mime-application { background-position: 0 -48px; width: 16px; height: 16px; }

.icons-mime-archive { background-position: 0 -64px; width: 16px; height: 16px; }

.icons-mime-audio { background-position: 0 -80px; width: 16px; height: 16px; }

.icons-mime-html { background-position: 0 -96px; width: 16px; height: 16px; }

.icons-mime-image { background-position: 0 -112px; width: 16px; height: 16px; }

.icons-mime-office { background-position: 0 -128px; width: 16px; height: 16px; }

.icons-mime-script { background-position: 0 -144px; width: 16px; height: 16px; }

.icons-mime-text { background-position: 0 -160px; width: 16px; height: 16px; }

.icons-mime-unknow { background-position: 0 -176px; width: 16px; height: 16px; }

.icons-mime-video { background-position: 0 -192px; width: 16px; height: 16px; }

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) { .icons-sprite, .icons-icon-delete, .icons-icon-edit, .icons-icon-exlink, .icons-icon-upload-active, .icons-icon-upload, .icons-mime-application, .icons-mime-archive, .icons-mime-audio, .icons-mime-html, .icons-mime-image, .icons-mime-office, .icons-mime-script, .icons-mime-text, .icons-mime-unknow, .icons-mime-video, .i-edit, .i-delete, .i-upload, .i-upload-active, .i-exlink, .mime-office, .mime-text, .mime-image, .mime-html, .mime-archive, .mime-application, .mime-audio, .mime-script, .mime-video, .mime-unknow { background-image: url("../img/<EMAIL>?_=e65bc46"); background-repeat: no-repeat; background-size: 24px 256px; }
  .icons-icon-delete { background-position: 0 0; }
  .icons-icon-edit { background-position: 0 -16px; }
  .icons-icon-exlink { background-position: 0 -32px; }
  .icons-icon-upload-active { background-position: 0 -208px; }
  .icons-icon-upload { background-position: 0 -232px; }
  .icons-icon-upload:active, .icons-icon-upload.icon-upload-active { background-position: 0 -208px; }
  .icons-mime-application { background-position: 0 -48px; }
  .icons-mime-archive { background-position: 0 -64px; }
  .icons-mime-audio { background-position: 0 -80px; }
  .icons-mime-html { background-position: 0 -96px; }
  .icons-mime-image { background-position: 0 -112px; }
  .icons-mime-office { background-position: 0 -128px; }
  .icons-mime-script { background-position: 0 -144px; }
  .icons-mime-text { background-position: 0 -160px; }
  .icons-mime-unknow { background-position: 0 -176px; }
  .icons-mime-video { background-position: 0 -192px; }
  .i-edit { background-position: 0 -16px; }
  .i-delete { background-position: 0 0; }
  .i-upload { background-position: 0 -232px; }
  .i-upload:active, .i-upload.icon-upload-active { background-position: 0 -208px; }
  .i-upload-active { background-position: 0 -208px; }
  .i-exlink { background-position: 0 -32px; }
  .mime-office { background-position: 0 -128px; }
  .mime-text { background-position: 0 -160px; }
  .mime-image { background-position: 0 -112px; }
  .mime-html { background-position: 0 -96px; }
  .mime-archive { background-position: 0 -64px; }
  .mime-application { background-position: 0 -48px; }
  .mime-audio { background-position: 0 -80px; }
  .mime-script { background-position: 0 -144px; }
  .mime-video { background-position: 0 -192px; }
  .mime-unknow { background-position: 0 -176px; } }

/* @mixin sprite-background($name) { // background-image: sprite-url($sprites); // background-position: sprite-position($sprites, $name); @include icons-sprite($name); // background-repeat: no-repeat; // display: block; // height: image-height(sprite-file($sprites, $name)); // width: image-width(sprite-file($sprites, $name)); @media  (-webkit-min-device-pixel-ratio: 2),  (min-resolution: 192dpi) { @include icons-2x-sprite($name); // Workaround for https://gist.github.com/2140082 //@if (sprite-position($sprites, $name) != sprite-position($sprites-retina, $name)) { //  $ypos: round(nth(sprite-position($sprites-retina, $name), 2) / 2); //  background-position: 0 $ypos; //} // Hard coded width of the normal sprite image. There must be a smarter way to do this. // @include background-size(auto 256px); // background-image: sprite-url($sprites-retina); } } */
.i-edit, .i-delete, .i-exlink, .mime-office, .mime-text, .mime-image, .mime-html, .mime-archive, .mime-application, .mime-audio, .mime-script, .mime-video, .mime-unknow, .i-upload, .i-upload-active { display: inline-block; vertical-align: text-bottom; text-indent: -9999em; }

.i-edit:hover, .i-delete:hover, .i-exlink:hover, .mime-office:hover, .mime-text:hover, .mime-image:hover, .mime-html:hover, .mime-archive:hover, .mime-application:hover, .mime-audio:hover, .mime-script:hover, .mime-video:hover, .mime-unknow:hover, .i-upload:hover, .i-upload-active:hover { opacity: 0.75; }

.i-edit, .i-delete, .i-exlink, .mime-office, .mime-text, .mime-image, .mime-html, .mime-archive, .mime-application, .mime-audio, .mime-script, .mime-video, .mime-unknow { width: 16px; height: 16px; }

.i-upload, .i-upload-active { width: 24px; height: 24px; }

.i-edit { background-position: 0 -16px; }

.i-delete { background-position: 0 0; }

.i-upload { background-position: 0 -232px; }

.i-upload:active, .i-upload.icon-upload-active { background-position: 0 -208px; }

.i-upload-active { background-position: 0 -208px; }

.i-caret-up, .i-caret-down, .i-caret-left, .i-caret-right { display: inline-block; border-style: solid; border-color: transparent transparent #BBB transparent; border-width: 3px 4px 5px; }

.i-caret-down { border-color: #BBB transparent transparent transparent; border-width: 5px 4px 3px; }

.i-caret-left { border-color: transparent #BBB transparent transparent; border-width: 4px 5px 4px 3px; }

.i-caret-right { border-color: transparent transparent transparent #BBB; border-width: 4px 3px 4px 5px; }

.i-exlink { background-position: 0 -32px; }

/* 文件类型图标 */
.mime-office { background-position: 0 -128px; }

.mime-text { background-position: 0 -160px; }

.mime-image { background-position: 0 -112px; }

.mime-html { background-position: 0 -96px; }

.mime-archive { background-position: 0 -64px; }

.mime-application { background-position: 0 -48px; }

.mime-audio { background-position: 0 -80px; }

.mime-script { background-position: 0 -144px; }

.mime-video { background-position: 0 -192px; }

.mime-unknow { background-position: 0 -176px; }

/* Logo 图标 */
.i-logo, .i-logo-s { width: 169px; height: 40px; display: inline-block; background: url("../img/typecho-logo.svg") no-repeat; text-indent: -9999em; background-size: auto 40px; opacity: .15; }

.i-logo:hover, .i-logo-s:hover { opacity: .2; }

.i-logo-s { width: 26px; height: 26px; background-size: auto 26px; }

/* Editor */
.editor { margin-bottom: -0.5em; }

.editor-sprite, .editor-editor-bold, .editor-editor-code, .editor-editor-exit-fullscreen, .editor-editor-fullscreen, .editor-editor-heading, .editor-editor-hr, .editor-editor-image, .editor-editor-italic, .editor-editor-link, .editor-editor-more, .editor-editor-olist, .editor-editor-quote, .editor-editor-redo, .editor-editor-ulist, .editor-editor-undo, .wmd-button-row li#wmd-bold-button span, .wmd-button-row li#wmd-italic-button span, .wmd-button-row li#wmd-link-button span, .wmd-button-row li#wmd-quote-button span, .wmd-button-row li#wmd-code-button span, .wmd-button-row li#wmd-image-button span, .wmd-button-row li#wmd-olist-button span, .wmd-button-row li#wmd-ulist-button span, .wmd-button-row li#wmd-heading-button span, .wmd-button-row li#wmd-hr-button span, .wmd-button-row li#wmd-more-button span, .wmd-button-row li#wmd-undo-button span, .wmd-button-row li#wmd-redo-button span, .wmd-button-row li#wmd-fullscreen-button span, .wmd-button-row li#wmd-exit-fullscreen-button span { background-image: url("../img/editor.png?_=ce98884"); background-repeat: no-repeat; }

.editor-editor-bold { background-position: 0 0; width: 20px; height: 20px; }

.editor-editor-code { background-position: 0 -20px; width: 20px; height: 20px; }

.editor-editor-exit-fullscreen { background-position: 0 -40px; width: 20px; height: 20px; }

.editor-editor-fullscreen { background-position: 0 -60px; width: 20px; height: 20px; }

.editor-editor-heading { background-position: 0 -80px; width: 20px; height: 20px; }

.editor-editor-hr { background-position: 0 -100px; width: 20px; height: 20px; }

.editor-editor-image { background-position: 0 -120px; width: 20px; height: 20px; }

.editor-editor-italic { background-position: 0 -140px; width: 20px; height: 20px; }

.editor-editor-link { background-position: 0 -160px; width: 20px; height: 20px; }

.editor-editor-more { background-position: 0 -180px; width: 20px; height: 20px; }

.editor-editor-olist { background-position: 0 -200px; width: 20px; height: 20px; }

.editor-editor-quote { background-position: 0 -220px; width: 20px; height: 20px; }

.editor-editor-redo { background-position: 0 -240px; width: 20px; height: 20px; }

.editor-editor-ulist { background-position: 0 -260px; width: 20px; height: 20px; }

.editor-editor-undo { background-position: 0 -280px; width: 20px; height: 20px; }

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) { .editor-sprite, .editor-editor-bold, .editor-editor-code, .editor-editor-exit-fullscreen, .editor-editor-fullscreen, .editor-editor-heading, .editor-editor-hr, .editor-editor-image, .editor-editor-italic, .editor-editor-link, .editor-editor-more, .editor-editor-olist, .editor-editor-quote, .editor-editor-redo, .editor-editor-ulist, .editor-editor-undo, .wmd-button-row li#wmd-bold-button span, .wmd-button-row li#wmd-italic-button span, .wmd-button-row li#wmd-link-button span, .wmd-button-row li#wmd-quote-button span, .wmd-button-row li#wmd-code-button span, .wmd-button-row li#wmd-image-button span, .wmd-button-row li#wmd-olist-button span, .wmd-button-row li#wmd-ulist-button span, .wmd-button-row li#wmd-heading-button span, .wmd-button-row li#wmd-hr-button span, .wmd-button-row li#wmd-more-button span, .wmd-button-row li#wmd-undo-button span, .wmd-button-row li#wmd-redo-button span, .wmd-button-row li#wmd-fullscreen-button span, .wmd-button-row li#wmd-exit-fullscreen-button span { background-image: url("../img/<EMAIL>?_=f3643ed"); background-repeat: no-repeat; background-size: 20px 300px; }
  .editor-editor-bold { background-position: 0 0; }
  .editor-editor-code { background-position: 0 -20px; }
  .editor-editor-exit-fullscreen { background-position: 0 -40px; }
  .editor-editor-fullscreen { background-position: 0 -60px; }
  .editor-editor-heading { background-position: 0 -80px; }
  .editor-editor-hr { background-position: 0 -100px; }
  .editor-editor-image { background-position: 0 -120px; }
  .editor-editor-italic { background-position: 0 -140px; }
  .editor-editor-link { background-position: 0 -160px; }
  .editor-editor-more { background-position: 0 -180px; }
  .editor-editor-olist { background-position: 0 -200px; }
  .editor-editor-quote { background-position: 0 -220px; }
  .editor-editor-redo { background-position: 0 -240px; }
  .editor-editor-ulist { background-position: 0 -260px; }
  .editor-editor-undo { background-position: 0 -280px; }
  .wmd-button-row li#wmd-bold-button span { background-position: 0 0; }
  .wmd-button-row li#wmd-italic-button span { background-position: 0 -140px; }
  .wmd-button-row li#wmd-link-button span { background-position: 0 -160px; }
  .wmd-button-row li#wmd-quote-button span { background-position: 0 -220px; }
  .wmd-button-row li#wmd-code-button span { background-position: 0 -20px; }
  .wmd-button-row li#wmd-image-button span { background-position: 0 -120px; }
  .wmd-button-row li#wmd-olist-button span { background-position: 0 -200px; }
  .wmd-button-row li#wmd-ulist-button span { background-position: 0 -260px; }
  .wmd-button-row li#wmd-heading-button span { background-position: 0 -80px; }
  .wmd-button-row li#wmd-hr-button span { background-position: 0 -100px; }
  .wmd-button-row li#wmd-more-button span { background-position: 0 -180px; }
  .wmd-button-row li#wmd-undo-button span { background-position: 0 -280px; }
  .wmd-button-row li#wmd-redo-button span { background-position: 0 -240px; }
  .wmd-button-row li#wmd-fullscreen-button span { background-position: 0 -60px; }
  .wmd-button-row li#wmd-exit-fullscreen-button span { background-position: 0 -40px; } }

.wmd-button-row { list-style: none; margin: 0; padding: 0; height: 26px; line-height: 1; }

.wmd-button-row li { display: inline-block; margin-right: 4px; padding: 3px; cursor: pointer; vertical-align: middle; border-radius: 2px; }

.wmd-button-row li:hover { background-color: #E9E9E6; }

.wmd-button-row li.wmd-spacer { height: 20px; margin: 0 10px 0 6px; padding: 0; width: 1px; background: #E9E9E6; cursor: default; }

.wmd-button-row li span { display: block; width: 20px; height: 20px; }

.wmd-button-row li#wmd-bold-button span { background-position: 0 0; }

.wmd-button-row li#wmd-italic-button span { background-position: 0 -140px; }

.wmd-button-row li#wmd-link-button span { background-position: 0 -160px; }

.wmd-button-row li#wmd-quote-button span { background-position: 0 -220px; }

.wmd-button-row li#wmd-code-button span { background-position: 0 -20px; }

.wmd-button-row li#wmd-image-button span { background-position: 0 -120px; }

.wmd-button-row li#wmd-olist-button span { background-position: 0 -200px; }

.wmd-button-row li#wmd-ulist-button span { background-position: 0 -260px; }

.wmd-button-row li#wmd-heading-button span { background-position: 0 -80px; }

.wmd-button-row li#wmd-hr-button span { background-position: 0 -100px; }

.wmd-button-row li#wmd-more-button span { background-position: 0 -180px; }

.wmd-button-row li#wmd-undo-button span { background-position: 0 -280px; }

.wmd-button-row li#wmd-redo-button span { background-position: 0 -240px; }

.wmd-button-row li#wmd-fullscreen-button span { background-position: 0 -60px; }

.wmd-button-row li#wmd-exit-fullscreen-button span { background-position: 0 -40px; }

#btn-cancel-preview { display: none; }

.wmd-edittab { float: right; margin-top: 3px; font-size: .92857em; }

.wmd-edittab a { display: inline-block; padding: 0 8px; margin-left: 5px; height: 20px; line-height: 20px; }

.wmd-edittab a:hover { text-decoration: none; }

.wmd-edittab a.active { background: #E9E9E6; color: #999; }

.wmd-hidetab { display: none; }

.wmd-visualhide { visibility: hidden; }

/* 对话框 */
.wmd-prompt-background { background-color: #000; }

.wmd-prompt-dialog { position: fixed; z-index: 1001; top: 50%; left: 50%; margin-top: -95px; margin-left: -200px; padding: 20px; width: 360px; background: #F6F6F3; }

.wmd-prompt-dialog p { margin: 0 0 5px; }

.wmd-prompt-dialog form { margin-top: 10px; }

.wmd-prompt-dialog input[type="text"] { margin-bottom: 10px; width: 100%; }

.wmd-prompt-dialog button { margin-right: 10px; }

/* 预览 */
#wmd-preview { background: #FFF; margin: 1em 0; padding: 0 15px; word-wrap: break-word; overflow: auto; border-radius: 2px; }

#wmd-preview img { max-width: 100%; }

#wmd-preview code, #wmd-preview pre { padding: 2px 4px; background: #DDD; font-size: 14px; }

#wmd-preview code { color: #C13; }

#wmd-preview pre { padding: 1em; }

#wmd-preview pre code { padding: 0; color: #444; }

#wmd-preview blockquote { margin: 1em 1.5em; padding-left: 1.5em; border-left: 4px solid #E9E9E6; color: #777; }

#wmd-preview hr { margin: 2em auto; width: 100px; border: 1px solid #E9E9E6; border-width: 2px 0 0 0; }

#wmd-preview .summary:after { display: block; margin: 2em 0; background: #FFF9E8; color: #cf9900; font-size: .85714em; text-align: center; content: "- more -"; }

#wmd-preview .embed { border: 1px solid #ccc; height: 40px; overflow: hidden; line-height: 40px; text-align: center; font-size: 12px; color: #777; }

#wmd-preview table { width: 100%; }

#wmd-preview table th, #wmd-preview table td { border: 1px solid #DDD; padding: 5px 8px; word-break: break-all; }

#wmd-preview table th { background: #EEE; }

#wmd-preview span.line { display: inline; height: 1px; line-height: 1px; position: absolute; }

#wmd-preview .focus, #wmd-preview .focus * { background-color: rgba(255, 230, 0, 0.5) !important; }

/* 上传面板动画效果 */
@keyframes fullscreen-upload { 0% { opacity: 0; }
  100% { opacity: 1; } }

/* 编辑器全屏 */
.fullscreen #wmd-button-bar, .fullscreen #text, .fullscreen #wmd-preview, .fullscreen .submit { position: absolute; top: 0; width: 50%; background: #FFF; z-index: 999; box-sizing: border-box; border-radius: 0; }

.fullscreen #wmd-button-bar { left: 0; padding: 13px 20px; border-bottom: 1px solid #F3F3F0; z-index: 1000; }

.fullscreen #text { top: 53px; left: 0; padding: 20px; border: none; outline: none; }

.fullscreen #wmd-preview { top: 53px; right: 0; margin: 0; padding: 5px 20px; border: none; border-left: 1px solid #F3F3F0; background: #F6F6F3; overflow: auto; }

.fullscreen .submit { right: 0; margin: 0; padding: 10px 20px; border-bottom: 1px solid #F3F3F0; }

.fullscreen #upload-panel { -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.225); box-shadow: 0 4px 16px rgba(0, 0, 0, 0.225); border-style: solid; }

.fullscreen #tab-files { position: absolute; top: 52px; right: 0; width: 280px; z-index: 1001; animation: fullscreen-upload 0.5s; }

.fullscreen .wmd-edittab, .fullscreen .typecho-post-option, .fullscreen .title, .fullscreen .url-slug, .fullscreen .typecho-page-title, .fullscreen .typecho-head-nav, .fullscreen .message { display: none; }

.fullscreen .wmd-hidetab { display: block; }

.fullscreen .wmd-visualhide, .fullscreen #btn-fullscreen-upload { visibility: visible; }

.preview .submit { width: 100%; background: #FFFFDD; }

.preview #wmd-button-bar, .preview #wmd-preview, .preview #text, .preview #upload-panel, .preview #tab-files, .preview #btn-preview, .preview #btn-fullscreen-upload, .preview #auto-save-message { display: none; }

.preview .preview-frame { width: 100%; border: 0; padding: 0; margin: 0; background: #fff; z-index: 999; position: absolute; top: 53px; left: 0; }

.preview .preview-loading { background-image: url(../img/ajax-loader.gif); background-position: center; background-repeat: no-repeat; }

.preview #btn-cancel-preview { display: inline-block; }

@media (max-width: 575px) { #wmd-spacer2, #wmd-olist-button, #wmd-ulist-button, #wmd-heading-button, #wmd-hr-button, #wmd-more-button, #wmd-spacer3, #wmd-undo-button, #wmd-redo-button, #wmd-spacer4, #wmd-fullscreen-button, #wmd-exit-fullscreen-button, #btn-fullscreen-upload { display: none; } }

/** Jquery Timepicker */
#ui-datepicker-div { display: none; margin-top: -1px; padding: 10px; border: 1px solid #D9D9D6; background: #FFF; }

.ui-timepicker-div .ui-widget-header { margin-bottom: 8px; }

.ui-timepicker-div dl { text-align: left; }

.ui-timepicker-div dl dt { float: left; clear: left; }

.ui-timepicker-div dl dd { margin: 0 0 10px 40%; }

.ui-tpicker-grid-label { background: none; border: none; margin: 0; padding: 0; }

#ui-datepicker-div .ui-datepicker-header { margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #EEE; }

#ui-datepicker-div .ui-datepicker-prev { float: left; cursor: pointer; }

#ui-datepicker-div .ui-datepicker-next { float: right; cursor: pointer; }

#ui-datepicker-div .ui-datepicker-title { font-weight: bold; text-align: center; }

#ui-datepicker-div .ui-datepicker-calendar th { line-height: 24px; }

#ui-datepicker-div .ui-datepicker-calendar a { display: block; width: 30px; background-color: #F3F3F0; line-height: 24px; text-align: center; }

#ui-datepicker-div .ui-datepicker-calendar a:hover { background-color: #E9E9E6; text-decoration: none; }

#ui-datepicker-div .ui-datepicker-today a { background-color: #E9E9E6; color: #444; }

#ui-datepicker-div .ui-datepicker-current-day a { background-color: #467B96 !important; color: #FFF; }

#ui-datepicker-div .ui-timepicker-div { margin-top: 20px; border-top: 1px solid #EEE; }

#ui-datepicker-div .ui-slider { position: relative; margin-top: 18px; border: 1px solid #E9E9E6; background-color: #F6F6F3; height: 4px; }

#ui-datepicker-div .ui-slider .ui-slider-handle { position: absolute; top: -7px; margin-left: -5px; z-index: 2; width: 10px; height: 16px; background-color: #467B96; }

#ui-datepicker-div .ui-datepicker-buttonpane { padding-top: 10px; border-top: 1px solid #EEE; }

#ui-datepicker-div .ui-datepicker-current, #ui-datepicker-div .ui-datepicker-close { float: left; }

#ui-datepicker-div .ui-datepicker-close { float: right; }

.ui-effects-transfer { border: 2px dotted #ccc; }

/** Jquery Tokeninput */
ul.token-input-list { list-style: none; margin: 0; padding: 0 4px; min-height: 32px; border: 1px solid #D9D9D6; cursor: text; z-index: 999; background-color: #FFF; clear: left; border-radius: 2px; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }

ul.token-input-list li { margin: 4px 0; }

ul.token-input-list li input { padding: 0; border: 0; width: 100%; -webkit-appearance: caret; }

li.token-input-token { padding: 0 6px; height: 27px; line-height: 27px; background-color: #F3F3F0; cursor: default; font-size: .92857em; text-align: right; white-space: nowrap; }

li.token-input-token p { float: left; display: inline; margin: 0; }

li.token-input-token span { color: #BBB; font-weight: bold; cursor: pointer; }

li.token-input-selected-token { background-color: #E9E9E6; }

li.token-input-input-token { padding: 0 4px; }

div.token-input-dropdown { position: absolute; background-color: #FFF; overflow: hidden; border: 1px solid #D9D9D6; border-top-width: 0; cursor: default; z-index: 1; font-size: .92857em; }

div.token-input-dropdown p { margin: 0; padding: 5px 10px; color: #777; font-weight: bold; }

div.token-input-dropdown ul { list-style: none; margin: 0; padding: 0; }

div.token-input-dropdown ul li { padding: 4px 10px; background-color: #FFF; }

div.token-input-dropdown ul li.token-input-dropdown-item { background-color: #FFF; }

div.token-input-dropdown ul li em { font-style: normal; }

div.token-input-dropdown ul li.token-input-selected-dropdown-item { background-color: #467B96; color: #FFF; }

/* Hide from both screenreaders and browsers: h5bp.com/u */
.hidden { display: none; }

/* Hide only visually, but have it available for screenreaders: h5bp.com/v */
.sr-only { border: 0; height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; }

/* Extends the .sr-only class to allow the element to be focusable when navigated to via the keyboard: h5bp.com/p */
.sr-only.focusable:active, .sr-only.focusable:focus { clip: auto; height: auto; margin: 0; overflow: visible; position: static; width: auto; }

/* Hide visually and from screenreaders, but maintain layout */
.invisible { visibility: hidden; }
