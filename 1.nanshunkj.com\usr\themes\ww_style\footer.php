<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>

<!-- 页脚 -->
<footer class="footer">
    <div class="footer-container">
        <div class="footer-links">
            <a href="#" class="footer-link">关于我们</a>
            <a href="#" class="footer-link">联系方式</a>
            <a href="#" class="footer-link">隐私政策</a>
            <a href="#" class="footer-link">使用条款</a>
        </div>
        <div class="copyright">
            © <?php echo date('Y'); ?> WW Style. 保留所有权利
        </div>
    </div>
</footer>

<script>
    // 导航栏滚动效果
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // 模拟文章加载
    let isLoading = false;
    window.addEventListener('scroll', function() {
        if (isLoading) return;

        const scrollHeight = document.documentElement.scrollHeight;
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        const clientHeight = document.documentElement.clientHeight;

        if (scrollTop + clientHeight > scrollHeight - 200) {
            loadMoreArticles();
        }
    });

    function loadMoreArticles() {
        const loader = document.querySelector('.loader');
        if (!loader) return;

        loader.style.display = 'block';
        isLoading = true;

        // 模拟加载延迟
        setTimeout(() => {
            // 这里应该是AJAX请求获取更多文章
            // 为了演示，我们只是隐藏加载器
            loader.style.display = 'none';
            isLoading = false;
        }, 1500);
    }

    // 筛选按钮切换
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // 这里可以添加实际的筛选逻辑
        });
    });

    // 搜索框控制
    const searchBtn = document.querySelector('.search-btn');
    const searchOverlay = document.getElementById('search-overlay');
    const searchClose = document.getElementById('search-close');
    const searchInput = document.querySelector('.search-input');

    if (searchBtn && searchOverlay && searchClose) {
        searchBtn.addEventListener('click', function() {
            searchOverlay.style.display = 'flex';
            setTimeout(() => {
                searchInput.focus();
            }, 100);
        });

        searchClose.addEventListener('click', function() {
            searchOverlay.style.display = 'none';
        });

        // 点击搜索框外部关闭搜索框
        searchOverlay.addEventListener('click', function(e) {
            if (e.target === searchOverlay) {
                searchOverlay.style.display = 'none';
            }
        });

        // ESC键关闭搜索框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && searchOverlay.style.display === 'flex') {
                searchOverlay.style.display = 'none';
            }
        });
    }

    // 页面加载指示器
    const loadingIndicator = document.getElementById('page-loading-indicator');
    if (loadingIndicator) {
        window.addEventListener('load', function() {
            setTimeout(function() {
                loadingIndicator.classList.remove('active');
            }, 500);
        });
    }

    // 回到顶部按钮
    const scrollTopBtn = document.getElementById('scroll-top');
    if (scrollTopBtn) {
        scrollTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                scrollTopBtn.classList.add('active');
            } else {
                scrollTopBtn.classList.remove('active');
            }
        });
    }

    // 文章分享功能
    const shareBtn = document.getElementById('share-btn');
    const shareModal = document.getElementById('share-modal');
    const shareClose = document.getElementById('share-close');

    if (shareBtn && shareModal) {
        shareBtn.addEventListener('click', function() {
            shareModal.classList.add('active');
        });

        if (shareClose) {
            shareClose.addEventListener('click', function() {
                shareModal.classList.remove('active');
            });

            // 点击模态框外部关闭
            shareModal.addEventListener('click', function(e) {
                if (e.target === shareModal) {
                    shareModal.classList.remove('active');
                }
            });
        }

        // 分享到社交媒体
        const shareItems = document.querySelectorAll('.share-item');
        shareItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                const url = encodeURIComponent(window.location.href);
                const title = encodeURIComponent(document.title);

                if (type === 'weibo') {
                    window.open('https://service.weibo.com/share/share.php?url=' + url + '&title=' + title);
                } else if (type === 'qq') {
                    window.open('https://connect.qq.com/widget/shareqq/index.html?url=' + url + '&title=' + title);
                } else if (type === 'twitter') {
                    window.open('https://twitter.com/intent/tweet?url=' + url + '&text=' + title);
                } else if (type === 'facebook') {
                    window.open('https://www.facebook.com/sharer/sharer.php?u=' + url);
                } else if (type === 'wechat') {
                    alert('请打开微信，使用"扫一扫"，扫描当前页面网址');
                } else if (type === 'copy') {
                    // 复制链接
                    const tempInput = document.createElement('input');
                    tempInput.value = window.location.href;
                    document.body.appendChild(tempInput);
                    tempInput.select();
                    document.execCommand('copy');
                    document.body.removeChild(tempInput);

                    // 显示提示
                    showToast('链接已复制到剪贴板');
                }

                // 关闭分享弹窗
                shareModal.classList.remove('active');
            });
        });
    }

    // 显示Toast消息的函数
    function showToast(message, duration = 3000) {
        let toast = document.getElementById('toast-message');

        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'toast-message';
            document.body.appendChild(toast);
        }

        toast.textContent = message;
        toast.classList.add('active');

        setTimeout(function() {
            toast.classList.remove('active');
        }, duration);
    }

    // 将showToast函数暴露给全局
    window.showToast = showToast;
</script>

<!-- 页面加载指示器 -->
<div id="page-loading-indicator" class="active">
    <div class="loading-spinner"></div>
</div>

<!-- 回到顶部按钮 -->
<div id="scroll-top">
    <i class="ri-arrow-up-line"></i>
</div>

<?php $this->footer(); ?>
</body>
</html>
