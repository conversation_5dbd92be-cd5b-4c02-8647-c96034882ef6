import tkinter as tk
from tkinter import messagebox

def main():
    # 创建主窗口
    root = tk.Tk()
    root.title("Hello World 程序")
    root.geometry("400x300")
    root.resizable(False, False)
    
    # 设置窗口居中
    root.eval('tk::PlaceWindow . center')
    
    # 创建标签显示Hello World
    label = tk.Label(
        root, 
        text="Hello World!", 
        font=("Arial", 24, "bold"),
        fg="blue",
        bg="lightgray"
    )
    label.pack(expand=True)
    
    # 创建按钮
    def show_message():
        messagebox.showinfo("消息", "Hello World from Python!")
    
    button = tk.Button(
        root,
        text="点击我",
        font=("Arial", 12),
        command=show_message,
        bg="lightblue",
        width=10,
        height=2
    )
    button.pack(pady=20)
    
    # 创建退出按钮
    exit_button = tk.But<PERSON>(
        root,
        text="退出",
        font=("Arial", 12),
        command=root.quit,
        bg="lightcoral",
        width=10,
        height=2
    )
    exit_button.pack(pady=10)
    
    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main() 