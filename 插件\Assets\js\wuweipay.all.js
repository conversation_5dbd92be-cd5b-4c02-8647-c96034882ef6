/*!
 ** @Description: 吴畏支付脚本
 ** @Author: 吴畏
 ** @AuthorUri: https://8ww.fun
 ** @Date: 2023-05-01 01:19:31
 ** @LastEditTime: 2023-05-15 21:53:39
 ** @Copyright (c) 2023 by 吴畏 All Rights Reserved.
 */
jQuery(document).ready(function($){
    var ajaxUrl = wuwei.ajaxurl,
        verifyStatus = false,
        postId,
        payType,
        orderNum,
        qrpay,
        webIn;
    
    // 点击支付按钮
    $('.wuweiPayBtn').click(function(){
        postId = $(this).data('id');
        payType = $(this).data('type');
        
        $.post({
            url: ajaxUrl,
            data: {
                action: 'wuwei_buy_page',
                postID: postId,
                payType: payType
            },
            success: function(response){
                initBuyPage(response);
                initPayment(response);
                
                // 绑定关闭事件
                $('.wuweiPayClose').on('click', closePayment);
                
                // 绑定支付方式选择事件
                $('.wuweiPayOption').click(function(){
                    var payMethod = $(this).data('paymethod');
                    $('.wuweiPayQrcode').empty();
                    showLoading(response);
                    processPayment(payMethod);
                });
            }
        });
    });
    
    // 初始化购买页面
    function initBuyPage(response){
        var alipayHtml = '',
            wechatHtml = '';
        
        // 支付宝选项
        if (response.methods && response.methods.alipay) {
            alipayHtml = '<div class="wuweiPayOption" data-paymethod="alipay">' +
                         '<i class="wuweiAlipay"></i>' +
                         '<span class="payName">' + response.methods.alipay.name + '</span>' +
                         '</div>';
        }
        
        // 微信支付选项
        if (response.methods && response.methods.wechat) {
            wechatHtml = '<div class="wuweiPayOption" data-paymethod="wechat">' +
                         '<i class="wuweiWechat"></i>' +
                         '<span class="payName">' + response.methods.wechat.name + '</span>' +
                         '</div>';
        }
        
        // 创建支付弹窗
        var paymentHtml = '<div class="wuweiPayPopupLayer">' +
                          '<div class="wuweiPayModal">' +
                          '<div class="wuweiPayHeader">' +
                          '<div class="wuweiPayTitle">' + response.title1 + '</div>' +
                          '<button type="button" class="wuweiPayClose" aria-label="' + response.close + '">' +
                          '<i class="wuweiClose"></i>' +
                          '</button>' +
                          '</div>' +
                          '<div class="wuweiPayContent">' +
                          '<div class="wuweiPaymentOptions">' + alipayHtml + wechatHtml + '</div>' +
                          '<div class="wuweiPayLoad"></div>' +
                          '<div class="wuweiPayQrcode"></div>' +
                          '</div>' +
                          '<div class="wuweiPayFooter"></div>' +
                          '</div>' +
                          '</div>';
        
        $('.wuweiPayBtn').after(paymentHtml).fadeIn('slow');
    }
    
    // 初始化支付
    function initPayment(response){
        $('.wuweiPayLoad').html('').hide();
        $('.wuweiPayQrcode').html('').hide();
        $('.wuweiPayFooter').html('').hide();
        $('.wuweiPayBack').html(response.title1).removeClass('wuweiPayBack').addClass('wuweiPayTitle');
        $('.wuweiPayPopupLayer').fadeIn('slow');
        $('.wuweiPaymentOptions').slideDown('slow');
    }
    
    // 显示加载中
    function showLoading(response){
        var loadingHtml = '<div class="wuweiGoogleLoad animation-6">' +
                          '<div class="shape shape1"></div>' +
                          '<div class="shape shape2"></div>' +
                          '<div class="shape shape3"></div>' +
                          '<div class="shape shape4"></div>' +
                          '</div>';
        
        $('.wuweiPayLoad').html(loadingHtml).slideDown();
        $('.wuweiPayTitle,.wuweiPayBack').html(response.title2);
        $('.wuweiPaymentOptions').hide();
        $('.wuweiPayQrcode').hide();
    }
    
    // 移除加载中
    function removeLoading(){
        $('.wuweiPayLoad').html('').hide();
    }
    
    // 处理支付
    function processPayment(payMethod){
        $.post({
            url: ajaxUrl,
            data: {
                action: 'wuwei_pay_page',
                postID: postId,
                payType: payType,
                payMethod: payMethod
            },
            success: function(response){
                orderNum = response.order_id;
                qrpay = response.qrpay;
                webIn = response.webIn;
                
                if (response.code === 0) {
                    if (response.qrpay) {
                        if (response.webIn) {
                            showPayQrcode(response);
                            if (!verifyStatus) {
                                verifyPayment();
                                verifyStatus = true;
                            }
                        }
                    } else {
                        window.location.href = response.pay_url;
                    }
                } else {
                    alert(response.msg || '支付请求失败');
                    closePayment();
                }
            }
        });
    }
    
    // 显示支付二维码
    function showPayQrcode(response){
        removeLoading();
        $('.wuweiPayTitle').html(response.title3).removeClass('wuweiPayTitle').addClass('wuweiPayBack').fadeIn();
        $('.wuweiPaymentOptions').hide();
        
        var qrcodeHtml = '<div class="wuweiQrcode">' +
                         '<img src="' + response.qr_code + '" alt="支付二维码">' +
                         '<p>请使用' + (response.pay_type === 'alipay' ? '支付宝' : '微信') + '扫码支付</p>' +
                         '<p>￥' + response.amount + '</p>' +
                         '</div>';
        
        $('.wuweiPayQrcode').html(qrcodeHtml).slideDown();
        
        $('.wuweiPayBack').click(function(){
            backToPaymentOptions(response);
        });
    }
    
    // 返回支付方式选择
    function backToPaymentOptions(response){
        $('.wuweiPayBack').html(response.title1).removeClass('wuweiPayBack').addClass('wuweiPayTitle');
        $('.wuweiPayQrcode').html('').hide();
        $('.wuweiPaymentOptions').slideDown();
    }
    
    // 关闭支付
    function closePayment(){
        $('.wuweiPayPopupLayer').fadeOut('slow', function(){
            $(this).remove();
        });
    }
    
    // 验证支付状态
    function verifyPayment(){
        if (orderNum) {
            $.post({
                url: ajaxUrl,
                data: {
                    action: 'wuwei_status_page',
                    orderNum: orderNum
                },
                dataType: 'json',
                success: function(response){
                    if (response.status === 'paid') {
                        if (qrpay) {
                            if (!webIn) {
                                $('.wuweiQrShow .wuweiQrcode img').after(
                                    '<div class="wuweiOverlay">' +
                                    '<div class="content">' +
                                    '<i class="wuweiPaySuccess"></i> 支付成功！' +
                                    '<div style="padding-top:4px;">即将转跳~</div>' +
                                    '</div>' +
                                    '</div>'
                                );
                            } else {
                                $('.wuweiPayQrcode .wuweiQrcode img').after(
                                    '<div class="wuweiOverlay">' +
                                    '<div class="content">' +
                                    '<i class="wuweiPaySuccess"></i> 支付成功！' +
                                    '<div style="padding-top:4px;">即将刷新转跳~</div>' +
                                    '</div>' +
                                    '</div>'
                                );
                            }
                        }
                        
                        setTimeout(function(){
                            if (response.type === 'vip') {
                                window.location.href = '/user';
                            } else {
                                var currentUrl = window.location.href;
                                window.location.replace(currentUrl);
                            }
                        }, 1500);
                    } else {
                        setTimeout(function(){
                            verifyPayment();
                        }, 2000);
                    }
                }
            });
        }
    }
});
