<?php if (!defined("__TYPECHO_ROOT_DIR__")) exit(); ?>
<!DOCTYPE html>
<html lang="zh">
<?php $this->need("header.php"); ?>
<body class="jasmine-body">
<div class="jasmine-container grid grid-cols-12">
    <?php $this->need("component/sidebar-left.php"); ?>
    <div class="flex col-span-12 lg:col-span-8 flex-col lg:border-x-2 border-stone-100 dark:border-neutral-600 lg:pt-0 lg:px-6 pb-10 px-3 sm:px-4">
        <?php $this->need("component/menu.php"); ?>
        
        <?php $this->need("component/slider.php"); ?>
        
        <div class="flex flex-col px-2">
            <?php while($this->next()): ?>
                <?php 
                    $isSticky = isset($this->isSticky) && $this->isSticky;
                    $config = Typecho_Widget::widget('Widget_Options')->plugin('WuweiPin');
                    $style_type = isset($config->style_type) ? $config->style_type : 'border';
                ?>
                <article class="post-item <?php echo $isSticky ? 'sticky-post' : ''; ?>">
                    
                    <div class="flex items-start gap-4 p-4">
                        <div class="post-thumb shrink-0">
                            <?php if($this->fields->thumbnail): ?>
                                <img src="<?php echo $this->fields->thumbnail; ?>" 
                                     alt="<?php $this->title() ?>" 
                                     loading="lazy">
                            <?php else: ?>
                                <img src="<?php echo getRandomThumbnail(); ?>" 
                                     alt="<?php $this->title() ?>" 
                                     loading="lazy">
                            <?php endif; ?>
                        </div>
                        
                        <div class="flex-1 min-w-0 flex flex-col justify-between px-3">
                            <h2 class="text-base sm:text-lg font-semibold">
                                <a href="<?php $this->permalink() ?>" 
                                   class="text-gray-900 dark:text-gray-100 hover:text-[#3273dc] dark:hover:text-[#3273dc] transition-colors">
                                    <?php if($isSticky): ?>
                                        <?php 
                                            $config = Typecho_Widget::widget('Widget_Options')->plugin('WuweiPin');
                                            $style_type = isset($config->style_type) ? $config->style_type : 'ribbon';
                                            $sticky_text = isset($config->sticky_text) ? $config->sticky_text : '置顶';
                                            
                                            if ($style_type === 'corner') {
                                                echo '<span class="sticky-mark" data-text="' . $sticky_text . '"></span>';
                                            } else {
                                                echo '<span class="sticky-mark">' . $sticky_text . '</span>';
                                            }
                                        ?>
                                    <?php endif; ?>
                                    <?php $this->title(); ?>
                                </a>
                            </h2>
                            
                            <div class="text-base text-gray-500 dark:text-gray-400">
                                <?php echo strip_tags($this->content); ?>
                            </div>
                            
                            <div class="flex flex-wrap sm:flex-nowrap items-center justify-between text-sm text-gray-400">
                                <div class="flex items-center gap-4 w-full sm:w-auto">
                                    <span class="flex items-center gap-1.5 whitespace-nowrap min-w-0 flex-shrink">
                                        <iconify-icon icon="ri:folder-line" class="text-lg flex-shrink-0"></iconify-icon>
                                        <span class="truncate"><?php $this->category(','); ?></span>
                                    </span>
                                    <span class="flex items-center gap-1.5 whitespace-nowrap flex-shrink-0">
                                        <iconify-icon icon="ri:time-line" class="text-lg"></iconify-icon>
                                        <?php $this->date('m-d'); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </article>
            <?php endwhile; ?>
        </div>
        
        <?php $this->need("component/paging.php"); ?>
    </div>
    <div class="hidden lg:col-span-3 lg:block" id="sidebar-right">
        <?php $this->need("component/sidebar.php"); ?>
    </div>
</div>

<style>
/* 文章列表卡片基础样式 */
.post-item {
    height: 120px !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    width: 100% !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    overflow: visible !important;
    border-radius: 12px !important;
    background: #fff !important;
    margin-top: 8px !important;
}

/* 卡片悬停效果 */
.post-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

/* 标题悬停效果 */
.post-item h2 a {
    transition: color 0.3s ease !important;
}

.post-item:hover h2 a {
    color: #3273dc !important;
}

/* 缩略图样式 */
.post-thumb {
    width: 74px !important;
    height: 74px !important;
    min-width: 74px !important;
    min-height: 74px !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

.post-thumb img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 8px !important;
}

/* 深色模式适配 */
.dark .post-item {
    background: #161829 !important;
}

.dark .post-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
}

/* 右侧内容区域样式 */
.post-item .flex-1 {
    height: 74px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    padding: 0 !important;
    width: calc(100% - 90px) !important;
}

/* 标题样式 */
.post-item h2 {
    height: 28px !important;
    line-height: 28px !important;
    font-size: clamp(15px, 1.5vw, 16px) !important;
    font-weight: 600 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
}

.post-item h2 a {
    display: block !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    color: #333 !important;
    max-width: 100% !important;
}

/* 摘要样式 */
.post-item .text-gray-500 {
    height: 24px !important;
    line-height: 24px !important;
    font-size: clamp(14px, 1.2vw, 15px) !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    color: #666 !important;
    width: 100% !important;
}

/* 文章信息样式 */
.post-item .flex-wrap {
    height: 22px !important;
    line-height: 22px !important;
    font-size: clamp(12px, 1vw, 13px) !important;
    margin: 0 !important;
    padding: 0 !important;
    color: #999 !important;
    width: 100% !important;
}

/* 深色模式适配 */
.dark .post-item h2 a {
    color: #e5e7eb !important;
}

.dark .post-item .text-gray-500 {
    color: #9ca3af !important;
}

.dark .post-item .flex-wrap {
    color: #6b7280 !important;
}

/* 文章卡片载入动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 优化卡片内容过渡效果 */
.post-item > div {
    transition: transform 0.2s ease !important;
}

.post-item:hover > div {
    transform: scale(0.99) !important;
}

/* 添加文字渐变效果 */
.post-excerpt {
    -webkit-mask-image: linear-gradient(to right, black 80%, transparent 98%) !important;
    mask-image: linear-gradient(to right, black 80%, transparent 98%) !important;
}

/* 优化交互反馈 */
.post-item {
    cursor: pointer !important;
    user-select: none !important;
    -webkit-tap-highlight-color: transparent !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

/* 点击效果 */
.post-item:active {
    transform: scale(0.99) !important;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .post-item {
        animation-duration: 0.2s;
    }
}
</style>

<?php $this->need("footer.php"); ?>
</body>
</html>
