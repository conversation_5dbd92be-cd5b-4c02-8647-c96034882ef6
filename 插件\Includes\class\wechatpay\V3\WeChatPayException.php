<?php ?><?php // /* *****  * @自助授权：https://sq.shilin.studio  * @自助下单：https://order.shilin.studio  * @Author: 诗林工作室  * @AuthorUri: https://shilin.studio  * @Date: 2025-03-30 20:49:54  * @LastEditTime: 2025-03-30 05:48:38  * Copyright (c) 2024 by Shilin Studio All Rights Reserved. */ - by 贝塔PHP加密|https://sg.bt58.vip ?><?php
if(!function_exists('sg_load')){$__v=phpversion();$__x=explode('.',$__v);$__v2=$__x[0].'.'.(int)$__x[1];$__u=strtolower(substr(php_uname(),0,3));$__ts=(@constant('PHP_ZTS') || @constant('ZEND_THREAD_SAFE')?'ts':'');$__f=$__f0='ixed.'.$__v2.$__ts.'.'.$__u;$__ff=$__ff0='ixed.'.$__v2.'.'.(int)$__x[2].$__ts.'.'.$__u;$__ed=@ini_get('extension_dir');$__e=$__e0=@realpath($__ed);$__dl=function_exists('dl') && function_exists('file_exists') && @ini_get('enable_dl') && !@ini_get('safe_mode');if($__dl && $__e && version_compare($__v,'5.2.5','<') && function_exists('getcwd') && function_exists('dirname')){$__d=$__d0=getcwd();if(@$__d[1]==':') {$__d=str_replace('\\','/',substr($__d,2));$__e=str_replace('\\','/',substr($__e,2));}$__e.=($__h=str_repeat('/..',substr_count($__e,'/')));$__f='/ixed/'.$__f0;$__ff='/ixed/'.$__ff0;while(!file_exists($__e.$__d.$__ff) && !file_exists($__e.$__d.$__f) && strlen($__d)>1){$__d=dirname($__d);}if(file_exists($__e.$__d.$__ff)) dl($__h.$__d.$__ff); else if(file_exists($__e.$__d.$__f)) dl($__h.$__d.$__f);}if(!function_exists('sg_load') && $__dl && $__e0){if(file_exists($__e0.'/'.$__ff0)) dl($__ff0); else if(file_exists($__e0.'/'.$__f0)) dl($__f0);}if(!function_exists('sg_load')){$__ixedurl='https://www.sourceguardian.com/loaders/download.php?php_v='.urlencode($__v).'&php_ts='.($__ts?'1':'0').'&php_is='.@constant('PHP_INT_SIZE').'&os_s='.urlencode(php_uname('s')).'&os_r='.urlencode(php_uname('r')).'&os_m='.urlencode(php_uname('m'));$__sapi=php_sapi_name();if(!$__e0) $__e0=$__ed;if(function_exists('php_ini_loaded_file')) $__ini=php_ini_loaded_file(); else $__ini='php.ini';if((substr($__sapi,0,3)=='cgi')||($__sapi=='cli')||($__sapi=='embed')){$__msg="\nPHP script '".__FILE__."' is protected by SourceGuardian and requires a SourceGuardian loader '".$__f0."' to be installed.\n\n1) Download the required loader '".$__f0."' from the SourceGuardian site: ".$__ixedurl."\n2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="\n3) Edit ".$__ini." and add 'extension=".$__f0."' directive";}}$__msg.="\n\n";}else{$__msg="<html><body>PHP script '".__FILE__."' is protected by <a href=\"https://www.sourceguardian.com/\">SourceGuardian</a> and requires a SourceGuardian loader '".$__f0."' to be installed.<br><br>1) <a href=\"".$__ixedurl."\" target=\"_blank\">Click here</a> to download the required '".$__f0."' loader from the SourceGuardian site<br>2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="<br>3) Edit ".$__ini." and add 'extension=".$__f0."' directive<br>4) Restart the web server";}}$__msg.="</body></html>";}die($__msg);exit();}}return sg_load('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');
