/* 评论区基础样式 */
.respond {
    margin-bottom: 2rem;
}

/* 评论输入框样式 */
#textarea-user {
    min-height: 120px;
    resize: vertical;
    transition: all 0.3s ease;
    font-size: 0.9375rem;
    line-height: 1.6;
}

#textarea-user:focus {
    border-color: #000;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
}

.dark #textarea-user:focus {
    border-color: #fff;
    box-shadow: 0 0 0 1px rgba(255,255,255,0.1);
}

/* 评论按钮样式 */
.comment-submit-btn {
    background-color: #000;
    color: #fff;
    transition: all 0.3s ease;
}

.comment-submit-btn:hover {
    background-color: #333;
    transform: translateY(-1px);
}

/* 表情面板样式 */
.emoji-panel {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 320px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 1000;
    display: none;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.1);
}

.dark .emoji-panel {
    background: #1a1a1a;
    border-color: rgba(255,255,255,0.1);
}

.emoji-tabs {
    display: flex;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 8px;
    gap: 8px;
    overflow-x: auto;
}

.dark .emoji-tabs {
    border-color: rgba(255,255,255,0.1);
}

.emoji-tab {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s;
    color: #666;
}

.dark .emoji-tab {
    color: #999;
}

.emoji-tab.active {
    background: #000;
    color: #fff;
}

.dark .emoji-tab.active {
    background: #fff;
    color: #000;
}

.emoji-content {
    height: 200px;
    overflow-y: auto;
    padding: 12px;
}

.emoji-category {
    display: none;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
}

.emoji-category.active {
    display: grid;
}

.emoji-item {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s;
    padding: 6px;
}

.emoji-item:hover {
    background: rgba(0,0,0,0.05);
    transform: scale(1.1);
}

.dark .emoji-item:hover {
    background: rgba(255,255,255,0.05);
}

/* 登录对话框样式 */
.login-dialog {
    position: fixed;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0,0,0,0.3);
    backdrop-filter: blur(8px);
    z-index: 99999;
    padding: 1rem;
}

.login-dialog > div {
    width: 100%;
    max-width: 360px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    animation: dialog-slide-in 0.3s ease;
    border: 1px solid rgba(0,0,0,0.1);
}

.dark .login-dialog > div {
    background: #1a1a1a;
    border-color: rgba(255,255,255,0.1);
}

.login-dialog .close-dialog-btn {
    opacity: 0.6;
    transition: all 0.2s;
}

.login-dialog .close-dialog-btn:hover {
    opacity: 1;
    transform: rotate(90deg);
}

/* 表单输入框样式 */
.login-dialog input[type="text"],
.login-dialog input[type="password"],
.login-dialog input[type="email"] {
    height: 42px;
    font-size: 0.9375rem;
    border-width: 1px;
    transition: all 0.3s ease;
}

.login-dialog input[type="text"]:focus,
.login-dialog input[type="password"]:focus,
.login-dialog input[type="email"]:focus {
    border-color: #000;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
}

.dark .login-dialog input[type="text"]:focus,
.dark .login-dialog input[type="password"]:focus,
.dark .login-dialog input[type="email"]:focus {
    border-color: #fff;
    box-shadow: 0 0 0 1px rgba(255,255,255,0.1);
}

/* 按钮样式 */
.login-dialog button[type="submit"],
.quick-login-btn,
.quick-register-btn {
    font-weight: 500;
    letter-spacing: 0.025em;
    transition: all 0.3s ease;
}

.login-dialog button[type="submit"]:hover,
.quick-login-btn:hover,
.quick-register-btn:hover {
    transform: translateY(-1px);
}

/* 动画效果 */
@keyframes dialog-slide-in {
    from {
        opacity: 0;
        transform: translateY(-12px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式适配 */
@media (max-width: 480px) {
    .login-dialog {
        padding: 1rem;
    }
    
    .login-dialog > div {
        max-width: 100%;
    }
    
    .emoji-panel {
        width: calc(100% - 40px);
        right: 20px;
    }
    
    .emoji-category {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .emoji-item {
        font-size: 20px;
    }
}

/* 评论列表样式 */
.comment-list {
    margin-top: 2rem;
}

.comment-body {
    transition: all 0.3s ease;
}

.comment-body:hover {
    background: rgba(0,0,0,0.02);
}

.dark .comment-body:hover {
    background: rgba(255,255,255,0.02);
}

.author-name {
    font-weight: 500;
    color: #000;
}

.dark .author-name {
    color: #fff;
}

.comment-content {
    margin-top: 0.5rem;
    line-height: 1.6;
}

/* 评论回复按钮 */
.comments-reply {
    opacity: 0.8;
    transition: all 0.2s;
}

.comments-reply:hover {
    opacity: 1;
    transform: translateY(-1px);
}

/* 评论分页 */
.page-navigator {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 2rem;
}

.page-navigator li {
    display: inline-flex;
}

.page-navigator a {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    background: rgba(0,0,0,0.05);
    color: #666;
    transition: all 0.2s;
}

.dark .page-navigator a {
    background: rgba(255,255,255,0.05);
    color: #999;
}

.page-navigator a:hover {
    background: rgba(0,0,0,0.1);
    color: #000;
}

.dark .page-navigator a:hover {
    background: rgba(255,255,255,0.1);
    color: #fff;
}

.page-navigator .current a {
    background: #000;
    color: #fff;
}

.dark .page-navigator .current a {
    background: #fff;
    color: #000;
} 