// 阅读进度条功能
window.addEventListener('scroll', function() {
    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
    const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrolled = (winScroll / height) * 100;
    document.getElementById("reading-progress").style.width = scrolled + "%";
});

// 社交分享功能
function shareToWeibo() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`http://service.weibo.com/share/share.php?url=${url}&title=${title}`);
}

function shareToWechat() {
    // 检查是否在微信浏览器中
    const isWechat = /MicroMessenger/i.test(navigator.userAgent);
    
    if (isWechat) {
        // 如果在微信内，直接调用微信分享
        alert('请点击右上角菜单进行分享');
    } else {
        const modal = document.getElementById('wechat-share-modal');
        const qrcodeContainer = document.getElementById('wechat-qrcode');
        modal.style.display = 'flex';
        
        try {
            // 清空之前的内容
            qrcodeContainer.innerHTML = '';
            
            // 创建canvas元素
            const canvas = document.createElement('canvas');
            qrcodeContainer.appendChild(canvas);
            
            // 使用QRious生成二维码
            new QRious({
                element: canvas,
                value: window.location.href,
                size: 200,
                backgroundAlpha: 1,
                foreground: '#000000',
                background: '#ffffff',
                level: 'H', // 高纠错级别
                padding: 10
            });

            // 确保二维码容器可见
            qrcodeContainer.style.display = 'block';
            qrcodeContainer.style.background = '#fff';
            qrcodeContainer.style.padding = '10px';
            qrcodeContainer.style.borderRadius = '4px';
            
            // 设置canvas样式
            canvas.style.display = 'block';
            canvas.style.margin = '0 auto';
            
        } catch (error) {
            console.error('QR code generation failed:', error);
            qrcodeContainer.innerHTML = '<p style="color: #666;">二维码生成失败，请刷新页面重试</p>';
        }
    }
}

function closeWechatModal() {
    const modal = document.getElementById('wechat-share-modal');
    modal.style.display = 'none';
}

// 点击模态框背景关闭
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('wechat-share-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeWechatModal();
        }
    });
});

function copyLink() {
    navigator.clipboard.writeText(window.location.href)
        .then(() => {
            // 创建一个临时提示元素
            const toast = document.createElement('div');
            toast.className = 'copy-toast';
            toast.textContent = '链接已复制到剪贴板';
            document.body.appendChild(toast);
            
            // 2秒后移除提示
            setTimeout(() => {
                toast.remove();
            }, 2000);
        })
        .catch(() => alert('复制失败，请手动复制'));
} 