/*
** 后台面板JS
** <AUTHOR>
** <AUTHOR> https://shilin.studio
*/
(function ($, window, document, undefined) {
    jQuery(document).ready(function ($) {
        /*
        ** 页面滚动监听
        ** <AUTHOR>
        ** <AUTHOR> https://shilin.studio
        */
        $(document).ready(function () {
            $(window).scroll(function () {
                var scrollPosition = $(this).scrollTop();
                var shilinAdminPanelHeight = $('.shilin-header').height();
                if (scrollPosition > shilinAdminPanelHeight) {
                    // 当滚动条位置大于20时执行的操作
                    // $('.shilinFooterM').addClass('shilinHeaderScroll');
                    // $('.shilinFooterM').css('display', 'block');
                } else {
                    // 当滚动条位置小于等于20时执行的操作
                    // $('.shilinFooterM').css('display', 'none');
                    // $('.shilinHeader').removeAttr('style');
                }
            });
        });

        /*
        ** 左侧菜单显隐
        ** <AUTHOR>
        ** <AUTHOR> https://shilin.studio
        */
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var $ = layui.$;
            var isShow = true;

            //头部事件
            util.event('lay-header-event', {
                menuLeft: function (othis) { // 左侧菜单事件
                    //选择出所有的span，并判断是不是hidden,用来隐藏icon后面的span里面的内容，而icon不隐藏
                    $('.shilinAdminPanel h4,.shilinList cite').each(function () {
                        if ($(this).is(':hidden')) {
                            $(this).css('display', 'inline');
                        } else {
                            $(this).css('display', 'none');

                        };
                    });
                    // $('.shilinSideMenu span').each(function () {//同上
                    //     if ($(this).is(':hidden')) {
                    //         $(this).css('display','inline');
                    //     } else {
                    //         $(this).css('display','none');
                    //     };
                    // });

                    //判断isshow的状态
                    if (isShow) {//收起来
                        $('.shilinLeftMenu,.shilinList').width(60); //设置宽度
                        // $('.layui-logo').width(60);
                        // $('.kit-side-fold i').css('margin-right', '70%');  //修改图标的位置
                        $('.shilinSS').removeClass("shilin-shousuo2");  //修改图标
                        $('.shilinSS').addClass("shilin-zhankai2");  //修改图标
                        $('.layui-nav-more').css('right', '10px');
                        $('.shilinSideMenu li').removeClass("layui-nav-itemed");//取消选中二级导航栏
                        //将footer和body的宽度修改
                        $('.shilinMI').css('padding-left', 0 + 'px');
                        // $('.layui-layout-left').css('left', 60 + 'px');
                        // $('.shilinBody').css('left', 60 + 'px');
                        // $('.shilinFooter').css('left', 60 + 'px');
                        //将二级导航栏隐藏
                        // $('dd span').each(function () {
                        //   $(this).hide();
                        // });
                        //修改标志位
                        isShow = false;
                    } else {//展开来
                        $('.shilinLeftMenu,.shilinList').width(200);
                        // $('.layui-logo').width(200);
                        //  $('.kit-side-fold i').css('margin-right', '10%');
                        // $('.layui-layout-left').css('left', 200 + 'px');
                        $('.shilinMI').css('padding-left', 50 + 'px');
                        $('.layui-nav-more').removeAttr('style');
                        // $('.shilinBody').css('left', 200 + 'px');
                        // $('.shilinFooter').css('left', 200 + 'px');
                        $('dd span').each(function () {
                            $(this).show();
                        });
                        $('.shilinSS').addClass("shilin-shousuo2");  //修改图标
                        $('.shilinSS').removeClass("shilin-zhankai2");  //修改图标
                        isShow = true;
                    };
                },
                // menuRight: function () {  // 右侧菜单事件
                //     layer.open({
                //         type: 1,
                //         title: '更多',
                //         content: '<div style="padding: 15px;">处理右侧面板的操作</div>',
                //         area: ['260px', '100%'],
                //         offset: 'rt', // 右上角
                //         anim: 'slideLeft', // 从右侧抽屉滑出
                //         shadeClose: true,
                //         scrollbar: false
                //     });
                // }
            });
        });

        /*
        ** 移动菜单功能
        ** <AUTHOR>
        ** <AUTHOR> https://shilin.studio
        */
        var _wid = $(window).width();

        $(document).on('click', '.shilin-expand-all', function (e) {
            $(".shilin-expand-all i").toggleClass("shilin-zhankai2 shilin-shousuo2");//类名切换，有则删除，无则添加
        })

        $(document).on('click', '.shilin-menu', function (e) {
            $('.shilin-nav-options').toggleClass('show');
            $(".shilin-mNav a i").toggleClass("shilin-zhankai2 shilin-shousuo2");//类名切换，有则删除，无则添加
        })

        if (_wid < 783) {
            $('.shilin-nav-options').minitouch({
                direction: 'right',
                onEnd: function (e) {
                    e.removeClass('show');
                }
            })
        }

        /*
        ** 添加/移除默认属性
        ** <AUTHOR>
        ** <AUTHOR> https://shilin.studio
        */
        // 移除数字值的上下条增减按钮
        // $("input[type='number']").removeAttr("max").removeAttr("min");

    });
})(jQuery, window, document);