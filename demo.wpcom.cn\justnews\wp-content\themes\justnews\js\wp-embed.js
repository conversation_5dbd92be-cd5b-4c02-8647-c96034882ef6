!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";!function(e,t){t.querySelector&&e.addEventListener&&"undefined"!=typeof URL&&(e.wp=e.wp||{},e.wp.receiveEmbedMessage||(e.wp.receiveEmbedMessage=function(n){var s=n.data;if((s||s.secret||s.message||s.value)&&!/[^a-zA-Z0-9]/.test(s.secret)){var r,a,o,i,c,d=t.querySelectorAll('iframe[data-secret="'+s.secret+'"]'),l=t.querySelectorAll('blockquote[data-secret="'+s.secret+'"]'),u=new RegExp("^https?:$","i");for(r=0;r<l.length;r++)l[r].style.display="none";for(r=0;r<d.length;r++)a=d[r],n.source===a.contentWindow&&(a.removeAttribute("style"),"height"===s.message?((o=parseInt(s.value,10))>1e3?o=1e3:~~o<80&&(o=80),a.height=o):"link"===s.message&&(i=new URL(a.getAttribute("src")),c=new URL(s.value),u.test(c.protocol)&&c.host===i.host&&t.activeElement===a&&(e.top.location.href=s.value)))}},e.addEventListener("message",e.wp.receiveEmbedMessage,!1),t.addEventListener("DOMContentLoaded",(function(){var e,n,s,r=t.querySelectorAll("iframe.wp-embedded-content");for(e=0;e<r.length;e++)(s=(n=r[e]).getAttribute("data-secret"))||(s=Math.random().toString(36).substring(2,12),n.src+="#?secret="+s,n.setAttribute("data-secret",s)),n.contentWindow.postMessage({message:"ready",secret:s},"*")}),!1)))}(window,document)}));
