class ResourceManager {
    constructor() {
        this.init();
    }

    init() {
        this.initTagEvents();
        this.initCardEffects();
    }

    initTagEvents() {
        document.querySelectorAll('.category-tags a').forEach(tag => {
            tag.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleTagClick(tag);
            });
        });
    }

    initCardEffects() {
        document.querySelectorAll('.resource-item').forEach((card, index) => {
            // 添加入场动画延迟
            card.style.animationDelay = `${index * 0.05}s`;
            
            // 添加悬浮效果
            card.addEventListener('mouseenter', () => {
                card.classList.add('hover');
            });
            
            card.addEventListener('mouseleave', () => {
                card.classList.remove('hover');
            });
        });
    }

    handleTagClick(tag) {
        const tagGroup = tag.closest('.category-tags');
        const section = tag.closest('.category-section');
        const tagText = tag.textContent.trim();

        // 更新标签状态
        tagGroup.querySelectorAll('a').forEach(t => t.classList.remove('active'));
        tag.classList.add('active');

        // 筛选资源
        this.filterResources(section, tagText);
    }

    filterResources(section, tagText) {
        const items = section.querySelectorAll('.resource-item');
        
        items.forEach(item => {
            const name = item.querySelector('.resource-name').textContent;
            const desc = item.querySelector('.resource-desc').textContent;
            const shouldShow = tagText === '全部' || 
                             name.includes(tagText) || 
                             desc.includes(tagText);

            item.classList.toggle('hidden', !shouldShow);
            
            if (shouldShow) {
                // 重置动画
                item.style.animation = 'none';
                item.offsetHeight; // 触发重排
                item.style.animation = null;
            }
        });
    }
}

// 等待 DOM 加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new ResourceManager());
} else {
    new ResourceManager();
}