/*!
 ** @Description: 诗林支付脚本
 ** @Author: 诗林工作室
 ** @AuthorUri: https://shilin.studio
 ** @Date: 2024-10-20 01:19:31
 ** @LastEditTime: 2025-03-29 21:53:39
 ** @Copyright (c) 2024 by Shilin Studio All Rights Reserved.
 */
jQuery(document).ready(function($){
    var a=shilin.ajaxurl,s=window.sr=ScrollReveal(),c=new ClipboardJS('.shilinCopy'),v=!1,p,t,y,w,r={},q,i,u,m,g,d,n,l;
    $('.shilinPayBtn').click(function(){
        p=$(this).data('id');
        t=$(this).data('type');
        $.post({
            url:a,
            data:{action:'shilin_buy_page',postID:p,shilinType:t},
            success:function(e){
                shilinBuyPageInit(e);
                shilinPayInitial(e);
                $('.shilinPayClose').on('click',shilinPayClose);
                $('.shilinPayOption').click(function(){
                    var m=$(this).data('paymethod');
                    $('.shilinPayQrcode').empty();
                    shilinLoadding(e);
                    shilinPayMode(m)
                })
            }
        })
    });
    $('.selectpay').click(function(){
        p=$('.shilin-payment').data('payid');
        t=$('.shilin-payment').data('paytype');
        r=$('.shilin-payment').data('paynum');
        m=$(this).data('payment');
        l='<div class="shilinLoveful"><ul><li></li><li></li><li></li><li></li><li></li><li></li><li></li><li></li><li></li></ul></div><footer><div class="copyright"><p class="footCopyright">Copyright &copy; '+new Date().getFullYear()+' <a href="https://shilin.studio/shilinpay/" target="_blank">ShilinPay</a> All Rights Reserved By <a href="https://shilin.studio/" target="_blank">Shilin.Studio</a>.</p></div></footer>';
        $('body').css('background','#03002Exxx').html(l).slideDown();
        if(m=='alipay'){
            changePayParam('alipay');
            $('.shilin-payment .wechatpay').removeClass('wechat');
            $('.shilin-payment .alipay').addClass('ali')
        }else if(m=='wechatpay'){
            changePayParam('wechatpay');
            $('.shilin-payment .alipay').removeClass('ali');
            $('.shilin-payment .wechatpay').addClass('wechat')
        }
        $.post({
            url:a,
            data:{action:'shilin_pay_page',postID:p,shilinType:t,shilinMethod:m},
            success:function(e){
                r=e.orderNum;
                q=e.qrpay;
                i=e.webIn;
                if(!v){
                    shilinPayVerify();
                    v=!0
                }
            }
        })
    });
    $('.shilinCopy').click(function(){
        layui.use(function(){
            var e=layui.layer,t=layui.util,$=layui.$;
            t.on('lay-on',{
                "copy-msg":function(){
                    c.on('success',function(e){
                        e.clearSelection();
                        e.msg('复制成功！',{icon:1,time:800})
                    })
                },
                "copy-msg-light":function(){
                    e.msg('复制成功！',{icon:0},function(){})
                },
                'copy-tips':function(){
                    c.on('success',function(e){
                        e.clearSelection();
                        e.tips('复制成功！',this,{tips:[1,'#16b777']})
                    })
                }
            })
        })
    });
    function shilinBuyPageInit(e){
        var a,w,p;
        if(e.alipayOn){
            a='<div class="shilinPayOption" data-paymethod="alipay"><i class="shilinAlipay"></i><span class="payName">'+e.alipayName+'</span></div>'
        }else{
            a=''
        }
        if(e.wechatpayOn){
            w='<div class="shilinPayOption" data-paymethod="wechatpay"><i class="shilinWechatpay"></i><span class="payName">'+e.wechatpayName+'</span></div>'
        }else{
            w=''
        }
        if(e.paypalOn){
            p='<div class="shilinPayOption" data-paymethod="paypal"><i class="shilinPaypal"></i><span class="payName">'+e.paypalName+'</span></div>'
        }else{
            p=''
        }
        var n='<div class="shilinPayPopupLayer"><div class="shilinPayModal"><div class="shilinPayHeader"><div class="shilinPayTitle">'+e.title1+'</div><button type="button" class="shilinPayClose" aria-label="'+e.close+'"><i class="shilinClose"></i></button></div><div class="shilinPayContent"><div class="shilinPaymentOptions">'+a+w+p+'</div><div class="shilinPayLoad"></div><div class="shilinPayQrcode"></div></div><div class="shilinPayFooter"></div></div></div>';
        $('.shilinPayBtn').after(n).fadeIn('slow')
    }
    function shilinPayInitial(e){
        $('.shilinPayLoad').html('').hide();
        $('.shilinPayQrcode').html('').hide();
        $('.shilinPayFooter').html('').hide();
        $('.shilinPayBack').html(e.title1).removeClass('shilinPayBack').addClass('shilinPayTitle');
        $('.shilinPayPopupLayer').fadeIn('slow');
        $('.shilinPaymentOptions').slideDown('slow')
    }
    function shilinLoadding(e){
        l='<div class="shilinGoogleLoad animation-6"><div class="shape shape1"></div><div class="shape shape2"></div><div class="shape shape3"></div><div class="shape shape4"></div></div>';
        $('.shilinPayLoad').html(l).slideDown();
        $('.shilinPayTitle,.shilinPayBack').html(e.title2);
        $('.shilinPaymentOptions').hide();
        $('.shilinPayQrcode').hide()
    }
    function shilinLoaddingRemove(){
        $('.shilinPayLoad').html('');
        $('.shilinPayLoad').hide()
    }
    function shilinPayClose(){
        $('.shilinPayPopupLayer').fadeOut('slow')
    }
    function shilinPayQrcodePage(e){
        n='<div class="shilinPayLogo"><img src="'+e.imgUrl+'/payitem/'+e.tag+'-logo.png"/></div><div class="shilinQrcode">'+e.qrcode+'</div><div class="title" style="color:'+e.footbgcolor+';margin:0;">'+e.name+e.qrDesc+' <b style="font-size:25px;color:'+e.footbgcolor+'">'+e.price+'</b> '+e.unit+'</div>';
        var t=e.desc;
        $('.shilinPayQrcode').html(n).slideDown();
        $('.shilinPayFooter').html(t).slideDown().css('background',e.footbgcolor)
    }
    function shilinPayBack(e){
        $('.shilinPayQrcode').empty().slideUp();
        $('.shilinPayFooter').empty().slideUp();
        $('.shilinPayBack').html(e.title1).removeClass('shilinPayBack').addClass('shilinPayTitle');
        $('.shilinPaymentOptions').slideDown()
    }
    function shilinPayMode(m){
        $.post({
            url:a,
            data:{action:'shilin_pay_page',postID:p,shilinType:t,shilinMethod:m},
            success:function(e){
                r=e.orderNum;
                q=e.qrpay;
                i=e.webIn;
                if(e.source=='epay'){
                    if(e.qrpay){
                        if(e.webIn){
                            shilinPayIn(e);
                            if(!v){
                                shilinPayVerify();
                                v=!0
                            }
                        }
                    }else{
                        window.location.href=e.url
                    }
                }else{
                    if(e.client=='pc'){
                        if(e.qrpay){
                            if(e.webIn){
                                shilinPayIn(e);
                                if(!v){
                                    shilinPayVerify();
                                    v=!0
                                }
                            }
                        }else{
                            if(m=='alipay'){
                                $('.shilinPayQrcode').html(e.url).slideDown()
                            }else{
                                window.location.href=e.url
                            }
                        }
                    }else{
                        if(m=='alipay'){
                            $('.shilinPayQrcode').html(e.url).slideDown()
                        }else{
                            window.location.href=e.url
                        }
                    }
                }
                r=e.orderNum;
                q=e.qrpay;
                i=e.webIn;
                if(!v){
                    shilinPayVerify();
                    v=!0
                }
            }
        })
    }
    function shilinPayIn(e){
        shilinLoaddingRemove();
        $('.shilinPayTitle').html(e.title3).removeClass('shilinPayTitle').addClass('shilinPayBack').fadeIn();
        $('.shilinPaymentOptions').hide();
        shilinPayQrcodePage(e);
        $('.shilinPayBack').click(function(){
            shilinPayBack(e)
        })
    }
    function shilinPayVerify(){
        if(r){
            $.post({
                url:a,
                data:{action:'shilin_status_page',orderNum:r},
                dataType:'json',
                success:function(e){
                    if(e.status=='paid'){
                        if(q){
                            if(!i){
                                $('.shilinQrShow .shilinQrcode img').after('<div class="shilinOverlay"><div class="content"><i class="shilinPaySuccess"></i> 支付成功！<div style="padding-top:4px;">即将转跳~</div></div></div>')
                            }else{
                                $('.shilinPayQrcode .shilinQrcode img').after('<div class="shilinOverlay"><div class="content"><i class="shilinPaySuccess"></i> 支付成功！<div style="padding-top:4px;">即将刷新转跳~</div></div></div>')
                            }
                        }
                        setTimeout(function(){
                            if(e.type=='vip'){
                                window.location.href='/user'
                            }else{
                                w=window.location.href;
                                window.location.replace(w)
                            }
                        },300)
                    }else{
                        setTimeout(function(){
                            shilinPayVerify()
                        },2000)
                    }
                }
            })
        }
    }
    function shilinPayJumpSwitch(e){
        if(m=='alipay'){
            g='data:img/png;base64,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';
            $('.shilin-payment .wechatpay').removeClass('wechat');
            $('.shilin-payment .alipay').addClass('ali')
        }else if(m=='wechatpay'){
            g='data:img/png;base64,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';
            $('.shilin-payment .alipay').removeClass('ali');
            $('.shilin-payment .wechatpay').addClass('wechat')
        }
    }
});
    