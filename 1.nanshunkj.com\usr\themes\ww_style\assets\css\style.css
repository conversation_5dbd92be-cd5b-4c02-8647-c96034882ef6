/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background-color: #0e0e0e;
    color: #fff;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: inherit;
}

ul, li {
    list-style: none;
}

/* 顶部导航栏 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5%;
    transition: all 0.3s ease;
}

.header.scrolled {
    height: 50px;
    background-color: rgba(0, 0, 0, 0.95);
}

.logo {
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(to right, #FE2C55, #8134AF);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: 1px;
}

.nav-links {
    display: flex;
    gap: 25px;
}

.nav-links a {
    position: relative;
    font-size: 15px;
    color: #ccc;
    transition: color 0.3s;
}

.nav-links a:hover, .nav-links a.active {
    color: #FE2C55;
}

.nav-links a.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 15px;
    height: 2px;
    background-color: #FE2C55;
    border-radius: 2px;
}

.user-area {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-btn {
    color: #ccc;
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s;
}

.search-btn:hover {
    color: #FE2C55;
}

.login-btn {
    background: linear-gradient(to right, #FE2C55, #8134AF);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
    animation: pulse 2s infinite;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(254, 44, 85, 0.4);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(254, 44, 85, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(254, 44, 85, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(254, 44, 85, 0);
    }
}

/* 用户下拉菜单 */
.user-dropdown {
    position: relative;
    cursor: pointer;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #FE2C55;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 180px;
    background-color: #191919;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    padding: 10px 0;
    margin-top: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s;
    z-index: 1001;
}

.user-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: #ccc;
    transition: all 0.3s;
}

.dropdown-item i {
    margin-right: 10px;
    font-size: 18px;
}

.dropdown-item:hover {
    background-color: rgba(254, 44, 85, 0.1);
    color: #FE2C55;
}

/* 移动端菜单按钮 */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    position: relative;
    cursor: pointer;
    z-index: 1002;
}

.menu-icon, .menu-icon:before, .menu-icon:after {
    display: block;
    width: 100%;
    height: 2px;
    background-color: #fff;
    position: absolute;
    transition: all 0.3s;
}

.menu-icon {
    top: 50%;
    transform: translateY(-50%);
}

.menu-icon:before {
    content: '';
    top: -8px;
}

.menu-icon:after {
    content: '';
    bottom: -8px;
}

.mobile-menu-toggle[aria-expanded="true"] .menu-icon {
    background-color: transparent;
}

.mobile-menu-toggle[aria-expanded="true"] .menu-icon:before {
    transform: rotate(45deg);
    top: 0;
}

.mobile-menu-toggle[aria-expanded="true"] .menu-icon:after {
    transform: rotate(-45deg);
    bottom: 0;
}

/* 主要内容区 */
.main-container {
    max-width: 1550px;
    margin: 70px auto 0;
    padding: 0 20px;
    display: flex;
    flex-direction: row;
    gap: 30px;
}



/* 文章流 */
.content-feed {
    flex: 1;
    width: calc(100% - 350px);
}

.feed-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    align-items: center;
}

.feed-title {
    font-size: 22px;
    font-weight: bold;
}

.feed-filters {
    display: flex;
    gap: 15px;
}

.filter-btn {
    background-color: #1f1f1f;
    color: #ccc;
    border: none;
    padding: 6px 15px;
    border-radius: 15px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-btn.active, .filter-btn:hover {
    background-color: #FE2C55;
    color: white;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.article-card {
    background-color: #191919;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s forwards;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.article-img {
    width: 100%;
    height: 160px;
    overflow: hidden;
    position: relative;
}

.article-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.article-card:hover .article-img img {
    transform: scale(1.05);
}

.premium-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(to right, #FE2C55, #8134AF);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.premium-item {
    color: #FE2C55;
}

/* 会员内容样式 */
.member-content-box {
    background-color: rgba(254, 44, 85, 0.05);
    border-left: 3px solid #FE2C55;
    padding: 15px;
    margin: 15px 0;
    border-radius: 0 8px 8px 0;
}

.member-content-locked {
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px dashed rgba(254, 44, 85, 0.5);
    border-radius: 8px;
    padding: 30px;
    margin: 15px 0;
    text-align: center;
}

.locked-icon {
    font-size: 40px;
    color: #FE2C55;
    margin-bottom: 15px;
}

.locked-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.locked-desc {
    color: #aaa;
    margin-bottom: 20px;
}

.locked-btn {
    margin-top: 15px;
}

/* 付费内容样式 */
.pay-content-unlocked {
    background-color: rgba(82, 196, 26, 0.05);
    border-left: 3px solid #52c41a;
    padding: 15px;
    margin: 15px 0;
    border-radius: 0 8px 8px 0;
}

.pay-content-locked {
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px dashed rgba(82, 196, 26, 0.5);
    border-radius: 8px;
    padding: 30px;
    margin: 15px 0;
    text-align: center;
}

.locked-price {
    font-size: 24px;
    font-weight: bold;
    color: #FE2C55;
    margin: 15px 0;
}

/* 高亮内容样式 */
.highlight-content {
    background: linear-gradient(to right, rgba(254, 44, 85, 0.1), rgba(129, 52, 175, 0.1));
    border-left: 3px solid #FE2C55;
    padding: 15px;
    margin: 15px 0;
    border-radius: 0 8px 8px 0;
}

/* 提示框样式 */
.tip-box {
    display: flex;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.tip-icon {
    font-size: 24px;
    margin-right: 15px;
}

.tip-content {
    flex: 1;
}

.tip-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.tip-body {
    color: #ccc;
}

.tip-info {
    border-left: 3px solid #1890ff;
}

.tip-info .tip-icon {
    color: #1890ff;
}

.tip-success {
    border-left: 3px solid #52c41a;
}

.tip-success .tip-icon {
    color: #52c41a;
}

.tip-warning {
    border-left: 3px solid #faad14;
}

.tip-warning .tip-icon {
    color: #faad14;
}

.tip-error {
    border-left: 3px solid #f5222d;
}

.tip-error .tip-icon {
    color: #f5222d;
}

.article-content {
    padding: 12px;
}

.article-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-excerpt {
    font-size: 13px;
    color: #aaa;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #777;
}

.article-author {
    display: flex;
    align-items: center;
    gap: 5px;
}

.author-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: cover;
}

.article-stats {
    display: flex;
    gap: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 3px;
}

.stat-item i {
    font-size: 14px;
}

/* 侧边栏 */
.sidebar {
    width: 320px;
    animation: fadeInRight 0.8s forwards;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.widget {
    background-color: #191919;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.widget-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    position: relative;
    padding-left: 12px;
}

.widget-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(to bottom, #FE2C55, #8134AF);
    border-radius: 2px;
}

.member-widget {
    background: #191919;
    border: 1px solid #333;
    text-align: center;
    padding: 20px;
}

.member-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

.member-desc {
    color: #aaa;
    font-size: 14px;
    margin-bottom: 20px;
}

.join-btn {
    background: linear-gradient(to right, #FE2C55, #8134AF);
    color: white;
    border: none;
    width: 100%;
    padding: 10px;
    border-radius: 20px;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.join-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(254, 44, 85, 0.4);
}

/* 热门推荐 */
.trending-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.trending-item {
    display: flex;
    gap: 12px;
    transition: all 0.3s;
}

.trending-item:hover {
    transform: translateY(-3px);
}

.trending-img {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.trending-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.trending-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.trending-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.trending-meta {
    display: flex;
    align-items: center;
    color: #777;
    font-size: 12px;
}

/* 标签云 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag {
    background-color: #2a2a2a;
    color: #aaa;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 13px;
    transition: all 0.3s;
}

.tag:hover {
    background-color: #FE2C55;
    color: white;
    transform: translateY(-2px);
}

/* 最新文章 */
.latest-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.latest-item {
    border-bottom: 1px solid #333;
    padding-bottom: 15px;
}

.latest-time {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #777;
    font-size: 12px;
    margin-bottom: 5px;
}

.latest-title {
    font-size: 14px;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.latest-title a:hover {
    color: #FE2C55;
}

/* 本周热门 */
.weekly-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.weekly-item {
    display: flex;
    gap: 10px;
    transition: all 0.3s;
}

.weekly-item:hover {
    transform: translateX(5px);
}

.weekly-rank {
    width: 22px;
    height: 22px;
    background-color: #2a2a2a;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    color: #aaa;
}

.weekly-item:nth-child(1) .weekly-rank {
    background-color: #FE2C55;
    color: white;
}

.weekly-item:nth-child(2) .weekly-rank {
    background-color: #FF6A00;
    color: white;
}

.weekly-item:nth-child(3) .weekly-rank {
    background-color: #FFB800;
    color: white;
}

.weekly-content {
    flex: 1;
}

.weekly-title {
    font-size: 14px;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.weekly-meta {
    display: flex;
    align-items: center;
    color: #777;
    font-size: 12px;
}

/* 微信群 */
.qrcode-container {
    display: flex;
    justify-content: center;
    padding: 10px 0;
}

.qrcode-img {
    width: 140px;
    height: 140px;
    background-color: white;
    padding: 5px;
    border-radius: 8px;
}

/* 主题购买 */
.theme-buy {
    padding: 10px 0;
}

.theme-info {
    text-align: center;
}

.theme-info p {
    margin-bottom: 10px;
    color: #aaa;
    font-size: 14px;
}

.buy-btn {
    display: inline-block;
    background: linear-gradient(to right, #FE2C55, #8134AF);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    margin-top: 10px;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.buy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(254, 44, 85, 0.4);
}

/* 加载更多 */
.loader {
    text-align: center;
    padding: 30px 0;
    display: none;
}

.loader-icon {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: #FE2C55;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 页脚 */
.footer {
    background-color: #191919;
    padding: 20px 0;
    text-align: center;
    margin-top: 50px;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 15px;
}

.footer-link {
    color: #aaa;
    font-size: 14px;
    transition: color 0.3s;
}

.footer-link:hover {
    color: #FE2C55;
}

.copyright {
    color: #777;
    font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .main-container {
        flex-direction: column;
    }

    .content-feed {
        width: 100%;
    }

    .sidebar {
        width: 100%;
    }

    .qrcode-container {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

@media (max-width: 1100px) {
    .articles-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .articles-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .header-inner {
        padding: 0 10px;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.95);
        flex-direction: column;
        padding: 20px;
        gap: 15px;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
        z-index: 999;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links a {
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        width: 100%;
    }

    .nav-links a:last-child {
        border-bottom: none;
    }

    .header.scrolled .nav-links {
        top: 50px;
    }

    .feed-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }
}

/* 搜索框 */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    display: none;
    justify-content: center;
    align-items: flex-start;
    padding-top: 100px;
    animation: fadeIn 0.3s;
}

.search-container {
    width: 100%;
    max-width: 600px;
}

.search-box {
    width: 100%;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: none;
    border-radius: 4px;
    background-color: #222;
    color: white;
    font-size: 14px;
    outline: none;
}

.search-submit {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    font-size: 16px;
    cursor: pointer;
}

.search-submit:hover {
    color: #FE2C55;
}

.search-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: #999;
    font-size: 20px;
    cursor: pointer;
}

.search-close:hover {
    color: #fff;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Toast消息 */
#toast-message {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 14px;
    z-index: 9999;
    opacity: 0;
    transition: all 0.3s;
}

#toast-message.active {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

/* 页面加载指示器 */
#page-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
}

#page-loading-indicator.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: #FE2C55;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 回到顶部按钮 */
#scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 40px;
    height: 40px;
    background-color: #FE2C55;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s;
    z-index: 999;
}

#scroll-top.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#scroll-top:hover {
    background-color: #8134AF;
    transform: translateY(-5px);
}

/* 文章内容页样式 */
.content-wrapper {
    display: flex;
    gap: 30px;
    margin-top: 20px;
}

.post-container {
    flex: 1;
    background-color: #191919;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.post-content {
    padding: 25px;
}

.post-header {
    margin-bottom: 20px;
}

.post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    color: #aaa;
    font-size: 14px;
}

.meta-category {
    color: #FE2C55;
}

.post-title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 15px;
    line-height: 1.4;
}

.post-desc {
    color: #aaa;
    font-size: 16px;
    margin-bottom: 20px;
    padding-left: 10px;
    border-left: 3px solid #FE2C55;
}

.post-thumb {
    width: 100%;
    max-height: 500px;
    overflow: hidden;
    margin-bottom: 25px;
    border-radius: 8px;
}

.post-thumb img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.post-body {
    font-size: 16px;
    line-height: 1.8;
    color: #eee;
}

.post-body p {
    margin-bottom: 20px;
}

.post-body h1, .post-body h2, .post-body h3, .post-body h4, .post-body h5, .post-body h6 {
    margin: 30px 0 15px;
    font-weight: bold;
}

.post-body h1 {
    font-size: 26px;
}

.post-body h2 {
    font-size: 24px;
}

.post-body h3 {
    font-size: 22px;
}

.post-body h4 {
    font-size: 20px;
}

.post-body h5 {
    font-size: 18px;
}

.post-body h6 {
    font-size: 16px;
}

.post-body img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 15px 0;
}

.post-body blockquote {
    border-left: 4px solid #FE2C55;
    padding: 10px 15px;
    margin: 20px 0;
    background-color: rgba(254, 44, 85, 0.1);
    color: #ccc;
}

.post-body code {
    background-color: #2a2a2a;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 14px;
}

.post-body pre {
    background-color: #2a2a2a;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 20px 0;
}

.post-body pre code {
    background-color: transparent;
    padding: 0;
}

.post-body a {
    color: #FE2C55;
    text-decoration: none;
    transition: all 0.3s;
}

.post-body a:hover {
    text-decoration: underline;
}

.post-body ul, .post-body ol {
    margin: 20px 0;
    padding-left: 20px;
}

.post-body li {
    margin-bottom: 10px;
}

.post-footer {
    margin-top: 40px;
    border-top: 1px solid #333;
    padding-top: 20px;
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.post-tags a {
    background-color: #2a2a2a;
    color: #aaa;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 13px;
    transition: all 0.3s;
}

.post-tags a:hover {
    background-color: #FE2C55;
    color: white;
    transform: translateY(-2px);
}

.post-actions {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.action-like, .action-share {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #aaa;
    cursor: pointer;
    transition: all 0.3s;
    padding: 8px 15px;
    border-radius: 20px;
    background-color: #2a2a2a;
}

.action-like:hover, .action-share:hover {
    background-color: #FE2C55;
    color: white;
}

.action-like.liked {
    background-color: #FE2C55;
    color: white;
}

.post-nav {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.post-nav-prev, .post-nav-next {
    max-width: 48%;
}

.post-nav a {
    display: block;
    padding: 10px 15px;
    background-color: #2a2a2a;
    border-radius: 8px;
    transition: all 0.3s;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.post-nav a:hover {
    background-color: #FE2C55;
    color: white;
}

.empty-nav {
    color: #555;
    padding: 10px 15px;
    background-color: #222;
    border-radius: 8px;
}

/* 作者信息 */
.author-info {
    display: flex;
    align-items: center;
    background-color: #222;
    border-radius: 12px;
    padding: 20px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.author-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
}

.author-data {
    flex: 1;
    min-width: 200px;
}

.author-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.author-desc {
    color: #aaa;
    font-size: 14px;
    margin-bottom: 10px;
}

.author-stats {
    display: flex;
    gap: 15px;
    color: #777;
    font-size: 13px;
}

.author-btn {
    background-color: #2a2a2a;
    color: #ccc;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    margin-left: auto;
}

.author-btn:hover {
    background-color: #FE2C55;
    color: white;
}

/* 相关文章 */
.related-posts {
    margin: 30px 0;
}

.related-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.related-item {
    display: flex;
    background-color: #222;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
}

.related-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.related-image {
    width: 100px;
    height: 70px;
    overflow: hidden;
    flex-shrink: 0;
}

.related-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-info {
    flex: 1;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.related-info .related-title {
    font-size: 14px;
    font-weight: normal;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-date {
    color: #777;
    font-size: 12px;
}

/* 会员内容预览 */
.content-preview {
    margin-bottom: 20px;
}

.premium-overlay {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    margin: 30px 0;
    border: 1px dashed #FE2C55;
}

.premium-info {
    max-width: 400px;
    margin: 0 auto;
}

.premium-icon {
    font-size: 50px;
    color: #FE2C55;
    margin-bottom: 20px;
}

.premium-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.premium-desc {
    color: #aaa;
    margin-bottom: 25px;
}

.premium-btn {
    display: inline-block;
    background: linear-gradient(to right, #FE2C55, #8134AF);
    color: white;
    padding: 10px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
}

.premium-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(254, 44, 85, 0.4);
}

/* 分享弹窗 */
.share-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    display: none;
    justify-content: center;
    align-items: center;
}

.share-modal.active {
    display: flex;
    animation: fadeIn 0.3s;
}

.share-content {
    background-color: #222;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    padding: 20px;
    position: relative;
}

.share-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.share-title {
    font-size: 18px;
    font-weight: bold;
}

.share-close {
    font-size: 20px;
    cursor: pointer;
    color: #aaa;
    transition: color 0.3s;
}

.share-close:hover {
    color: #FE2C55;
}

.share-body {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.share-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    border-radius: 8px;
    background-color: #2a2a2a;
    cursor: pointer;
    transition: all 0.3s;
}

.share-item:hover {
    background-color: #FE2C55;
    transform: translateY(-3px);
}

.share-item i {
    font-size: 24px;
}

/* 响应式调整 */
@media (max-width: 1100px) {
    .content-wrapper {
        flex-direction: column;
    }

    .post-container, .sidebar {
        width: 100%;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }
}
