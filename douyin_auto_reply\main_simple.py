#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音聊天自动回复工具 - 简化版主程序
即使没有完整依赖也能运行基础功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import random
from datetime import datetime

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SimpleAutoReply:
    """简化版自动回复工具"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_widgets()
        
        # 状态变量
        self.is_running = False
        self.message_count = 0
        self.reply_count = 0
        
        # 回复模板
        self.replies = {
            "你好": ["你好！很高兴收到您的消息😊", "嗨！有什么可以帮助您的吗？"],
            "咨询": ["感谢您的咨询，请详细说明您的需求", "我来为您详细解答"],
            "价格": ["关于价格问题，请稍等，我马上为您查询"],
            "购买": ["感谢您的购买意向，请告诉我具体需求"],
            "在吗": ["在的！有什么可以帮您？", "我在的，请说"],
            "default": ["收到您的消息了", "感谢您的留言"]
        }
        
        self.log("🎉 抖音聊天自动回复工具 MVP 启动成功!")
        self.log("📌 这是简化版本，用于演示核心功能")
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("抖音聊天自动回复工具 - MVP简化版")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
    
    def setup_variables(self):
        """设置变量"""
        self.status_var = tk.StringVar(value="未启动")
        self.douyin_status_var = tk.StringVar(value="模拟模式")
        self.message_count_var = tk.StringVar(value="0")
        self.reply_count_var = tk.StringVar(value="0")
    
    def setup_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, text="抖音聊天自动回复工具 MVP", 
                              font=("Arial", 18, "bold"), fg="blue")
        title_label.pack(pady=(0, 15))
        
        # 说明文字
        info_label = tk.Label(main_frame, 
                             text="简化版本 - 模拟自动回复功能演示\n实际使用需要完整安装依赖包", 
                             font=("Arial", 10), fg="gray")
        info_label.pack(pady=(0, 15))
        
        # 状态信息框架
        status_frame = ttk.LabelFrame(main_frame, text="📊 状态信息", padding="15")
        status_frame.pack(fill="x", pady=(0, 15))
        
        # 状态信息网格
        status_grid = ttk.Frame(status_frame)
        status_grid.pack(fill="x")
        
        # 状态标签
        ttk.Label(status_grid, text="运行状态:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky="w", padx=(0, 10))
        status_label = ttk.Label(status_grid, textvariable=self.status_var, 
                                foreground="red", font=("Arial", 10, "bold"))
        status_label.grid(row=0, column=1, sticky="w")
        
        ttk.Label(status_grid, text="抖音聊天:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky="w", padx=(0, 10))
        douyin_label = ttk.Label(status_grid, textvariable=self.douyin_status_var,
                                foreground="green", font=("Arial", 10, "bold"))
        douyin_label.grid(row=1, column=1, sticky="w")
        
        ttk.Label(status_grid, text="检测消息:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky="w", padx=(0, 10))
        ttk.Label(status_grid, textvariable=self.message_count_var, font=("Arial", 10)).grid(row=2, column=1, sticky="w")
        
        ttk.Label(status_grid, text="自动回复:", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky="w", padx=(0, 10))
        ttk.Label(status_grid, textvariable=self.reply_count_var, font=("Arial", 10)).grid(row=3, column=1, sticky="w")
        
        # 控制按钮框架
        control_frame = ttk.LabelFrame(main_frame, text="🎮 控制面板", padding="15")
        control_frame.pack(fill="x", pady=(0, 15))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        # 控制按钮
        self.start_button = ttk.Button(button_frame, text="🚀 启动监控", 
                                      command=self.start_demo, width=15)
        self.start_button.pack(side="left", padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ 停止监控", 
                                     command=self.stop_demo, state="disabled", width=15)
        self.stop_button.pack(side="left", padx=(0, 10))
        
        self.simulate_button = ttk.Button(button_frame, text="🎭 模拟消息", 
                                         command=self.simulate_message, width=15)
        self.simulate_button.pack(side="left")
        
        # 日志显示框架
        log_frame = ttk.LabelFrame(main_frame, text="📝 实时日志", padding="10")
        log_frame.pack(fill="both", expand=True)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, width=80, height=20, 
                                                 font=("Consolas", 9), wrap="word")
        self.log_text.pack(fill="both", expand=True, pady=(0, 10))
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.pack(fill="x")
        
        clear_button = ttk.Button(log_button_frame, text="🗑️ 清空日志", command=self.clear_log)
        clear_button.pack(side="right")
    
    def log(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = int(self.log_text.index(tk.END).split('.')[0])
        if lines > 500:
            self.log_text.delete(1.0, "100.0")
    
    def start_demo(self):
        """开始演示"""
        if self.is_running:
            self.log("⚠️ 演示已在运行中")
            return
        
        self.is_running = True
        self.status_var.set("运行中")
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        
        self.log("🚀 开始自动监控演示")
        self.log("💡 这是模拟模式，展示自动回复的基本流程")
        
        # 启动模拟线程
        demo_thread = threading.Thread(target=self.demo_loop, daemon=True)
        demo_thread.start()
    
    def stop_demo(self):
        """停止演示"""
        self.is_running = False
        self.status_var.set("已停止")
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        
        self.log("⏹️ 停止监控演示")
    
    def demo_loop(self):
        """演示循环"""
        self.log("🔄 监控循环启动")
        
        mock_messages = ["你好", "咨询", "价格多少", "在吗", "想购买", "客服"]
        
        try:
            while self.is_running:
                # 随机生成模拟消息
                if random.random() < 0.3:  # 30% 概率生成消息
                    message = random.choice(mock_messages)
                    self.process_mock_message(message)
                
                time.sleep(3)  # 等待3秒
                
        except Exception as e:
            self.log(f"❌ 演示循环出错: {e}")
        
        self.log("🔄 监控循环结束")
    
    def simulate_message(self):
        """手动模拟消息"""
        mock_messages = ["你好", "咨询", "价格多少", "在吗", "想购买", "客服", "有产品介绍吗"]
        message = random.choice(mock_messages)
        self.process_mock_message(message)
    
    def process_mock_message(self, message):
        """处理模拟消息"""
        # 模拟检测到新消息
        self.message_count += 1
        self.message_count_var.set(str(self.message_count))
        self.log(f"📥 检测到新消息: '{message}'")
        
        # 生成回复
        reply = self.generate_reply(message)
        
        # 模拟思考时间
        think_time = random.uniform(1, 2)
        self.log(f"🤔 正在生成回复... ({think_time:.1f}秒)")
        
        # 模拟发送延迟
        def send_reply():
            time.sleep(think_time)
            self.reply_count += 1
            self.reply_count_var.set(str(self.reply_count))
            self.log(f"✅ 自动回复: '{reply}'")
            self.log(f"📤 回复发送成功")
        
        # 在后台线程中处理回复
        reply_thread = threading.Thread(target=send_reply, daemon=True)
        reply_thread.start()
    
    def generate_reply(self, message):
        """生成回复内容"""
        # 关键词匹配
        for keyword, replies in self.replies.items():
            if keyword in message and keyword != "default":
                return random.choice(replies)
        
        # 默认回复
        return random.choice(self.replies["default"])
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("📝 日志已清空")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
        抖音聊天自动回复工具 MVP 使用说明:
        
        1. 点击"启动监控"开始模拟自动回复
        2. 点击"模拟消息"手动触发消息处理
        3. 程序会自动生成模拟消息并回复
        4. 观察日志了解工作流程
        
        注意: 这是演示版本，实际使用需要:
        - 安装完整依赖包
        - 配置抖音聊天窗口检测
        - 设置OCR文字识别
        """
        messagebox.showinfo("使用帮助", help_text)
    
    def on_closing(self):
        """窗口关闭事件"""
        if self.is_running:
            if messagebox.askokcancel("退出", "演示正在运行中，确定要退出吗？"):
                self.stop_demo()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """运行应用"""
        # 添加菜单栏
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=lambda: messagebox.showinfo("关于", "抖音聊天自动回复工具 MVP v1.0"))
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 启动主循环
        self.root.mainloop()

def main():
    """主函数"""
    try:
        print("="*60)
        print("           抖音聊天自动回复工具 MVP")
        print("="*60)
        print("🚀 启动简化版本...")
        
        app = SimpleAutoReply()
        app.run()
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main() 