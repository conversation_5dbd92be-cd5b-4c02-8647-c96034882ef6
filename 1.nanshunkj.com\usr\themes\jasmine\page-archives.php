<?php
/**
 * 归档页面
 * @package custom
 */
if (!defined("__TYPECHO_ROOT_DIR__")) exit();
?>
<!DOCTYPE html>
<html lang="zh">
<?php $this->need("header.php"); ?>
<style>
/* 年份和月份展开/收起动画 */
.year-item, .month-item {
    transition: all 0.2s ease;
}

details > summary {
    list-style: none;
}

details > summary::-webkit-details-marker {
    display: none;
}

/* 暗色模式适配 */
.dark .chart-container {
    background: rgba(38, 38, 38, 0.8);
}

/* 图表容器样式 */
.chart-container {
    height: 400px;
    position: relative;
}

.chart-inner {
    height: calc(100% - 3rem);
    width: 100%;
}

#categoryChart, #tagChart {
    width: 100% !important;
    height: 100% !important;
}

/* 图表响应式 */
@media (max-width: 768px) {
    .chart-container {
        height: 350px !important;
    }
}

/* 展开/收起箭头动画 */
details[open] > summary span:last-child {
    transform: rotate(180deg);
}

summary span:last-child {
    transition: transform 0.3s ease;
}

/* 图表容器统一样式 */
.chart-box {
    height: 400px;
    display: flex;
    flex-direction: column;
}

.chart-wrapper {
    flex: 1;
    position: relative;
    min-height: 0; /* 防止溢出 */
}

#categoryChart, #tagChart {
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    top: 0;
    left: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-box {
        height: 350px;
    }
}
</style>
<body class="jasmine-body">
    <div class="jasmine-container grid grid-cols-12">
        <?php $this->need("component/sidebar-left.php"); ?>
        
        <div class="flex col-span-12 lg:col-span-8 flex-col border-x-2 border-stone-100 dark:border-neutral-600 lg:pt-0 lg:px-6 pb-10 px-3">
            <?php $this->need("component/menu.php"); ?>
            
            <!-- 总览模块 -->
            <div style="padding: 50px 0 32px 0; margin-bottom: 32px; border-bottom: 2px solid #e5e7eb;">
                <h2 class="flex items-center gap-3" style="margin: 0 0 50px 0; font-size: 24px; font-weight: bold; color: #000000;">
                    <span style="font-size: 24px;">📊</span>
                    总览
                </h2>
                <div class="grid grid-cols-3 gap-4" style="margin-bottom: 32px;">
                    <?php
                    // 使用 Widget 获取统计数据
                    $this->widget('Widget_Stat')->to($stat);
                    
                    // 修改数据查询部分，统一查询条件
                    $db = Typecho_Db::get();
                    $commonSelect = $db->select('m.name', 'COUNT(DISTINCT c.cid) as count')
                        ->from('table.metas m')
                        ->join('table.relationships r', 'm.mid = r.mid')
                        ->join('table.contents c', 'r.cid = c.cid')
                        ->where('c.status = ?', 'publish')
                        ->where('c.type = ?', 'post')
                        ->where('c.password IS NULL')  // 排除加密文章
                        ->group('m.mid')
                        ->having('count > 0')
                        ->order('count', Typecho_Db::SORT_DESC);

                    // 获取分类数据
                    $categoryData = array();
                    $validCategoryCount = 0;
                    $categories = $db->fetchAll(clone $commonSelect
                        ->where('m.type = ?', 'category'));

                    foreach ($categories as $category) {
                        if ($category['count'] > 0) {
                            $validCategoryCount++;
                            $categoryData[] = array(
                                'name' => $category['name'],
                                'value' => (int)$category['count']
                            );
                        }
                    }
                    
                    // 获取标签数据
                    $tagData = array();
                    $validTagCount = 0;

                    // 直接使用 Widget 获取标签数据
                    $this->widget('Widget_Metas_Tag_Cloud', array(
                        'sort' => 'count',
                        'ignoreZeroCount' => true,
                        'desc' => true,
                        'limit' => 5
                    ))->to($tags);

                    while ($tags->next()) {
                        $validTagCount++;
                        $tagData[] = array(
                            'name' => $tags->name,
                            'value' => (int)$tags->count
                        );
                    }

                    // 确保数据按数量降序排序
                    usort($tagData, function($a, $b) {
                        return $b['value'] - $a['value'];
                    });
                    
                    // 不需要再使用 array_slice，因为已经在查询时限制了数量
                    ?>
                    
                    <!-- 文章总计 -->
                    <div class="bg-[#EBF3FE] rounded-lg p-4 border border-blue-200">
                        <div class="flex items-center gap-3">
                            <div class="text-3xl">📝</div>
                            <div>
                                <div class="text-blue-500 text-2xl font-bold"><?php echo $stat->publishedPostsNum; ?></div>
                                <div class="text-blue-500/80 text-sm">文章总计</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 标签总计 -->
                    <div class="bg-[#FFF7EC] rounded-lg p-4 border border-orange-200">
                        <div class="flex items-center gap-3">
                            <div class="text-3xl">🏷️</div>
                            <div>
                                <div class="text-orange-500 text-2xl font-bold"><?php echo $validTagCount; ?></div>
                                <div class="text-orange-500/80 text-sm">标签总计</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评论总计 -->
                    <div class="bg-[#F6FFED] rounded-lg p-4 border border-green-200">
                        <div class="flex items-center gap-3">
                            <div class="text-3xl">💬</div>
                            <div>
                                <div class="text-green-500 text-2xl font-bold"><?php echo $stat->publishedCommentsNum; ?></div>
                                <div class="text-green-500/80 text-sm">评论总计</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计图模块 -->
            <div style="padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #e5e7eb;">
                <h2 class="flex items-center gap-3" style="margin-top: 10px; margin-bottom: 10px; font-size: 24px; font-weight: bold; color: #000000;">
                    <span style="font-size: 24px;">📈</span>
                    统计图
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 分类统计图 -->
                    <div class="chart-box bg-white dark:bg-neutral-800 rounded-lg p-4">
                        <h3 class="text-lg font-medium mb-4 text-center dark:text-gray-200">分类统计</h3>
                        <div class="chart-wrapper">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 标签统计图 -->
                    <div class="chart-box bg-white dark:bg-neutral-800 rounded-lg p-4">
                        <h3 class="text-lg font-medium mb-4 text-center dark:text-gray-200">热门标签 Top5</h3>
                        <div class="chart-wrapper">
                            <canvas id="tagChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文章列表模块 -->
            <div style="margin-top: 50px;">
                <h2 class="flex items-center gap-3" style="margin-bottom: 50px; font-size: 24px; font-weight: bold; color: #000000;">
                    <span style="font-size: 24px;">📑</span>
                    文章数据
                </h2>
                <div class="space-y-6">
                    <?php
                    // 使用 Widget 获取文章列表
                    $this->widget('Widget_Contents_Post_Recent', 'pageSize=10000')->to($posts);
                    
                    // 按年份组织文章
                    $archives = array();
                    while($posts->next()) {
                        $year = date('Y', $posts->created);
                        $month = date('m', $posts->created);
                        $date = new DateTime(date('Y-m-d', $posts->created));
                        $weekOfMonth = ceil($date->format('j') / 7);
                        
                        if (!isset($archives[$year])) {
                            $archives[$year] = array();
                        }
                        if (!isset($archives[$year][$month])) {
                            $archives[$year][$month] = array(
                                'count' => 0,
                                'weeks' => array()
                            );
                        }
                        if (!isset($archives[$year][$month]['weeks'][$weekOfMonth])) {
                            $archives[$year][$month]['weeks'][$weekOfMonth] = array(
                                'count' => 0
                            );
                        }
                        
                        $archives[$year][$month]['count']++;
                        $archives[$year][$month]['weeks'][$weekOfMonth]['count']++;
                    }
                    
                    krsort($archives);
                    
                    if ($archives):
                        foreach ($archives as $year => $months):
                            // 计算年度文章总数
                            $yearPostCount = array_sum(array_map(function($month) {
                                return $month['count'];
                            }, $months));
                    ?>
                        <!-- 年份卡片 -->
                        <div class="bg-white dark:bg-neutral-800 rounded-lg border border-gray-100 dark:border-neutral-700 overflow-hidden">
                            <details class="group">
                                <summary class="flex items-center justify-between p-5 cursor-pointer hover:bg-gray-50 dark:hover:bg-neutral-700">
                                    <div class="flex items-center gap-3">
                                        <span style="font-size: 20px; font-weight: 500;" class="text-gray-900 dark:text-gray-100">
                                            <?php echo $year; ?>年本站总发布了
                                            <span style="font-size: 20px; margin: 0 4px;">📝</span>
                                            <?php echo $yearPostCount; ?> 篇文章
                                        </span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <span style="font-size: 16px; color: #666;" class="hidden md:inline dark:text-gray-400">点击查看详情</span>
                                        <span style="font-size: 24px; color: #666;" class="dark:text-gray-400">▼</span>
                                    </div>
                                </summary>

                                <!-- 月份层级 -->
                                <div class="border-t border-gray-100 dark:border-neutral-700 bg-gray-50/50 dark:bg-neutral-800/50">
                                    <div class="p-5" style="padding-left: 8em;">
                                        <?php 
                                        krsort($months); // 月份倒序排序
                                        foreach ($months as $month => $monthData): 
                                        ?>
                                            <details class="month-item group/month mb-5 last:mb-0">
                                                <summary class="flex items-center p-3 cursor-pointer hover:bg-white/80 dark:hover:bg-neutral-700/50 rounded-lg">
                                                    <span style="font-size: 20px; font-weight: 500;" class="text-gray-900 dark:text-gray-100">
                                                        <?php echo $month; ?>月
                                                    </span>
                                                    <span style="font-size: 20px; color: #666; margin-left: 16px;" class="dark:text-gray-400">
                                                        <span style="font-size: 20px;">📁</span>
                                                        <?php echo $monthData['count']; ?> 篇
                                                    </span>
                                                    <span style="font-size: 20px; color: #666; margin-left: 12px;" class="dark:text-gray-400">▼</span>
                                                </summary>

                                                <!-- 周层级 -->
                                                <div style="margin-top: 24px; margin-bottom: 16px; padding-left: 8em;">
                                                    <?php 
                                                    if (!empty($monthData['weeks'])):
                                                        krsort($monthData['weeks']);
                                                        foreach ($monthData['weeks'] as $weekNum => $weekData): 
                                                            if ($weekData['count'] > 0):
                                                    ?>
                                                        <div style="margin-bottom: 16px;">
                                                            <div class="flex items-center p-3 rounded-lg hover:bg-white/80 dark:hover:bg-neutral-700/50">
                                                                <span style="font-size: 20px;" class="text-gray-900 dark:text-gray-100">
                                                                    第 <?php echo $weekNum; ?> 周
                                                                </span>
                                                                <span style="font-size: 20px; color: #666; margin-left: 16px;" class="dark:text-gray-400">
                                                                    <span style="font-size: 20px;">⏰</span>
                                                                    <?php echo $weekData['count']; ?> 篇
                                                                </span>
                                                            </div>
                                                        </div>
                                                    <?php 
                                                            endif;
                                                        endforeach;
                                                    endif;
                                                    ?>
                                                </div>
                                            </details>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </details>
                        </div>
                    <?php
                        endforeach;
                    endif;
                    ?>
                </div>
            </div>

        </div>

        <div class="hidden lg:col-span-3 lg:block" id="sidebar-right">
            <?php $this->need("component/sidebar.php"); ?>
        </div>
    </div>
    <?php $this->need("footer.php"); ?>

    <!-- 使用本地 Chart.js -->
    <script src="<?php $this->options->themeUrl('assets/js/chart.min.js'); ?>"></script>

    <script>
    // 直接初始化图表，不需要异步加载
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // 使用已经处理好的数据
            const chartData = {
                category: <?php echo json_encode($categoryData); ?>,
                tags: <?php echo json_encode($tagData); ?>
            };

            // 分类统计饼图
            const categoryChart = new Chart(document.getElementById('categoryChart'), {
                type: 'doughnut',
                data: {
                    labels: chartData.category.map(item => item.name),
                    datasets: [{
                        data: chartData.category.map(item => item.value),
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56',
                            '#4BC0C0', '#9966FF', '#FF9F40'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                generateLabels: function(chart) {
                                    const data = chart.data;
                                    return data.labels.map((label, i) => ({
                                        text: `${label}: ${data.datasets[0].data[i]}篇`,
                                        fillStyle: data.datasets[0].backgroundColor[i]
                                    }));
                                }
                            }
                        }
                    }
                }
            });

            // 标签统计柱状图
            const tagChart = new Chart(document.getElementById('tagChart'), {
                type: 'bar',
                data: {
                    labels: chartData.tags.map(item => item.name),
                    datasets: [{
                        data: chartData.tags.map(item => item.value),
                        backgroundColor: '#36A2EB',
                        borderRadius: 6,
                        maxBarThickness: 50
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.raw} 篇`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                callback: function(value) {
                                    return value + ' 篇';
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    }
                }
            });

            // 处理暗色模式
            function updateChartsTheme() {
                const isDark = document.documentElement.classList.contains('dark');
                const textColor = isDark ? '#e5e7eb' : '#374151';
                
                [categoryChart, tagChart].forEach(chart => {
                    if (chart.options.plugins.legend) {
                        chart.options.plugins.legend.labels.color = textColor;
                    }
                    if (chart.options.scales) {
                        if (chart.options.scales.x) {
                            chart.options.scales.x.ticks.color = textColor;
                        }
                        if (chart.options.scales.y) {
                            chart.options.scales.y.ticks.color = textColor;
                        }
                    }
                    chart.update();
                });
            }

            // 监听主题变化
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class') {
                        updateChartsTheme();
                    }
                });
            });

            observer.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['class']
            });

            // 初始化主题
            updateChartsTheme();

            // 处理窗口大小变化
            window.addEventListener('resize', () => {
                categoryChart.resize();
                tagChart.resize();
            });

        } catch (error) {
            console.error('Failed to initialize charts:', error);
            document.querySelectorAll('#categoryChart, #tagChart').forEach(el => {
                el.innerHTML = `
                    <div style="text-align: center; color: #ef4444; padding: 20px;">
                        <p>图表加载失败</p>
                        <button onclick="location.reload()" 
                                style="margin-top: 10px; padding: 5px 10px; background: #ef4444; color: white; border-radius: 4px;">
                            重试
                        </button>
                    </div>
                `;
            });
        }
    });
    </script>
</body>
</html>
